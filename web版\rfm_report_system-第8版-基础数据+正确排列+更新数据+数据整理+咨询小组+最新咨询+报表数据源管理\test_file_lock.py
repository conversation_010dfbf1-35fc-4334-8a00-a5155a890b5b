#!/usr/bin/env python3
"""
测试文件占用处理功能
"""

import os
import time
import tempfile
import pandas as pd
from werkzeug.datastructures import FileStorage
from io import BytesIO

def safe_file_save(file, file_path, max_retries=3, retry_delay=1):
    """安全保存文件，处理文件占用问题"""
    for attempt in range(max_retries):
        try:
            # 尝试保存文件
            file.save(file_path)
            return True
        except PermissionError as e:
            if "WinError 32" in str(e) or "另一个程序正在使用此文件" in str(e):
                if attempt < max_retries - 1:
                    print(f"文件被占用，等待 {retry_delay} 秒后重试... (尝试 {attempt + 1}/{max_retries})")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避
                    continue
                else:
                    raise Exception(f"文件被其他程序占用，请关闭Excel等程序后重试。错误详情: {str(e)}")
            else:
                raise e
        except Exception as e:
            raise e
    return False


def is_file_locked(file_path):
    """检查文件是否被占用"""
    try:
        # 尝试以独占模式打开文件
        with open(file_path, 'r+b') as f:
            pass
        return False
    except (PermissionError, IOError):
        return True


def test_file_lock_handling():
    """测试文件占用处理"""
    
    print("=== 测试文件占用处理功能 ===")
    
    # 创建测试Excel文件
    test_data = {
        '原始分类': ['分类1', '分类2', '分类3'],
        '映射分类': ['新分类1', '新分类2', '新分类3']
    }
    df = pd.DataFrame(test_data)
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
        temp_path = tmp_file.name
        df.to_excel(temp_path, index=False)
    
    try:
        # 测试1：正常文件保存
        print("\n1. 测试正常文件保存...")
        
        # 读取文件内容
        with open(temp_path, 'rb') as f:
            file_content = f.read()
        
        # 创建FileStorage对象
        file_storage = FileStorage(
            stream=BytesIO(file_content),
            filename='test_mapping.xlsx',
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # 测试保存到新位置
        test_save_path = temp_path.replace('.xlsx', '_copy.xlsx')
        
        try:
            success = safe_file_save(file_storage, test_save_path)
            if success:
                print("✅ 正常文件保存成功")
                
                # 验证文件是否被占用
                if is_file_locked(test_save_path):
                    print("❌ 文件仍被占用")
                else:
                    print("✅ 文件未被占用，可以正常访问")
                
                # 清理测试文件
                if os.path.exists(test_save_path):
                    os.remove(test_save_path)
            else:
                print("❌ 文件保存失败")
        except Exception as e:
            print(f"❌ 文件保存异常: {str(e)}")
        
        # 测试2：文件占用检测
        print("\n2. 测试文件占用检测...")
        
        # 创建一个被占用的文件（通过保持文件句柄打开）
        locked_file_path = temp_path.replace('.xlsx', '_locked.xlsx')
        
        # 复制文件
        import shutil
        shutil.copy2(temp_path, locked_file_path)
        
        # 打开文件句柄（模拟Excel打开文件）
        try:
            with open(locked_file_path, 'r+b') as locked_file:
                print("文件已打开，模拟Excel占用状态")
                
                # 测试占用检测
                if is_file_locked(locked_file_path):
                    print("❌ 检测到文件被占用（这是预期的）")
                else:
                    print("✅ 文件占用检测失败")
                
                # 测试保存到被占用的文件
                file_storage.stream.seek(0)  # 重置流位置
                try:
                    success = safe_file_save(file_storage, locked_file_path)
                    print("❌ 意外成功保存到被占用的文件")
                except Exception as e:
                    if "另一个程序正在使用此文件" in str(e) or "WinError 32" in str(e):
                        print("✅ 正确检测到文件占用并抛出异常")
                    else:
                        print(f"❌ 异常类型不正确: {str(e)}")
        
        except Exception as e:
            print(f"测试过程中出现异常: {str(e)}")
        
        finally:
            # 清理测试文件
            if os.path.exists(locked_file_path):
                try:
                    os.remove(locked_file_path)
                except:
                    print("清理被占用的测试文件失败，请手动删除")
        
        print("\n=== 测试完成 ===")
        print("建议：")
        print("1. 上传Excel文件前，请确保文件已关闭")
        print("2. 如果遇到文件占用错误，请关闭Excel等程序后重试")
        print("3. 系统会自动重试3次，每次间隔递增")
        
    finally:
        # 清理临时文件
        if os.path.exists(temp_path):
            os.remove(temp_path)


if __name__ == "__main__":
    test_file_lock_handling()
