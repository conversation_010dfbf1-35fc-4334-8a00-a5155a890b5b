"""
简单配置文件生成器
"""
import getpass
import os

def create_simple_config():
    """创建简单的配置文件"""
    print("创建配置文件...")
    
    # 获取数据库信息
    host = input("数据库主机 [127.0.0.1]: ").strip() or '127.0.0.1'
    port = input("数据库端口 [3306]: ").strip() or '3306'
    user = input("数据库用户名 [root]: ").strip() or 'root'
    password = getpass.getpass("数据库密码: ")
    database = input("数据库名 [Old_Customer_System]: ").strip() or 'Old_Customer_System'
    
    try:
        # 备份原配置文件
        if os.path.exists('config.py'):
            os.rename('config.py', 'config.py.backup')
            print("✅ 原配置文件已备份")
        
        # 写入新配置文件
        with open('config.py', 'w', encoding='utf-8') as f:
            f.write('"""\n')
            f.write('系统配置文件\n')
            f.write('"""\n')
            f.write('import os\n')
            f.write('from datetime import timedelta\n\n')
            
            f.write('class Config:\n')
            f.write('    """基础配置类"""\n\n')
            
            f.write('    # 基础配置\n')
            f.write("    SECRET_KEY = 'your-secret-key-change-in-production'\n\n")
            
            f.write('    # 数据库配置\n')
            f.write(f"    MYSQL_HOST = '{host}'\n")
            f.write(f"    MYSQL_PORT = {port}\n")
            f.write(f"    MYSQL_USER = '{user}'\n")
            f.write(f"    MYSQL_PASSWORD = '{password}'\n")
            f.write(f"    MYSQL_DATABASE = '{database}'\n\n")
            
            f.write('    # SQLAlchemy 配置\n')
            f.write('    SQLALCHEMY_DATABASE_URI = (\n')
            f.write('        f"mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@"\n')
            f.write('        f"{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DATABASE}?charset=utf8mb4"\n')
            f.write('    )\n')
            f.write('    SQLALCHEMY_TRACK_MODIFICATIONS = False\n')
            f.write('    SQLALCHEMY_ENGINE_OPTIONS = {\n')
            f.write("        'pool_size': 10,\n")
            f.write("        'pool_timeout': 20,\n")
            f.write("        'pool_recycle': -1,\n")
            f.write("        'max_overflow': 0,\n")
            f.write("        'echo': False\n")
            f.write('    }\n\n')
            
            f.write('    # 会话配置\n')
            f.write('    PERMANENT_SESSION_LIFETIME = timedelta(minutes=30)\n')
            f.write('    SESSION_COOKIE_SECURE = False\n')
            f.write('    SESSION_COOKIE_HTTPONLY = True\n')
            f.write("    SESSION_COOKIE_SAMESITE = 'Lax'\n\n")
            
            f.write('    # 文件上传配置\n')
            f.write('    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB\n')
            f.write('    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), "uploads")\n')
            f.write("    ALLOWED_EXTENSIONS = {'xlsx', 'xls'}\n\n")
            
            f.write('    # 分页配置\n')
            f.write('    ITEMS_PER_PAGE = 20\n')
            f.write('    MAX_ITEMS_PER_PAGE = 100\n\n')
            
            f.write('    # 日志配置\n')
            f.write("    LOG_LEVEL = 'INFO'\n")
            f.write("    LOG_FILE = 'app.log'\n\n")
            
            f.write('    # 系统默认配置\n')
            f.write('    DEFAULT_CARD_NUMBER_MAX_LENGTH = 10\n')
            f.write('    DEFAULT_CONTENT_MAX_LENGTH = 500\n')
            f.write('    DEFAULT_PASSWORD_MIN_LENGTH = 8\n\n')
            
            f.write('    @staticmethod\n')
            f.write('    def init_app(app):\n')
            f.write('        """初始化应用配置"""\n')
            f.write("        upload_folder = app.config.get('UPLOAD_FOLDER')\n")
            f.write('        if upload_folder and not os.path.exists(upload_folder):\n')
            f.write('            os.makedirs(upload_folder)\n\n')
            
            f.write('class DevelopmentConfig(Config):\n')
            f.write('    """开发环境配置"""\n')
            f.write('    DEBUG = True\n\n')
            
            f.write('class ProductionConfig(Config):\n')
            f.write('    """生产环境配置"""\n')
            f.write('    DEBUG = False\n')
            f.write('    SESSION_COOKIE_SECURE = True\n\n')
            
            f.write('class TestingConfig(Config):\n')
            f.write('    """测试环境配置"""\n')
            f.write('    TESTING = True\n')
            f.write("    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'\n\n")
            
            f.write('# 配置字典\n')
            f.write('config = {\n')
            f.write("    'development': DevelopmentConfig,\n")
            f.write("    'production': ProductionConfig,\n")
            f.write("    'testing': TestingConfig,\n")
            f.write("    'default': DevelopmentConfig\n")
            f.write('}\n\n')
            
            f.write('def get_config():\n')
            f.write('    """获取当前配置"""\n')
            f.write("    return config.get(os.environ.get('FLASK_ENV', 'default'), DevelopmentConfig)\n")
        
        print("✅ 配置文件创建成功")
        return True
        
    except Exception as e:
        print(f"❌ 配置文件创建失败: {e}")
        return False

def test_config():
    """测试配置"""
    try:
        import sys
        import importlib
        
        # 重新加载配置模块
        if 'config' in sys.modules:
            importlib.reload(sys.modules['config'])
        
        from config import get_config
        config = get_config()
        
        print("配置信息:")
        print(f"  数据库主机: {config.MYSQL_HOST}")
        print(f"  数据库端口: {config.MYSQL_PORT}")
        print(f"  数据库用户: {config.MYSQL_USER}")
        print(f"  数据库名称: {config.MYSQL_DATABASE}")
        print(f"  密码长度: {len(config.MYSQL_PASSWORD)} 字符")
        
        return True
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def main():
    """主函数"""
    print("简单配置文件生成器")
    print("="*30)
    
    if create_simple_config():
        print("\n测试新配置:")
        print("-"*30)
        if test_config():
            print("\n✅ 配置创建完成！")
            print("现在可以运行:")
            print("  python test_system.py  # 测试系统")
            print("  python init_db.py      # 初始化数据库")
        else:
            print("\n❌ 配置有问题")
    else:
        print("\n❌ 配置创建失败")

if __name__ == '__main__':
    main()
