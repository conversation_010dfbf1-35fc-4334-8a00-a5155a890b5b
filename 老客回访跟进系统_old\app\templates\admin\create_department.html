{% extends "base.html" %}

{% block title %}新增部门 - 老客回访与跟进系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-building"></i> 新增部门</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('admin.index') }}">系统管理</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('admin.departments') }}">部门管理</a></li>
                <li class="breadcrumb-item active">新增部门</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-building"></i> 部门信息</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="department_name" class="form-label">部门名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="department_name" name="department_name" 
                                       placeholder="请输入部门名称" required>
                                <div class="form-text">部门的显示名称，如"技术部"、"销售部"</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="department_code" class="form-label">部门代码 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="department_code" name="department_code" 
                                       placeholder="请输入部门代码" required>
                                <div class="form-text">部门的唯一标识，如"TECH"、"SALES"</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="parent_id" class="form-label">上级部门</label>
                                <select class="form-select" id="parent_id" name="parent_id">
                                    <option value="">无上级部门（根部门）</option>
                                    {% for department in parent_departments %}
                                    <option value="{{ department.id }}">{{ department.full_name }}</option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">选择该部门的上级部门</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sort_order" class="form-label">排序顺序</label>
                                <input type="number" class="form-control" id="sort_order" name="sort_order" 
                                       value="0" min="0" max="999">
                                <div class="form-text">数字越小排序越靠前</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">部门描述</label>
                        <textarea class="form-control" id="description" name="description" rows="3" 
                                  placeholder="请输入部门描述（可选）"></textarea>
                        <div class="form-text">描述该部门的职能和作用</div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('admin.departments') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 创建部门
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-info-circle"></i> 部门层级结构</h6>
            </div>
            <div class="card-body">
                {% if parent_departments %}
                <div class="small">
                    <strong>现有部门：</strong>
                    <ul class="list-unstyled mt-2">
                        {% for department in parent_departments %}
                        <li class="mb-1">
                            <i class="fas fa-building text-muted"></i> {{ department.full_name }}
                            {% if department.children %}
                            <small class="text-muted">({{ department.children|length }} 个子部门)</small>
                            {% endif %}
                        </li>
                        {% endfor %}
                    </ul>
                </div>
                {% else %}
                <p class="text-muted small">暂无部门，这将是第一个部门</p>
                {% endif %}
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-exclamation-triangle"></i> 注意事项</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li><i class="fas fa-check text-success"></i> 部门名称和代码必须唯一</li>
                    <li><i class="fas fa-check text-success"></i> 部门代码建议使用英文大写</li>
                    <li><i class="fas fa-check text-success"></i> 可以创建多级部门结构</li>
                    <li><i class="fas fa-check text-success"></i> 排序数字越小越靠前显示</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}