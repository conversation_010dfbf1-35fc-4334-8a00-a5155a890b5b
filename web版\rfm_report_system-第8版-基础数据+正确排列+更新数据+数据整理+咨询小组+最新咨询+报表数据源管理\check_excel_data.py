#!/usr/bin/env python3
"""
检查Excel数据结构
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.models import db, DataProcessingTask
import pandas as pd

def check_excel_data():
    """检查Excel数据结构"""
    
    app = create_app('development')
    
    with app.app_context():
        try:
            # 查找已完成的任务
            task = DataProcessingTask.query.filter_by(status='completed').first()
            if not task:
                print("没有找到已完成的任务")
                return
            
            print(f"任务: {task.task_name}")
            print(f"输出文件: {task.output_file_path}")
            
            if not task.output_file_path or not os.path.exists(task.output_file_path):
                print("输出文件不存在")
                return
            
            # 读取Excel文件
            df = pd.read_excel(task.output_file_path)
            print(f"\n数据概况:")
            print(f"  行数: {len(df)}")
            print(f"  列数: {len(df.columns)}")
            
            print(f"\n所有列名:")
            for i, col in enumerate(df.columns):
                print(f"  {i+1:2d}. {col}")
            
            # 查找等级相关列
            level_columns = [col for col in df.columns if '等级' in col]
            print(f"\n等级相关列 ({len(level_columns)} 个):")
            for col in level_columns:
                print(f"  - {col}")
            
            # 查找会员卡级相关列
            card_columns = [col for col in df.columns if '会员卡级' in col or '会员等级' in col]
            print(f"\n会员卡级相关列 ({len(card_columns)} 个):")
            for col in card_columns:
                print(f"  - {col}")
            
            # 查找科室标签相关列
            dept_columns = [col for col in df.columns if '科室标签' in col]
            print(f"\n科室标签相关列 ({len(dept_columns)} 个):")
            for col in dept_columns:
                print(f"  - {col}")
            
            # 检查会员卡号列
            if '会员卡号' in df.columns:
                print(f"\n会员卡号统计:")
                print(f"  总数: {len(df)}")
                print(f"  非空: {df['会员卡号'].notna().sum()}")
                print(f"  唯一值: {df['会员卡号'].nunique()}")
            
            # 检查等级数据分布
            if level_columns:
                print(f"\n等级数据分布示例:")
                for col in level_columns[:2]:  # 只显示前2个
                    print(f"\n{col}:")
                    value_counts = df[col].value_counts().head(10)
                    for value, count in value_counts.items():
                        print(f"  {value}: {count}")
            
            # 显示前几行数据
            print(f"\n前3行数据预览:")
            print(df.head(3).to_string())
            
        except Exception as e:
            print(f"检查失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    check_excel_data()
