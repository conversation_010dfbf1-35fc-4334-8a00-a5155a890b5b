"""
简化的启动脚本
"""
import os
import sys
from app import create_app, db
from config import get_config

def main():
    """主函数"""
    print("=== 老客回访与跟进系统 ===")
    
    # 创建应用
    config_class = get_config()
    app = create_app(config_class)
    
    # 检查数据库是否已初始化
    with app.app_context():
        try:
            from app.models import Role
            if not Role.query.first():
                print("数据库未初始化，请先运行 python init_db.py")
                return
        except Exception as e:
            print(f"数据库连接失败: {e}")
            print("请检查数据库配置或运行 python init_db.py 初始化数据库")
            return
    
    print("系统启动成功！")
    print("访问地址: http://localhost:5000")
    print("默认管理员账号: admin / admin123")
    print("按 Ctrl+C 停止服务")
    
    # 启动应用
    try:
        app.run(host='0.0.0.0', port=5000, debug=app.config.get('DEBUG', False))
    except KeyboardInterrupt:
        print("\n系统已停止")

if __name__ == '__main__':
    main()
