# -*- coding: utf-8 -*-
"""
基础管理路由
"""

from flask import render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from app.basic_mgmt import bp
from app.forms.user_forms import UserForm, UserSearchForm, ResetPasswordForm
from app.services.user_service import UserService
from app.services.permission_service import require_permission, require_role, require_any_permission
from app.repositories.user_repository import UserRepository

@bp.route('/users')
@login_required
@require_permission('manage_users')
def users():
    """用户管理列表"""
    search_form = UserSearchForm()
    user_repo = UserRepository()
    
    # 获取搜索参数
    query = request.args.get('query', '')
    role = request.args.get('role', '')
    department_id = request.args.get('department_id', type=int)
    is_active = request.args.get('is_active', '')
    page = request.args.get('page', 1, type=int)
    
    # 构建查询条件
    criteria = {}
    if role:
        criteria['role'] = role
    if department_id:
        criteria['department_id'] = department_id
    if is_active:
        criteria['is_active'] = bool(int(is_active))
    
    # 分页查询
    if query:
        # 如果有搜索关键词，使用搜索功能
        users_list = user_repo.search_by_simple_code(query)
        # 简单的内存分页（实际项目中应该在数据库层面实现）
        total = len(users_list)
        per_page = 20
        start = (page - 1) * per_page
        end = start + per_page
        users_list = users_list[start:end]
        
        # 创建分页对象
        class SimplePagination:
            def __init__(self, items, total, page, per_page):
                self.items = items
                self.total = total
                self.page = page
                self.per_page = per_page
                self.pages = (total + per_page - 1) // per_page
                self.has_prev = page > 1
                self.has_next = page < self.pages
                self.prev_num = page - 1 if self.has_prev else None
                self.next_num = page + 1 if self.has_next else None
        
        pagination = SimplePagination(users_list, total, page, per_page)
    else:
        pagination = user_repo.paginate(page=page, per_page=20, criteria=criteria)
    
    # 设置表单默认值
    if request.method == 'GET':
        search_form.query.data = query
        search_form.role.data = role
        search_form.department_id.data = department_id or 0
        search_form.is_active.data = is_active
    
    return render_template('basic_mgmt/users.html', 
                         pagination=pagination, 
                         search_form=search_form,
                         title='用户管理')

@bp.route('/users/create', methods=['GET', 'POST'])
@login_required
@require_permission('manage_users')
def create_user():
    """创建用户"""
    form = UserForm()
    
    if form.validate_on_submit():
        try:
            user_service = UserService()
            
            # 生成简码
            if not form.simple_code.data:
                form.simple_code.data = user_service.generate_simple_code(form.real_name.data)
            
            user_data = {
                'username': form.username.data,
                'real_name': form.real_name.data,
                'role': form.role.data,
                'department_id': form.department_id.data,
                'simple_code': form.simple_code.data,
                'password': form.password.data,
                'is_active': form.is_active.data
            }
            
            user = user_service.create_user(user_data)
            flash(f'用户 "{user.username}" 创建成功！', 'success')
            return redirect(url_for('basic_mgmt.users'))
            
        except Exception as e:
            flash(f'创建用户失败: {str(e)}', 'error')
    
    return render_template('basic_mgmt/create_user.html', form=form, title='创建用户')

@bp.route('/users/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
@require_permission('manage_users')
def edit_user(user_id):
    """编辑用户"""
    user_repo = UserRepository()
    user = user_repo.get_by_id(user_id)
    if not user:
        flash('用户不存在', 'error')
        return redirect(url_for('basic_mgmt.users'))
    
    form = UserForm(user=user, obj=user)
    
    if form.validate_on_submit():
        try:
            user_service = UserService()
            
            user_data = {
                'username': form.username.data,
                'real_name': form.real_name.data,
                'role': form.role.data,
                'department_id': form.department_id.data,
                'simple_code': form.simple_code.data,
                'is_active': form.is_active.data
            }
            
            # 如果提供了新密码，包含在更新数据中
            if form.password.data:
                user_data['password'] = form.password.data
            
            user_service.update_user(user_id, user_data)
            flash(f'用户 "{user.username}" 更新成功！', 'success')
            return redirect(url_for('basic_mgmt.users'))
            
        except Exception as e:
            flash(f'更新用户失败: {str(e)}', 'error')
    
    return render_template('basic_mgmt/edit_user.html', form=form, user=user, title='编辑用户')

@bp.route('/users/<int:user_id>/toggle-status', methods=['POST'])
@login_required
@require_permission('manage_users')
def toggle_user_status(user_id):
    """切换用户激活状态"""
    user_service = UserService()
    user = user_service.get_user_by_id(user_id)
    
    if not user:
        return jsonify({'success': False, 'message': '用户不存在'})
    
    try:
        if user.is_active:
            success = user_service.deactivate_user(user_id)
            action = '停用'
        else:
            success = user_service.activate_user(user_id)
            action = '激活'
        
        if success:
            return jsonify({'success': True, 'message': f'用户{action}成功', 'is_active': not user.is_active})
        else:
            return jsonify({'success': False, 'message': f'用户{action}失败'})
            
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@bp.route('/users/<int:user_id>/reset-password', methods=['POST'])
@login_required
@require_permission('manage_users')
def reset_user_password(user_id):
    """重置用户密码"""
    form = ResetPasswordForm()
    
    if form.validate_on_submit():
        try:
            user_service = UserService()
            success = user_service.reset_password(user_id, form.new_password.data)
            
            if success:
                return jsonify({'success': True, 'message': '密码重置成功'})
            else:
                return jsonify({'success': False, 'message': '密码重置失败'})
                
        except Exception as e:
            return jsonify({'success': False, 'message': str(e)})
    
    return jsonify({'success': False, 'message': '表单验证失败'})

@bp.route('/departments')
@login_required
@require_permission('manage_departments')
def departments():
    """部门管理列表"""
    from app.forms.department_forms import DepartmentSearchForm
    from app.services.department_service import DepartmentService
    
    search_form = DepartmentSearchForm()
    dept_service = DepartmentService()
    
    # 获取搜索参数
    query = request.args.get('query', '')
    is_active = request.args.get('is_active', '')
    
    # 获取部门列表
    if query:
        departments_list = dept_service.search_departments(query)
    else:
        departments_list = dept_service.get_all_departments()
    
    # 根据状态过滤
    if is_active:
        active_filter = bool(int(is_active))
        departments_list = [d for d in departments_list if d.is_active == active_filter]
    
    # 设置表单默认值
    if request.method == 'GET':
        search_form.query.data = query
        search_form.is_active.data = is_active
    
    return render_template('basic_mgmt/departments.html', 
                         departments=departments_list,
                         search_form=search_form,
                         title='部门管理')

@bp.route('/departments/create', methods=['GET', 'POST'])
@login_required
@require_permission('manage_departments')
def create_department():
    """创建部门"""
    from app.forms.department_forms import DepartmentForm
    from app.services.department_service import DepartmentService
    
    form = DepartmentForm()
    
    if form.validate_on_submit():
        try:
            dept_service = DepartmentService()
            
            dept_data = {
                'name': form.name.data,
                'description': form.description.data,
                'is_active': form.is_active.data
            }
            
            department = dept_service.create_department(dept_data)
            flash(f'部门 "{department.name}" 创建成功！', 'success')
            return redirect(url_for('basic_mgmt.departments'))
            
        except Exception as e:
            flash(f'创建部门失败: {str(e)}', 'error')
    
    return render_template('basic_mgmt/create_department.html', form=form, title='创建部门')

@bp.route('/departments/<int:dept_id>/edit', methods=['GET', 'POST'])
@login_required
@require_permission('manage_departments')
def edit_department(dept_id):
    """编辑部门"""
    from app.forms.department_forms import DepartmentForm
    from app.services.department_service import DepartmentService
    
    dept_service = DepartmentService()
    department = dept_service.get_department_by_id(dept_id)
    if not department:
        flash('部门不存在', 'error')
        return redirect(url_for('basic_mgmt.departments'))
    
    form = DepartmentForm(department=department, obj=department)
    
    if form.validate_on_submit():
        try:
            dept_data = {
                'name': form.name.data,
                'description': form.description.data,
                'is_active': form.is_active.data
            }
            
            dept_service.update_department(dept_id, dept_data)
            flash(f'部门 "{department.name}" 更新成功！', 'success')
            return redirect(url_for('basic_mgmt.departments'))
            
        except Exception as e:
            flash(f'更新部门失败: {str(e)}', 'error')
    
    return render_template('basic_mgmt/edit_department.html', 
                         form=form, department=department, title='编辑部门')

@bp.route('/departments/<int:dept_id>/toggle-status', methods=['POST'])
@login_required
@require_permission('manage_departments')
def toggle_department_status(dept_id):
    """切换部门激活状态"""
    from app.services.department_service import DepartmentService
    
    dept_service = DepartmentService()
    department = dept_service.get_department_by_id(dept_id)
    
    if not department:
        return jsonify({'success': False, 'message': '部门不存在'})
    
    try:
        if department.is_active:
            # 检查是否可以停用
            can_deactivate, message = dept_service.can_delete_department(dept_id)
            if not can_deactivate:
                return jsonify({'success': False, 'message': message})
            
            success = dept_service.deactivate_department(dept_id)
            action = '停用'
        else:
            success = dept_service.activate_department(dept_id)
            action = '激活'
        
        if success:
            return jsonify({'success': True, 'message': f'部门{action}成功', 'is_active': not department.is_active})
        else:
            return jsonify({'success': False, 'message': f'部门{action}失败'})
            
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@bp.route('/channels')
@login_required
@require_any_permission('manage_channels', 'view_department_customers')
def channels():
    """渠道管理列表"""
    from app.forms.channel_forms import ChannelSearchForm
    from app.services.channel_service import ChannelService
    
    search_form = ChannelSearchForm()
    channel_service = ChannelService()
    
    # 获取搜索参数
    query = request.args.get('query', '')
    category = request.args.get('category', '')
    is_active = request.args.get('is_active', '')
    
    # 获取渠道列表
    if query:
        channels_list = channel_service.search_channels(query)
    else:
        channels_list = channel_service.get_active_channels()
        if not is_active or is_active == '':
            # 如果没有状态筛选，显示所有渠道
            from app.repositories.channel_repository import ChannelRepository
            channel_repo = ChannelRepository()
            channels_list = channel_repo.get_all()
    
    # 根据分类和状态过滤
    if category:
        channels_list = [c for c in channels_list if c.category == category]
    if is_active:
        active_filter = bool(int(is_active))
        channels_list = [c for c in channels_list if c.is_active == active_filter]
    
    # 设置表单默认值
    if request.method == 'GET':
        search_form.query.data = query
        search_form.category.data = category
        search_form.is_active.data = is_active
    
    return render_template('basic_mgmt/channels.html', 
                         channels=channels_list,
                         search_form=search_form,
                         title='渠道管理')

@bp.route('/channels/create', methods=['GET', 'POST'])
@login_required
@require_permission('manage_channels')
def create_channel():
    """创建渠道"""
    from app.forms.channel_forms import ChannelForm
    from app.services.channel_service import ChannelService
    
    form = ChannelForm()
    
    if form.validate_on_submit():
        try:
            channel_service = ChannelService()
            
            channel_data = {
                'name': form.name.data,
                'category': form.category.data,
                'simple_code': form.simple_code.data,
                'is_active': form.is_active.data
            }
            
            channel = channel_service.create_channel(channel_data)
            flash(f'渠道 "{channel.name}" 创建成功！', 'success')
            return redirect(url_for('basic_mgmt.channels'))
            
        except Exception as e:
            flash(f'创建渠道失败: {str(e)}', 'error')
    
    return render_template('basic_mgmt/create_channel.html', form=form, title='创建渠道')

@bp.route('/channels/<int:channel_id>/toggle-status', methods=['POST'])
@login_required
@require_permission('manage_channels')
def toggle_channel_status(channel_id):
    """切换渠道激活状态"""
    from app.services.channel_service import ChannelService
    
    channel_service = ChannelService()
    channel = channel_service.get_channel_by_id(channel_id)
    
    if not channel:
        return jsonify({'success': False, 'message': '渠道不存在'})
    
    try:
        if channel.is_active:
            success = channel_service.deactivate_channel(channel_id)
            action = '停用'
        else:
            success = channel_service.activate_channel(channel_id)
            action = '激活'
        
        if success:
            return jsonify({'success': True, 'message': f'渠道{action}成功', 'is_active': not channel.is_active})
        else:
            return jsonify({'success': False, 'message': f'渠道{action}失败'})
            
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@bp.route('/channels/import', methods=['GET', 'POST'])
@login_required
@require_permission('manage_channels')
def import_channels():
    """批量导入渠道"""
    from app.forms.channel_forms import ChannelImportForm
    from app.services.channel_service import ChannelService
    import os
    from werkzeug.utils import secure_filename
    
    form = ChannelImportForm()
    
    if form.validate_on_submit():
        try:
            # 保存上传的文件
            file = form.file.data
            filename = secure_filename(file.filename)
            upload_path = os.path.join('uploads', filename)
            
            # 确保uploads目录存在
            os.makedirs('uploads', exist_ok=True)
            
            file.save(upload_path)
            
            # 导入渠道
            channel_service = ChannelService()
            success, message, result = channel_service.import_channels_from_excel(upload_path)
            
            # 删除临时文件
            try:
                os.remove(upload_path)
            except:
                pass
            
            if success:
                flash(f'{message}。处理了 {result.get("processed_rows", 0)} 行数据，'
                      f'成功创建 {result.get("created_channels", 0)} 个渠道，'
                      f'跳过 {result.get("skipped_rows", 0)} 行。', 'success')
                return redirect(url_for('basic_mgmt.channels'))
            else:
                flash(message, 'error')
                
        except Exception as e:
            flash(f'导入失败: {str(e)}', 'error')
    
    return render_template('basic_mgmt/import_channels.html', form=form, title='批量导入渠道')

@bp.route('/channels/template')
@login_required
@require_permission('manage_channels')
def download_channel_template():
    """下载渠道导入模板"""
    import pandas as pd
    from flask import send_file, current_app
    import io
    import os
    import tempfile
    
    try:
        # 方案1：使用临时文件
        try:
            # 创建模板数据
            template_data = {
                '渠道名称': ['示例渠道1', '示例渠道2', '示例渠道3'],
                '渠道分类': ['线上推广', '线下活动', '合作伙伴'],
                '简码': ['SLQD1', 'SLQD2', 'SLQD3']
            }
            
            df = pd.DataFrame(template_data)
            
            # 使用临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as tmp_file:
                # 保存Excel文件到临时文件
                df.to_excel(tmp_file.name, index=False, sheet_name='渠道导入模板')
                
                # 返回文件下载
                return send_file(tmp_file.name, 
                                as_attachment=True, 
                                download_name='渠道导入模板.xlsx',
                                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        except:
            # 方案2：使用预先创建的模板文件
            template_path = os.path.join(os.getcwd(), 'uploads', 'channel_import_template.xlsx')
            
            if os.path.exists(template_path):
                return send_file(template_path, 
                                as_attachment=True, 
                                download_name='渠道导入模板.xlsx',
                                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            else:
                raise FileNotFoundError("模板文件不存在")
        
    except Exception as e:
        flash(f'模板下载失败: {str(e)}', 'error')
        return redirect(url_for('basic_mgmt.channels'))

@bp.route('/settings')
@login_required
@require_permission('manage_system')
def system_settings():
    """系统设置页面"""
    from app.services.system_service import SystemService
    
    # 获取当前设置
    settings = SystemService.get_all_settings()
    
    return render_template('basic_mgmt/system_settings.html', 
                         settings=settings,
                         title='系统设置')

@bp.route('/settings/general', methods=['GET', 'POST'])
@login_required
@require_permission('manage_system')
def general_settings():
    """通用设置"""
    from app.forms.system_forms import SystemSettingsForm
    from app.services.system_service import SystemService
    
    form = SystemSettingsForm()
    
    if request.method == 'GET':
        # 加载当前设置
        settings = SystemService.get_all_settings()
        form.card_number_length.data = int(settings.get('card_number_length', 10))
        form.inquiry_content_max_length.data = int(settings.get('inquiry_content_max_length', 500))
        form.follow_up_note_max_length.data = int(settings.get('follow_up_note_max_length', 500))
        form.system_name.data = settings.get('system_name', '老客登记信息反馈系统')
        form.system_version.data = settings.get('system_version', '1.0.0')
        form.auto_generate_simple_code.data = settings.get('auto_generate_simple_code', 'true') == 'true'
        form.enable_duplicate_check.data = settings.get('enable_duplicate_check', 'true') == 'true'
        form.require_last_visit_date.data = settings.get('require_last_visit_date', 'false') == 'true'
        form.data_retention_days.data = int(settings.get('data_retention_days', 365))
        form.settings_note.data = settings.get('settings_note', '')
    
    if form.validate_on_submit():
        try:
            settings_data = {
                'card_number_length': form.card_number_length.data,
                'inquiry_content_max_length': form.inquiry_content_max_length.data,
                'follow_up_note_max_length': form.follow_up_note_max_length.data,
                'system_name': form.system_name.data,
                'system_version': form.system_version.data,
                'auto_generate_simple_code': form.auto_generate_simple_code.data,
                'enable_duplicate_check': form.enable_duplicate_check.data,
                'require_last_visit_date': form.require_last_visit_date.data,
                'data_retention_days': form.data_retention_days.data,
                'settings_note': form.settings_note.data
            }
            
            SystemService.update_settings(settings_data)
            flash('系统设置保存成功！', 'success')
            return redirect(url_for('basic_mgmt.system_settings'))
            
        except Exception as e:
            flash(f'保存设置失败: {str(e)}', 'error')
    
    return render_template('basic_mgmt/general_settings.html', 
                         form=form, title='通用设置')

@bp.route('/settings/field-rules', methods=['GET', 'POST'])
@login_required
@require_permission('manage_system')
def field_rules():
    """字段规则设置"""
    from app.forms.system_forms import FieldRulesForm
    from app.services.system_service import SystemService
    
    form = FieldRulesForm()
    
    if request.method == 'GET':
        # 加载当前规则
        rules = SystemService.get_field_rules()
        form.card_number_rule.data = rules.get('card_number_rule', '')
        form.onsite_consultant_rule.data = rules.get('onsite_consultant_rule', '')
        form.online_consultant_rule.data = rules.get('online_consultant_rule', '')
        form.channel_rule.data = rules.get('channel_rule', '')
        form.inquiry_content_rule.data = rules.get('inquiry_content_rule', '')
        form.last_visit_date_rule.data = rules.get('last_visit_date_rule', '')
        form.follow_up_note_rule.data = rules.get('follow_up_note_rule', '')
        form.follow_up_time_rule.data = rules.get('follow_up_time_rule', '')
        form.create_time_rule.data = rules.get('create_time_rule', '')
    
    if form.validate_on_submit():
        try:
            rules_data = {
                'card_number_rule': form.card_number_rule.data,
                'onsite_consultant_rule': form.onsite_consultant_rule.data,
                'online_consultant_rule': form.online_consultant_rule.data,
                'channel_rule': form.channel_rule.data,
                'inquiry_content_rule': form.inquiry_content_rule.data,
                'last_visit_date_rule': form.last_visit_date_rule.data,
                'follow_up_note_rule': form.follow_up_note_rule.data,
                'follow_up_time_rule': form.follow_up_time_rule.data,
                'create_time_rule': form.create_time_rule.data
            }
            
            SystemService.update_field_rules(rules_data)
            flash('字段规则保存成功！', 'success')
            return redirect(url_for('basic_mgmt.system_settings'))
            
        except Exception as e:
            flash(f'保存规则失败: {str(e)}', 'error')
    
    return render_template('basic_mgmt/field_rules.html', 
                         form=form, title='字段规则设置')

@bp.route('/settings/reset', methods=['POST'])
@login_required
@require_permission('manage_system')
def reset_settings():
    """重置系统设置"""
    from app.services.system_service import SystemService
    
    try:
        SystemService.reset_to_defaults()
        flash('系统设置已重置为默认值！', 'success')
    except Exception as e:
        flash(f'重置设置失败: {str(e)}', 'error')
    
    return redirect(url_for('basic_mgmt.system_settings'))