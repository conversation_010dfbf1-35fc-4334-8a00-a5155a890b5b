# -*- coding: utf-8 -*-
"""
老客登记信息反馈系统数据模型
"""

from datetime import datetime
from decimal import Decimal
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from sqlalchemy import Numeric
import secrets

from app import db

class User(UserMixin, db.Model):
    """用户模型"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    salt = db.Column(db.String(32), nullable=False)
    real_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.Enum('admin', 'director', 'manager', 'online_consultant', 'onsite_consultant'), 
                     nullable=False, index=True)
    department_id = db.Column(db.Integer, db.<PERSON>Key('departments.id'), nullable=True, index=True)
    simple_code = db.Column(db.String(20), nullable=True, index=True)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    last_login = db.Column(db.DateTime, nullable=True)
    
    # 关系
    department = db.relationship('Department', backref='users')
    created_customers = db.relationship('Customer', foreign_keys='Customer.created_by', backref='creator')
    onsite_customers = db.relationship('Customer', foreign_keys='Customer.onsite_consultant_id', backref='onsite_consultant')
    online_customers = db.relationship('Customer', foreign_keys='Customer.online_consultant_id', backref='online_consultant')
    follow_ups = db.relationship('Customer', foreign_keys='Customer.follow_up_by', backref='follow_up_consultant')
    
    def set_password(self, password):
        """设置密码"""
        self.salt = secrets.token_hex(16)
        self.password_hash = generate_password_hash(password + self.salt)
    
    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password + self.salt)
    
    def has_permission(self, permission):
        """检查用户权限"""
        role_permissions = {
            'admin': ['all'],
            'director': ['view_all_customers', 'view_all_reports', 'upload_data', 'view_all_statistics', 'view_department_statistics', 'view_own_statistics'],
            'manager': ['view_department_customers', 'view_department_reports', 'manage_channels', 'manage_departments', 'manage_system', 'upload_data', 'view_department_statistics', 'view_own_statistics'],
            'online_consultant': ['create_customer', 'view_own_customers', 'upload_data', 'view_own_statistics'],
            'onsite_consultant': ['view_assigned_customers', 'update_follow_up', 'upload_data', 'view_own_statistics']
        }
        user_permissions = role_permissions.get(self.role, [])
        return 'all' in user_permissions or permission in user_permissions
    
    def __repr__(self):
        return f'<User {self.username}>'

class Department(db.Model):
    """部门模型"""
    __tablename__ = 'departments'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, index=True)
    description = db.Column(db.Text, nullable=True)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def __repr__(self):
        return f'<Department {self.name}>'

class Channel(db.Model):
    """渠道模型"""
    __tablename__ = 'channels'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False, index=True)
    category = db.Column(db.String(100), nullable=True, index=True)
    simple_code = db.Column(db.String(50), nullable=True, index=True)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # 关系
    customers = db.relationship('Customer', backref='channel')
    
    def __repr__(self):
        return f'<Channel {self.name}>'

class Customer(db.Model):
    """客户模型"""
    __tablename__ = 'customers'
    
    id = db.Column(db.Integer, primary_key=True)
    card_number = db.Column(db.String(18), unique=True, nullable=False, index=True)
    onsite_consultant_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    online_consultant_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    channel_id = db.Column(db.Integer, db.ForeignKey('channels.id'), nullable=False, index=True)
    inquiry_content = db.Column(db.Text, nullable=True)
    last_visit_date = db.Column(db.Date, nullable=True, index=True)
    registration_date = db.Column(db.Date, nullable=False, default=datetime.utcnow().date, index=True)
    follow_up_note = db.Column(db.Text, nullable=True)
    follow_up_time = db.Column(db.DateTime, nullable=True)
    follow_up_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def get_follow_up_visit_status(self):
        """获取跟进到院状态"""
        # 如果没有跟进记录，返回空
        if not self.follow_up_time:
            return ""

        # 查找跟进时间之后的到院记录
        from app.models import VisitRecord
        visit_after_followup = VisitRecord.query.filter(
            VisitRecord.member_card_number == self.card_number,
            VisitRecord.visit_date > self.follow_up_time.date()
        ).first()

        if visit_after_followup:
            return "已到院"
        else:
            return ""

    def get_follow_up_consumption_amount(self):
        """获取跟进消费金额"""
        # 如果没有跟进记录，返回None表示空
        if not self.follow_up_time:
            return None

        # 查找跟进时间之后的消费记录总金额
        from app.models import ConsumptionRecord
        from sqlalchemy import func

        total_amount = db.session.query(func.sum(ConsumptionRecord.amount)).filter(
            ConsumptionRecord.member_card_number == self.card_number,
            ConsumptionRecord.consumption_date > self.follow_up_time.date()
        ).scalar()

        amount = float(total_amount) if total_amount else 0.0
        # 如果金额<=0，返回None表示空
        return amount if amount > 0 else None

    def __repr__(self):
        return f'<Customer {self.card_number}>'

class OnsiteGroup(db.Model):
    """现场小组模型"""
    __tablename__ = 'onsite_groups'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, index=True)
    description = db.Column(db.Text, nullable=True)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    def __repr__(self):
        return f'<OnsiteGroup {self.name}>'

class OnsiteGroupMember(db.Model):
    """现场小组成员关系模型"""
    __tablename__ = 'onsite_group_members'
    
    id = db.Column(db.Integer, primary_key=True)
    group_id = db.Column(db.Integer, db.ForeignKey('onsite_groups.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    joined_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    # 唯一约束
    __table_args__ = (db.UniqueConstraint('group_id', 'user_id', name='uk_group_user'),)
    
    # 关系
    group = db.relationship('OnsiteGroup', backref='members')
    user = db.relationship('User', backref='group_memberships')
    
    def __repr__(self):
        return f'<OnsiteGroupMember group_id={self.group_id} user_id={self.user_id}>'

class BusinessData(db.Model):
    """业务数据模型（到院信息和消费金额）"""
    __tablename__ = 'business_data'
    
    id = db.Column(db.Integer, primary_key=True)
    card_number = db.Column(db.String(18), nullable=False, index=True)
    visit_date = db.Column(db.Date, nullable=False, index=True)
    consumption_amount = db.Column(db.Numeric(10, 2), default=0, nullable=False)
    data_source = db.Column(db.String(100), nullable=True)
    imported_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    def __repr__(self):
        return f'<BusinessData {self.card_number} {self.visit_date}>'

class SystemSetting(db.Model):
    """系统设置模型"""
    __tablename__ = 'system_settings'
    
    id = db.Column(db.Integer, primary_key=True)
    setting_key = db.Column(db.String(100), unique=True, nullable=False, index=True)
    setting_value = db.Column(db.Text, nullable=True)
    description = db.Column(db.String(255), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    @staticmethod
    def get_setting(key, default=None):
        """获取系统设置"""
        setting = SystemSetting.query.filter_by(setting_key=key).first()
        return setting.setting_value if setting else default
    
    @staticmethod
    def set_setting(key, value, description=None):
        """设置系统设置"""
        setting = SystemSetting.query.filter_by(setting_key=key).first()
        if setting:
            setting.setting_value = value
            setting.updated_at = datetime.utcnow()
            if description:
                setting.description = description
        else:
            setting = SystemSetting(
                setting_key=key,
                setting_value=value,
                description=description
            )
            db.session.add(setting)
        db.session.commit()
        return setting
    
    def __repr__(self):
        return f'<SystemSetting {self.setting_key}>'


class VisitRecord(db.Model):
    """到院明细模型"""
    __tablename__ = 'visit_records'

    id = db.Column(db.Integer, primary_key=True)
    member_card_number = db.Column(db.String(18), nullable=False, index=True)
    visit_date = db.Column(db.Date, nullable=False, index=True)
    visit_time = db.Column(db.Time, nullable=True)
    visit_type = db.Column(db.String(50), nullable=True)  # 到院类型
    department = db.Column(db.String(100), nullable=True)  # 科室
    doctor = db.Column(db.String(100), nullable=True)  # 医生
    remarks = db.Column(db.Text, nullable=True)  # 备注
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    def __repr__(self):
        return f'<VisitRecord {self.member_card_number} - {self.visit_date}>'


class ConsumptionRecord(db.Model):
    """消费明细模型"""
    __tablename__ = 'consumption_records'

    id = db.Column(db.Integer, primary_key=True)
    member_card_number = db.Column(db.String(18), nullable=False, index=True)
    consumption_date = db.Column(db.Date, nullable=False, index=True)
    consumption_time = db.Column(db.Time, nullable=True)
    item_name = db.Column(db.String(200), nullable=True)  # 消费项目
    item_category = db.Column(db.String(100), nullable=True)  # 项目分类
    amount = db.Column(Numeric(10, 2), nullable=False, default=0)  # 消费金额
    quantity = db.Column(db.Integer, nullable=False, default=1)  # 数量
    unit_price = db.Column(Numeric(10, 2), nullable=True)  # 单价
    department = db.Column(db.String(100), nullable=True)  # 科室
    doctor = db.Column(db.String(100), nullable=True)  # 医生
    payment_method = db.Column(db.String(50), nullable=True)  # 支付方式
    remarks = db.Column(db.Text, nullable=True)  # 备注
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    def __repr__(self):
        return f'<ConsumptionRecord {self.member_card_number} - {self.consumption_date} - {self.amount}>'


class UploadHistory(db.Model):
    """上传历史记录模型"""
    __tablename__ = 'upload_history'

    id = db.Column(db.Integer, primary_key=True)
    upload_type = db.Column(db.String(50), nullable=False)  # 'visit' 或 'consumption'
    upload_mode = db.Column(db.String(50), nullable=False)  # 'smart_incremental' 或 'batch_upsert'
    file_name = db.Column(db.String(255), nullable=False)  # 上传的文件名
    total_records = db.Column(db.Integer, nullable=False, default=0)  # 文件总记录数
    new_records = db.Column(db.Integer, nullable=False, default=0)  # 新增记录数
    updated_records = db.Column(db.Integer, nullable=False, default=0)  # 更新记录数
    skipped_records = db.Column(db.Integer, nullable=False, default=0)  # 跳过记录数
    error_records = db.Column(db.Integer, nullable=False, default=0)  # 错误记录数
    date_range_start = db.Column(db.Date, nullable=True)  # 数据日期范围开始
    date_range_end = db.Column(db.Date, nullable=True)  # 数据日期范围结束
    upload_time = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)  # 上传时间
    processing_time = db.Column(db.Float, nullable=True)  # 处理耗时（秒）
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    remarks = db.Column(db.Text, nullable=True)  # 备注信息

    # 关联用户
    user = db.relationship('User', backref=db.backref('upload_history', lazy='dynamic'))

    def __repr__(self):
        return f'<UploadHistory {self.upload_type} - {self.upload_mode} - {self.upload_time}>'