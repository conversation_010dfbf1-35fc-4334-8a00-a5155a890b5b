# 老客登记信息反馈系统需求文档

## 项目概述

老客登记信息反馈系统（Old Customer System）是一个基于Web的客户信息管理系统，主要用于医疗机构管理老客户的登记信息、跟进情况和统计分析。系统采用MySQL数据库，通过Web浏览器提供操作界面，支持多角色权限管理和完整的业务流程。

## 需求

### 需求1：系统初始化和配置管理

**用户故事：** 作为系统管理员，我希望能够在首次运行时配置数据库连接并初始化系统，以便快速部署和使用系统。

#### 验收标准

1. 当系统首次启动时，系统应当引导用户配置MySQL数据库连接信息
2. 如果连接成功且不存在Old_Customer_System数据库，系统应当自动创建该数据库及所需数据表
3. 如果数据库Old_Customer_System已存在，系统应当向用户确认是初始化（清空数据并重建）还是应用现有数据库
4. 当数据库准备就绪后，系统应当跳转至管理员账号注册页面
5. 当第一个注册的账号完成注册时，该账号应当拥有最高管理员权限

### 需求2：用户认证和权限管理

**用户故事：** 作为系统管理员，我希望能够管理不同角色的用户账号和权限，以确保数据安全和操作规范。

#### 验收标准

1. 当用户访问系统时，系统应当提供登录界面进行身份验证
2. 当管理员创建新账号时，系统应当支持分配以下角色：管理员、经营院长、部门主管、网络咨询、现场咨询
3. 当管理员管理账号时，系统应当支持创建新账号、重置密码、停用/启用账号功能
4. 当用户搜索账号时，系统应当支持基于中文、全拼或首字母的简码快速检索
5. 当用户修改密码时，系统应当提供安全的密码修改功能

### 需求3：基础数据管理

**用户故事：** 作为系统管理员，我希望能够管理部门和渠道等基础数据，以支持业务操作的标准化。

#### 验收标准

1. 当管理员管理部门时，系统应当支持添加、修改、停用部门功能
2. 当管理员管理渠道时，系统应当支持渠道分类和渠道信息维护
3. 当管理员录入渠道数据时，系统应当支持手动单个添加和Excel文件批量导入
4. 当用户搜索渠道时，系统应当支持基于简码的快速检索功能
5. 当管理员配置卡号规则时，系统应当支持设置卡号长度（默认10位，最大18位）

### 需求4：客户登记信息管理

**用户故事：** 作为网络咨询人员，我希望能够登记和管理客户信息，以便跟踪客户状态和后续跟进。

#### 验收标准

1. 当网络咨询人员登记客户信息时，系统应当支持录入卡号、所属现场、激活渠道、咨询内容、最近来院时间等信息
2. 当系统记录登记时间时，系统应当自动记录当前服务器时间且不可修改
3. 当用户输入卡号时，系统应当验证卡号为纯数字且符合配置的长度要求
4. 当用户选择所属现场时，系统应当提供所有在职现场咨询人员的下拉选择
5. 当系统记录所属网资时，系统应当自动填充为当前操作的网络咨询人员且不可修改
6. 当用户选择激活渠道时，系统应当提供所有已启用渠道的下拉选择
7. 当用户输入咨询内容时，系统应当限制最大长度为500字

### 需求5：咨询跟进管理

**用户故事：** 作为现场咨询人员，我希望能够查看分配给我的客户信息并填写跟进情况，以便完成客户服务流程。

#### 验收标准

1. 当现场咨询人员查看客户信息时，系统应当只显示所属现场为该人员的客户信息
2. 当现场咨询人员填写跟进情况时，系统应当支持在咨询跟进情况字段中录入最大500字的内容
3. 当系统记录跟进时间时，系统应当自动记录咨询跟进情况字段最后一次修改的时间戳
4. 当网络咨询人员查看跟进情况时，系统应当允许查看但不允许修改咨询跟进情况字段
5. 当用户查看信息列表时，系统应当显示卡号、所属现场、所属网资、激活渠道、咨询内容、最近来院时间、咨询跟进情况、咨询跟进人、咨询跟进时间、信息登记人、信息登记时间等字段

### 需求6：数据权限控制

**用户故事：** 作为不同角色的用户，我希望只能访问和操作符合我权限范围的数据，以确保数据安全和业务规范。

#### 验收标准

1. 当管理员访问系统时，系统应当提供所有功能模块的完全访问和操作权限
2. 当经营院长访问系统时，系统应当允许查看所有登记信息和所有统计报表
3. 当部门主管访问系统时，系统应当只允许查看其所属部门下所有员工登记的信息和部门相关统计报表
4. 当网络咨询人员访问系统时，系统应当只允许查看和修改自己登记的信息，对咨询跟进情况字段仅可查看不可修改
5. 当现场咨询人员访问系统时，系统应当只允许查看所属现场为自己的客户信息，并可填写和修改咨询跟进情况字段

### 需求7：数据维护功能

**用户故事：** 作为系统管理员，我希望能够维护现场小组映射关系和导入业务数据，以支持后续的统计分析。

#### 验收标准

1. 当管理员维护现场小组映射时，系统应当支持维护现场咨询人员与现场小组的从属关系
2. 当管理员录入映射数据时，系统应当支持手动添加/修改和Excel批量导入/更新
3. 当管理员导入业务数据时，系统应当支持通过Excel批量导入客户的到院信息与消费金额
4. 当系统处理导入数据时，系统应当提供数据验证和错误提示功能
5. 当导入完成时，系统应当提供导入结果的反馈信息

### 需求8：统计报表功能

**用户故事：** 作为经营院长和部门主管，我希望能够查看各种统计报表，以便了解业务情况和做出决策。

#### 验收标准

1. 当用户查看统计报表时，系统应当支持按现场小组和所属现场人员进行统计维度分析
2. 当系统生成统计数据时，系统应当计算跟进客户总数指标
3. 当系统生成统计数据时，系统应当计算待跟进客户数指标
4. 当系统生成统计数据时，系统应当计算回访后到院数指标
5. 当系统生成统计数据时，系统应当计算回访后产生消费金额指标
6. 当不同角色用户访问报表时，系统应当根据用户权限显示相应范围的统计数据