{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">创建渠道</h1>
    <a href="{{ url_for('basic_mgmt.channels') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>返回列表
    </a>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.name.label(class="form-label") }}
                        {{ form.name(class="form-control") }}
                        {% if form.name.errors %}
                            <div class="text-danger">
                                {% for error in form.name.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.category.label(class="form-label") }}
                                {{ form.category(class="form-control", placeholder="如：线上、线下、合作伙伴等") }}
                                {% if form.category.errors %}
                                    <div class="text-danger">
                                        {% for error in form.category.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.simple_code.label(class="form-label") }}
                                {{ form.simple_code(class="form-control", placeholder="留空自动生成") }}
                                <small class="form-text text-muted">用于快速搜索的简码</small>
                                {% if form.simple_code.errors %}
                                    <div class="text-danger">
                                        {% for error in form.simple_code.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_active(class="form-check-input") }}
                            {{ form.is_active.label(class="form-check-label") }}
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('basic_mgmt.channels') }}" class="btn btn-secondary me-md-2">取消</a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>渠道说明
                </h6>
            </div>
            <div class="card-body">
                <p class="text-muted mb-2">渠道用于标识客户的来源途径。</p>
                <ul class="list-unstyled mb-0">
                    <li class="mb-1">• 渠道名称必须唯一</li>
                    <li class="mb-1">• 可以按分类组织渠道</li>
                    <li class="mb-1">• 简码用于快速搜索</li>
                    <li class="mb-1">• 停用的渠道不会在登记时显示</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}