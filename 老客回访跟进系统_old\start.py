"""
一键启动脚本 - 自动检查依赖并启动系统
"""
import os
import sys
import subprocess
import importlib

def check_package(package_name, import_name=None):
    """检查包是否已安装"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        return True
    except ImportError:
        return False

def install_package(package_name):
    """安装单个包"""
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package_name])
        return True
    except subprocess.CalledProcessError:
        # 尝试使用国内镜像
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', package_name,
                '-i', 'https://pypi.tuna.tsinghua.edu.cn/simple/'
            ])
            return True
        except subprocess.CalledProcessError:
            return False

def check_and_install_dependencies():
    """检查并安装必需的依赖"""
    print("检查依赖包...")
    
    # 核心依赖包
    required_packages = [
        ('flask', 'flask'),
        ('flask_sqlalchemy', 'flask_sqlalchemy'),
        ('flask_login', 'flask_login'),
        ('flask_wtf', 'flask_wtf'),
        ('pymysql', 'pymysql'),
        ('pandas', 'pandas'),
        ('openpyxl', 'openpyxl'),
        ('werkzeug', 'werkzeug'),
        ('wtforms', 'wtforms'),
        ('dateutil', 'python-dateutil'),
    ]
    
    missing_packages = []
    
    for import_name, package_name in required_packages:
        if not check_package(import_name):
            missing_packages.append(package_name)
    
    if missing_packages:
        print(f"发现缺失的依赖包: {', '.join(missing_packages)}")
        print("正在自动安装...")
        
        for package in missing_packages:
            print(f"安装 {package}...")
            if install_package(package):
                print(f"✅ {package} 安装成功")
            else:
                print(f"❌ {package} 安装失败")
                return False
    
    print("✅ 所有依赖包检查完成")
    return True

def check_database_config():
    """检查数据库配置"""
    try:
        from config import get_config
        config = get_config()
        
        # 检查基本配置
        required_configs = ['MYSQL_HOST', 'MYSQL_USER', 'MYSQL_DATABASE']
        for config_name in required_configs:
            if not hasattr(config, config_name):
                print(f"⚠️ 缺少数据库配置: {config_name}")
                return False
        
        print("✅ 数据库配置检查通过")
        return True
    except Exception as e:
        print(f"❌ 数据库配置检查失败: {e}")
        return False

def check_database_initialized():
    """检查数据库是否已初始化"""
    try:
        from app import create_app, db
        from config import get_config

        config_class = get_config()
        app = create_app(config_class)

        with app.app_context():
            from app.models import Role
            if Role.query.first():
                print("✅ 数据库已初始化")
                return True
            else:
                print("⚠️ 数据库未初始化")
                return False
    except Exception as e:
        error_msg = str(e)
        # 如果是路由冲突错误，说明应用可以启动，只是有代码问题
        if "View function mapping is overwriting" in error_msg:
            print(f"⚠️ 应用启动时发现路由冲突: {error_msg}")
            print("⚠️ 请检查代码中是否有重复的路由定义")
            return False
        else:
            print(f"⚠️ 数据库连接失败: {e}")
            return False

def initialize_database():
    """初始化数据库"""
    print("正在初始化数据库...")
    try:
        # 获取当前脚本所在目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        init_script = os.path.join(current_dir, 'init_db.py')

        if not os.path.exists(init_script):
            print(f"❌ 找不到初始化脚本: {init_script}")
            return False

        result = subprocess.run([sys.executable, init_script],
                              capture_output=True, text=True, cwd=current_dir)
        if result.returncode == 0:
            print("✅ 数据库初始化成功")
            return True
        else:
            print(f"❌ 数据库初始化失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return False

def start_application():
    """启动应用"""
    print("启动系统...")
    try:
        from app import create_app
        from config import get_config

        config_class = get_config()
        app = create_app(config_class)

        # 尝试不同的端口
        ports = [5000, 5001, 5002, 5003, 8000]
        success = False

        for port in ports:
            try:
                print("\n" + "="*50)
                print("🎉 老客回访与跟进系统启动成功！")
                print("="*50)
                print(f"访问地址: http://localhost:{port}")
                print("默认管理员账号: admin")
                print("默认密码: admin123")
                print("请在首次登录后立即修改密码！")
                print("按 Ctrl+C 停止服务")
                print("="*50)

                app.run(host='127.0.0.1', port=port, debug=False, threaded=True)
                success = True
                break

            except OSError as e:
                if "Address already in use" in str(e) or "访问套接字" in str(e):
                    print(f"⚠️ 端口 {port} 被占用，尝试下一个端口...")
                    continue
                else:
                    raise e

        if not success:
            print("❌ 所有端口都被占用")
            print("解决方案:")
            print("1. 运行: python check_ports.py 检查端口占用")
            print("2. 运行: python start_alt_port.py 使用备用启动")
            print("3. 关闭占用端口的其他程序")

    except KeyboardInterrupt:
        print("\n系统已停止")
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        print("解决方案:")
        print("1. 检查是否有权限问题")
        print("2. 尝试使用管理员权限运行")
        print("3. 运行: python start_alt_port.py")

def main():
    """主函数"""
    print("老客回访与跟进系统 - 一键启动")
    print("="*40)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ Python版本过低，需要Python 3.8或更高版本")
        return
    
    # 检查并安装依赖
    if not check_and_install_dependencies():
        print("❌ 依赖包安装失败，请手动安装")
        return
    
    # 检查数据库配置
    if not check_database_config():
        print("❌ 请先配置数据库连接信息 (编辑 config.py)")
        return
    
    # 检查数据库是否已初始化
    if not check_database_initialized():
        choice = input("数据库未初始化，是否现在初始化？(y/n): ").lower().strip()
        if choice == 'y':
            if not initialize_database():
                print("❌ 数据库初始化失败")
                return
        else:
            print("请先运行 python init_db.py 初始化数据库")
            return
    
    # 启动应用
    start_application()

if __name__ == '__main__':
    main()
