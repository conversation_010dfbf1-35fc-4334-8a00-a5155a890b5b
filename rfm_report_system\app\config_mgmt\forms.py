from flask_wtf import FlaskForm
from wtforms import StringField, IntegerField, PasswordField, SelectField, SubmitField, HiddenField
from wtforms.validators import DataRequired, NumberRange, Length, ValidationError
from app.models import DatabaseConfig

class DatabaseConfigForm(FlaskForm):
    """数据库配置表单"""
    name = StringField('配置名称', validators=[
        DataRequired(message='配置名称不能为空'),
        Length(min=2, max=100, message='配置名称长度必须在2-100个字符之间')
    ])
    host = StringField('数据库地址', validators=[
        DataRequired(message='数据库地址不能为空'),
        Length(max=255, message='数据库地址长度不能超过255个字符')
    ])
    port = IntegerField('端口', validators=[
        DataRequired(message='端口不能为空'),
        NumberRange(min=1, max=65535, message='端口号必须在1-65535之间')
    ], default=3306)
    username = StringField('用户名', validators=[
        DataRequired(message='用户名不能为空'),
        Length(max=100, message='用户名长度不能超过100个字符')
    ])
    password = PasswordField('密码', validators=[
        DataRequired(message='密码不能为空')
    ])
    database_name = SelectField('数据库名', choices=[], validators=[
        DataRequired(message='请选择数据库')
    ])
    submit = SubmitField('保存配置')
    test_connection = SubmitField('测试连接')
    
    def __init__(self, original_name=None, *args, **kwargs):
        super(DatabaseConfigForm, self).__init__(*args, **kwargs)
        self.original_name = original_name
    
    def validate_name(self, name):
        """验证配置名称是否已存在"""
        if name.data != self.original_name:
            config = DatabaseConfig.query.filter_by(name=name.data).first()
            if config is not None:
                raise ValidationError('配置名称已存在，请选择其他名称')

class EditDatabaseConfigForm(FlaskForm):
    """编辑数据库配置表单"""
    id = HiddenField()
    name = StringField('配置名称', validators=[
        DataRequired(message='配置名称不能为空'),
        Length(min=2, max=100, message='配置名称长度必须在2-100个字符之间')
    ])
    host = StringField('数据库地址', validators=[
        DataRequired(message='数据库地址不能为空'),
        Length(max=255, message='数据库地址长度不能超过255个字符')
    ])
    port = IntegerField('端口', validators=[
        DataRequired(message='端口不能为空'),
        NumberRange(min=1, max=65535, message='端口号必须在1-65535之间')
    ])
    username = StringField('用户名', validators=[
        DataRequired(message='用户名不能为空'),
        Length(max=100, message='用户名长度不能超过100个字符')
    ])
    password = PasswordField('密码（留空表示不修改）')
    database_name = StringField('数据库名', validators=[
        DataRequired(message='数据库名不能为空')
    ])
    submit = SubmitField('更新配置')
    
    def __init__(self, original_name=None, *args, **kwargs):
        super(EditDatabaseConfigForm, self).__init__(*args, **kwargs)
        self.original_name = original_name
    
    def validate_name(self, name):
        """验证配置名称是否已存在"""
        if name.data != self.original_name:
            config = DatabaseConfig.query.filter_by(name=name.data).first()
            if config is not None:
                raise ValidationError('配置名称已存在，请选择其他名称')
