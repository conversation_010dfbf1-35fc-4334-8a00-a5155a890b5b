"""
时间处理工具模块
"""

from datetime import datetime, timezone, timedelta
from flask import current_app


def get_local_timezone():
    """获取本地时区"""
    try:
        timezone_name = current_app.config.get('TIMEZONE', 'Asia/Shanghai')
        if timezone_name == 'Asia/Shanghai':
            return timezone(timedelta(hours=8))  # 中国时区 UTC+8
        else:
            # 可以扩展支持其他时区
            return timezone(timedelta(hours=8))
    except:
        return timezone(timedelta(hours=8))


def utc_to_local(utc_dt):
    """将UTC时间转换为本地时间"""
    if utc_dt is None:
        return None
    
    if utc_dt.tzinfo is None:
        # 如果没有时区信息，假设是UTC时间
        utc_dt = utc_dt.replace(tzinfo=timezone.utc)
    
    local_tz = get_local_timezone()
    return utc_dt.astimezone(local_tz)


def local_to_utc(local_dt):
    """将本地时间转换为UTC时间"""
    if local_dt is None:
        return None
    
    local_tz = get_local_timezone()
    if local_dt.tzinfo is None:
        # 如果没有时区信息，假设是本地时间
        local_dt = local_dt.replace(tzinfo=local_tz)
    
    return local_dt.astimezone(timezone.utc)


def now_local():
    """获取当前本地时间"""
    return datetime.now(get_local_timezone())


def now_utc():
    """获取当前UTC时间"""
    return datetime.now(timezone.utc)


def format_local_datetime(dt, format_str='%Y-%m-%d %H:%M:%S'):
    """格式化本地时间显示"""
    if dt is None:
        return '-'
    
    local_dt = utc_to_local(dt)
    return local_dt.strftime(format_str)


def format_duration(duration):
    """格式化时长显示"""
    if duration is None:
        return '-'
    
    total_seconds = int(duration.total_seconds())
    hours = total_seconds // 3600
    minutes = (total_seconds % 3600) // 60
    seconds = total_seconds % 60
    
    if hours > 0:
        return f"{hours}小时{minutes}分钟{seconds}秒"
    elif minutes > 0:
        return f"{minutes}分钟{seconds}秒"
    else:
        return f"{seconds}秒"


def get_date_range_str(start_date, end_date):
    """获取日期范围字符串"""
    if start_date and end_date:
        start_str = format_local_datetime(start_date, '%Y-%m-%d')
        end_str = format_local_datetime(end_date, '%Y-%m-%d')
        if start_str == end_str:
            return start_str
        else:
            return f"{start_str} 至 {end_str}"
    elif start_date:
        return format_local_datetime(start_date, '%Y-%m-%d')
    elif end_date:
        return format_local_datetime(end_date, '%Y-%m-%d')
    else:
        return '-'
