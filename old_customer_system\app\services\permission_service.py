# -*- coding: utf-8 -*-
"""
权限控制服务
"""

from typing import List, Dict, Any, Optional
from functools import wraps
from flask import abort, flash, redirect, url_for
from flask_login import current_user
from app.models import User, Customer

class PermissionService:
    """权限控制服务"""
    
    # 角色权限映射
    ROLE_PERMISSIONS = {
        'admin': [
            'manage_users', 'manage_departments', 'manage_channels', 'manage_system',
            'view_all_customers', 'create_customer', 'edit_customer', 'delete_customer',
            'view_all_reports', 'export_data', 'import_data', 'upload_data',
            'view_all_statistics', 'view_department_statistics', 'view_own_statistics'
        ],
        'director': [
            'view_all_customers', 'view_all_reports', 'export_data', 'upload_data',
            'view_all_statistics', 'view_department_statistics', 'view_own_statistics'
        ],
        'manager': [
            'manage_channels', 'view_department_customers', 'view_department_reports',
            'export_data', 'upload_data', 'view_department_statistics', 'view_own_statistics'
        ],
        'online_consultant': [
            'create_customer', 'view_own_customers', 'edit_own_customers', 'upload_data',
            'view_own_statistics'
        ],
        'onsite_consultant': [
            'view_assigned_customers', 'update_follow_up', 'upload_data',
            'view_own_statistics'
        ]
    }
    
    @staticmethod
    def has_permission(user: User, permission: str) -> bool:
        """检查用户是否有指定权限"""
        if not user or not user.is_active:
            return False
        
        user_permissions = PermissionService.ROLE_PERMISSIONS.get(user.role, [])
        return permission in user_permissions
    
    @staticmethod
    def has_any_permission(user: User, permissions: List[str]) -> bool:
        """检查用户是否有任意一个权限"""
        return any(PermissionService.has_permission(user, perm) for perm in permissions)
    
    @staticmethod
    def has_all_permissions(user: User, permissions: List[str]) -> bool:
        """检查用户是否有所有权限"""
        return all(PermissionService.has_permission(user, perm) for perm in permissions)
    
    @staticmethod
    def can_access_customer(user: User, customer: Customer) -> bool:
        """检查用户是否可以访问指定客户"""
        if not user or not user.is_active:
            return False
        
        if user.role == 'admin' or user.role == 'director':
            # 管理员和经营院长可以访问所有客户
            return True
        elif user.role == 'manager':
            # 部门主管可以访问本部门的客户
            return customer.online_consultant.department_id == user.department_id
        elif user.role == 'online_consultant':
            # 网络咨询只能访问自己创建的客户
            return customer.created_by == user.id
        elif user.role == 'onsite_consultant':
            # 现场咨询只能访问分配给自己的客户
            return customer.onsite_consultant_id == user.id
        
        return False
    
    @staticmethod
    def can_edit_customer(user: User, customer: Customer) -> bool:
        """检查用户是否可以编辑指定客户"""
        if not user or not user.is_active:
            return False
        
        if user.role == 'admin':
            # 管理员可以编辑所有客户
            return True
        elif user.role == 'online_consultant':
            # 网络咨询只能编辑自己创建的客户
            return customer.created_by == user.id
        
        return False
    
    @staticmethod
    def can_update_follow_up(user: User, customer: Customer) -> bool:
        """检查用户是否可以更新跟进信息"""
        if not user or not user.is_active:
            return False
        
        if user.role == 'admin':
            # 管理员可以更新所有跟进信息
            return True
        elif user.role == 'onsite_consultant':
            # 现场咨询只能更新分配给自己的客户的跟进信息
            return customer.onsite_consultant_id == user.id
        
        return False
    
    @staticmethod
    def filter_customers_by_permission(user: User, customers: List[Customer]) -> List[Customer]:
        """根据用户权限过滤客户列表"""
        if not user or not user.is_active:
            return []
        
        if user.role == 'admin' or user.role == 'director':
            # 管理员和经营院长可以查看所有客户
            return customers
        elif user.role == 'manager':
            # 部门主管只能查看本部门的客户
            return [c for c in customers if c.online_consultant.department_id == user.department_id]
        elif user.role == 'online_consultant':
            # 网络咨询只能查看自己创建的客户
            return [c for c in customers if c.created_by == user.id]
        elif user.role == 'onsite_consultant':
            # 现场咨询只能查看分配给自己的客户
            return [c for c in customers if c.onsite_consultant_id == user.id]
        
        return []
    
    @staticmethod
    def get_accessible_departments(user: User) -> List[int]:
        """获取用户可访问的部门ID列表"""
        if not user or not user.is_active:
            return []
        
        if user.role == 'admin' or user.role == 'director':
            # 管理员和经营院长可以访问所有部门
            from app.models import Department
            return [dept.id for dept in Department.query.filter_by(is_active=True).all()]
        elif user.role == 'manager' and user.department_id:
            # 部门主管只能访问自己的部门
            return [user.department_id]
        
        return []

def require_permission(permission: str):
    """权限检查装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
            
            if not PermissionService.has_permission(current_user, permission):
                flash('您没有权限访问此页面', 'error')
                abort(403)
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_any_permission(*permissions):
    """需要任意一个权限的装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
            
            if not PermissionService.has_any_permission(current_user, list(permissions)):
                flash('您没有权限访问此页面', 'error')
                abort(403)
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_role(*roles):
    """角色检查装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
            
            if current_user.role not in roles:
                flash('您没有权限访问此页面', 'error')
                abort(403)
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_customer_access(customer_id_param='customer_id'):
    """客户访问权限检查装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                flash('请先登录', 'error')
                return redirect(url_for('auth.login'))
            
            # 获取客户ID
            customer_id = kwargs.get(customer_id_param)
            if not customer_id:
                abort(404)
            
            # 获取客户信息
            from app.models import Customer
            customer = Customer.query.get_or_404(customer_id)
            
            # 检查访问权限
            if not PermissionService.can_access_customer(current_user, customer):
                flash('您没有权限访问此客户信息', 'error')
                abort(403)
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator