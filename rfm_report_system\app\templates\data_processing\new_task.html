{% extends "base.html" %}

{% block content %}
<div class="row mt-4">
    <div class="col-12">
        <h2><i class="fas fa-plus me-2"></i>新建数据处理任务</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">首页</a></li>
                <li class="breadcrumb-item active">新建任务</li>
            </ol>
        </nav>
    </div>
</div>

<!-- 当前数据库配置信息 -->
<div class="row mt-3">
    <div class="col-12">
        <div class="alert alert-info">
            <i class="fas fa-database me-2"></i>
            <strong>当前数据库:</strong> {{ active_config.name }} 
            ({{ active_config.host }}:{{ active_config.port }}/{{ active_config.database_name }})
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form method="POST" novalidate>
                    {{ form.hidden_tag() }}
                    
                    <!-- 基本信息 -->
                    <div class="mb-4">
                        <h5><i class="fas fa-info-circle me-2"></i>基本信息</h5>
                        <hr>
                        
                        <div class="mb-3">
                            {{ form.task_name.label(class="form-label") }}
                            {{ form.task_name(class="form-control" + (" is-invalid" if form.task_name.errors else "")) }}
                            {% if form.task_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.task_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.description.label(class="form-label") }}
                            {{ form.description(class="form-control", placeholder="可选：描述此任务的目的和特殊要求") }}
                        </div>
                    </div>
                    
                    <!-- 处理类型 -->
                    <div class="mb-4">
                        <h5><i class="fas fa-cogs me-2"></i>处理类型</h5>
                        <hr>
                        
                        <div class="mb-3">
                            {% for subfield in form.task_type %}
                                <div class="form-check">
                                    {{ subfield(class="form-check-input") }}
                                    {{ subfield.label(class="form-check-label") }}
                                </div>
                            {% endfor %}
                            {% if form.task_type.errors %}
                                <div class="text-danger">
                                    {% for error in form.task_type.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- 基础会员来源（正向盘） -->
                    <div class="mb-4" id="baseQuarterSection" style="display: none;">
                        <h5><i class="fas fa-users me-2"></i>基础会员来源</h5>
                        <hr>

                        <div class="mb-3">
                            {{ form.base_quarter_table.label(class="form-label") }}
                            {{ form.base_quarter_table(class="form-select") }}
                            <div class="form-text">选择以哪个季度表的会员卡号为基础进行分析</div>
                            {% if form.base_quarter_table.errors %}
                                <div class="text-danger">
                                    {% for error in form.base_quarter_table.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- 季度表选择（结果盘） -->
                    <div class="mb-4" id="quarterTablesSection" style="display: none;">
                        <h5><i class="fas fa-table me-2"></i>季度表选择</h5>
                        <hr>

                        <div class="mb-3">
                            {{ form.quarter_tables.label(class="form-label") }}
                            {% if form.quarter_tables.choices %}
                                <div class="row">
                                    {% for subfield in form.quarter_tables %}
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                {{ subfield(class="form-check-input") }}
                                                {{ subfield.label(class="form-check-label") }}
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    未找到季度表，请检查数据库连接和表结构
                                </div>
                            {% endif %}
                            {% if form.quarter_tables.errors %}
                                <div class="text-danger">
                                    {% for error in form.quarter_tables.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- 分析季度 -->
                    <div class="mb-4">
                        <h5><i class="fas fa-calendar me-2"></i>分析季度</h5>
                        <hr>
                        
                        <div class="mb-3">
                            {{ form.quarters.label(class="form-label") }}
                            {% if form.quarters.choices %}
                                <div class="row">
                                    {% for subfield in form.quarters %}
                                        <div class="col-md-3 mb-2">
                                            <div class="form-check">
                                                {{ subfield(class="form-check-input") }}
                                                {{ subfield.label(class="form-check-label") }}
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    无可用的季度选项
                                </div>
                            {% endif %}
                            {% if form.quarters.errors %}
                                <div class="text-danger">
                                    {% for error in form.quarters.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- 科室选择 -->
                    <div class="mb-4">
                        <h5><i class="fas fa-hospital me-2"></i>科室选择</h5>
                        <hr>
                        
                        <div class="mb-3">
                            {{ form.departments.label(class="form-label") }}
                            <div class="row">
                                {% for subfield in form.departments %}
                                    <div class="col-md-4 mb-2">
                                        <div class="form-check">
                                            {{ subfield(class="form-check-input") }}
                                            {{ subfield.label(class="form-check-label") }}
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                            <div class="form-text">默认选择皮肤科和注射科</div>
                        </div>

                        <!-- TOP数量配置 -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.category_top_count.label(class="form-label") }}
                                    {{ form.category_top_count(class="form-control") }}
                                    <small class="form-text text-muted">{{ form.category_top_count.description }}</small>
                                    {% if form.category_top_count.errors %}
                                        <div class="text-danger">
                                            {% for error in form.category_top_count.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.item_top_count.label(class="form-label") }}
                                    {{ form.item_top_count(class="form-control") }}
                                    <small class="form-text text-muted">{{ form.item_top_count.description }}</small>
                                    {% if form.item_top_count.errors %}
                                        <div class="text-danger">
                                            {% for error in form.item_top_count.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- 配置预览 -->
                        <div class="mb-3">
                            <div class="alert alert-info" id="configPreview" style="display: none;">
                                <h6><i class="fas fa-eye me-2"></i>配置预览</h6>
                                <div id="configPreviewContent"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 分类映射选择 -->
                    <div class="mb-4">
                        <h5><i class="fas fa-exchange-alt me-2"></i>分类映射选择</h5>
                        <hr>

                        <div class="mb-3">
                            <label class="form-label">选择要使用的分类映射</label>
                            <div class="row">
                                <div class="col-md-4 mb-2">
                                    <div class="form-check">
                                        {{ form.use_level1_mapping(class="form-check-input") }}
                                        {{ form.use_level1_mapping.label(class="form-check-label") }}
                                    </div>
                                </div>
                                <div class="col-md-4 mb-2">
                                    <div class="form-check">
                                        {{ form.use_level2_mapping(class="form-check-input") }}
                                        {{ form.use_level2_mapping.label(class="form-check-label") }}
                                    </div>
                                </div>
                                <div class="col-md-4 mb-2">
                                    <div class="form-check">
                                        {{ form.use_level3_mapping(class="form-check-input") }}
                                        {{ form.use_level3_mapping.label(class="form-check-label") }}
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-2">
                                    <div class="form-check">
                                        {{ form.use_field_mapping(class="form-check-input") }}
                                        {{ form.use_field_mapping.label(class="form-check-label") }}
                                        <small class="form-text text-muted">同时添加现场小组和最新现场列</small>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-2">
                                    <div class="form-check">
                                        {{ form.use_field_group(class="form-check-input") }}
                                        {{ form.use_field_group.label(class="form-check-label") }}
                                    </div>
                                </div>
                            </div>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                选择后将使用参照表中的映射关系进行分类和现场数据处理。
                                <a href="{{ url_for('mapping.index') }}" target="_blank">管理参照表</a>
                            </div>
                        </div>

                        <!-- 映射预览 -->
                        <div id="mappingPreview" class="mt-3" style="display: none;">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-eye me-2"></i>映射预览</h6>
                                <div id="mappingContent"></div>
                            </div>
                        </div>
                    </div>

                    <!-- TOP排名时间范围配置 -->
                    <div class="mb-4">
                        <h5><i class="fas fa-calendar-alt me-2"></i>TOP排名时间范围配置</h5>
                        <hr>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>说明：</strong>
                            <ul class="mb-0">
                                <li><strong>TOP排名统计时间：</strong>用于确定哪些品类/品项进入TOP排名（如：2024年全年数据确定TOP5品类）</li>
                                <li><strong>TOP列值计算时间：</strong>用于计算每个会员在TOP品类/品项上的消费金额（如：计算会员在24Q4的消费）</li>
                                <li>如果不选择，将使用分析季度的完整时间范围</li>
                            </ul>
                        </div>

                        <!-- TOP排名统计时间范围 -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6><i class="fas fa-chart-bar me-2"></i>TOP排名统计时间范围</h6>
                                <small class="text-muted">确定哪些品类/品项进入TOP排名</small>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.top_ranking_start_date.label(class="form-label") }}
                                            {{ form.top_ranking_start_date(class="form-control") }}
                                            {% if form.top_ranking_start_date.errors %}
                                                <div class="text-danger">
                                                    {% for error in form.top_ranking_start_date.errors %}
                                                        {{ error }}
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.top_ranking_end_date.label(class="form-label") }}
                                            {{ form.top_ranking_end_date(class="form-control") }}
                                            {% if form.top_ranking_end_date.errors %}
                                                <div class="text-danger">
                                                    {% for error in form.top_ranking_end_date.errors %}
                                                        {{ error }}
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                <div class="form-text">
                                    <strong>示例：</strong>2024-01-01 到 2024-12-31（用2024年全年数据确定TOP5品类和TOP15品项）
                                </div>
                            </div>
                        </div>

                        <!-- TOP列值计算时间范围 -->
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-calculator me-2"></i>TOP列值计算时间范围</h6>
                                <small class="text-muted">计算每个会员在TOP品类/品项上的消费金额</small>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.top_value_start_date.label(class="form-label") }}
                                            {{ form.top_value_start_date(class="form-control") }}
                                            {% if form.top_value_start_date.errors %}
                                                <div class="text-danger">
                                                    {% for error in form.top_value_start_date.errors %}
                                                        {{ error }}
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.top_value_end_date.label(class="form-label") }}
                                            {{ form.top_value_end_date(class="form-control") }}
                                            {% if form.top_value_end_date.errors %}
                                                <div class="text-danger">
                                                    {% for error in form.top_value_end_date.errors %}
                                                        {{ error }}
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                <div class="form-text">
                                    <strong>示例：</strong>2024-10-01 到 2024-12-31（计算会员在24Q4期间在TOP品类上的消费金额）
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('main.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>返回
                        </a>
                        {{ form.submit(class="btn btn-primary btn-lg") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 侧边栏说明 -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-question-circle me-2"></i>处理说明</h6>
            </div>
            <div class="card-body">
                <h6>处理类型说明：</h6>
                <ul class="list-unstyled">
                    <li><strong>正向盘：</strong>
                        <br>• 选择基础会员来源季度表
                        <br>• 选择要分析的季度范围
                        <br>• 基于基础表的会员分析各季度业绩
                    </li>
                    <li class="mt-2"><strong>结果盘：</strong>
                        <br>• 选择多个季度表
                        <br>• 使用所有表中会员的并集
                        <br>• 分析并集会员的业绩表现
                    </li>
                </ul>
                
                <h6 class="mt-3">处理步骤：</h6>
                <ol>
                    <li>创建基础缓存表</li>
                    <li>计算季度执行业绩</li>
                    <li>计算科室季度业绩</li>
                    <li>计算TOP排名</li>
                    <li>导出Excel结果</li>
                </ol>

                <h6 class="mt-3">分类映射说明：</h6>
                <ul class="list-unstyled">
                    <li><strong>一级分类映射：</strong>
                        <br>• 科室分类合并（如：皮肤美容项目 → 皮肤）
                    </li>
                    <li class="mt-2"><strong>二级分类映射：</strong>
                        <br>• 品类合并（如：衡力+Botox → 肉毒素）
                    </li>
                    <li class="mt-2"><strong>三级分类映射：</strong>
                        <br>• 品项合并（如：四代+五代微针 → 黄金微针）
                    </li>
                    <li class="mt-2"><strong>现场映射：</strong>
                        <br>• 现场人员名称统一（如：张三 → 李四）
                        <br>• 添加"最新现场"列，跳过"不在盘内"
                    </li>
                    <li class="mt-2"><strong>现场小组映射：</strong>
                        <br>• 现场人员归组（如：李四 → 王婆小组）
                        <br>• 添加"现场小组"列，基于最新现场
                    </li>
                </ul>

                <h6 class="mt-3">TOP排名时间范围说明：</h6>
                <ul class="list-unstyled">
                    <li><strong>TOP排名统计时间：</strong>
                        <br>• 用于确定哪些品类/品项进入TOP排名
                        <br>• 例：用2024年全年数据确定TOP5品类
                    </li>
                    <li class="mt-2"><strong>TOP列值计算时间：</strong>
                        <br>• 用于计算每个会员的消费金额
                        <br>• 例：计算会员在24Q4的TOP品类消费
                    </li>
                </ul>

                <div class="alert alert-warning mt-2">
                    <small>
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        映射仅影响TOP排名计算，不影响原始数据。时间范围可以灵活配置以满足不同分析需求。
                    </small>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-clock me-2"></i>预计处理时间</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <small>
                        <i class="fas fa-info-circle me-1"></i>
                        根据数据量大小，处理时间通常在1-5分钟之间。大数据量可能需要更长时间。
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // 初始化配置预览
    updateConfigPreview();

    // 监听科室选择和TOP数量变化
    $('input[name="departments"], input[name="category_top_count"], input[name="item_top_count"]').on('change input', function() {
        updateConfigPreview();
    });

    // 处理类型变化时的逻辑
    $('input[name="task_type"]').change(function() {
        var taskType = $(this).val();

        if (taskType === 'forward') {
            // 正向盘：显示基础会员来源选择，隐藏季度表多选
            $('#baseQuarterSection').show();
            $('#quarterTablesSection').hide();

            // 清除结果盘的选择
            $('input[name="quarter_tables"]').prop('checked', false);
        } else if (taskType === 'result') {
            // 结果盘：显示季度表多选，隐藏基础会员来源
            $('#baseQuarterSection').hide();
            $('#quarterTablesSection').show();

            // 清除正向盘的选择
            $('#base_quarter_table').val('');
        }
    });

    // 页面加载时根据选中的类型显示对应区域
    $(document).ready(function() {
        var selectedType = $('input[name="task_type"]:checked').val();
        if (selectedType) {
            $('input[name="task_type"]:checked').trigger('change');
        }

        // 加载映射预览
        loadMappingPreview();
    });

    // 映射选择变化时更新预览
    $('input[name="use_level1_mapping"], input[name="use_level2_mapping"], input[name="use_level3_mapping"], input[name="use_field_mapping"], input[name="use_field_group"]').change(function() {
        updateMappingPreview();
    });

    // 加载映射预览数据
    function loadMappingPreview() {
        $.get('/data/api/mapping_preview')
            .done(function(response) {
                if (response.success) {
                    window.mappingData = response.mappings;
                    window.templateName = response.template_name;
                    updateMappingPreview();
                } else {
                    // 没有激活的参照表，隐藏映射选择区域
                    $('.mb-4:has(#mappingPreview)').hide();
                }
            })
            .fail(function() {
                // 请求失败，隐藏映射选择区域
                $('.mb-4:has(#mappingPreview)').hide();
            });
    }

    // 更新映射预览
    function updateMappingPreview() {
        if (!window.mappingData) return;

        var useLevel1 = $('input[name="use_level1_mapping"]').is(':checked');
        var useLevel2 = $('input[name="use_level2_mapping"]').is(':checked');
        var useLevel3 = $('input[name="use_level3_mapping"]').is(':checked');
        var useFieldMapping = $('input[name="use_field_mapping"]').is(':checked');
        var useFieldGroup = $('input[name="use_field_group"]').is(':checked');

        if (!useLevel1 && !useLevel2 && !useLevel3 && !useFieldMapping && !useFieldGroup) {
            $('#mappingPreview').hide();
            return;
        }

        var html = '<div class="row">';

        if (useLevel1 && window.mappingData.level1) {
            html += '<div class="col-md-4">';
            html += '<h6><i class="fas fa-layer-group me-1"></i>一级分类映射</h6>';
            html += '<ul class="list-unstyled small">';
            for (var key in window.mappingData.level1) {
                html += '<li>' + key + ' → <span class="badge bg-primary">' + window.mappingData.level1[key] + '</span></li>';
            }
            html += '</ul></div>';
        }

        if (useLevel2 && window.mappingData.level2) {
            html += '<div class="col-md-4">';
            html += '<h6><i class="fas fa-tags me-1"></i>二级分类映射</h6>';
            html += '<ul class="list-unstyled small">';
            for (var key in window.mappingData.level2) {
                html += '<li>' + key + ' → <span class="badge bg-success">' + window.mappingData.level2[key] + '</span></li>';
            }
            html += '</ul></div>';
        }

        if (useLevel3 && window.mappingData.level3) {
            html += '<div class="col-md-4">';
            html += '<h6><i class="fas fa-tag me-1"></i>三级分类映射</h6>';
            html += '<ul class="list-unstyled small">';
            for (var key in window.mappingData.level3) {
                html += '<li>' + key + ' → <span class="badge bg-info">' + window.mappingData.level3[key] + '</span></li>';
            }
            html += '</ul></div>';
        }

        // 现场映射预览
        if (useFieldMapping && window.mappingData.field_mapping) {
            html += '<div class="col-md-4">';
            html += '<h6><i class="fas fa-user me-1"></i>现场映射</h6>';
            html += '<ul class="list-unstyled small">';
            for (var key in window.mappingData.field_mapping) {
                html += '<li>' + key + ' → <span class="badge bg-warning">' + window.mappingData.field_mapping[key] + '</span></li>';
            }
            html += '</ul></div>';
        }

        // 现场小组映射预览
        if (useFieldGroup && window.mappingData.field_group) {
            html += '<div class="col-md-4">';
            html += '<h6><i class="fas fa-users me-1"></i>现场小组映射</h6>';
            html += '<ul class="list-unstyled small">';
            for (var key in window.mappingData.field_group) {
                html += '<li>' + key + ' → <span class="badge bg-danger">' + window.mappingData.field_group[key] + '</span></li>';
            }
            html += '</ul></div>';
        }

        html += '</div>';
        html += '<small class="text-muted">参照表: ' + window.templateName + '</small>';

        $('#mappingContent').html(html);
        $('#mappingPreview').show();
    }

    // 表单提交验证
    $('form').submit(function(e) {
        var taskType = $('input[name="task_type"]:checked').val();

        if (taskType === 'forward') {
            // 正向盘验证
            var baseQuarter = $('#base_quarter_table').val();
            if (!baseQuarter) {
                e.preventDefault();
                alert('请选择基础会员来源季度表！');
                return false;
            }
        } else if (taskType === 'result') {
            // 结果盘验证
            var selectedTables = $('input[name="quarter_tables"]:checked').length;
            if (selectedTables === 0) {
                e.preventDefault();
                alert('请至少选择一个季度表！');
                return false;
            }
        }

        var selectedQuarters = $('input[name="quarters"]:checked').length;
        if (selectedQuarters === 0) {
            e.preventDefault();
            alert('请至少选择一个分析季度！');
            return false;
        }

        return true;
    });
    
    // 全选/取消全选功能
    function addSelectAllButton(containerSelector, checkboxName) {
        var container = $(containerSelector);
        if (container.find('input[type="checkbox"]').length > 3) {
            var selectAllBtn = $('<button type="button" class="btn btn-sm btn-outline-secondary mb-2">全选/取消全选</button>');
            selectAllBtn.click(function() {
                var checkboxes = container.find('input[name="' + checkboxName + '"]');
                var allChecked = checkboxes.length === checkboxes.filter(':checked').length;
                checkboxes.prop('checked', !allChecked);
            });
            container.find('.row').before(selectAllBtn);
        }
    }
    
    // 为季度和科室添加全选按钮
    addSelectAllButton('#quarters', 'quarters');
    addSelectAllButton('#quarter_tables', 'quarter_tables');
});

// 更新配置预览
function updateConfigPreview() {
    var selectedDepartments = [];
    $('input[name="departments"]:checked').each(function() {
        selectedDepartments.push($(this).next('label').text());
    });

    var categoryTopCount = parseInt($('#category_top_count').val()) || 5;
    var itemTopCount = parseInt($('#item_top_count').val()) || 15;

    if (selectedDepartments.length === 0) {
        $('#configPreview').hide();
        return;
    }

    var html = '<div class="row">';

    // 品类配置
    html += '<div class="col-md-6">';
    html += '<h6><i class="fas fa-tags me-2 text-primary"></i>品类配置</h6>';
    html += '<ul class="list-unstyled">';
    selectedDepartments.forEach(function(dept) {
        html += '<li><i class="fas fa-check-circle text-success me-1"></i>' + dept + ' TOP1-' + categoryTopCount + '</li>';
    });
    html += '</ul>';
    html += '</div>';

    // 品项配置
    html += '<div class="col-md-6">';
    html += '<h6><i class="fas fa-cube me-2 text-info"></i>品项配置</h6>';
    html += '<ul class="list-unstyled">';
    selectedDepartments.forEach(function(dept) {
        html += '<li><i class="fas fa-check-circle text-success me-1"></i>' + dept + ' TOP1-' + itemTopCount + '</li>';
    });
    html += '</ul>';
    html += '</div>';

    html += '</div>';

    // 总列数统计
    var totalCategoryColumns = selectedDepartments.length * categoryTopCount;
    var totalItemColumns = selectedDepartments.length * itemTopCount;
    var totalColumns = totalCategoryColumns + totalItemColumns;

    html += '<hr>';
    html += '<div class="row text-center">';
    html += '<div class="col-md-3">';
    html += '<h5 class="text-primary">' + totalCategoryColumns + '</h5>';
    html += '<small class="text-muted">品类列数</small>';
    html += '</div>';
    html += '<div class="col-md-3">';
    html += '<h5 class="text-info">' + totalItemColumns + '</h5>';
    html += '<small class="text-muted">品项列数</small>';
    html += '</div>';
    html += '<div class="col-md-3">';
    html += '<h5 class="text-success">' + totalColumns + '</h5>';
    html += '<small class="text-muted">总列数</small>';
    html += '</div>';
    html += '<div class="col-md-3">';
    html += '<h5 class="text-warning">' + selectedDepartments.length + '</h5>';
    html += '<small class="text-muted">科室数</small>';
    html += '</div>';
    html += '</div>';

    $('#configPreviewContent').html(html);
    $('#configPreview').show();
}
</script>
{% endblock %}
