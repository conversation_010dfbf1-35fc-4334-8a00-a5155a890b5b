{% extends "base.html" %}

{% block content %}
<div class="row mt-4">
    <div class="col-12">
        <h2><i class="fas fa-tachometer-alt me-2"></i>系统首页</h2>
        <p class="text-muted">欢迎使用数据处理系统</p>
    </div>
</div>

<!-- 系统状态卡片 -->
<div class="row mt-3">
    <div class="col-md-3">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">总任务数</h5>
                        <h3>{{ stats.total_tasks }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-tasks fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">已完成</h5>
                        <h3>{{ stats.completed_tasks }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-white bg-danger">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">失败任务</h5>
                        <h3>{{ stats.failed_tasks }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">数据库配置</h5>
                        <h3>{{ stats.db_configs }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-database fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 当前数据库配置 -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-database me-2"></i>当前数据库配置</h5>
            </div>
            <div class="card-body">
                {% if active_db_config %}
                    <table class="table table-sm">
                        <tr>
                            <td><strong>配置名称:</strong></td>
                            <td>{{ active_db_config.name }}</td>
                        </tr>
                        <tr>
                            <td><strong>数据库地址:</strong></td>
                            <td>{{ active_db_config.host }}:{{ active_db_config.port }}</td>
                        </tr>
                        <tr>
                            <td><strong>数据库名:</strong></td>
                            <td>{{ active_db_config.database_name }}</td>
                        </tr>
                        <tr>
                            <td><strong>用户名:</strong></td>
                            <td>{{ active_db_config.username }}</td>
                        </tr>
                    </table>
                {% else %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        尚未配置数据库连接，请先 
                        <a href="{{ url_for('config_mgmt.add_config') }}" class="alert-link">添加数据库配置</a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- 快速操作 -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bolt me-2"></i>快速操作</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('data_processing.new_task') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>新建数据处理任务
                    </a>
                    <a href="{{ url_for('config_mgmt.list_configs') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-cog me-2"></i>管理数据库配置
                    </a>
                    <a href="{{ url_for('data_processing.task_history') }}" class="btn btn-outline-info">
                        <i class="fas fa-history me-2"></i>查看任务历史
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近任务 -->
{% if recent_tasks %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-clock me-2"></i>最近任务</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>任务名称</th>
                                <th>类型</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for task in recent_tasks %}
                            <tr>
                                <td>{{ task.task_name }}</td>
                                <td>
                                    <span class="badge bg-secondary">
                                        {{ '正向盘' if task.task_type == 'forward' else '结果盘' }}
                                    </span>
                                </td>
                                <td>
                                    {% if task.status == 'completed' %}
                                        <span class="badge bg-success">已完成</span>
                                    {% elif task.status == 'failed' %}
                                        <span class="badge bg-danger">失败</span>
                                    {% elif task.status == 'running' %}
                                        <span class="badge bg-primary">运行中</span>
                                    {% else %}
                                        <span class="badge bg-secondary">等待中</span>
                                    {% endif %}
                                </td>
                                <td>{{ task.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>
                                    {% if task.status == 'completed' and task.output_file_path %}
                                        <a href="{{ url_for('data_processing.download_result', task_id=task.id) }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-download"></i>
                                        </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
