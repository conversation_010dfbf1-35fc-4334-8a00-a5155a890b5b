{% extends "base.html" %}

{% block title %}正向盘报表 - RFM报表{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 页面标题 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-arrow-right text-success me-2"></i>
                    正向盘报表
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">首页</a></li>
                        <li class="breadcrumb-item"><a href="{{ url_for('rfm_reports.index') }}">RFM报表</a></li>
                        <li class="breadcrumb-item active">正向盘报表</li>
                    </ol>
                </nav>
            </div>

            <!-- 数据源选择 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-database me-2"></i>
                                数据源选择
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <label for="dataSourceSelect" class="form-label">选择正向盘数据源：</label>
                                    <select class="form-select" id="dataSourceSelect">
                                        <option value="">请选择数据源...</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-primary me-2" id="applyDataSource" disabled>
                                        <i class="fas fa-check me-1"></i> 应用数据源
                                    </button>
                                    <button class="btn btn-outline-primary" id="refreshReports" disabled title="重新计算所有报表">
                                        <i class="fas fa-sync-alt me-1"></i> 刷新报表
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-muted small" id="dataSourceInfo">
                                        请先选择数据源
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 报表内容区域 -->
            <div id="reportContent" style="display: none;">
                <!-- 一级标签页 -->
                <ul class="nav nav-tabs nav-tabs-custom" id="mainTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="customer-overview-tab" data-bs-toggle="tab" 
                                data-bs-target="#customer-overview" type="button" role="tab">
                            <i class="fas fa-users me-1"></i> 客户资源总览
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="department-analysis-tab" data-bs-toggle="tab" 
                                data-bs-target="#department-analysis" type="button" role="tab">
                            <i class="fas fa-building me-1"></i> 科室业绩分析
                        </button>
                    </li>
                </ul>

                <!-- 标签页内容 -->
                <div class="tab-content" id="mainTabContent">
                    <!-- 客户资源总览 -->
                    <div class="tab-pane fade show active" id="customer-overview" role="tabpanel">
                        <div class="card border-0">
                            <div class="card-body">
                                <!-- 二级标签页 -->
                                <ul class="nav nav-pills nav-pills-custom mb-3" id="customerTabs" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" data-analysis="base-volume-trend">
                                            <i class="fas fa-chart-area me-1"></i> 基盘体量趋势
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" data-analysis="quarterly-upgrade-trend">
                                            <i class="fas fa-exchange-alt me-1"></i> 季度升降级趋势
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" data-analysis="upgrade-matrix-change">
                                            <i class="fas fa-th me-1"></i> 升降矩阵变化
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" data-analysis="rfm-health-assessment">
                                            <i class="fas fa-heartbeat me-1"></i> RFM健康度评估
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" data-analysis="quarterly-resource-utilization">
                                            <i class="fas fa-chart-pie me-1"></i> 季末资源利用率
                                        </button>
                                    </li>
                                </ul>

                                <!-- 分析内容区域 -->
                                <div id="customerAnalysisContent">
                                    <div class="text-center py-5">
                                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">请选择分析类型</h5>
                                        <p class="text-muted">点击上方标签页查看相应的分析内容</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 科室业绩分析 -->
                    <div class="tab-pane fade" id="department-analysis" role="tabpanel">
                        <div class="card border-0">
                            <div class="card-body">
                                <!-- 二级标签页 -->
                                <ul class="nav nav-pills nav-pills-custom mb-3" id="departmentTabs" role="tablist">
                                    <li class="nav-item dropdown">
                                        <button class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                                            <i class="fas fa-building me-1"></i> 科室总盘
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#" data-analysis="department-overview/micro-plastic">
                                                <i class="fas fa-syringe me-1"></i> 微整
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" data-analysis="department-overview/dermatology">
                                                <i class="fas fa-hand-paper me-1"></i> 皮肤
                                            </a></li>
                                        </ul>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" data-analysis="quarterly-category-analysis">
                                            <i class="fas fa-tags me-1"></i> 季度重点品类业绩分析
                                        </button>
                                    </li>
                                </ul>

                                <!-- 分析内容区域 -->
                                <div id="departmentAnalysisContent">
                                    <div class="text-center py-5">
                                        <i class="fas fa-building fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">请选择分析类型</h5>
                                        <p class="text-muted">点击上方标签页查看相应的分析内容</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 无数据源提示 -->
            <div id="noDataSourceMessage" class="text-center py-5">
                <i class="fas fa-database fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">请先选择数据源</h5>
                <p class="text-muted">
                    选择一个正向盘数据源后，即可查看相应的RFM分析报表。
                    如果没有可用的数据源，请先在
                    <a href="{{ url_for('rfm_reports.data_source_management') }}">数据源管理</a>
                    中导入数据。
                </p>
            </div>
        </div>
    </div>
</div>

<script>
let currentDataSource = null;
let currentAnalysisType = null;
let originalConsultantData = null;
let dataSourcesCache = null;  // 数据源缓存
let lastDataSourcesLoadTime = 0;  // 上次加载时间

// 报表缓存
let reportsCache = null;  // 缓存的报表数据
let lastReportsCalculateTime = 0;  // 上次计算时间
let isReportsCalculated = false;  // 是否已计算过报表

// 筛选器状态管理
let filterState = {
    isLoading: false,
    debounceTimer: null,
    lastFilterData: null,
    storageKey: 'rfm_consultant_filter_state'
};

// 筛选状态持久化功能
const FilterStateManager = {
    // 保存筛选状态到本地存储
    saveState: function(dataSourceId, filterData) {
        try {
            const stateData = {
                dataSourceId: dataSourceId,
                subdivisionLevels: filterData.subdivision_levels,
                memberCardLevels: filterData.member_card_levels,
                timestamp: Date.now()
            };
            localStorage.setItem(filterState.storageKey, JSON.stringify(stateData));
        } catch (error) {
            console.warn('无法保存筛选状态:', error);
        }
    },

    // 从本地存储加载筛选状态
    loadState: function(dataSourceId) {
        try {
            const savedState = localStorage.getItem(filterState.storageKey);
            if (!savedState) return null;

            const stateData = JSON.parse(savedState);
            
            // 检查数据源是否匹配
            if (stateData.dataSourceId !== dataSourceId) {
                return null;
            }

            // 检查状态是否过期（24小时）
            const maxAge = 24 * 60 * 60 * 1000; // 24小时
            if (Date.now() - stateData.timestamp > maxAge) {
                this.clearState();
                return null;
            }

            return {
                subdivision_levels: stateData.subdivisionLevels || [],
                member_card_levels: stateData.memberCardLevels || []
            };
        } catch (error) {
            console.warn('无法加载筛选状态:', error);
            return null;
        }
    },

    // 清除筛选状态
    clearState: function() {
        try {
            localStorage.removeItem(filterState.storageKey);
        } catch (error) {
            console.warn('无法清除筛选状态:', error);
        }
    },

    // 验证筛选状态
    validateState: function(state, availableOptions) {
        if (!state || !availableOptions) return false;

        const validSubdivision = state.subdivision_levels.every(level => 
            availableOptions.subdivision_levels.includes(level)
        );
        const validCardLevel = state.member_card_levels.every(level => 
            availableOptions.member_card_levels.includes(level)
        );

        return validSubdivision && validCardLevel && 
               state.subdivision_levels.length > 0 && 
               state.member_card_levels.length > 0;
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadDataSources();
    initializeEventListeners();
});

function loadDataSources() {
    // 检查缓存是否有效（5分钟内）
    const now = Date.now();
    const cacheValidTime = 5 * 60 * 1000; // 5分钟

    if (dataSourcesCache && (now - lastDataSourcesLoadTime) < cacheValidTime) {
        populateDataSourceSelect(dataSourcesCache);
        return;
    }

    // 显示加载状态
    const select = document.getElementById('dataSourceSelect');
    select.innerHTML = '<option value="" disabled>正在加载数据源...</option>';

    fetch('/rfm/api/forward-data-sources')
        .then(response => {
            console.log('API响应状态:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('API响应数据:', data);
            if (data.success) {
                // 更新缓存
                dataSourcesCache = data.data_sources;
                lastDataSourcesLoadTime = now;
                populateDataSourceSelect(data.data_sources);
            } else {
                console.error('API返回错误:', data.error);
                showError('获取数据源失败: ' + (data.error || '未知错误'));
            }
        })
        .catch(error => {
            console.error('获取数据源失败:', error);
            showError('网络错误，请稍后重试');
        });
}

function populateDataSourceSelect(dataSources) {
    const select = document.getElementById('dataSourceSelect');

    if (dataSources.length === 0) {
        select.innerHTML = '<option value="" disabled>暂无可用数据源</option>';
        return;
    }

    // 清空并添加默认选项
    select.innerHTML = '<option value="">请选择数据源...</option>';

    dataSources.forEach(ds => {
        const option = document.createElement('option');
        option.value = ds.id;
        option.textContent = `${ds.source_task_name} (${ds.imported_records.toLocaleString()} 条记录)`;
        option.dataset.dataSource = JSON.stringify(ds);
        select.appendChild(option);
    });
}

function initializeEventListeners() {
    // 数据源选择变化
    document.getElementById('dataSourceSelect').addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const applyButton = document.getElementById('applyDataSource');
        const infoDiv = document.getElementById('dataSourceInfo');
        
        if (this.value) {
            const dataSource = JSON.parse(selectedOption.dataset.dataSource);
            applyButton.disabled = false;

            // 检查是否有缓存的报表
            const cacheStatus = isReportsCalculated ?
                `<span class="badge bg-success ms-1">已缓存</span>` :
                `<span class="badge bg-secondary ms-1">未计算</span>`;

            infoDiv.innerHTML = `
                <strong>${dataSource.source_task_name}</strong> ${cacheStatus}<br>
                <small class="text-muted">
                    记录数: ${dataSource.imported_records.toLocaleString()} |
                    季度: ${dataSource.quarters.join(', ')} |
                    导入时间: ${new Date(dataSource.imported_at).toLocaleString()}
                </small>
            `;
        } else {
            applyButton.disabled = true;
            const refreshBtn = document.getElementById('refreshReports');
            refreshBtn.disabled = true;
            infoDiv.textContent = '请先选择数据源';
        }
    });
    
    // 应用数据源
    document.getElementById('applyDataSource').addEventListener('click', function() {
        const select = document.getElementById('dataSourceSelect');
        const selectedOption = select.options[select.selectedIndex];

        if (select.value) {
            currentDataSource = JSON.parse(selectedOption.dataset.dataSource);

            // 启用刷新报表按钮
            const refreshBtn = document.getElementById('refreshReports');
            refreshBtn.disabled = false;

            // 清空报表缓存，强制重新计算
            reportsCache = null;
            isReportsCalculated = false;

            showReportContent();
        }
    });

    // 刷新报表
    document.getElementById('refreshReports').addEventListener('click', function() {
        if (currentDataSource) {
            // 清空报表缓存，强制重新计算
            reportsCache = null;
            isReportsCalculated = false;

            // 如果当前有选中的分析类型，重新加载
            if (currentAnalysisType) {
                loadAnalysisContent(currentAnalysisType, true); // 强制刷新
            }
        }
    });
    
    // 分析类型点击事件
    document.addEventListener('click', function(e) {
        if (e.target.matches('[data-analysis]') || e.target.closest('[data-analysis]')) {
            const button = e.target.matches('[data-analysis]') ? e.target : e.target.closest('[data-analysis]');
            const analysisType = button.dataset.analysis;
            
            if (analysisType && currentDataSource) {
                loadAnalysisContent(analysisType);
                
                // 更新按钮状态
                document.querySelectorAll('[data-analysis]').forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
            }
        }
    });
}

function showReportContent() {
    document.getElementById('noDataSourceMessage').style.display = 'none';
    document.getElementById('reportContent').style.display = 'block';

    // 如果还没有计算过报表，默认加载第一个分析
    if (!isReportsCalculated) {
        loadAnalysisContent('base-volume-trend');
        document.querySelector('[data-analysis="base-volume-trend"]').classList.add('active');
    } else {
        // 如果已经计算过，显示当前选中的分析类型
        if (currentAnalysisType) {
            loadAnalysisContent(currentAnalysisType);
        } else {
            loadAnalysisContent('base-volume-trend');
            document.querySelector('[data-analysis="base-volume-trend"]').classList.add('active');
        }
    }
}

function loadAnalysisContent(analysisType, forceRefresh = false) {
    currentAnalysisType = analysisType;

    // 确定内容容器
    let contentContainer;
    if (analysisType.startsWith('department-overview/') || analysisType === 'quarterly-category-analysis') {
        contentContainer = document.getElementById('departmentAnalysisContent');
    } else {
        contentContainer = document.getElementById('customerAnalysisContent');
    }

    // 检查缓存（只对基盘体量趋势分析使用缓存）
    if (!forceRefresh && analysisType === 'base-volume-trend' && reportsCache && isReportsCalculated) {
        console.log('使用缓存的报表数据');
        renderAnalysisContent(contentContainer, reportsCache);
        return;
    }

    // 显示加载状态
    contentContainer.innerHTML = `
        <div class="text-center py-5">
            <i class="fas fa-spinner fa-spin fa-2x text-primary mb-3"></i>
            <h5 class="text-primary">正在${forceRefresh ? '重新' : ''}加载分析数据...</h5>
        </div>
    `;

    // 获取分析数据
    fetch(`/rfm/api/analysis-data/${currentDataSource.id}/${analysisType}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 缓存基盘体量趋势分析的数据
                if (analysisType === 'base-volume-trend') {
                    reportsCache = data.data;
                    isReportsCalculated = true;
                    lastReportsCalculateTime = Date.now();
                }

                renderAnalysisContent(contentContainer, data.data);
            } else {
                showAnalysisError(contentContainer, data.error);
            }
        })
        .catch(error => {
            console.error('获取分析数据失败:', error);
            showAnalysisError(contentContainer, '网络错误，请稍后重试');
        });
}

function renderAnalysisContent(container, data) {
    if (currentAnalysisType === 'base-volume-trend') {
        if (data.error && data.missing_fields) {
            // 显示数据结构错误信息
            renderDataStructureError(container, data);
        } else if (data.reports) {
            // 渲染基盘体量趋势的3个报表
            renderBaseVolumeTrendReports(container, data);
        } else {
            // 显示其他错误
            renderGeneralError(container, data);
        }
    } else {
        // 其他分析类型显示占位符内容
        container.innerHTML = `
            <div class="alert alert-info">
                <h5 class="alert-heading">
                    <i class="fas fa-info-circle me-2"></i>
                    ${getAnalysisTitle(currentAnalysisType)}
                </h5>
                <p class="mb-2"><strong>数据源：</strong>${currentDataSource.source_task_name}</p>
                <p class="mb-2"><strong>分析类型：</strong>${currentAnalysisType}</p>
                <p class="mb-2"><strong>数据记录：</strong>${data.total_records.toLocaleString()} 条</p>
                <p class="mb-2"><strong>包含季度：</strong>${data.quarters.join(', ')}</p>
                <hr>
                <p class="mb-0">
                    <i class="fas fa-construction me-1"></i>
                    <strong>待实现功能</strong> - 此分析功能正在开发中，敬请期待！
                </p>
            </div>
        `;
    }
}

function showAnalysisError(container, error) {
    container.innerHTML = `
        <div class="alert alert-danger">
            <h5 class="alert-heading">
                <i class="fas fa-exclamation-triangle me-2"></i>
                加载失败
            </h5>
            <p class="mb-0">${error}</p>
        </div>
    `;
}

function getAnalysisTitle(analysisType) {
    const titles = {
        'base-volume-trend': '基盘体量趋势',
        'quarterly-upgrade-trend': '季度升降级趋势',
        'upgrade-matrix-change': '升降矩阵变化',
        'rfm-health-assessment': 'RFM健康度评估',
        'quarterly-resource-utilization': '季末资源利用率',
        'department-overview/micro-plastic': '科室总盘 - 微整',
        'department-overview/dermatology': '科室总盘 - 皮肤',
        'quarterly-category-analysis': '季度重点品类业绩分析'
    };
    
    return titles[analysisType] || analysisType;
}

function renderBaseVolumeTrendReports(container, data) {
    const reports = data.reports;
    const quarters = data.quarters || [];

    container.innerHTML = `
        <div class="row mb-4">
            <div class="col-12">
                <div class="alert alert-success">
                    <h5 class="alert-heading">
                        <i class="fas fa-chart-area me-2"></i>
                        基盘体量趋势分析
                    </h5>
                    <p class="mb-2"><strong>数据源：</strong>${currentDataSource.source_task_name}</p>
                    <p class="mb-2"><strong>数据记录：</strong>${data.total_records.toLocaleString()} 条</p>
                    <p class="mb-0"><strong>包含季度：</strong>${quarters.join(', ')}</p>
                </div>
            </div>
        </div>

        <!-- 报表一：存量老客RFM基盘体量 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-users me-2"></i>
                            存量老客RFM基盘体量 - 锁盘老客结构变化
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="rfmStructureReport"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 报表二：会员老客RFM基盘体量 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-gem me-2"></i>
                            会员老客RFM基盘体量 - 锁盘会员结构变化
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="memberStructureReport"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 报表三：咨询归属RFM基盘体量 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user-tie me-2"></i>
                            咨询归属RFM基盘体量
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="consultantAttributionReport"></div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 渲染各个报表
    renderRfmStructureReport('rfmStructureReport', reports.rfm_structure, quarters);
    renderMemberStructureReport('memberStructureReport', reports.member_structure, quarters);

    // 保存原始咨询归属数据
    originalConsultantData = reports.consultant_attribution;
    renderConsultantAttributionReport('consultantAttributionReport', reports.consultant_attribution, quarters);
}

function renderRfmStructureReport(containerId, reportData, quarters) {
    const container = document.getElementById(containerId);

    if (!reportData || !reportData.data || reportData.data.length === 0) {
        container.innerHTML = '<div class="alert alert-warning">暂无数据</div>';
        return;
    }

    // 生成表格HTML
    let tableHtml = `
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead style="background-color: #212529 !important;">
                    <tr>
                        <th rowspan="2" class="align-middle" style="color: white !important; background-color: #212529 !important;">综合等级</th>
                        <th rowspan="2" class="align-middle" style="color: white !important; background-color: #212529 !important;">细分等级</th>
    `;

    // 动态生成季度列头
    quarters.forEach(quarter => {
        tableHtml += `<th colspan="2" class="text-center" style="color: white !important; background-color: #212529 !important;">${quarter}</th>`;
    });

    tableHtml += `
                    </tr>
                    <tr>
    `;

    quarters.forEach(quarter => {
        tableHtml += `
            <th class="text-center" style="color: white !important; background-color: #212529 !important;">锁盘老客人数</th>
            <th class="text-center" style="color: white !important; background-color: #212529 !important;">环比增长</th>
        `;
    });

    tableHtml += `
                    </tr>
                </thead>
                <tbody>
    `;

    // 按综合等级和细分等级分组数据
    const groupedData = {};
    reportData.data.forEach(row => {
        const key = `${row.综合等级}_${row.细分等级}`;
        if (!groupedData[key]) {
            groupedData[key] = {
                综合等级: row.综合等级,
                细分等级: row.细分等级,
                quarters: {}
            };
        }
        groupedData[key].quarters[row.季度] = {
            锁盘老客人数: row.锁盘老客人数,
            环比增长: row.环比增长
        };
    });

    // 生成表格行
    Object.values(groupedData).forEach(group => {
        const isSubtotal = group.综合等级 && group.综合等级.includes('小计');
        const isTotal = group.综合等级 && group.综合等级.includes('合计');
        const rowClass = isSubtotal ? 'table-warning fw-bold' : (isTotal ? 'table-info fw-bold' : '');

        tableHtml += `
            <tr class="${rowClass}">
                <td>${group.综合等级}</td>
                <td>${group.细分等级}</td>
        `;

        quarters.forEach(quarter => {
            const quarterData = group.quarters[quarter] || {};
            const count = quarterData.锁盘老客人数 || 0;
            const growth = quarterData.环比增长 || '-';

            tableHtml += `
                <td class="text-end">${count.toLocaleString()}</td>
                <td class="text-end">${growth}</td>
            `;
        });

        tableHtml += '</tr>';
    });

    tableHtml += `
                </tbody>
            </table>
        </div>
    `;

    container.innerHTML = tableHtml;
}

function renderMemberStructureReport(containerId, reportData, quarters) {
    const container = document.getElementById(containerId);

    if (!reportData || !reportData.data || reportData.data.length === 0) {
        container.innerHTML = '<div class="alert alert-warning">暂无数据</div>';
        return;
    }

    // 类似的表格生成逻辑，但使用会员池和会员卡级
    let tableHtml = `
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead style="background-color: #212529 !important;">
                    <tr>
                        <th rowspan="2" class="align-middle" style="color: white !important; background-color: #212529 !important;">会员池</th>
                        <th rowspan="2" class="align-middle" style="color: white !important; background-color: #212529 !important;">会员卡级</th>
    `;

    quarters.forEach(quarter => {
        tableHtml += `<th colspan="2" class="text-center" style="color: white !important; background-color: #212529 !important;">${quarter}</th>`;
    });

    tableHtml += `
                    </tr>
                    <tr>
    `;

    quarters.forEach(quarter => {
        tableHtml += `
            <th class="text-center" style="color: white !important; background-color: #212529 !important;">锁盘老客人数</th>
            <th class="text-center" style="color: white !important; background-color: #212529 !important;">环比增长</th>
        `;
    });

    tableHtml += `
                    </tr>
                </thead>
                <tbody>
    `;

    // 按会员池和会员卡级分组数据
    const groupedData = {};
    reportData.data.forEach(row => {
        const key = `${row.会员池}_${row.会员卡级}`;
        if (!groupedData[key]) {
            groupedData[key] = {
                会员池: row.会员池,
                会员卡级: row.会员卡级,
                quarters: {}
            };
        }
        groupedData[key].quarters[row.季度] = {
            锁盘老客人数: row.锁盘老客人数,
            环比增长: row.环比增长
        };
    });

    // 生成表格行
    Object.values(groupedData).forEach(group => {
        tableHtml += `
            <tr>
                <td>${group.会员池}</td>
                <td>${group.会员卡级}</td>
        `;

        quarters.forEach(quarter => {
            const quarterData = group.quarters[quarter] || {};
            const count = quarterData.锁盘老客人数 || 0;
            const growth = quarterData.环比增长 || '-';

            tableHtml += `
                <td class="text-end">${count.toLocaleString()}</td>
                <td class="text-end">${growth}</td>
            `;
        });

        tableHtml += '</tr>';
    });

    tableHtml += `
                </tbody>
            </table>
        </div>
    `;

    container.innerHTML = tableHtml;
}

function renderConsultantAttributionReport(containerId, reportData, quarters) {
    const container = document.getElementById(containerId);

    if (!reportData || !reportData.data || reportData.data.length === 0) {
        container.innerHTML = '<div class="alert alert-warning">暂无数据</div>';
        return;
    }

    // 直接实现筛选功能
    let filterHtml = '';
    if (reportData.filter_options) {
        filterHtml = `
            <div class="row mb-3">
                <div class="col-md-6">
                    <label class="form-label">细分等级筛选：</label>
                    <div class="filter-checkboxes" id="subdivisionFilter">
        `;

        reportData.filter_options.subdivision_levels.forEach(level => {
            filterHtml += `
                <div class="form-check form-check-inline">
                    <input class="form-check-input consultant-filter" type="checkbox" value="${level}" id="sub_${level}" checked data-filter-type="subdivision">
                    <label class="form-check-label" for="sub_${level}">${level}</label>
                </div>
            `;
        });

        filterHtml += `
                    </div>
                </div>
                <div class="col-md-6">
                    <label class="form-label">会员卡级筛选：</label>
                    <div class="filter-checkboxes" id="cardLevelFilter">
        `;

        reportData.filter_options.member_card_levels.forEach(level => {
            filterHtml += `
                <div class="form-check form-check-inline">
                    <input class="form-check-input consultant-filter" type="checkbox" value="${level}" id="card_${level}" checked data-filter-type="card_level">
                    <label class="form-check-label" for="card_${level}">${level}</label>
                </div>
            `;
        });

        filterHtml += `
                    </div>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-12">
                    <button type="button" class="btn btn-primary me-2" id="refreshConsultantData">
                        <i class="fas fa-sync-alt me-1"></i>
                        刷新数据
                    </button>
                    <button type="button" class="btn btn-outline-secondary me-2" id="selectAllFilters">
                        <i class="fas fa-check-square me-1"></i>
                        全选
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="clearAllFilters">
                        <i class="fas fa-square me-1"></i>
                        清空
                    </button>
                    <span class="text-muted ms-3">
                        <i class="fas fa-info-circle me-1"></i>
                        选择筛选条件后，点击"刷新数据"按钮更新报表
                    </span>
                </div>
            </div>
            <hr>
        `;
    }

    // 生成表格
    let tableHtml = `
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead style="background-color: #212529 !important;">
                    <tr>
                        <th rowspan="2" class="align-middle" style="color: white !important; background-color: #212529 !important;">现场小组</th>
                        <th rowspan="2" class="align-middle" style="color: white !important; background-color: #212529 !important;">最新现场</th>
    `;

    quarters.forEach(quarter => {
        tableHtml += `<th colspan="2" class="text-center" style="color: white !important; background-color: #212529 !important;">${quarter}</th>`;
    });

    tableHtml += `
                    </tr>
                    <tr>
    `;

    quarters.forEach(quarter => {
        tableHtml += `
            <th class="text-center" style="color: white !important; background-color: #212529 !important;">锁盘老客人数</th>
            <th class="text-center" style="color: white !important; background-color: #212529 !important;">环比增长</th>
        `;
    });

    tableHtml += `
                    </tr>
                </thead>
                <tbody>
    `;

    // 按现场小组和最新现场分组数据
    const groupedData = {};
    reportData.data.forEach(row => {
        const key = `${row.现场小组}_${row.最新现场}`;
        if (!groupedData[key]) {
            groupedData[key] = {
                现场小组: row.现场小组,
                最新现场: row.最新现场,
                quarters: {}
            };
        }
        groupedData[key].quarters[row.季度] = {
            锁盘老客人数: row.锁盘老客人数,
            环比增长: row.环比增长
        };
    });

    // 生成表格行
    Object.values(groupedData).forEach(group => {
        const isSubtotal = group.现场小组 && group.现场小组.includes('小计');
        const isTotal = group.现场小组 && group.现场小组.includes('合计');
        const rowClass = isSubtotal ? 'table-warning fw-bold' : (isTotal ? 'table-info fw-bold' : '');

        tableHtml += `
            <tr class="${rowClass}">
                <td>${group.现场小组}</td>
                <td>${group.最新现场}</td>
        `;

        quarters.forEach(quarter => {
            const quarterData = group.quarters[quarter] || {};
            const count = quarterData.锁盘老客人数 || 0;
            const growth = quarterData.环比增长 || '-';

            tableHtml += `
                <td class="text-end">${count.toLocaleString()}</td>
                <td class="text-end">${growth}</td>
            `;
        });

        tableHtml += '</tr>';
    });

    tableHtml += `
                </tbody>
            </table>
        </div>
    `;

    container.innerHTML = filterHtml + tableHtml;

    // 添加刷新按钮事件监听
    const refreshBtn = container.querySelector('#refreshConsultantData');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            updateConsultantReport();
        });
    }

    // 添加全选按钮事件监听
    const selectAllBtn = container.querySelector('#selectAllFilters');
    if (selectAllBtn) {
        selectAllBtn.addEventListener('click', function() {
            container.querySelectorAll('.consultant-filter').forEach(cb => {
                cb.checked = true;
            });
        });
    }

    // 添加清空按钮事件监听
    const clearAllBtn = container.querySelector('#clearAllFilters');
    if (clearAllBtn) {
        clearAllBtn.addEventListener('click', function() {
            container.querySelectorAll('.consultant-filter').forEach(cb => {
                cb.checked = false;
            });
        });
    }
}

function updateConsultantReport() {
    if (!originalConsultantData) {
        alert('没有原始数据，请先应用数据源');
        return;
    }

    if (!currentDataSource || !currentDataSource.id) {
        alert('没有当前数据源，请先选择并应用数据源');
        return;
    }

    // 显示加载状态
    const refreshBtn = document.querySelector('#refreshConsultantData');
    if (refreshBtn) {
        refreshBtn.disabled = true;
        refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>加载中...';
    }

    // 获取选中的筛选条件
    const subdivisionFilters = [];
    const cardLevelFilters = [];

    document.querySelectorAll('.consultant-filter[data-filter-type="subdivision"]:checked').forEach(cb => {
        subdivisionFilters.push(cb.value);
    });

    document.querySelectorAll('.consultant-filter[data-filter-type="card_level"]:checked').forEach(cb => {
        cardLevelFilters.push(cb.value);
    });

    // 调用后端API获取筛选后的数据
    const requestData = {
        subdivision_levels: subdivisionFilters,
        member_card_levels: cardLevelFilters
    };

    fetch(`/rfm/api/consultant-attribution-filter/${currentDataSource.id}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 重新渲染表格部分
            renderConsultantTableOnly('consultantAttributionReport', data.data, data.quarters);
        } else {
            alert('筛选失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('筛选请求失败:', error);
        alert('网络错误，请稍后重试');
    })
    .finally(() => {
        // 恢复按钮状态
        const refreshBtn = document.querySelector('#refreshConsultantData');
        if (refreshBtn) {
            refreshBtn.disabled = false;
            refreshBtn.innerHTML = '<i class="fas fa-sync-alt me-1"></i>刷新数据';
        }
    });
}

function renderConsultantTableOnly(containerId, reportData, quarters) {
    const container = document.getElementById(containerId);
    const tableContainer = container.querySelector('.table-responsive');

    if (!tableContainer) return;

    // 只重新生成表格部分，保留筛选器
    let tableHtml = `
        <table class="table table-striped table-hover">
            <thead style="background-color: #212529 !important;">
                <tr>
                    <th rowspan="2" class="align-middle" style="color: white !important; background-color: #212529 !important;">现场小组</th>
                    <th rowspan="2" class="align-middle" style="color: white !important; background-color: #212529 !important;">最新现场</th>
    `;

    quarters.forEach(quarter => {
        tableHtml += `<th colspan="2" class="text-center" style="color: white !important; background-color: #212529 !important;">${quarter}</th>`;
    });

    tableHtml += `
                </tr>
                <tr>
    `;

    quarters.forEach(quarter => {
        tableHtml += `
            <th class="text-center" style="color: white !important; background-color: #212529 !important;">锁盘老客人数</th>
            <th class="text-center" style="color: white !important; background-color: #212529 !important;">环比增长</th>
        `;
    });

    tableHtml += `
                </tr>
            </thead>
            <tbody>
    `;

    // 按现场小组和最新现场分组数据
    const groupedData = {};
    reportData.data.forEach(row => {
        const key = `${row.现场小组}_${row.最新现场}`;
        if (!groupedData[key]) {
            groupedData[key] = {
                现场小组: row.现场小组,
                最新现场: row.最新现场,
                quarters: {}
            };
        }
        groupedData[key].quarters[row.季度] = {
            锁盘老客人数: row.锁盘老客人数,
            环比增长: row.环比增长
        };
    });

    // 生成表格行
    Object.values(groupedData).forEach(group => {
        const isSubtotal = group.现场小组 && group.现场小组.includes('小计');
        const isTotal = group.现场小组 && group.现场小组.includes('合计');
        const rowClass = isSubtotal ? 'table-warning fw-bold' : (isTotal ? 'table-info fw-bold' : '');

        tableHtml += `
            <tr class="${rowClass}">
                <td>${group.现场小组}</td>
                <td>${group.最新现场}</td>
        `;

        quarters.forEach(quarter => {
            const quarterData = group.quarters[quarter] || {};
            const count = quarterData.锁盘老客人数 || 0;
            const growth = quarterData.环比增长 || '-';

            tableHtml += `
                <td class="text-end">${count.toLocaleString()}</td>
                <td class="text-end">${growth}</td>
            `;
        });

        tableHtml += '</tr>';
    });

    tableHtml += `
            </tbody>
        </table>
    `;

    tableContainer.innerHTML = tableHtml;
}

// 渲染咨询归属数据表格
function renderConsultantDataTable(containerId, reportData, quarters) {
    const container = document.getElementById(containerId);

    if (!reportData || !reportData.data || reportData.data.length === 0) {
        container.innerHTML = '<div class="alert alert-warning">暂无数据</div>';
        return;
    }

    // 生成表格
    let tableHtml = `
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead style="background-color: #212529 !important;">
                    <tr>
                        <th rowspan="2" class="align-middle" style="color: white !important; background-color: #212529 !important;">现场小组</th>
                        <th rowspan="2" class="align-middle" style="color: white !important; background-color: #212529 !important;">最新现场</th>
    `;

    quarters.forEach(quarter => {
        tableHtml += `<th colspan="2" class="text-center" style="color: white !important; background-color: #212529 !important;">${quarter}</th>`;
    });

    tableHtml += `
                    </tr>
                    <tr>
    `;

    quarters.forEach(quarter => {
        tableHtml += `
            <th class="text-center" style="color: white !important; background-color: #212529 !important;">锁盘老客人数</th>
            <th class="text-center" style="color: white !important; background-color: #212529 !important;">环比增长</th>
        `;
    });

    tableHtml += `
                    </tr>
                </thead>
                <tbody>
    `;

    // 按现场小组和最新现场分组数据
    const groupedData = {};
    reportData.data.forEach(row => {
        const key = `${row.现场小组}_${row.最新现场}`;
        if (!groupedData[key]) {
            groupedData[key] = {
                现场小组: row.现场小组,
                最新现场: row.最新现场,
                quarters: {}
            };
        }
        groupedData[key].quarters[row.季度] = {
            锁盘老客人数: row.锁盘老客人数,
            环比增长: row.环比增长
        };
    });

    // 生成表格行
    Object.values(groupedData).forEach(group => {
        const isSubtotal = group.现场小组 && group.现场小组.includes('小计');
        const isTotal = group.现场小组 && group.现场小组.includes('合计');
        const rowClass = isSubtotal ? 'table-warning fw-bold' : (isTotal ? 'table-info fw-bold' : '');

        tableHtml += `
            <tr class="${rowClass}">
                <td>${group.现场小组}</td>
                <td>${group.最新现场}</td>
        `;

        quarters.forEach(quarter => {
            const quarterData = group.quarters[quarter] || {};
            const count = quarterData.锁盘老客人数 || 0;
            const growth = quarterData.环比增长 || '-';

            tableHtml += `
                <td class="text-end">${count.toLocaleString()}</td>
                <td class="text-end">${growth}</td>
            `;
        });

        tableHtml += '</tr>';
    });

    tableHtml += `
                </tbody>
            </table>
        </div>
    `;

    container.innerHTML = tableHtml;
}

// 初始化筛选器事件监听
function initializeFilterEventListeners(container) {
    // 筛选器复选框变化事件
    const filterCheckboxes = container.querySelectorAll('.consultant-filter');
    filterCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            // 立即更新状态显示
            updateFilterStatus();
            
            // 防抖更新数据
            if (!filterState.isLoading) {
                debounceFilterUpdate();
            }
        });
    });

    // 全选/全不选按钮事件
    const actionButtons = container.querySelectorAll('[data-action]');
    actionButtons.forEach(button => {
        button.addEventListener('click', function() {
            const action = this.dataset.action;
            const target = this.dataset.target;
            
            if (action === 'select-all') {
                selectAllFilters(target, true);
            } else if (action === 'select-none') {
                selectAllFilters(target, false);
            }
            
            // 立即更新状态显示
            updateFilterStatus();
            
            if (!filterState.isLoading) {
                debounceFilterUpdate();
            }
        });
    });

    // 全局全选/清空按钮事件
    const selectAllBtn = container.querySelector('#selectAllFilters');
    const clearAllBtn = container.querySelector('#clearAllFilters');
    
    if (selectAllBtn) {
        selectAllBtn.addEventListener('click', function() {
            selectAllFilters('subdivision', true);
            selectAllFilters('card_level', true);
            updateFilterStatus();
            if (!filterState.isLoading) {
                debounceFilterUpdate();
            }
        });
    }
    
    if (clearAllBtn) {
        clearAllBtn.addEventListener('click', function() {
            selectAllFilters('subdivision', false);
            selectAllFilters('card_level', false);
            updateFilterStatus();
            if (!filterState.isLoading) {
                debounceFilterUpdate();
            }
        });
    }
    
    // 初始化时更新状态显示
    updateFilterStatus();
}

// 全选/全不选指定类型的筛选器
function selectAllFilters(filterType, checked) {
    const selector = filterType === 'subdivision' ? 
        'input[data-filter-type="subdivision"]' : 
        'input[data-filter-type="card_level"]';
    
    const checkboxes = document.querySelectorAll(selector);
    checkboxes.forEach(checkbox => {
        checkbox.checked = checked;
    });
    
    updateFilterStatus();
}

// 防抖更新筛选器
function debounceFilterUpdate() {
    if (filterState.debounceTimer) {
        clearTimeout(filterState.debounceTimer);
    }
    
    filterState.debounceTimer = setTimeout(() => {
        applyConsultantFilter();
    }, 300); // 300ms防抖延迟
}

// 应用咨询归属筛选
function applyConsultantFilter() {
    if (filterState.isLoading) {
        return;
    }

    // 获取选中的筛选条件
    const subdivisionLevels = [];
    const memberCardLevels = [];
    
    document.querySelectorAll('input[data-filter-type="subdivision"]:checked').forEach(checkbox => {
        subdivisionLevels.push(checkbox.value);
    });
    
    document.querySelectorAll('input[data-filter-type="card_level"]:checked').forEach(checkbox => {
        memberCardLevels.push(checkbox.value);
    });

    // 验证筛选条件
    if (subdivisionLevels.length === 0 || memberCardLevels.length === 0) {
        showFilterError('请至少选择一个细分等级和一个会员卡级');
        return;
    }

    // 设置加载状态
    setFilterLoadingState(true);
    
    const filterData = {
        subdivision_levels: subdivisionLevels,
        member_card_levels: memberCardLevels
    };

    // 发送筛选请求
    fetch(`/rfm/api/consultant-attribution-filter/${currentDataSource.id}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(filterData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 更新数据表格
            const quarters = data.quarters || [];
            renderConsultantDataTable('consultantDataTable', data.data, quarters);
            
            // 更新筛选状态显示
            updateFilterResultStatus(data);
            
            // 保存筛选状态
            filterState.lastFilterData = filterData;
            FilterStateManager.saveState(currentDataSource.id, filterData);
            
        } else {
            showFilterError(data.error || '筛选失败，请稍后重试');
        }
    })
    .catch(error => {
        console.error('筛选请求失败:', error);
        showFilterError('网络错误，请稍后重试');
    })
    .finally(() => {
        setFilterLoadingState(false);
    });
}

// 设置筛选器加载状态
function setFilterLoadingState(loading) {
    filterState.isLoading = loading;
    
    const filterContainer = document.getElementById('consultantAttributionReport');
    if (!filterContainer) return;
    
    const checkboxes = filterContainer.querySelectorAll('.consultant-filter');
    const buttons = filterContainer.querySelectorAll('button');
    
    checkboxes.forEach(checkbox => {
        checkbox.disabled = loading;
    });
    
    buttons.forEach(button => {
        button.disabled = loading;
    });
    
    if (loading) {
        const dataTable = document.getElementById('consultantDataTable');
        if (dataTable) {
            dataTable.innerHTML = `
                <div class="text-center py-3">
                    <i class="fas fa-spinner fa-spin fa-2x text-primary mb-3"></i>
                    <p class="text-muted">正在筛选数据...</p>
                </div>
            `;
        }
    }
}

// 更新筛选状态显示
function updateFilterStatus() {
    const subdivisionCount = document.querySelectorAll('input[data-filter-type="subdivision"]:checked').length;
    const cardLevelCount = document.querySelectorAll('input[data-filter-type="card_level"]:checked').length;
    
    const subdivisionCountSpan = document.getElementById('selectedSubdivisionCount');
    const cardLevelCountSpan = document.getElementById('selectedCardLevelCount');
    
    if (subdivisionCountSpan) {
        subdivisionCountSpan.textContent = subdivisionCount;
    }
    
    if (cardLevelCountSpan) {
        cardLevelCountSpan.textContent = cardLevelCount;
    }
}

// 更新筛选结果状态
function updateFilterResultStatus(data) {
    const statusDiv = document.getElementById('filterStatus');
    if (statusDiv && data.total_records !== undefined && data.filtered_records !== undefined) {
        const filterRate = data.total_records > 0 ? 
            ((data.filtered_records / data.total_records) * 100).toFixed(1) : 0;
        
        statusDiv.innerHTML = `
            <small>
                <i class="fas fa-info-circle me-1"></i>
                筛选结果: 共 ${data.filtered_records.toLocaleString()} 条记录 
                (占总数据的 ${filterRate}%, 总计 ${data.total_records.toLocaleString()} 条)
            </small>
        `;
        
        // 根据筛选结果调整状态样式
        statusDiv.className = data.filtered_records > 0 ? 
            'alert alert-success mb-0' : 'alert alert-warning mb-0';
    }
}

// 显示筛选错误
function showFilterError(message) {
    const statusDiv = document.getElementById('filterStatus');
    if (statusDiv) {
        statusDiv.innerHTML = `
            <small>
                <i class="fas fa-exclamation-triangle me-1"></i>
                ${message}
            </small>
        `;
        statusDiv.className = 'alert alert-danger mb-0';
    }
    
    // 3秒后恢复正常状态
    setTimeout(() => {
        updateFilterStatus();
        const statusDiv = document.getElementById('filterStatus');
        if (statusDiv) {
            statusDiv.className = 'alert alert-info mb-0';
        }
    }, 3000);
}

// 恢复筛选状态
function restoreFilterState() {
    if (!currentDataSource) return;
    
    // 尝试从本地存储恢复筛选状态
    const savedState = FilterStateManager.loadState(currentDataSource.id);
    if (!savedState) return;
    
    // 获取当前可用的筛选选项
    fetch(`/rfm/api/filter-options/${currentDataSource.id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 验证保存的状态是否仍然有效
                if (FilterStateManager.validateState(savedState, data)) {
                    // 恢复筛选器选中状态
                    applyFilterState(savedState);
                    
                    // 自动应用筛选
                    setTimeout(() => {
                        if (!filterState.isLoading) {
                            applyConsultantFilter();
                        }
                    }, 100);
                }
            }
        })
        .catch(error => {
            console.warn('无法获取筛选选项:', error);
        });
}

// 应用筛选状态到界面
function applyFilterState(state) {
    // 先清空所有选择
    document.querySelectorAll('.consultant-filter').forEach(checkbox => {
        checkbox.checked = false;
    });
    
    // 恢复细分等级选择
    state.subdivision_levels.forEach(level => {
        const checkbox = document.querySelector(`input[data-filter-type="subdivision"][value="${level}"]`);
        if (checkbox) {
            checkbox.checked = true;
        }
    });
    
    // 恢复会员卡级选择
    state.member_card_levels.forEach(level => {
        const checkbox = document.querySelector(`input[data-filter-type="card_level"][value="${level}"]`);
        if (checkbox) {
            checkbox.checked = true;
        }
    });
    
    // 更新状态显示
    updateFilterStatus();
}

// 显示错误信息的通用函数
function showError(message) {
    console.error(message);
    // 可以在这里添加更多的错误显示逻辑
}

// 渲染数据结构错误信息
function renderDataStructureError(container, data) {
    container.innerHTML = `
        <div class="alert alert-warning">
            <h5 class="alert-heading">
                <i class="fas fa-exclamation-triangle me-2"></i>
                数据结构不完整
            </h5>
            <p class="mb-2">${data.error}</p>
            <hr>
            <p class="mb-0">
                <strong>建议：</strong>请在数据处理页面重新生成包含正确字段结构的正向盘数据源。
            </p>
        </div>
    `;
}

// 渲染一般错误信息
function renderGeneralError(container, data) {
    container.innerHTML = `
        <div class="alert alert-danger">
            <h5 class="alert-heading">
                <i class="fas fa-exclamation-triangle me-2"></i>
                数据加载失败
            </h5>
            <p class="mb-0">${data.error || '未知错误'}</p>
        </div>
    `;
}
</script>

<!-- 引入增强版筛选器JavaScript -->
<script src="{{ url_for('static', filename='js/consultant-filter-enhanced.js') }}"></script>

{% endblock %}