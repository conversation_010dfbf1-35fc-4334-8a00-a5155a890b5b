#!/usr/bin/env python3
"""
紧急修复脚本 - 解决500错误问题
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def emergency_fix():
    """紧急修复系统问题"""
    print("=" * 50)
    print("紧急修复系统问题")
    print("=" * 50)
    
    try:
        from app import create_app, db
        from sqlalchemy import text
        
        app = create_app()
        
        with app.app_context():
            # 1. 测试数据库连接
            print("1. 测试数据库连接...")
            try:
                db.session.execute(text("SELECT 1"))
                print("✓ 数据库连接正常")
            except Exception as e:
                print(f"❌ 数据库连接失败: {e}")
                return False
            
            # 2. 检查关键表是否存在
            print("\n2. 检查关键表...")
            required_tables = ['user_accounts', 'user_roles', 'permissions', 'role_permissions', 'departments', 'channels']
            
            for table in required_tables:
                try:
                    count = db.session.execute(text(f"SELECT COUNT(*) FROM {table}")).scalar()
                    print(f"✓ 表 {table}: {count} 条记录")
                except Exception as e:
                    print(f"❌ 表 {table} 检查失败: {e}")
                    return False
            
            # 3. 修复权限问题
            print("\n3. 修复权限问题...")
            try:
                # 确保REPORT_VIEW权限存在
                report_view_exists = db.session.execute(text("""
                    SELECT COUNT(*) FROM permissions WHERE permission_code = 'REPORT_VIEW'
                """)).scalar()
                
                if report_view_exists == 0:
                    db.session.execute(text("""
                        INSERT INTO permissions (permission_name, permission_code, module_name, description)
                        VALUES ('报表查看', 'REPORT_VIEW', 'REPORT', '查看统计报表')
                    """))
                    print("✓ 添加REPORT_VIEW权限")
                else:
                    print("✓ REPORT_VIEW权限已存在")
                
                # 确保管理员有REPORT_VIEW权限
                admin_has_report_view = db.session.execute(text("""
                    SELECT COUNT(*) FROM role_permissions rp
                    JOIN user_roles r ON rp.role_id = r.id
                    JOIN permissions p ON rp.permission_id = p.id
                    WHERE r.role_code = 'ADMIN' AND p.permission_code = 'REPORT_VIEW'
                """)).scalar()
                
                if admin_has_report_view == 0:
                    db.session.execute(text("""
                        INSERT INTO role_permissions (role_id, permission_id)
                        SELECT r.id, p.id 
                        FROM user_roles r, permissions p
                        WHERE r.role_code = 'ADMIN' AND p.permission_code = 'REPORT_VIEW'
                    """))
                    print("✓ 为管理员添加REPORT_VIEW权限")
                else:
                    print("✓ 管理员已有REPORT_VIEW权限")
                
                db.session.commit()
                
            except Exception as e:
                print(f"❌ 权限修复失败: {e}")
                db.session.rollback()
                return False
            
            # 4. 检查用户部门关联
            print("\n4. 检查用户部门关联...")
            try:
                users_without_dept = db.session.execute(text("""
                    SELECT COUNT(*) FROM user_accounts WHERE department_id IS NULL
                """)).scalar()
                
                if users_without_dept > 0:
                    print(f"⚠️ 发现 {users_without_dept} 个用户没有部门分配")
                    
                    # 获取默认部门
                    default_dept = db.session.execute(text("""
                        SELECT id FROM departments ORDER BY id LIMIT 1
                    """)).scalar()
                    
                    if default_dept:
                        db.session.execute(text("""
                            UPDATE user_accounts SET department_id = :dept_id WHERE department_id IS NULL
                        """), {'dept_id': default_dept})
                        print(f"✓ 为用户分配默认部门: {default_dept}")
                        db.session.commit()
                    else:
                        print("❌ 没有可用的部门")
                        return False
                else:
                    print("✓ 所有用户都已分配部门")
                    
            except Exception as e:
                print(f"❌ 用户部门检查失败: {e}")
                db.session.rollback()
                return False
            
            # 5. 测试权限检查功能
            print("\n5. 测试权限检查功能...")
            try:
                from app.models.user import User
                
                admin_user = db.session.execute(text("""
                    SELECT u.id FROM user_accounts u
                    JOIN user_roles r ON u.role_id = r.id
                    WHERE r.role_code = 'ADMIN' AND u.is_active = 1
                    LIMIT 1
                """)).scalar()
                
                if admin_user:
                    user = User.query.get(admin_user)
                    if user:
                        has_channel_view = user.has_permission('CHANNEL_VIEW')
                        has_report_view = user.has_permission('REPORT_VIEW')
                        print(f"✓ 权限测试: CHANNEL_VIEW={has_channel_view}, REPORT_VIEW={has_report_view}")
                        
                        if not has_channel_view or not has_report_view:
                            print("❌ 管理员权限不完整")
                            return False
                    else:
                        print("❌ 无法获取管理员用户对象")
                        return False
                else:
                    print("❌ 没有找到管理员用户")
                    return False
                    
            except Exception as e:
                print(f"❌ 权限测试失败: {e}")
                return False
            
            # 6. 添加基本的数据库索引
            print("\n6. 添加数据库索引...")
            try:
                indexes = [
                    ("idx_channels_created_time", "channels", "created_time DESC"),
                    ("idx_user_accounts_role_id", "user_accounts", "role_id"),
                    ("idx_role_permissions_role_id", "role_permissions", "role_id")
                ]
                
                for index_name, table_name, columns in indexes:
                    try:
                        # 检查索引是否存在
                        index_exists = db.session.execute(text(f"""
                            SELECT COUNT(*) FROM information_schema.statistics 
                            WHERE table_schema = DATABASE() 
                            AND table_name = '{table_name}' 
                            AND index_name = '{index_name}'
                        """)).scalar()
                        
                        if index_exists == 0:
                            db.session.execute(text(f"CREATE INDEX {index_name} ON {table_name} ({columns})"))
                            print(f"✓ 添加索引: {index_name}")
                        else:
                            print(f"✓ 索引已存在: {index_name}")
                    except Exception as e:
                        print(f"⚠️ 索引 {index_name} 添加失败: {e}")
                
                db.session.commit()
                
            except Exception as e:
                print(f"❌ 索引添加失败: {e}")
                db.session.rollback()
            
            print("\n" + "=" * 50)
            print("紧急修复完成！")
            print("=" * 50)
            print("修复内容:")
            print("1. ✓ 数据库连接正常")
            print("2. ✓ 关键表结构完整")
            print("3. ✓ 权限系统修复")
            print("4. ✓ 用户部门关联修复")
            print("5. ✓ 权限检查功能正常")
            print("6. ✓ 数据库索引优化")
            
            print("\n现在可以重新启动系统:")
            print("python start.py")
            
            return True
            
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("请检查Python环境和依赖包安装")
        return False
    except Exception as e:
        print(f"❌ 修复过程中出现错误: {e}")
        return False

if __name__ == '__main__':
    success = emergency_fix()
    print(f"\n修复状态: {'成功' if success else '失败'}")
    sys.exit(0 if success else 1)