import os
import threading
from datetime import datetime
from flask import render_template, redirect, url_for, flash, request, jsonify, send_file, current_app
from flask_login import login_required, current_user
from app.data_processing import bp
from app.data_processing.forms import DataProcessingForm
from app.data_processing.processor import RFMDataProcessor
from app.models import DatabaseConfig, DataProcessingTask, db
import logging

@bp.route('/new_task', methods=['GET', 'POST'])
@login_required
def new_task():
    """创建新的数据处理任务"""
    # 检查是否有激活的数据库配置
    active_config = DatabaseConfig.get_active_config()
    if not active_config:
        flash('请先配置并激活一个数据库连接', 'warning')
        return redirect(url_for('config_mgmt.list_configs'))
    
    form = DataProcessingForm()
    
    # 获取可用的季度表和季度选项
    try:
        processor = RFMDataProcessor(active_config)
        if processor.connect_database():
            quarter_tables = processor.get_quarter_tables()
            
            # 设置季度表选项（用于结果盘）
            form.quarter_tables.choices = [(table, table) for table in quarter_tables]

            # 设置基础会员来源选项（用于正向盘）
            base_choices = [('', '请选择基础会员来源')] + [(table, table) for table in quarter_tables]
            form.base_quarter_table.choices = base_choices

            # 生成季度选项（基于表名推断）
            quarters = set()
            for table in quarter_tables:
                # 从表名提取季度信息，如 24q3 -> 24Q3
                import re
                match = re.match(r'^(\d{2})[qQ]([1-4])$', table)
                if match:
                    year, quarter = match.groups()
                    quarters.add(f"{year}Q{quarter}")

            # 添加额外的季度选项
            current_year = datetime.now().year % 100
            for year in range(current_year - 2, current_year + 2):
                for q in range(1, 5):
                    quarters.add(f"{year:02d}Q{q}")

            form.quarters.choices = [(q, q) for q in sorted(quarters)]
            
    except Exception as e:
        flash(f'获取数据库信息失败: {str(e)}', 'error')
        form.quarter_tables.choices = []
        form.quarters.choices = []
    
    if form.validate_on_submit():
        # 额外的业务逻辑验证
        if form.task_type.data == 'forward':
            # 正向盘验证
            if not form.base_quarter_table.data:
                flash('正向盘模式下请选择基础会员来源季度表', 'error')
                return render_template('data_processing/new_task.html',
                                     title='新建数据处理任务',
                                     form=form,
                                     active_config=active_config)
        else:
            # 结果盘验证
            if not form.quarter_tables.data:
                flash('结果盘模式下请至少选择一个季度表', 'error')
                return render_template('data_processing/new_task.html',
                                     title='新建数据处理任务',
                                     form=form,
                                     active_config=active_config)

        if not form.quarters.data:
            flash('请至少选择一个分析季度', 'error')
            return render_template('data_processing/new_task.html',
                                 title='新建数据处理任务',
                                 form=form,
                                 active_config=active_config)

        # 创建数据处理任务
        task = DataProcessingTask(
            task_name=form.task_name.data,
            task_type=form.task_type.data,
            created_by=current_user.id
        )

        # 设置任务参数
        # 映射选择参数
        mapping_options = {
            'use_level1_mapping': form.use_level1_mapping.data,
            'use_level2_mapping': form.use_level2_mapping.data,
            'use_level3_mapping': form.use_level3_mapping.data,
            'use_field_mapping': form.use_field_mapping.data,
            'use_field_group': form.use_field_group.data
        }

        # TOP排名时间范围参数
        top_ranking_dates = {
            'top_ranking_start_date': form.top_ranking_start_date.data,
            'top_ranking_end_date': form.top_ranking_end_date.data,
            'top_value_start_date': form.top_value_start_date.data,
            'top_value_end_date': form.top_value_end_date.data
        }

        if form.task_type.data == 'forward':
            # 正向盘：基础会员来源表 + 分析季度
            parameters = {
                'base_quarter_table': form.base_quarter_table.data,
                'quarter_tables': [form.base_quarter_table.data],  # 传递基础表给处理器
                'quarters': form.quarters.data,  # 要分析的所有季度
                'departments': form.departments.data,
                'mapping_options': mapping_options,  # 映射选择
                'top_ranking_dates': top_ranking_dates,  # TOP排名时间范围
                'category_top_count': form.category_top_count.data,  # 品类TOP数
                'item_top_count': form.item_top_count.data,  # 品项TOP数
                'description': form.description.data
            }
        else:
            # 结果盘：多个季度表 + 分析季度
            parameters = {
                'quarter_tables': form.quarter_tables.data,  # 用户选择的所有季度表
                'quarters': form.quarters.data,  # 要分析的所有季度
                'departments': form.departments.data,
                'mapping_options': mapping_options,  # 映射选择
                'top_ranking_dates': top_ranking_dates,  # TOP排名时间范围
                'category_top_count': form.category_top_count.data,  # 品类TOP数
                'item_top_count': form.item_top_count.data,  # 品项TOP数
                'description': form.description.data
            }
        task.set_parameters(parameters)
        
        db.session.add(task)
        db.session.commit()
        
        # 启动后台处理任务
        try:
            thread = threading.Thread(target=process_data_background, args=(task.id,))
            thread.daemon = True
            thread.start()
            current_app.logger.info(f"后台任务线程已启动，任务ID: {task.id}")
        except Exception as e:
            current_app.logger.error(f"启动后台任务失败: {str(e)}")
            task.fail_task(f"启动后台任务失败: {str(e)}")
        
        flash('数据处理任务已创建并开始执行', 'success')
        return redirect(url_for('data_processing.task_detail', task_id=task.id))
    
    return render_template('data_processing/new_task.html', 
                         title='新建数据处理任务', 
                         form=form,
                         active_config=active_config)

@bp.route('/task_history')
@login_required
def task_history():
    """任务历史列表"""
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    tasks = DataProcessingTask.query.filter_by(
        created_by=current_user.id
    ).order_by(DataProcessingTask.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('data_processing/task_history.html', 
                         title='任务历史', 
                         tasks=tasks)

@bp.route('/task/<int:task_id>')
@login_required
def task_detail(task_id):
    """任务详情"""
    task = DataProcessingTask.query.get_or_404(task_id)
    
    # 检查权限
    if task.created_by != current_user.id:
        flash('您没有权限查看此任务', 'error')
        return redirect(url_for('data_processing.task_history'))
    
    return render_template('data_processing/task_detail.html', 
                         title=f'任务详情 - {task.task_name}', 
                         task=task)

@bp.route('/api/task_status/<int:task_id>')
@login_required
def task_status(task_id):
    """API: 获取任务状态"""
    task = DataProcessingTask.query.get_or_404(task_id)
    
    # 检查权限
    if task.created_by != current_user.id:
        return jsonify({'error': '权限不足'}), 403
    
    return jsonify({
        'id': task.id,
        'status': task.status,
        'progress': task.progress,
        'error_message': task.error_message,
        'result_summary': task.get_result_summary(),
        'progress_logs': task.get_progress_logs(),
        'duration': str(task.duration) if task.duration else None
    })

@bp.route('/download/<int:task_id>')
@login_required
def download_result(task_id):
    """下载处理结果"""
    task = DataProcessingTask.query.get_or_404(task_id)
    
    # 检查权限
    if task.created_by != current_user.id:
        flash('您没有权限下载此文件', 'error')
        return redirect(url_for('data_processing.task_history'))
    
    # 检查任务状态和文件
    if task.status != 'completed' or not task.output_file_path:
        flash('任务未完成或文件不存在', 'error')
        return redirect(url_for('data_processing.task_detail', task_id=task_id))
    
    if not os.path.exists(task.output_file_path):
        flash('文件不存在', 'error')
        return redirect(url_for('data_processing.task_detail', task_id=task_id))
    
    return send_file(
        task.output_file_path,
        as_attachment=True,
        download_name=f"{task.task_name}_{task.created_at.strftime('%Y%m%d_%H%M%S')}.xlsx"
    )

def process_data_background(task_id):
    """后台数据处理函数"""
    from app import create_app
    app = create_app()

    with app.app_context():
        task = DataProcessingTask.query.get(task_id)
        if not task:
            app.logger.error(f"任务 {task_id} 不存在")
            return

        app.logger.info(f"开始执行后台任务 {task_id}")

        try:
            # 开始任务
            task.start_task()
            app.logger.info(f"任务 {task_id} 已标记为开始")

            # 获取数据库配置
            active_config = DatabaseConfig.get_active_config()
            if not active_config:
                raise Exception("没有激活的数据库配置")

            app.logger.info(f"任务 {task_id} 使用数据库配置: {active_config.name}")

            # 创建处理器
            logger = logging.getLogger(f'task_{task_id}')
            processor = RFMDataProcessor(active_config, logger)

            # 获取任务参数
            params = task.get_parameters()
            app.logger.info(f"任务 {task_id} 参数: {params}")

            # 更新进度 - 开始处理
            task.update_progress(10, step_name="初始化处理器", message="数据处理器初始化完成")
            app.logger.info(f"任务 {task_id} 进度更新: 10%")

            # 执行数据处理
            result_summary = processor.process_data(
                task_type=task.task_type,
                quarter_tables=params['quarter_tables'],
                quarters=params['quarters'],
                departments=params.get('departments', ['皮肤', '注射']),
                mapping_options=params.get('mapping_options', {}),  # 传递映射选项
                top_ranking_dates=params.get('top_ranking_dates', {}),  # 传递TOP排名时间范围
                category_top_count=params.get('category_top_count', 5),  # 传递品类TOP数
                item_top_count=params.get('item_top_count', 15),  # 传递品项TOP数
                task=task  # 传递任务对象以便更新进度
            )

            # 更新进度 - 数据处理完成
            task.update_progress(80, step_name="数据处理完成", message="RFM分析计算完成")
            
            # 更新进度 - 开始导出
            task.update_progress(90, step_name="生成Excel报表", message="正在生成Excel报表文件...")

            # 导出结果
            upload_folder = app.config.get('UPLOAD_FOLDER', 'uploads')
            output_dir = os.path.join(upload_folder, 'results')
            os.makedirs(output_dir, exist_ok=True)

            output_filename = f"rfm_result_{task.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            output_path = os.path.join(output_dir, output_filename)

            app.logger.info(f"任务 {task_id} 开始导出Excel到: {output_path}")

            if processor.export_to_excel(output_path):
                # 完成任务
                task.update_progress(100, step_name="任务完成", message="数据处理和报表生成完成")
                task.complete_task(output_path, result_summary)
                app.logger.info(f"任务 {task_id} 执行成功")
            else:
                raise Exception("导出Excel文件失败")
                
        except Exception as e:
            # 任务失败
            error_msg = str(e)
            task.fail_task(error_msg)
            app.logger.error(f"任务 {task_id} 处理失败: {error_msg}")

            # 记录详细错误信息
            import traceback
            app.logger.error(f"任务 {task_id} 错误详情: {traceback.format_exc()}")

@bp.route('/delete_task/<int:task_id>', methods=['POST'])
@login_required
def delete_task(task_id):
    """删除任务"""
    task = DataProcessingTask.query.get_or_404(task_id)
    
    # 检查权限
    if task.created_by != current_user.id:
        flash('您没有权限删除此任务', 'error')
        return redirect(url_for('data_processing.task_history'))
    
    # 删除输出文件
    if task.output_file_path and os.path.exists(task.output_file_path):
        try:
            os.remove(task.output_file_path)
        except Exception as e:
            current_app.logger.warning(f"删除文件失败: {str(e)}")
    
    # 删除任务记录
    db.session.delete(task)
    db.session.commit()
    
    flash('任务已删除', 'success')
    return redirect(url_for('data_processing.task_history'))

@bp.route('/api/start_task/<int:task_id>', methods=['POST'])
@login_required
def start_task(task_id):
    """API: 手动启动任务"""
    task = DataProcessingTask.query.get_or_404(task_id)

    # 检查权限
    if task.created_by != current_user.id:
        return jsonify({'success': False, 'message': '权限不足'}), 403

    # 检查任务状态
    if task.status != 'pending':
        return jsonify({'success': False, 'message': f'任务状态为 {task.status}，无法启动'}), 400

    try:
        # 启动后台处理任务
        thread = threading.Thread(target=process_data_background, args=(task.id,))
        thread.daemon = True
        thread.start()

        current_app.logger.info(f"手动启动后台任务，任务ID: {task.id}")
        return jsonify({'success': True, 'message': '任务启动成功'})

    except Exception as e:
        current_app.logger.error(f"手动启动任务失败: {str(e)}")
        return jsonify({'success': False, 'message': f'启动失败: {str(e)}'}), 500

@bp.route('/api/stop_task/<int:task_id>', methods=['POST'])
@login_required
def stop_task(task_id):
    """API: 停止任务"""
    task = DataProcessingTask.query.get_or_404(task_id)

    # 检查权限
    if task.created_by != current_user.id:
        return jsonify({'success': False, 'message': '权限不足'}), 403

    # 检查任务状态
    if task.status != 'running':
        return jsonify({'success': False, 'message': f'任务状态为 {task.status}，无法停止'}), 400

    try:
        # 标记任务为失败（强制停止）
        task.fail_task("用户手动停止任务")

        current_app.logger.info(f"用户手动停止任务，任务ID: {task.id}")
        return jsonify({'success': True, 'message': '任务已停止'})

    except Exception as e:
        current_app.logger.error(f"停止任务失败: {str(e)}")
        return jsonify({'success': False, 'message': f'停止失败: {str(e)}'}), 500

@bp.route('/api/mapping_preview', methods=['GET'])
@login_required
def mapping_preview():
    """API: 获取映射预览"""
    try:
        from app.models import MappingTemplate, CategoryMapping

        # 获取当前激活的模板
        active_template = MappingTemplate.get_active_template()

        if not active_template:
            return jsonify({
                'success': False,
                'message': '当前没有激活的参照表'
            })

        # 获取各级映射数据
        level1_mappings = CategoryMapping.get_mappings_by_type('level1', active_template.template_name)
        level2_mappings = CategoryMapping.get_mappings_by_type('level2', active_template.template_name)
        level3_mappings = CategoryMapping.get_mappings_by_type('level3', active_template.template_name)
        field_mappings = CategoryMapping.get_mappings_by_type('field_mapping', active_template.template_name)
        field_group_mappings = CategoryMapping.get_mappings_by_type('field_group', active_template.template_name)

        return jsonify({
            'success': True,
            'template_name': active_template.template_name,
            'mappings': {
                'level1': level1_mappings,
                'level2': level2_mappings,
                'level3': level3_mappings,
                'field_mapping': field_mappings,
                'field_group': field_group_mappings
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取映射预览失败: {str(e)}'
        }), 500
