#!/usr/bin/env python3
"""
系统诊断脚本
"""

import sys
import os

def check_python():
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")

def check_imports():
    print("\n检查导入...")
    
    modules = [
        'flask',
        'flask_sqlalchemy', 
        'flask_login',
        'pandas',
        'numpy',
        'pymysql',
        'openpyxl',
        'bcrypt',
        'cryptography'
    ]
    
    for module in modules:
        try:
            __import__(module)
            print(f"✓ {module}")
        except ImportError as e:
            print(f"✗ {module}: {e}")

def check_files():
    print("\n检查文件...")
    
    files = [
        '.env',
        'config.py',
        'app/__init__.py',
        'app/models.py'
    ]
    
    for file in files:
        if os.path.exists(file):
            print(f"✓ {file}")
        else:
            print(f"✗ {file}")

def check_database():
    print("\n检查数据库连接...")
    
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        import pymysql
        
        host = os.getenv('SYSTEM_DB_HOST', 'localhost')
        port = int(os.getenv('SYSTEM_DB_PORT', 3306))
        user = os.getenv('SYSTEM_DB_USER', 'root')
        password = os.getenv('SYSTEM_DB_PASSWORD', '')
        
        print(f"连接参数: {user}@{host}:{port}")
        
        connection = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            cursor.execute("SHOW DATABASES LIKE 'rfm_office'")
            result = cursor.fetchone()
            
        connection.close()
        
        if result:
            print("✓ 数据库 rfm_office 存在")
        else:
            print("✗ 数据库 rfm_office 不存在")
            
    except Exception as e:
        print(f"✗ 数据库连接失败: {e}")

def check_app():
    print("\n检查应用创建...")
    
    try:
        from app import create_app
        app = create_app()
        print("✓ 应用创建成功")
        
        with app.app_context():
            from app.models import User
            user_count = User.query.count()
            print(f"✓ 用户数量: {user_count}")
            
    except Exception as e:
        print(f"✗ 应用创建失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("RFM系统诊断")
    print("=" * 40)
    
    check_python()
    check_imports()
    check_files()
    check_database()
    check_app()
    
    print("\n诊断完成")

if __name__ == '__main__':
    main()
