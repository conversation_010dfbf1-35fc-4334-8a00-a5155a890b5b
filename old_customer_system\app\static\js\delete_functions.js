// 客户管理功能JavaScript文件
console.log('客户管理功能JavaScript文件已加载');

// 导出客户数据功能
function exportCustomers() {
    console.log('exportCustomers called');

    if (confirm('确定要导出当前筛选条件下的客户数据吗？')) {
        try {
            // 获取当前的搜索参数
            const params = new URLSearchParams(window.location.search);

            // 构建导出URL - 使用相对路径避免模板问题
            const exportUrl = '/registration/customers/export?' + params.toString();

            console.log('导出URL:', exportUrl);

            // 创建隐藏的下载链接
            const link = document.createElement('a');
            link.href = exportUrl;
            link.download = '客户数据_' + new Date().toISOString().slice(0, 10) + '.xlsx';
            link.style.display = 'none';

            document.body.appendChild(link);
            console.log('点击下载链接');
            link.click();

            // 延迟移除链接
            setTimeout(() => {
                document.body.removeChild(link);
                console.log('下载链接已移除');
            }, 100);

        } catch (error) {
            console.error('导出过程中出现错误:', error);
            alert('导出失败，请稍后重试');
        }
    }
}

// 全选/取消全选功能
function toggleSelectAll() {
    console.log('toggleSelectAll called');
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.customer-checkbox');

    console.log('selectAll element:', selectAll);
    console.log('checkboxes found:', checkboxes.length);

    if (selectAll && checkboxes.length > 0) {
        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll.checked;
        });
        updateBatchDeleteButton();
    }
}

// 更新批量删除按钮显示状态
function updateBatchDeleteButton() {
    console.log('updateBatchDeleteButton called');
    const checkboxes = document.querySelectorAll('.customer-checkbox:checked');
    const batchDeleteBtn = document.getElementById('batchDeleteBtn');

    console.log('checked checkboxes:', checkboxes.length);
    console.log('batchDeleteBtn element:', batchDeleteBtn);

    if (batchDeleteBtn) {
        if (checkboxes.length > 0) {
            batchDeleteBtn.style.display = 'inline-block';
            console.log('showing batch delete button');
        } else {
            batchDeleteBtn.style.display = 'none';
            console.log('hiding batch delete button');
        }
    }
}

// 单个客户删除功能
function deleteCustomer(customerId, cardNumber) {
    console.log('deleteCustomer called:', customerId, cardNumber);
    
    if (!customerId || !cardNumber) {
        console.error('deleteCustomer: 缺少必要参数');
        return;
    }
    
    if (confirm(`确定要删除客户 ${cardNumber} 吗？此操作不可恢复！`)) {
        // 创建表单并提交
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/registration/delete_customer/${customerId}`;

        // 添加CSRF token
        const csrfToken = document.querySelector('meta[name=csrf-token]');
        console.log('CSRF token found:', csrfToken);
        
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        } else {
            console.warn('未找到CSRF token');
        }

        document.body.appendChild(form);
        console.log('submitting form for customer:', customerId);
        form.submit();
    }
}

// 批量删除功能
function batchDeleteCustomers() {
    console.log('batchDeleteCustomers called');
    const checkboxes = document.querySelectorAll('.customer-checkbox:checked');

    if (checkboxes.length === 0) {
        alert('请选择要删除的客户');
        return;
    }

    const cardNumbers = [];
    checkboxes.forEach(checkbox => {
        const row = checkbox.closest('tr');
        // 查找包含卡号的strong标签
        const cardNumberElement = row.querySelector('strong');
        if (cardNumberElement) {
            cardNumbers.push(cardNumberElement.textContent);
        }
    });

    console.log('selected cards:', cardNumbers);

    if (confirm(`确定要删除以下 ${checkboxes.length} 个客户吗？\n${cardNumbers.join(', ')}\n\n此操作不可恢复！`)) {
        const form = document.getElementById('batchDeleteForm');
        console.log('batch delete form:', form);
        
        if (form) {
            // 清空现有的客户ID输入
            const existingInputs = form.querySelectorAll('input[name="customer_ids"]');
            existingInputs.forEach(input => input.remove());
            
            // 添加选中的客户ID
            checkboxes.forEach(checkbox => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'customer_ids';
                input.value = checkbox.value;
                form.appendChild(input);
            });
            
            form.submit();
        } else {
            console.error('未找到批量删除表单');
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('删除功能初始化...');
    
    // 为客户复选框添加事件监听器
    const customerCheckboxes = document.querySelectorAll('.customer-checkbox');
    customerCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBatchDeleteButton);
    });
    
    // 初始化批量删除按钮状态
    updateBatchDeleteButton();
    
    console.log('删除功能初始化完成');
});
