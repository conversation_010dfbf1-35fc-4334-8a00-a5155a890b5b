{% extends "base.html" %}

{% block title %}部门管理 - 老客回访与跟进系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-building"></i> 部门管理</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('admin.index') }}">系统管理</a></li>
                <li class="breadcrumb-item active">部门管理</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-list"></i> 部门列表</h5>
                <a href="{{ url_for('admin.create_department') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> 新增部门
                </a>
            </div>
            <div class="card-body">
                {% if departments.items %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>部门名称</th>
                                <th>部门代码</th>
                                <th>上级部门</th>
                                <th>描述</th>
                                <th>排序</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for department in departments.items %}
                            <tr>
                                <td>
                                    <strong>{{ department.department_name }}</strong>
                                    {% if department.children %}
                                    <small class="text-muted">({{ department.children|length }} 个子部门)</small>
                                    {% endif %}
                                </td>
                                <td><code>{{ department.department_code }}</code></td>
                                <td>
                                    {% if department.parent %}
                                    <span class="text-muted">{{ department.parent.department_name }}</span>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if department.description %}
                                    <span class="text-truncate" style="max-width: 200px; display: inline-block;" 
                                          title="{{ department.description }}">{{ department.description }}</span>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>{{ department.sort_order }}</td>
                                <td>
                                    {% if department.is_active %}
                                    <span class="badge bg-success">启用</span>
                                    {% else %}
                                    <span class="badge bg-secondary">停用</span>
                                    {% endif %}
                                </td>
                                <td>{{ department.created_time.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('admin.edit_department', dept_id=department.id) }}" 
                                           class="btn btn-outline-primary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        {% if department.users|length == 0 and department.children|length == 0 %}
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="confirmDelete({{ department.id }}, '{{ department.department_name }}')" 
                                                title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        {% else %}
                                        <button type="button" class="btn btn-outline-secondary" disabled title="该部门下有用户或子部门，无法删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                {% if departments.pages > 1 %}
                <nav aria-label="部门列表分页">
                    <ul class="pagination justify-content-center">
                        {% if departments.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.departments', page=departments.prev_num) }}">上一页</a>
                        </li>
                        {% endif %}

                        {% for page_num in departments.iter_pages() %}
                        {% if page_num %}
                        {% if page_num != departments.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.departments', page=page_num) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                        {% endfor %}

                        {% if departments.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.departments', page=departments.next_num) }}">下一页</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-building fa-3x text-muted mb-3"></i>
                    <p class="text-muted">暂无部门数据</p>
                    <a href="{{ url_for('admin.create_department') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> 创建第一个部门
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除部门 <strong id="deleteDeptName"></strong> 吗？</p>
                <p class="text-danger small">此操作不可撤销！</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete(deptId, deptName) {
    document.getElementById('deleteDeptName').textContent = deptName;
    document.getElementById('deleteForm').action = '/admin/departments/' + deptId + '/delete';
    
    var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
{% endblock %}