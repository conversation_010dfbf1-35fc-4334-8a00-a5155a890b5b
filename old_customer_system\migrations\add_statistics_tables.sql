-- 添加统计相关表的SQL脚本

-- 创建到院明细表
CREATE TABLE IF NOT EXISTS visit_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    member_card_number VARCHAR(18) NOT NULL,
    visit_date DATE NOT NULL,
    visit_time TIME NULL,
    visit_type VARCHAR(50) NULL COMMENT '到院类型',
    department VARCHAR(100) NULL COMMENT '科室',
    doctor <PERSON><PERSON><PERSON><PERSON>(100) NULL COMMENT '医生',
    remarks TEXT NULL COMMENT '备注',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by INT NOT NULL,
    INDEX idx_member_card_number (member_card_number),
    INDEX idx_visit_date (visit_date),
    FOREIGN KEY (created_by) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='到院明细表';

-- 创建消费明细表
CREATE TABLE IF NOT EXISTS consumption_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    member_card_number VARCHAR(18) NOT NULL,
    consumption_date DATE NOT NULL,
    consumption_time TIME NULL,
    item_name VARCHAR(200) NULL COMMENT '消费项目',
    item_category VARCHAR(100) NULL COMMENT '项目分类',
    amount DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '消费金额',
    quantity INT NOT NULL DEFAULT 1 COMMENT '数量',
    unit_price DECIMAL(10,2) NULL COMMENT '单价',
    department VARCHAR(100) NULL COMMENT '科室',
    doctor VARCHAR(100) NULL COMMENT '医生',
    payment_method VARCHAR(50) NULL COMMENT '支付方式',
    remarks TEXT NULL COMMENT '备注',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by INT NOT NULL,
    INDEX idx_member_card_number (member_card_number),
    INDEX idx_consumption_date (consumption_date),
    INDEX idx_amount (amount),
    FOREIGN KEY (created_by) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消费明细表';

-- 添加权限数据
INSERT IGNORE INTO permissions (permission_code, permission_name, description, category) VALUES
('upload_data', '数据上传', '可以上传到院明细和消费明细数据', 'data_management'),
('view_all_statistics', '查看所有统计', '可以查看所有统计报表数据', 'statistics'),
('view_department_statistics', '查看部门统计', '可以查看本部门统计报表数据', 'statistics'),
('view_own_statistics', '查看个人统计', '可以查看个人相关统计报表数据', 'statistics'),
('export_statistics', '导出统计报表', '可以导出统计报表到Excel', 'statistics');

-- 为管理员角色添加新权限
INSERT IGNORE INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.role_code = 'ADMIN' 
AND p.permission_code IN ('upload_data', 'view_all_statistics', 'export_statistics');

-- 为主管角色添加部门统计权限
INSERT IGNORE INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.role_code = 'MANAGER' 
AND p.permission_code IN ('view_department_statistics', 'export_statistics');

-- 为现场顾问添加个人统计权限
INSERT IGNORE INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.role_code = 'ONSITE_CONSULTANT' 
AND p.permission_code = 'view_own_statistics';

-- 为网络咨询员添加个人统计权限
INSERT IGNORE INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.role_code = 'ONLINE_CONSULTANT' 
AND p.permission_code = 'view_own_statistics';
