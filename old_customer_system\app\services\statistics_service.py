# -*- coding: utf-8 -*-
"""
统计分析服务
"""

from datetime import datetime, date
from typing import List, Dict, Optional
from sqlalchemy import and_, or_, func
from app import db
from app.models import Customer, VisitRecord, ConsumptionRecord, User, Channel


class StatisticsService:
    """统计分析服务"""
    
    @staticmethod
    def get_follow_up_statistics(
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        onsite_consultant_id: Optional[int] = None,
        online_consultant_id: Optional[int] = None,
        channel_id: Optional[int] = None
    ) -> Dict:
        """获取跟进统计数据"""
        
        # 构建客户查询条件
        customer_query = Customer.query
        
        if start_date:
            customer_query = customer_query.filter(Customer.registration_date >= start_date)
        if end_date:
            customer_query = customer_query.filter(Customer.registration_date <= end_date)
        if onsite_consultant_id and onsite_consultant_id > 0:
            customer_query = customer_query.filter(Customer.onsite_consultant_id == onsite_consultant_id)
        if online_consultant_id and online_consultant_id > 0:
            customer_query = customer_query.filter(Customer.online_consultant_id == online_consultant_id)
        if channel_id and channel_id > 0:
            customer_query = customer_query.filter(Customer.channel_id == channel_id)
        
        customers = customer_query.all()
        
        # 统计数据
        total_customers = len(customers)
        visit_success_count = 0
        consumption_success_count = 0
        total_consumption_amount = 0
        
        # 详细数据
        customer_details = []
        
        for customer in customers:
            # 查找登记时间之后的到院记录
            visit_records = VisitRecord.query.filter(
                and_(
                    VisitRecord.member_card_number == customer.card_number,
                    VisitRecord.visit_date > customer.registration_date
                )
            ).all()
            
            # 查找登记时间之后的消费记录
            consumption_records = ConsumptionRecord.query.filter(
                and_(
                    ConsumptionRecord.member_card_number == customer.card_number,
                    ConsumptionRecord.consumption_date > customer.registration_date
                )
            ).all()
            
            # 计算该客户的统计数据
            has_visit = len(visit_records) > 0
            has_consumption = len(consumption_records) > 0
            customer_consumption_amount = sum(record.amount for record in consumption_records)
            
            if has_visit:
                visit_success_count += 1
            if has_consumption:
                consumption_success_count += 1
                total_consumption_amount += customer_consumption_amount
            
            # 添加客户详细信息
            customer_details.append({
                'customer': customer,
                'visit_records': visit_records,
                'consumption_records': consumption_records,
                'has_visit': has_visit,
                'has_consumption': has_consumption,
                'consumption_amount': customer_consumption_amount,
                'visit_count': len(visit_records),
                'consumption_count': len(consumption_records)
            })
        
        return {
            'total_customers': total_customers,
            'visit_success_count': visit_success_count,
            'consumption_success_count': consumption_success_count,
            'total_consumption_amount': float(total_consumption_amount),
            'visit_success_rate': (visit_success_count / total_customers * 100) if total_customers > 0 else 0,
            'consumption_success_rate': (consumption_success_count / total_customers * 100) if total_customers > 0 else 0,
            'average_consumption': (float(total_consumption_amount) / consumption_success_count) if consumption_success_count > 0 else 0,
            'customer_details': customer_details
        }
    
    @staticmethod
    def get_consultant_statistics(
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> Dict:
        """获取顾问统计数据"""
        
        # 获取所有现场顾问
        onsite_consultants = User.query.filter_by(role='onsite_consultant', is_active=True).all()
        
        consultant_stats = []
        
        for consultant in onsite_consultants:
            stats = StatisticsService.get_follow_up_statistics(
                start_date=start_date,
                end_date=end_date,
                onsite_consultant_id=consultant.id
            )
            
            consultant_stats.append({
                'consultant': consultant,
                'stats': stats
            })
        
        return {
            'consultant_stats': consultant_stats,
            'total_stats': StatisticsService.get_follow_up_statistics(
                start_date=start_date,
                end_date=end_date
            )
        }
    
    @staticmethod
    def get_onsite_consultant_statistics(
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> Dict:
        """获取现场咨询师统计数据"""

        # 获取所有现场咨询师（有客户的）
        onsite_consultants = db.session.query(User).join(
            Customer, User.id == Customer.onsite_consultant_id
        ).filter(
            User.role.in_(['onsite_consultant', 'admin', 'director', 'manager'])
        ).distinct().all()

        consultant_stats = []

        for consultant in onsite_consultants:
            # 构建客户查询条件
            customer_query = Customer.query.filter(
                Customer.onsite_consultant_id == consultant.id
            )

            # 应用日期筛选
            if start_date:
                customer_query = customer_query.filter(Customer.registration_date >= start_date)
            if end_date:
                customer_query = customer_query.filter(Customer.registration_date <= end_date)

            customers = customer_query.all()

            # 统计指标
            total_customers = len(customers)  # 客户量
            followed_customers = 0  # 跟进数量
            visit_success_count = 0  # 跟进到院量
            total_consumption_amount = 0.0  # 跟进消费金额

            for customer in customers:
                # 判断是否已跟进
                if customer.follow_up_time and customer.follow_up_note and customer.follow_up_note.strip():
                    followed_customers += 1

                    # 获取跟进到院状态
                    visit_status = customer.get_follow_up_visit_status()
                    if visit_status == "已到院":
                        visit_success_count += 1

                    # 获取跟进消费金额
                    consumption_amount = customer.get_follow_up_consumption_amount()
                    if consumption_amount:
                        total_consumption_amount += consumption_amount

            consultant_stats.append({
                'consultant': consultant,
                'total_customers': total_customers,
                'followed_customers': followed_customers,
                'visit_success_count': visit_success_count,
                'total_consumption_amount': total_consumption_amount,
                'follow_up_rate': round((followed_customers / total_customers * 100) if total_customers > 0 else 0, 2),
                'visit_success_rate': round((visit_success_count / followed_customers * 100) if followed_customers > 0 else 0, 2),
                'avg_consumption': round((total_consumption_amount / visit_success_count) if visit_success_count > 0 else 0, 2)
            })

        # 按客户量排序
        consultant_stats.sort(key=lambda x: x['total_customers'], reverse=True)

        # 计算总计
        total_stats = {
            'total_customers': sum(stat['total_customers'] for stat in consultant_stats),
            'followed_customers': sum(stat['followed_customers'] for stat in consultant_stats),
            'visit_success_count': sum(stat['visit_success_count'] for stat in consultant_stats),
            'total_consumption_amount': sum(stat['total_consumption_amount'] for stat in consultant_stats)
        }

        total_stats['follow_up_rate'] = round((total_stats['followed_customers'] / total_stats['total_customers'] * 100) if total_stats['total_customers'] > 0 else 0, 2)
        total_stats['visit_success_rate'] = round((total_stats['visit_success_count'] / total_stats['followed_customers'] * 100) if total_stats['followed_customers'] > 0 else 0, 2)
        total_stats['avg_consumption'] = round((total_stats['total_consumption_amount'] / total_stats['visit_success_count']) if total_stats['visit_success_count'] > 0 else 0, 2)

        return {
            'consultant_stats': consultant_stats,
            'total_stats': total_stats
        }

    @staticmethod
    def get_channel_statistics(
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> Dict:
        """获取渠道统计数据"""

        # 获取所有激活渠道
        channels = Channel.query.filter_by(is_active=True).all()

        channel_stats = []

        for channel in channels:
            stats = StatisticsService.get_follow_up_statistics(
                start_date=start_date,
                end_date=end_date,
                channel_id=channel.id
            )

            channel_stats.append({
                'channel': channel,
                'stats': stats
            })

        return {
            'channel_stats': channel_stats,
            'total_stats': StatisticsService.get_follow_up_statistics(
                start_date=start_date,
                end_date=end_date
            )
        }
    
    @staticmethod
    def get_daily_statistics(
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> Dict:
        """获取每日统计数据"""
        
        if not start_date:
            start_date = date.today().replace(day=1)  # 本月第一天
        if not end_date:
            end_date = date.today()
        
        daily_stats = []
        current_date = start_date
        
        while current_date <= end_date:
            stats = StatisticsService.get_follow_up_statistics(
                start_date=current_date,
                end_date=current_date
            )
            
            daily_stats.append({
                'date': current_date,
                'stats': stats
            })
            
            # 下一天
            from datetime import timedelta
            current_date += timedelta(days=1)
        
        return {
            'daily_stats': daily_stats,
            'period_stats': StatisticsService.get_follow_up_statistics(
                start_date=start_date,
                end_date=end_date
            )
        }

    @staticmethod
    def export_onsite_consultant_statistics_to_excel(consultant_data: Dict) -> str:
        """导出现场咨询师统计数据到Excel"""
        import pandas as pd
        import tempfile
        from datetime import datetime

        # 创建临时文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=f'_consultant_stats_{timestamp}.xlsx')

        # 准备导出数据
        export_data = []

        # 添加各咨询师数据
        for stat in consultant_data['consultant_stats']:
            export_data.append({
                '现场咨询师': stat['consultant'].real_name,
                '客户量': stat['total_customers'],
                '跟进数量': stat['followed_customers'],
                '跟进到院量': stat['visit_success_count'],
                '跟进消费金额': stat['total_consumption_amount'],
                '跟进率(%)': stat['follow_up_rate'],
                '到院成功率(%)': stat['visit_success_rate'],
                '平均消费金额': stat['avg_consumption']
            })

        # 添加总计行
        total_stats = consultant_data['total_stats']
        export_data.append({
            '现场咨询师': '总计',
            '客户量': total_stats['total_customers'],
            '跟进数量': total_stats['followed_customers'],
            '跟进到院量': total_stats['visit_success_count'],
            '跟进消费金额': total_stats['total_consumption_amount'],
            '跟进率(%)': total_stats['follow_up_rate'],
            '到院成功率(%)': total_stats['visit_success_rate'],
            '平均消费金额': total_stats['avg_consumption']
        })

        # 创建DataFrame并导出
        df = pd.DataFrame(export_data)

        # 使用ExcelWriter进行格式化
        with pd.ExcelWriter(temp_file.name, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='现场咨询师统计', index=False)

            # 获取工作表
            worksheet = writer.sheets['现场咨询师统计']

            # 设置列宽
            column_widths = {
                'A': 15,  # 现场咨询师
                'B': 10,  # 客户量
                'C': 10,  # 跟进数量
                'D': 12,  # 跟进到院量
                'E': 15,  # 跟进消费金额
                'F': 12,  # 跟进率
                'G': 15,  # 到院成功率
                'H': 15   # 平均消费金额
            }

            for col, width in column_widths.items():
                worksheet.column_dimensions[col].width = width

            # 设置总计行样式（加粗）
            from openpyxl.styles import Font
            total_row = len(df)
            for col in range(1, len(df.columns) + 1):
                cell = worksheet.cell(row=total_row + 1, column=col)
                cell.font = Font(bold=True)

        return temp_file.name

    @staticmethod
    def export_statistics_to_excel(statistics_data: Dict) -> str:
        """导出统计数据到Excel"""
        import pandas as pd
        import tempfile
        from datetime import datetime
        
        # 创建临时文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=f'_statistics_{timestamp}.xlsx')
        
        with pd.ExcelWriter(temp_file.name, engine='openpyxl') as writer:
            # 总体统计
            summary_data = {
                '统计项目': ['总登记客户数', '跟进到院成功数', '跟进消费成功数', '总消费金额', '到院成功率(%)', '消费成功率(%)', '平均消费金额'],
                '数值': [
                    statistics_data['total_customers'],
                    statistics_data['visit_success_count'],
                    statistics_data['consumption_success_count'],
                    statistics_data['total_consumption_amount'],
                    round(statistics_data['visit_success_rate'], 2),
                    round(statistics_data['consumption_success_rate'], 2),
                    round(statistics_data['average_consumption'], 2)
                ]
            }
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='总体统计', index=False)
            
            # 客户详细数据
            if statistics_data.get('customer_details'):
                detail_data = []
                for detail in statistics_data['customer_details']:
                    customer = detail['customer']
                    detail_data.append({
                        '客户卡号': customer.card_number,
                        '登记日期': customer.registration_date,
                        '现场顾问': customer.onsite_consultant.real_name if customer.onsite_consultant else '',
                        '网络咨询员': customer.online_consultant.real_name if customer.online_consultant else '',
                        '激活渠道': customer.channel.name if customer.channel else '',
                        '是否到院': '是' if detail['has_visit'] else '否',
                        '到院次数': detail['visit_count'],
                        '是否消费': '是' if detail['has_consumption'] else '否',
                        '消费次数': detail['consumption_count'],
                        '消费金额': detail['consumption_amount']
                    })
                
                detail_df = pd.DataFrame(detail_data)
                detail_df.to_excel(writer, sheet_name='客户详细数据', index=False)
        
        return temp_file.name
