<!DOCTYPE html>
<html>
<head>
    <title>简单JavaScript测试</title>
</head>
<body>
    <h1>JavaScript语法测试</h1>
    <div id="result"></div>
    
    <script>
        console.log('开始测试JavaScript...');
        
        function testAPI() {
            console.log('测试API调用...');
            
            fetch('/rfm/api/available-tasks', {
                method: 'GET',
                credentials: 'same-origin',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => {
                console.log('API响应状态:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('API响应数据:', data);
                document.getElementById('result').innerHTML = 
                    '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            })
            .catch(error => {
                console.error('API错误:', error);
                document.getElementById('result').innerHTML = 
                    '<div style="color: red;">错误: ' + error.message + '</div>';
            });
        }
        
        // 页面加载后自动测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始测试API...');
            testAPI();
        });
    </script>
</body>
</html>
