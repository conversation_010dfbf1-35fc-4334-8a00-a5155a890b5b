{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">部门管理</h1>
    <a href="{{ url_for('basic_mgmt.create_department') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>添加部门
    </a>
</div>

<!-- 搜索表单 -->
<div class="card mb-3">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                {{ search_form.query.label(class="form-label") }}
                {{ search_form.query(class="form-control", placeholder="部门名称") }}
            </div>
            <div class="col-md-3">
                {{ search_form.is_active.label(class="form-label") }}
                {{ search_form.is_active(class="form-select") }}
            </div>
            <div class="col-md-5 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-search me-1"></i>搜索
                </button>
                <a href="{{ url_for('basic_mgmt.departments') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-undo me-1"></i>重置
                </a>
            </div>
        </form>
    </div>
</div>

<!-- 部门列表 -->
<div class="card">
    <div class="card-body">
        {% if departments %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>部门名称</th>
                            <th>部门描述</th>
                            <th>用户数量</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for department in departments %}
                        <tr>
                            <td>
                                <strong>{{ department.name }}</strong>
                            </td>
                            <td>{{ department.description or '-' }}</td>
                            <td>
                                <span class="badge bg-info">{{ department.users|length }}</span>
                                {% if department.users %}
                                    <small class="text-muted">
                                        (激活: {{ department.users|selectattr('is_active')|list|length }})
                                    </small>
                                {% endif %}
                            </td>
                            <td>
                                {% if department.is_active %}
                                    <span class="badge bg-success">激活</span>
                                {% else %}
                                    <span class="badge bg-secondary">停用</span>
                                {% endif %}
                            </td>
                            <td>{{ department.created_at.strftime('%Y-%m-%d') }}</td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{{ url_for('basic_mgmt.edit_department', dept_id=department.id) }}" 
                                       class="btn btn-outline-primary" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-{{ 'secondary' if department.is_active else 'success' }}" 
                                            onclick="toggleDepartmentStatus({{ department.id }}, '{{ department.name }}', {{ department.is_active|lower }})" 
                                            title="{{ '停用' if department.is_active else '激活' }}">
                                        <i class="fas fa-{{ 'ban' if department.is_active else 'check' }}"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-building fa-3x text-muted mb-3"></i>
                <p class="text-muted">暂无部门数据</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function toggleDepartmentStatus(deptId, deptName, isActive) {
    const action = isActive ? '停用' : '激活';
    let confirmMessage = `确定要${action}部门 "${deptName}" 吗？`;
    
    if (isActive) {
        confirmMessage += '\n注意：停用部门可能会影响该部门下的用户。';
    }
    
    if (confirm(confirmMessage)) {
        fetch(`/basic/departments/${deptId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('操作失败: ' + data.message);
            }
        })
        .catch(error => {
            alert('操作失败: ' + error);
        });
    }
}
</script>
{% endblock %}