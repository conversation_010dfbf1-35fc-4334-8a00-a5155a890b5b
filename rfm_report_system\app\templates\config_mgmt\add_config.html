{% extends "base.html" %}

{% block content %}
<div class="row mt-4">
    <div class="col-12">
        <h2><i class="fas fa-plus me-2"></i>添加数据库配置</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">首页</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('config_mgmt.list_configs') }}">数据库配置</a></li>
                <li class="breadcrumb-item active">添加配置</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row mt-3">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form method="POST" id="configForm">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.name.label(class="form-label") }}
                                {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                                {% if form.name.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.host.label(class="form-label") }}
                                {{ form.host(class="form-control" + (" is-invalid" if form.host.errors else "")) }}
                                {% if form.host.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.host.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.port.label(class="form-label") }}
                                {{ form.port(class="form-control" + (" is-invalid" if form.port.errors else "")) }}
                                {% if form.port.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.port.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.username.label(class="form-label") }}
                                {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else "")) }}
                                {% if form.username.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.username.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.password.label(class="form-label") }}
                        {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else "")) }}
                        {% if form.password.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.password.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.database_name.label(class="form-label") }}
                        {{ form.database_name(class="form-control" + (" is-invalid" if form.database_name.errors else ""), disabled=True) }}
                        {% if form.database_name.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.database_name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">请先测试连接以加载可用的数据库列表</div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('config_mgmt.list_configs') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>返回
                        </a>
                        <div>
                            {{ form.test_connection(class="btn btn-info me-2", id="testBtn") }}
                            <button type="button" class="btn btn-primary" disabled id="saveBtn">
                                <i class="fas fa-save me-1"></i>保存配置
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-lightbulb me-2"></i>配置说明</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>配置名称用于标识不同的数据库连接</li>
                    <li><i class="fas fa-check text-success me-2"></i>数据库地址可以是IP地址或域名</li>
                    <li><i class="fas fa-check text-success me-2"></i>默认MySQL端口为3306</li>
                    <li><i class="fas fa-check text-success me-2"></i>用户需要有相应数据库的访问权限</li>
                    <li><i class="fas fa-check text-success me-2"></i>密码将被加密存储</li>
                </ul>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-shield-alt me-2"></i>安全提示</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <small>
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        请确保数据库用户具有适当的权限，建议创建专用的只读用户用于数据分析。
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    $('#testBtn').click(function(e) {
        e.preventDefault();
        
        var formData = {
            host: $('#host').val(),
            port: $('#port').val(),
            username: $('#username').val(),
            password: $('#password').val()
        };
        
        // 验证必填字段
        if (!formData.host || !formData.username || !formData.password) {
            alert('请填写完整的连接信息');
            return;
        }
        
        // 显示加载状态
        $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>测试中...');
        
        $.ajax({
            url: '{{ url_for("config_mgmt.test_connection") }}',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            success: function(response) {
                if (response.success) {
                    // 更新数据库选择列表
                    var select = $('#database_name');
                    select.empty().prop('disabled', false);
                    select.append('<option value="">请选择数据库</option>');
                    
                    response.databases.forEach(function(db) {
                        select.append('<option value="' + db + '">' + db + '</option>');
                    });
                    
                    // 启用保存按钮
                    $('#saveBtn').prop('disabled', false);
                    
                    // 显示成功消息
                    showAlert('success', response.message);
                } else {
                    showAlert('danger', response.message);
                }
            },
            error: function(xhr) {
                var response = JSON.parse(xhr.responseText);
                showAlert('danger', response.message);
            },
            complete: function() {
                $('#testBtn').prop('disabled', false).html('<i class="fas fa-plug me-1"></i>测试连接');
            }
        });
    });
    
    // 数据库选择变化时启用保存按钮
    $('#database_name').change(function() {
        if ($(this).val()) {
            $('#saveBtn').prop('disabled', false);
        } else {
            $('#saveBtn').prop('disabled', true);
        }
    });

    // 保存配置按钮点击事件
    $('#saveBtn').click(function(e) {
        e.preventDefault();

        var formData = {
            name: $('#name').val(),
            host: $('#host').val(),
            port: $('#port').val(),
            username: $('#username').val(),
            password: $('#password').val(),
            database_name: $('#database_name').val()
        };

        // 验证必填字段
        if (!formData.name || !formData.host || !formData.username || !formData.password || !formData.database_name) {
            alert('请填写完整的配置信息');
            return;
        }

        // 显示加载状态
        $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>保存中...');

        $.ajax({
            url: '{{ url_for("config_mgmt.save_config") }}',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            success: function(response) {
                if (response.success) {
                    showAlert('success', response.message);
                    setTimeout(function() {
                        window.location.href = '{{ url_for("config_mgmt.list_configs") }}';
                    }, 1500);
                } else {
                    showAlert('danger', response.message);
                    $('#saveBtn').prop('disabled', false).html('<i class="fas fa-save me-1"></i>保存配置');
                }
            },
            error: function(xhr) {
                var response = JSON.parse(xhr.responseText);
                showAlert('danger', response.message);
                $('#saveBtn').prop('disabled', false).html('<i class="fas fa-save me-1"></i>保存配置');
            }
        });
    });
    
    function showAlert(type, message) {
        var alertHtml = '<div class="alert alert-' + type + ' alert-dismissible fade show" role="alert">' +
                       message +
                       '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                       '</div>';
        
        // 移除现有的alert
        $('.alert').remove();
        
        // 在表单前插入新的alert
        $('.card-body').prepend(alertHtml);
    }
});
</script>
{% endblock %}
