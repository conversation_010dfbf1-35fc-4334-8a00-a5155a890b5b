{% extends "base.html" %}

{% block title %}新增渠道 - 老客回访与跟进系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-plus"></i> 新增渠道</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('channel.index') }}">渠道管理</a></li>
                <li class="breadcrumb-item active">新增渠道</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-tag"></i> 渠道信息</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="channel_category" class="form-label">渠道分类 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="channel_category" name="channel_category" 
                                       placeholder="请输入渠道分类" required maxlength="100">
                                <div class="form-text">如：线上推广、线下活动、老客推荐等</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="channel_name" class="form-label">渠道名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="channel_name" name="channel_name" 
                                       placeholder="请输入渠道名称" required maxlength="200">
                                <div class="form-text">如：百度推广、地推活动、老客户介绍等</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('channel.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 保存渠道
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-info-circle"></i> 填写说明</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li><i class="fas fa-check text-success"></i> 渠道分类用于对渠道进行归类管理</li>
                    <li><i class="fas fa-check text-success"></i> 渠道名称要具体明确，便于识别</li>
                    <li><i class="fas fa-check text-success"></i> 相同分类和名称的渠道不能重复创建</li>
                    <li><i class="fas fa-check text-success"></i> 创建后可以在列表中进行编辑和管理</li>
                </ul>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-lightbulb"></i> 常见渠道分类</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6><span class="badge bg-primary">线上推广</span></h6>
                    <p class="small text-muted">百度推广、微信推广、抖音推广、小红书推广等</p>
                </div>
                
                <div class="mb-3">
                    <h6><span class="badge bg-success">线下活动</span></h6>
                    <p class="small text-muted">地推活动、展会推广、社区活动、合作推广等</p>
                </div>
                
                <div class="mb-3">
                    <h6><span class="badge bg-warning">老客推荐</span></h6>
                    <p class="small text-muted">老客户介绍、员工推荐、朋友推荐等</p>
                </div>
                
                <div class="mb-3">
                    <h6><span class="badge bg-info">其他渠道</span></h6>
                    <p class="small text-muted">自然到店、电话咨询、官网咨询等</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 字符计数和验证
document.getElementById('channel_category').addEventListener('input', function() {
    const maxLength = 100;
    const currentLength = this.value.length;
    
    if (currentLength > maxLength) {
        this.value = this.value.substring(0, maxLength);
    }
});

document.getElementById('channel_name').addEventListener('input', function() {
    const maxLength = 200;
    const currentLength = this.value.length;
    
    if (currentLength > maxLength) {
        this.value = this.value.substring(0, maxLength);
    }
});
</script>
{% endblock %}
