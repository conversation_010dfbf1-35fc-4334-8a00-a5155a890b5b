# -*- coding: utf-8 -*-
"""
Repository层单元测试
"""

import unittest
from datetime import datetime, date
from app import create_app, db
from app.models import User, Department, Channel, Customer, SystemSetting
from app.repositories import (
    UserRepository, DepartmentRepository, ChannelRepository, 
    CustomerRepository, SystemSettingRepository
)

class RepositoryTestCase(unittest.TestCase):
    """Repository测试基类"""
    
    def setUp(self):
        """测试前准备"""
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        
        # 创建测试数据
        self.create_test_data()
    
    def tearDown(self):
        """测试后清理"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def create_test_data(self):
        """创建测试数据"""
        # 创建部门
        self.dept = Department(name='测试部门', description='测试用部门')
        db.session.add(self.dept)
        
        # 创建用户
        self.admin_user = User(
            username='admin',
            real_name='管理员',
            role='admin',
            is_active=True
        )
        self.admin_user.set_password('admin123')
        
        self.online_user = User(
            username='online1',
            real_name='网络咨询1',
            role='online_consultant',
            department_id=1,
            is_active=True
        )
        self.online_user.set_password('online123')
        
        self.onsite_user = User(
            username='onsite1',
            real_name='现场咨询1',
            role='onsite_consultant',
            department_id=1,
            is_active=True
        )
        self.onsite_user.set_password('onsite123')
        
        db.session.add_all([self.admin_user, self.online_user, self.onsite_user])
        
        # 创建渠道
        self.channel = Channel(
            name='测试渠道',
            category='线上',
            simple_code='CSCQ',
            is_active=True
        )
        db.session.add(self.channel)
        
        db.session.commit()

class TestUserRepository(RepositoryTestCase):
    """用户Repository测试"""
    
    def setUp(self):
        super().setUp()
        self.user_repo = UserRepository()
    
    def test_find_by_username(self):
        """测试根据用户名查找用户"""
        user = self.user_repo.find_by_username('admin')
        self.assertIsNotNone(user)
        self.assertEqual(user.username, 'admin')
        
        # 测试不存在的用户名
        user = self.user_repo.find_by_username('nonexistent')
        self.assertIsNone(user)
    
    def test_find_by_role(self):
        """测试根据角色查找用户"""
        admins = self.user_repo.find_by_role('admin')
        self.assertEqual(len(admins), 1)
        self.assertEqual(admins[0].role, 'admin')
        
        consultants = self.user_repo.find_by_role('online_consultant')
        self.assertEqual(len(consultants), 1)
    
    def test_search_by_simple_code(self):
        """测试简码搜索"""
        # 更新用户简码
        self.user_repo.update(self.online_user.id, {'simple_code': 'WLZX'})
        
        results = self.user_repo.search_by_simple_code('WLZX')
        self.assertEqual(len(results), 1)
        
        # 测试中文搜索
        results = self.user_repo.search_by_simple_code('网络')
        self.assertEqual(len(results), 1)
    
    def test_change_password(self):
        """测试修改密码"""
        result = self.user_repo.change_password(self.admin_user.id, 'newpassword')
        self.assertTrue(result)
        
        # 验证新密码
        user = self.user_repo.get_by_id(self.admin_user.id)
        self.assertTrue(user.check_password('newpassword'))

class TestCustomerRepository(RepositoryTestCase):
    """客户Repository测试"""
    
    def setUp(self):
        super().setUp()
        self.customer_repo = CustomerRepository()
        
        # 创建测试客户
        self.customer = Customer(
            card_number='1234567890',
            onsite_consultant_id=self.onsite_user.id,
            online_consultant_id=self.online_user.id,
            channel_id=self.channel.id,
            inquiry_content='测试咨询内容',
            created_by=self.online_user.id
        )
        db.session.add(self.customer)
        db.session.commit()
    
    def test_find_by_card_number(self):
        """测试根据卡号查找客户"""
        customer = self.customer_repo.find_by_card_number('1234567890')
        self.assertIsNotNone(customer)
        self.assertEqual(customer.card_number, '1234567890')
        
        # 测试不存在的卡号
        customer = self.customer_repo.find_by_card_number('0000000000')
        self.assertIsNone(customer)
    
    def test_find_by_online_consultant(self):
        """测试根据网络咨询人员查找客户"""
        customers = self.customer_repo.find_by_online_consultant(self.online_user.id)
        self.assertEqual(len(customers), 1)
        self.assertEqual(customers[0].online_consultant_id, self.online_user.id)
    
    def test_find_pending_follow_up(self):
        """测试查找待跟进客户"""
        pending = self.customer_repo.find_pending_follow_up()
        self.assertEqual(len(pending), 1)  # 新创建的客户没有跟进记录
        
        # 添加跟进记录
        self.customer_repo.update_follow_up(
            self.customer.id, '已跟进', self.onsite_user.id
        )
        
        pending = self.customer_repo.find_pending_follow_up()
        self.assertEqual(len(pending), 0)  # 现在没有待跟进的客户
    
    def test_filter_by_user_permission(self):
        """测试根据用户权限过滤客户"""
        customers = [self.customer]
        
        # 管理员可以看到所有客户
        filtered = self.customer_repo.filter_by_user_permission(self.admin_user, customers)
        self.assertEqual(len(filtered), 1)
        
        # 网络咨询只能看到自己创建的客户
        filtered = self.customer_repo.filter_by_user_permission(self.online_user, customers)
        self.assertEqual(len(filtered), 1)
        
        # 现场咨询只能看到分配给自己的客户
        filtered = self.customer_repo.filter_by_user_permission(self.onsite_user, customers)
        self.assertEqual(len(filtered), 1)

class TestSystemSettingRepository(RepositoryTestCase):
    """系统设置Repository测试"""
    
    def setUp(self):
        super().setUp()
        self.setting_repo = SystemSettingRepository()
    
    def test_get_set_setting(self):
        """测试获取和设置系统设置"""
        # 设置值
        setting = self.setting_repo.set_setting('test_key', 'test_value', '测试设置')
        self.assertIsNotNone(setting)
        
        # 获取值
        value = self.setting_repo.get_setting('test_key')
        self.assertEqual(value, 'test_value')
        
        # 获取不存在的设置
        value = self.setting_repo.get_setting('nonexistent', 'default')
        self.assertEqual(value, 'default')
    
    def test_card_number_length(self):
        """测试卡号长度设置"""
        # 设置卡号长度
        result = self.setting_repo.set_card_number_length(12)
        self.assertTrue(result)
        
        # 获取卡号长度
        length = self.setting_repo.get_card_number_length()
        self.assertEqual(length, 12)
        
        # 测试无效长度
        result = self.setting_repo.set_card_number_length(5)  # 小于最小值
        self.assertFalse(result)

if __name__ == '__main__':
    unittest.main()