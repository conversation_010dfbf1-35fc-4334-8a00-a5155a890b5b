"""
Flask应用工厂函数
"""
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from config import Config

# 初始化扩展
db = SQLAlchemy()
login_manager = LoginManager()

def create_app(config_class=Config):
    """应用工厂函数"""
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    # 初始化扩展
    db.init_app(app)
    login_manager.init_app(app)
    
    # 配置登录管理器
    login_manager.login_view = 'auth.login'
    login_manager.login_message = '请先登录'
    login_manager.login_message_category = 'info'
    
    # 注册蓝图
    try:
        from app.views.auth import bp as auth_bp
        app.register_blueprint(auth_bp, url_prefix='/auth')

        from app.views.main import bp as main_bp
        app.register_blueprint(main_bp)

        from app.views.admin import bp as admin_bp
        app.register_blueprint(admin_bp, url_prefix='/admin')

        from app.views.customer import bp as customer_bp
        app.register_blueprint(customer_bp, url_prefix='/customer')

        from app.views.channel import bp as channel_bp
        app.register_blueprint(channel_bp, url_prefix='/channel')

        from app.views.report import bp as report_bp
        app.register_blueprint(report_bp, url_prefix='/report')

        from app.views.api import bp as api_bp
        app.register_blueprint(api_bp, url_prefix='/api')
    except ImportError as e:
        print(f"警告: 部分视图模块导入失败: {e}")
        # 只注册基础模块
        from app.views.auth import bp as auth_bp
        app.register_blueprint(auth_bp, url_prefix='/auth')

        from app.views.main import bp as main_bp
        app.register_blueprint(main_bp)
    
    # 用户加载回调
    @login_manager.user_loader
    def load_user(user_id):
        from app.models.user import User
        return User.query.get(int(user_id))
    
    # 全局模板变量
    @app.context_processor
    def inject_global_vars():
        from app.models.system import SystemConfig
        return {
            'system_name': SystemConfig.get_config('system_name', '老客回访与跟进系统')
        }
    
    # 错误处理
    @app.errorhandler(404)
    def not_found_error(error):
        from flask import render_template
        return render_template('errors/404.html'), 404

    @app.errorhandler(500)
    def internal_error(error):
        from flask import render_template
        db.session.rollback()
        return render_template('errors/500.html'), 500
    
    # 初始化配置
    config_class.init_app(app)
    
    return app

# 导入模型（避免循环导入）
from app.models import user, customer, system
