{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">统计报表仪表板</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">首页</a></li>
                        <li class="breadcrumb-item active">统计报表</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 总体统计概览 -->
    <div class="row">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3>{{ total_stats.total_customers }}</h3>
                    <p>总登记客户数</p>
                </div>
                <div class="icon">
                    <i class="fas fa-users"></i>
                </div>
                <a href="{{ url_for('reports.statistics') }}" class="small-box-footer">
                    查看详情 <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
        
        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3>{{ total_stats.visit_success_count }}</h3>
                    <p>跟进到院成功数</p>
                </div>
                <div class="icon">
                    <i class="fas fa-hospital"></i>
                </div>
                <a href="{{ url_for('reports.statistics') }}" class="small-box-footer">
                    查看详情 <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
        
        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3>{{ total_stats.consumption_success_count }}</h3>
                    <p>跟进消费成功数</p>
                </div>
                <div class="icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <a href="{{ url_for('reports.statistics') }}" class="small-box-footer">
                    查看详情 <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
        
        <div class="col-lg-3 col-6">
            <div class="small-box bg-danger">
                <div class="inner">
                    <h3>{{ "%.0f"|format(total_stats.total_consumption_amount) }}</h3>
                    <p>总消费金额（元）</p>
                </div>
                <div class="icon">
                    <i class="fas fa-yen-sign"></i>
                </div>
                <a href="{{ url_for('reports.statistics') }}" class="small-box-footer">
                    查看详情 <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- 数据验证信息 -->
    <div class="row">
        <div class="col-12">
            <div class="card card-info">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-database"></i>
                        数据验证信息
                    </h3>
                    <div class="card-tools">
                        {% if validation_summary.last_updated %}
                        <span class="badge badge-info">
                            <i class="fas fa-clock"></i> 最后更新: {{ validation_summary.last_updated }}
                        </span>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    <!-- 数据验证统计 -->
                    <div class="row">
                        <!-- 消费记录验证 -->
                        <div class="col-md-6">
                            <div class="card card-success">
                                <div class="card-header">
                                    <h3 class="card-title">
                                        <i class="fas fa-shopping-cart"></i> 消费记录验证
                                    </h3>
                                </div>
                                <div class="card-body">
                                    <!-- 年份合计 -->
                                    <div class="info-box bg-gradient-success mb-3">
                                        <span class="info-box-icon">
                                            <i class="fas fa-calendar-alt"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">{{ validation_summary.consumption_stats.year_total.year }}年合计</span>
                                            <span class="info-box-number">
                                                ¥{{ "{:,.2f}".format(validation_summary.consumption_stats.year_total.amount) }}
                                            </span>
                                            <div class="progress">
                                                <div class="progress-bar" style="width: 100%"></div>
                                            </div>
                                            <span class="progress-description">
                                                {{ validation_summary.consumption_stats.year_total.count }}条记录
                                            </span>
                                        </div>
                                    </div>

                                    <!-- 月份合计 -->
                                    <div class="info-box bg-gradient-warning mb-3">
                                        <span class="info-box-icon">
                                            <i class="fas fa-calendar"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">{{ validation_summary.consumption_stats.month_total.year }}年{{ validation_summary.consumption_stats.month_total.month }}月合计</span>
                                            <span class="info-box-number">
                                                ¥{{ "{:,.2f}".format(validation_summary.consumption_stats.month_total.amount) }}
                                            </span>
                                            <div class="progress">
                                                <div class="progress-bar bg-warning" style="width: 100%"></div>
                                            </div>
                                            <span class="progress-description">
                                                {{ validation_summary.consumption_stats.month_total.count }}条记录
                                            </span>
                                        </div>
                                    </div>

                                    <!-- 最后1日合计 -->
                                    {% if validation_summary.consumption_stats.last_day.date %}
                                    <div class="info-box bg-gradient-danger">
                                        <span class="info-box-icon">
                                            <i class="fas fa-calendar-day"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">最后1日({{ validation_summary.consumption_stats.last_day.date }})合计</span>
                                            <span class="info-box-number">
                                                ¥{{ "{:,.2f}".format(validation_summary.consumption_stats.last_day.amount) }}
                                            </span>
                                            <div class="progress">
                                                <div class="progress-bar bg-danger" style="width: 100%"></div>
                                            </div>
                                            <span class="progress-description">
                                                {{ validation_summary.consumption_stats.last_day.count }}条记录
                                            </span>
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- 到院记录验证 -->
                        <div class="col-md-6">
                            <div class="card card-info">
                                <div class="card-header">
                                    <h3 class="card-title">
                                        <i class="fas fa-hospital"></i> 到院记录验证
                                    </h3>
                                </div>
                                <div class="card-body">
                                    <!-- 年份合计 -->
                                    <div class="info-box bg-gradient-info mb-3">
                                        <span class="info-box-icon">
                                            <i class="fas fa-calendar-alt"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">{{ validation_summary.visit_stats.year_total.year }}年合计</span>
                                            <span class="info-box-number">
                                                {{ validation_summary.visit_stats.year_total.count }}条
                                            </span>
                                            <div class="progress">
                                                <div class="progress-bar bg-info" style="width: 100%"></div>
                                            </div>
                                            <span class="progress-description">
                                                到院记录总数
                                            </span>
                                        </div>
                                    </div>

                                    <!-- 月份合计 -->
                                    <div class="info-box bg-gradient-warning mb-3">
                                        <span class="info-box-icon">
                                            <i class="fas fa-calendar"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">{{ validation_summary.visit_stats.month_total.year }}年{{ validation_summary.visit_stats.month_total.month }}月合计</span>
                                            <span class="info-box-number">
                                                {{ validation_summary.visit_stats.month_total.count }}条
                                            </span>
                                            <div class="progress">
                                                <div class="progress-bar bg-warning" style="width: 100%"></div>
                                            </div>
                                            <span class="progress-description">
                                                本月到院记录
                                            </span>
                                        </div>
                                    </div>

                                    <!-- 最后1日合计 -->
                                    {% if validation_summary.visit_stats.last_day.date %}
                                    <div class="info-box bg-gradient-danger">
                                        <span class="info-box-icon">
                                            <i class="fas fa-calendar-day"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">最后1日({{ validation_summary.visit_stats.last_day.date }})合计</span>
                                            <span class="info-box-number">
                                                {{ validation_summary.visit_stats.last_day.count }}条
                                            </span>
                                            <div class="progress">
                                                <div class="progress-bar bg-danger" style="width: 100%"></div>
                                            </div>
                                            <span class="progress-description">
                                                最后一天到院记录
                                            </span>
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 按日期详细验证信息 -->
                    <div class="row mt-3">
                        <!-- 消费记录每日统计 -->
                        <div class="col-md-6">
                            <div class="card card-success">
                                <div class="card-header">
                                    <h3 class="card-title">
                                        <i class="fas fa-chart-line"></i> 消费记录每日统计（最近10天）
                                    </h3>
                                </div>
                                <div class="card-body p-0">
                                    {% if validation_summary.consumption_daily %}
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>日期</th>
                                                <th>条数</th>
                                                <th>金额</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for day in validation_summary.consumption_daily %}
                                            <tr>
                                                <td>
                                                    <strong>{{ day.date }}</strong>
                                                    {% if loop.first %}
                                                    <span class="badge badge-success badge-sm">最新</span>
                                                    {% endif %}
                                                </td>
                                                <td>{{ day.count }}条</td>
                                                <td><strong>¥{{ "{:,.2f}".format(day.amount) }}</strong></td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                    {% else %}
                                    <div class="p-3 text-center text-muted">
                                        <i class="fas fa-info-circle"></i> 暂无消费记录数据
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- 到院记录每日统计 -->
                        <div class="col-md-6">
                            <div class="card card-info">
                                <div class="card-header">
                                    <h3 class="card-title">
                                        <i class="fas fa-chart-bar"></i> 到院记录每日统计（最近10天）
                                    </h3>
                                </div>
                                <div class="card-body p-0">
                                    {% if validation_summary.visit_daily %}
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>日期</th>
                                                <th>到院条数</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for day in validation_summary.visit_daily %}
                                            <tr>
                                                <td>
                                                    <strong>{{ day.date }}</strong>
                                                    {% if loop.first %}
                                                    <span class="badge badge-info badge-sm">最新</span>
                                                    {% endif %}
                                                </td>
                                                <td><strong>{{ day.count }}条</strong></td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                    {% else %}
                                    <div class="p-3 text-center text-muted">
                                        <i class="fas fa-info-circle"></i> 暂无到院记录数据
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 数据验证提示 -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> 数据验证说明</h6>
                                <ul class="mb-0">
                                    <li><strong>时间范围</strong>: 显示数据的起始和结束日期，用于验证数据完整性</li>
                                    <li><strong>合计金额</strong>: 消费记录的总金额，可用于财务核对</li>
                                    <li><strong>最后记录时间</strong>: 最新数据的具体时间，确认数据是否为最新</li>
                                    <li><strong>记录条数</strong>: 总记录数量，用于验证数据导入是否完整</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 功能快捷入口 -->
    <div class="row">
        <div class="col-md-4">
            <div class="card card-primary">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-upload"></i>
                        数据上传
                    </h3>
                </div>
                <div class="card-body">
                    <p>上传客户到院明细和消费明细数据，用于统计分析。</p>

                    <!-- 智能上传（推荐） -->
                    <div class="alert alert-success mb-3">
                        <h6><i class="fas fa-star"></i> 智能上传（推荐）</h6>
                        <p class="mb-2">支持增量上传和批量UPSERT，效率更高，避免重复数据</p>
                        <div class="btn-group w-100">
                            <a href="{{ url_for('reports.smart_upload_visit_records') }}" class="btn btn-success btn-sm">
                                <i class="fas fa-rocket"></i> 智能上传到院明细
                            </a>
                            <a href="{{ url_for('reports.smart_upload_consumption_records') }}" class="btn btn-success btn-sm">
                                <i class="fas fa-rocket"></i> 智能上传消费明细
                            </a>
                        </div>
                    </div>

                    <!-- 传统上传 -->
                    <div class="mb-2">
                        <small class="text-muted">传统上传（简单模式）</small>
                    </div>
                    <div class="btn-group-vertical w-100">
                        <a href="{{ url_for('reports.upload_visit_records') }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-hospital"></i> 上传到院明细
                        </a>
                        <a href="{{ url_for('reports.upload_consumption_records') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-shopping-cart"></i> 上传消费明细
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card card-info">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-bar"></i>
                        统计报表
                    </h3>
                </div>
                <div class="card-body">
                    <p>查看客户跟进效果统计，包括到院成功率和消费转化率。</p>
                    <div class="btn-group-vertical w-100">
                        <a href="{{ url_for('reports.statistics') }}" class="btn btn-info">
                            <i class="fas fa-chart-line"></i> 跟进统计报表
                        </a>
                        <a href="{{ url_for('reports.consultant_statistics') }}" class="btn btn-success">
                            <i class="fas fa-users"></i> 现场咨询师统计
                        </a>
                        <button class="btn btn-outline-info" disabled>
                            <i class="fas fa-chart-pie"></i> 渠道分析报表（开发中）
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card card-warning">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-cog"></i>
                        系统管理
                    </h3>
                </div>
                <div class="card-body">
                    <p>系统配置和数据管理功能。</p>
                    <div class="btn-group-vertical w-100">
                        <a href="{{ url_for('registration.customers') }}" class="btn btn-warning">
                            <i class="fas fa-users"></i> 客户管理
                        </a>
                        <a href="{{ url_for('basic_mgmt.users') }}" class="btn btn-outline-warning">
                            <i class="fas fa-user-cog"></i> 用户管理
                        </a>
                        <a href="{{ url_for('reports.clear_data_page') }}" class="btn btn-danger">
                            <i class="fas fa-trash-alt"></i> 数据清除
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 最近7天趋势图 -->
    {% if daily_stats %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-line"></i>
                        最近7天跟进趋势
                    </h3>
                </div>
                <div class="card-body">
                    <canvas id="trendChart" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
{% if daily_stats %}
$(document).ready(function() {
    // 准备图表数据
    var dates = [];
    var visitData = [];
    var consumptionData = [];
    var amountData = [];
    
    {% for daily in daily_stats.daily_stats %}
    dates.push('{{ daily.date.strftime("%m-%d") }}');
    visitData.push({{ daily.stats.visit_success_count }});
    consumptionData.push({{ daily.stats.consumption_success_count }});
    amountData.push({{ daily.stats.total_consumption_amount }});
    {% endfor %}
    
    // 创建趋势图
    var ctx = document.getElementById('trendChart').getContext('2d');
    var trendChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: dates,
            datasets: [{
                label: '到院成功数',
                data: visitData,
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }, {
                label: '消费成功数',
                data: consumptionData,
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: '跟进成功趋势'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});
{% endif %}
</script>
{% endblock %}
