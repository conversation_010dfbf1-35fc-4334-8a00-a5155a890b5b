#!/usr/bin/env python3
"""
快速修复脚本
解决权限和部门相关问题
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.user import User, Role, Permission, RolePermission, Department
from app.models.system import SystemConfig
from sqlalchemy import text

def fix_missing_permissions():
    """修复缺失的权限"""
    print("检查和修复权限...")
    
    # 需要确保存在的权限
    required_permissions = [
        {'permission_name': '用户管理', 'permission_code': 'USER_MANAGE', 'module_name': 'SYSTEM', 'description': '创建、编辑、删除用户账号'},
        {'permission_name': '角色管理', 'permission_code': 'ROLE_MANAGE', 'module_name': 'SYSTEM', 'description': '管理系统角色和权限'},
        {'permission_name': '系统配置', 'permission_code': 'SYSTEM_CONFIG', 'module_name': 'SYSTEM', 'description': '修改系统配置参数'},
        {'permission_name': '渠道查看', 'permission_code': 'CHANNEL_VIEW', 'module_name': 'CHANNEL', 'description': '查看渠道列表'},
        {'permission_name': '渠道管理', 'permission_code': 'CHANNEL_MANAGE', 'module_name': 'CHANNEL', 'description': '添加、修改、删除渠道'},
        {'permission_name': '渠道导入', 'permission_code': 'CHANNEL_IMPORT', 'module_name': 'CHANNEL', 'description': '批量导入渠道数据'},
        {'permission_name': '客户登记', 'permission_code': 'CUSTOMER_REGISTER', 'module_name': 'CUSTOMER', 'description': '登记新客户信息'},
        {'permission_name': '客户查看', 'permission_code': 'CUSTOMER_VIEW', 'module_name': 'CUSTOMER', 'description': '查看客户登记信息'},
        {'permission_name': '客户修改', 'permission_code': 'CUSTOMER_EDIT', 'module_name': 'CUSTOMER', 'description': '修改客户登记信息'},
        {'permission_name': '跟进添加', 'permission_code': 'FOLLOW_UP_ADD', 'module_name': 'FOLLOW_UP', 'description': '添加客户跟进记录'},
        {'permission_name': '跟进查看', 'permission_code': 'FOLLOW_UP_VIEW', 'module_name': 'FOLLOW_UP', 'description': '查看跟进记录'},
        {'permission_name': '映射管理', 'permission_code': 'MAPPING_MANAGE', 'module_name': 'DATA', 'description': '管理现场小组映射关系'},
        {'permission_name': '消费导入', 'permission_code': 'CONSUMPTION_IMPORT', 'module_name': 'DATA', 'description': '导入客户消费数据'},
        {'permission_name': '报表查看', 'permission_code': 'REPORT_VIEW', 'module_name': 'REPORT', 'description': '查看统计报表'},
        {'permission_name': '报表导出', 'permission_code': 'REPORT_EXPORT', 'module_name': 'REPORT', 'description': '导出报表数据'},
        {'permission_name': '审计日志查看', 'permission_code': 'AUDIT_LOG_VIEW', 'module_name': 'SYSTEM', 'description': '查看系统操作审计日志'}
    ]
    
    added_count = 0
    for perm_data in required_permissions:
        existing_perm = Permission.query.filter_by(permission_code=perm_data['permission_code']).first()
        if not existing_perm:
            permission = Permission(**perm_data)
            db.session.add(permission)
            added_count += 1
            print(f"添加权限: {perm_data['permission_name']}")
    
    if added_count > 0:
        db.session.commit()
        print(f"✓ 添加了 {added_count} 个缺失的权限")
    else:
        print("✓ 所有权限都已存在")

def fix_admin_permissions():
    """确保管理员拥有所有权限"""
    print("检查管理员权限...")
    
    admin_role = Role.query.filter_by(role_code='ADMIN').first()
    if not admin_role:
        print("❌ 未找到管理员角色")
        return
    
    all_permissions = Permission.query.all()
    added_count = 0
    
    for permission in all_permissions:
        existing = RolePermission.query.filter_by(
            role_id=admin_role.id,
            permission_id=permission.id
        ).first()
        if not existing:
            role_perm = RolePermission(role_id=admin_role.id, permission_id=permission.id)
            db.session.add(role_perm)
            added_count += 1
    
    if added_count > 0:
        db.session.commit()
        print(f"✓ 为管理员添加了 {added_count} 个权限")
    else:
        print("✓ 管理员已拥有所有权限")

def check_department_migration():
    """检查部门迁移状态"""
    print("检查部门迁移状态...")
    
    # 检查departments表是否存在
    dept_table_exists = db.session.execute(text("""
        SELECT COUNT(*) as count FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = 'departments'
    """)).fetchone()
    
    if dept_table_exists[0] == 0:
        print("❌ departments表不存在，需要运行迁移脚本")
        return False
    
    # 检查用户表是否有department_id字段
    dept_id_column_exists = db.session.execute(text("""
        SELECT COUNT(*) as count FROM information_schema.columns 
        WHERE table_schema = DATABASE() 
        AND table_name = 'user_accounts' 
        AND column_name = 'department_id'
    """)).fetchone()
    
    if dept_id_column_exists[0] == 0:
        print("❌ 用户表缺少department_id字段，需要运行迁移脚本")
        return False
    
    # 检查用户是否都有department_id
    users_without_dept = db.session.execute(text("""
        SELECT COUNT(*) as count FROM user_accounts 
        WHERE department_id IS NULL
    """)).fetchone()
    
    if users_without_dept[0] > 0:
        print(f"⚠️ 有 {users_without_dept[0]} 个用户没有分配部门ID")
        return False
    
    print("✓ 部门迁移状态正常")
    return True

def fix_user_department_assignment():
    """修复用户部门分配"""
    print("检查用户部门分配...")
    
    # 先检查是否存在department字段
    dept_column_exists = db.session.execute(text("""
        SELECT COUNT(*) as count FROM information_schema.columns 
        WHERE table_schema = DATABASE() 
        AND table_name = 'user_accounts' 
        AND column_name = 'department'
    """)).fetchone()
    
    # 检查是否存在department_old字段（迁移后的备份字段）
    dept_old_column_exists = db.session.execute(text("""
        SELECT COUNT(*) as count FROM information_schema.columns 
        WHERE table_schema = DATABASE() 
        AND table_name = 'user_accounts' 
        AND column_name = 'department_old'
    """)).fetchone()
    
    # 获取没有部门ID的用户
    if dept_column_exists[0] > 0:
        # 如果还有department字段，使用它
        users_without_dept = db.session.execute(text("""
            SELECT id, username, department FROM user_accounts 
            WHERE department_id IS NULL
        """)).fetchall()
    elif dept_old_column_exists[0] > 0:
        # 如果有department_old字段，使用它
        users_without_dept = db.session.execute(text("""
            SELECT id, username, department_old FROM user_accounts 
            WHERE department_id IS NULL
        """)).fetchall()
    else:
        # 如果都没有，只查询用户ID和用户名
        users_without_dept = db.session.execute(text("""
            SELECT id, username, NULL as department FROM user_accounts 
            WHERE department_id IS NULL
        """)).fetchall()
    
    if not users_without_dept:
        print("✓ 所有用户都已分配部门")
        return
    
    print(f"发现 {len(users_without_dept)} 个用户需要分配部门")
    
    # 获取所有部门
    departments = Department.query.all()
    if not departments:
        print("❌ 没有找到任何部门，无法分配")
        return
    
    dept_mapping = {dept.department_name: dept.id for dept in departments}
    default_dept = departments[0]  # 使用第一个部门作为默认部门
    
    # 为没有部门的用户分配部门
    fixed_count = 0
    for user_id, username, dept_name in users_without_dept:
        if dept_name and dept_name.strip() in dept_mapping:
            dept_id = dept_mapping[dept_name.strip()]
            db.session.execute(text("""
                UPDATE user_accounts 
                SET department_id = :dept_id 
                WHERE id = :user_id
            """), {'dept_id': dept_id, 'user_id': user_id})
            fixed_count += 1
            print(f"  修复用户 {username}: {dept_name} -> 部门ID {dept_id}")
        else:
            # 分配到默认部门
            db.session.execute(text("""
                UPDATE user_accounts 
                SET department_id = :dept_id 
                WHERE id = :user_id
            """), {'dept_id': default_dept.id, 'user_id': user_id})
            fixed_count += 1
            print(f"  修复用户 {username}: 分配到默认部门 {default_dept.department_name}")
    
    if fixed_count > 0:
        db.session.commit()
        print(f"✓ 修复了 {fixed_count} 个用户的部门分配")

def main():
    """主修复函数"""
    print("=" * 50)
    print("开始快速修复")
    print("=" * 50)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 检查部门迁移状态
            migration_ok = check_department_migration()
            
            # 2. 修复缺失的权限
            fix_missing_permissions()
            
            # 3. 确保管理员拥有所有权限
            fix_admin_permissions()
            
            # 4. 修复用户部门分配
            if migration_ok:
                fix_user_department_assignment()
            
            print("\n" + "=" * 50)
            print("快速修复完成！")
            print("=" * 50)
            
            if migration_ok:
                print("✅ 系统应该可以正常运行了")
                print("建议操作:")
                print("1. 重新启动系统: python start.py")
                print("2. 使用管理员账号登录测试")
            else:
                print("⚠️ 部门迁移未完成，请先运行: python migrate_departments.py")
            
        except Exception as e:
            print(f"❌ 修复过程中出现错误: {e}")
            db.session.rollback()
            raise

if __name__ == '__main__':
    main()