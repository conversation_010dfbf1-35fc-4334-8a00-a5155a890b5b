# -*- coding: utf-8 -*-
"""
渠道Repository
"""

from typing import List, Optional
from sqlalchemy import or_
from app.models import Channel
from .base_repository import BaseRepository

class ChannelRepository(BaseRepository):
    """渠道数据访问类"""
    
    def __init__(self):
        super().__init__(Channel)
    
    def find_by_name(self, name: str) -> Optional[Channel]:
        """根据渠道名称查找渠道"""
        return Channel.query.filter_by(name=name).first()
    
    def find_by_category(self, category: str) -> List[Channel]:
        """根据渠道分类查找渠道"""
        return Channel.query.filter_by(category=category, is_active=True).order_by(Channel.name).all()
    
    def get_active_channels(self) -> List[Channel]:
        """获取所有激活的渠道"""
        return Channel.query.filter_by(is_active=True).order_by(Channel.name).all()
    
    def search_by_simple_code(self, query: str) -> List[Channel]:
        """根据简码搜索渠道（支持中文、全拼、首字母）"""
        if not query:
            return self.get_active_channels()
        
        # 搜索渠道名称、简码
        search_filter = or_(
            Channel.name.like(f'%{query}%'),
            Channel.simple_code.like(f'%{query}%')
        )
        
        return Channel.query.filter(search_filter, Channel.is_active == True).order_by(Channel.name).all()
    
    def get_categories(self) -> List[str]:
        """获取所有渠道分类"""
        from sqlalchemy import distinct
        categories = Channel.query.with_entities(distinct(Channel.category)).filter(
            Channel.category.isnot(None),
            Channel.is_active == True
        ).all()
        return [cat[0] for cat in categories if cat[0]]
    
    def activate_channel(self, channel_id: int) -> bool:
        """激活渠道"""
        return self.update(channel_id, {'is_active': True})
    
    def deactivate_channel(self, channel_id: int) -> bool:
        """停用渠道"""
        return self.update(channel_id, {'is_active': False})
    
    def bulk_create_channels(self, channels_data: List[dict]) -> List[Channel]:
        """批量创建渠道"""
        try:
            channels = []
            for data in channels_data:
                # 检查是否已存在同名渠道
                existing = self.find_by_name(data.get('name', ''))
                if not existing:
                    # 如果没有简码，自动生成
                    if not data.get('simple_code'):
                        data['simple_code'] = self._generate_simple_code(data['name'])
                    
                    channel = Channel(**data)
                    channels.append(channel)
            
            if channels:
                from app import db
                db.session.add_all(channels)
                db.session.commit()
            
            return channels
        except Exception as e:
            from app import db
            db.session.rollback()
            raise e
    
    def _generate_simple_code(self, name: str) -> str:
        """生成简码"""
        try:
            from pypinyin import lazy_pinyin, Style
            import re
            
            # 获取拼音首字母
            pinyin_list = lazy_pinyin(name, style=Style.FIRST_LETTER)
            simple_code = ''.join(pinyin_list).upper()
            
            # 如果简码为空或太短，使用原名称
            if len(simple_code) < 2:
                # 移除特殊字符，保留中文、英文和数字
                clean_name = re.sub(r'[^\u4e00-\u9fff\w]', '', name)
                simple_code = clean_name.upper()[:10]
            
            return simple_code[:10]  # 限制长度
        except ImportError:
            # 如果没有pypinyin库，使用简单逻辑
            import re
            clean_name = re.sub(r'[^\u4e00-\u9fff\w]', '', name)
            return clean_name.upper()[:10]
        except:
            return name.upper()[:10]
    
    def get_channel_statistics(self) -> dict:
        """获取渠道统计信息"""
        total_channels = Channel.query.count()
        active_channels = Channel.query.filter_by(is_active=True).count()
        
        # 按分类统计
        category_stats = {}
        categories = self.get_categories()
        for category in categories:
            category_stats[category] = Channel.query.filter_by(
                category=category, is_active=True
            ).count()
        
        return {
            'total_channels': total_channels,
            'active_channels': active_channels,
            'inactive_channels': total_channels - active_channels,
            'category_statistics': category_stats
        }