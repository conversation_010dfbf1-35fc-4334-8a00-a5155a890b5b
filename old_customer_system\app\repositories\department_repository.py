# -*- coding: utf-8 -*-
"""
部门Repository
"""

from typing import List, Optional
from app.models import Department
from .base_repository import BaseRepository

class DepartmentRepository(BaseRepository):
    """部门数据访问类"""
    
    def __init__(self):
        super().__init__(Department)
    
    def find_by_name(self, name: str) -> Optional[Department]:
        """根据部门名称查找部门"""
        return Department.query.filter_by(name=name).first()
    
    def get_active_departments(self) -> List[Department]:
        """获取所有激活的部门"""
        return Department.query.filter_by(is_active=True).order_by(Department.name).all()
    
    def search_departments(self, query: str) -> List[Department]:
        """搜索部门"""
        if not query:
            return self.get_active_departments()
        
        return Department.query.filter(
            Department.name.like(f'%{query}%'),
            Department.is_active == True
        ).order_by(Department.name).all()
    
    def get_department_with_users(self, department_id: int) -> Optional[Department]:
        """获取部门及其用户信息"""
        return Department.query.filter_by(id=department_id).first()
    
    def activate_department(self, department_id: int) -> bool:
        """激活部门"""
        return self.update(department_id, {'is_active': True})
    
    def deactivate_department(self, department_id: int) -> bool:
        """停用部门"""
        return self.update(department_id, {'is_active': False})
    
    def get_department_statistics(self) -> dict:
        """获取部门统计信息"""
        total_departments = Department.query.count()
        active_departments = Department.query.filter_by(is_active=True).count()
        
        return {
            'total_departments': total_departments,
            'active_departments': active_departments,
            'inactive_departments': total_departments - active_departments
        }