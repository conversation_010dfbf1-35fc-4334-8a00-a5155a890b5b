#!/usr/bin/env python3
"""
清理业务数据库中的系统管理表
这些表应该只存在于 rfm_office 数据库中
"""

import pymysql
from app import create_app
from app.models import DatabaseConfig

def clean_business_database():
    """清理业务数据库中的系统表"""
    print("🧹 清理业务数据库中的系统管理表")
    print("=" * 50)
    
    app = create_app()
    
    with app.app_context():
        # 获取激活的业务数据库配置
        active_config = DatabaseConfig.get_active_config()
        
        if not active_config:
            print("❌ 没有激活的业务数据库配置")
            return
        
        print(f"📋 业务数据库: {active_config.database_name}")
        print(f"   连接: {active_config.host}:{active_config.port}")
        
        try:
            connection = pymysql.connect(
                host=active_config.host,
                port=active_config.port,
                user=active_config.username,
                password=active_config.get_password(),
                database=active_config.database_name,
                charset='utf8mb4'
            )
            
            print("✅ 成功连接到业务数据库")
            
            # 获取所有表
            with connection.cursor() as cursor:
                cursor.execute("SHOW TABLES")
                all_tables = [row[0] for row in cursor.fetchall()]
            
            # 识别系统管理表（这些应该只在 rfm_office 中）
            system_tables = [
                'users',
                'database_configs', 
                'data_processing_tasks',
                'system_settings',
                'cache_table_registry',
                'process_history'
            ]
            
            # 识别业务表
            business_tables = []
            tables_to_remove = []
            
            for table in all_tables:
                if table in system_tables:
                    tables_to_remove.append(table)
                else:
                    business_tables.append(table)
            
            print(f"\n📊 表分析:")
            print(f"   总表数: {len(all_tables)}")
            print(f"   业务表: {len(business_tables)}")
            print(f"   系统表(需要清理): {len(tables_to_remove)}")
            
            print(f"\n📄 业务表 (保留):")
            for table in business_tables:
                print(f"   ✅ {table}")
            
            if tables_to_remove:
                print(f"\n🗑️  系统表 (将被清理):")
                for table in tables_to_remove:
                    print(f"   ❌ {table}")
                
                # 确认是否清理
                print(f"\n⚠️  警告: 即将从业务数据库中删除系统管理表")
                print(f"   这些表应该只存在于 rfm_office 数据库中")
                print(f"   业务数据表不会受到影响")
                
                confirm = input(f"\n是否继续清理？(输入 'yes' 确认): ").strip().lower()
                
                if confirm == 'yes':
                    print(f"\n🧹 开始清理...")
                    
                    for table in tables_to_remove:
                        try:
                            with connection.cursor() as cursor:
                                cursor.execute(f"DROP TABLE IF EXISTS `{table}`")
                            print(f"   ✅ 已删除表: {table}")
                        except Exception as e:
                            print(f"   ❌ 删除表 {table} 失败: {e}")
                    
                    connection.commit()
                    print(f"\n✅ 清理完成！")
                    
                    # 重新检查
                    with connection.cursor() as cursor:
                        cursor.execute("SHOW TABLES")
                        remaining_tables = [row[0] for row in cursor.fetchall()]
                    
                    print(f"\n📋 清理后的业务数据库表:")
                    for table in remaining_tables:
                        print(f"   📄 {table}")
                    
                else:
                    print(f"❌ 清理已取消")
            else:
                print(f"\n✅ 业务数据库很干净，没有系统管理表")
            
            connection.close()
            
        except Exception as e:
            print(f"❌ 操作失败: {e}")

if __name__ == '__main__':
    try:
        clean_business_database()
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        import traceback
        traceback.print_exc()
