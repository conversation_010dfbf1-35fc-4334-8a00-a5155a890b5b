#!/usr/bin/env python3
"""
启动服务器脚本
"""

import os
import sys
import subprocess
from pathlib import Path
import pandas as pd

# 设置pandas选项，避免FutureWarning（如果支持的话）
try:
    pd.set_option('future.no_silent_downcasting', True)
except Exception:
    # 如果pandas版本不支持此选项，忽略
    pass

def ensure_correct_environment():
    """确保使用正确的Python环境"""
    # 获取当前脚本所在目录
    script_dir = Path(__file__).parent.absolute()

    # 检查虚拟环境是否存在
    venv_python = script_dir / "venv" / "Scripts" / "python.exe"

    if venv_python.exists():
        # 检查当前是否在虚拟环境中运行
        current_python = Path(sys.executable)

        # 如果当前不是在虚拟环境中运行，重新启动
        if current_python != venv_python:
            print(f"检测到虚拟环境，正在使用正确的Python环境启动...")
            print(f"当前Python: {current_python}")
            print(f"虚拟环境Python: {venv_python}")

            # 使用虚拟环境的Python重新启动脚本
            try:
                subprocess.run([str(venv_python), str(__file__)], check=True)
                return False  # 表示已经重新启动，当前进程应该退出
            except subprocess.CalledProcessError as e:
                print(f"启动失败: {e}")
                input("按回车键退出...")
                return False
        else:
            print(f"✅ 正在虚拟环境中运行: {current_python}")

    return True  # 表示可以继续执行

def main():
    try:
        print("=" * 50)
        print("  数据处理系统")
        print("=" * 50)

        # 确保使用正确的Python环境
        if not ensure_correct_environment():
            return  # 如果重新启动了，当前进程退出

        # 设置环境变量
        os.environ.setdefault('FLASK_ENV', 'development')
        
        print("正在启动系统...")
        
        # 导入应用
        from app import create_app
        app = create_app()
        
        print("系统启动成功！")
        print("访问地址: http://localhost:5000")
        print("默认用户: gdbinghu")
        print("密码: 请查看初始化时的输出或联系管理员")
        print("\n按 Ctrl+C 停止服务")
        print("=" * 50)
        
        # 启动服务器
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=False,  # 关闭调试模式避免重启问题
            threaded=True,
            use_reloader=False  # 关闭自动重载
        )
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保已安装所有依赖包")
        sys.exit(1)
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()
