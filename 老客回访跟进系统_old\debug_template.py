#!/usr/bin/env python3
"""
调试模板渲染问题
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.customer import Channel
from app.models.user import User
from config import get_config

def debug_template_issue():
    """调试模板渲染问题"""
    print("调试模板渲染问题...")
    
    config = get_config()
    app = create_app(config)
    
    with app.app_context():
        try:
            # 检查第2页的数据
            print("检查第2页数据...")
            page2 = Channel.query.order_by(Channel.created_time.desc()).paginate(
                page=2, per_page=20, error_out=False
            )
            
            print(f"第2页记录数: {len(page2.items)}")
            
            for i, channel in enumerate(page2.items):
                print(f"\n记录 {i+1}:")
                print(f"  ID: {channel.id}")
                print(f"  分类: {channel.channel_category}")
                print(f"  名称: {channel.channel_name}")
                print(f"  状态: {channel.is_active}")
                print(f"  创建时间: {channel.created_time}")
                print(f"  更新时间: {channel.updated_time}")
                print(f"  创建人ID: {channel.created_by}")
                
                # 检查创建人关联
                try:
                    if channel.creator:
                        print(f"  创建人: {channel.creator.real_name}")
                    else:
                        print(f"  创建人: None")
                except Exception as e:
                    print(f"  创建人查询失败: {e}")
                
                # 检查时间格式化
                try:
                    created_str = channel.created_time.strftime('%Y-%m-%d %H:%M')
                    print(f"  创建时间格式化: {created_str}")
                except Exception as e:
                    print(f"  创建时间格式化失败: {e}")
                
                try:
                    if channel.updated_time != channel.created_time:
                        updated_str = channel.updated_time.strftime('%Y-%m-%d %H:%M')
                        print(f"  更新时间格式化: {updated_str}")
                    else:
                        print(f"  更新时间格式化: -")
                except Exception as e:
                    print(f"  更新时间格式化失败: {e}")
            
            # 检查用户表
            print(f"\n检查用户表...")
            user_count = User.query.count()
            print(f"用户总数: {user_count}")
            
            # 检查有创建人的渠道
            channels_with_creator = Channel.query.filter(Channel.created_by.isnot(None)).count()
            print(f"有创建人的渠道数: {channels_with_creator}")
            
            # 检查创建人不存在的渠道
            orphan_channels = db.session.query(Channel).outerjoin(User, Channel.created_by == User.id).filter(
                Channel.created_by.isnot(None), User.id.is_(None)
            ).count()
            print(f"创建人不存在的渠道数: {orphan_channels}")
            
        except Exception as e:
            print(f"调试失败: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    debug_template_issue()
