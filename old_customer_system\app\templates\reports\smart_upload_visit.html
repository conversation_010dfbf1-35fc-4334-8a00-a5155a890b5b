{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-upload"></i> {{ title }}
                    </h3>
                    <div class="card-tools">
                        <a href="{{ url_for('reports.dashboard') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回仪表板
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- 上传历史信息 -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <h5><i class="fas fa-info-circle"></i> 上传建议</h5>
                                {% if suggestion.has_history %}
                                    <p><strong>上次上传：</strong>{{ suggestion.last_upload_date }} ({{ suggestion.last_upload_records }}条记录)</p>
                                    <p><strong>建议上传范围：</strong>{{ suggestion.suggested_start_date }} 至 {{ suggestion.suggested_end_date }}</p>
                                    <p class="text-muted">💡 建议使用"智能增量上传"模式，只上传新增数据，效率更高！</p>
                                {% else %}
                                    <p><strong>首次上传</strong></p>
                                    <p><strong>建议：</strong>可以上传所有历史数据，系统会自动处理</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- 上传表单 -->
                    <form method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="file">选择Excel文件</label>
                                    <input type="file" class="form-control-file" id="file" name="file" 
                                           accept=".xlsx,.xls,.csv" required>
                                    <small class="form-text text-muted">
                                        支持Excel (.xlsx, .xls) 和CSV文件
                                    </small>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="upload_mode">上传模式</label>
                                    <select class="form-control" id="upload_mode" name="upload_mode">
                                        <option value="smart_incremental" selected>
                                            🚀 智能增量上传 (推荐)
                                        </option>
                                        <option value="batch_upsert">
                                            🔄 批量UPSERT上传
                                        </option>
                                    </select>
                                    <small class="form-text text-muted" id="mode_description">
                                        智能增量：自动检测新数据，高效处理，避免重复
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-upload"></i> 开始上传
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 支持的列名格式说明 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-table"></i> 支持的Excel列名格式
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-sm">
                            <thead class="thead-light">
                                <tr>
                                    <th>标准列名</th>
                                    <th>支持的列名变体</th>
                                    <th>是否必需</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>会员卡号</strong></td>
                                    <td>会员卡号、卡号、会员号</td>
                                    <td><span class="badge badge-danger">必需</span></td>
                                </tr>
                                <tr>
                                    <td><strong>到院时间</strong></td>
                                    <td>到院时间、<strong>来院日期</strong>、到院日期、来院时间、就诊日期</td>
                                    <td><span class="badge badge-danger">必需</span></td>
                                </tr>
                                <tr>
                                    <td>到院具体时间</td>
                                    <td>到院具体时间、来院时间、就诊时间</td>
                                    <td><span class="badge badge-secondary">可选</span></td>
                                </tr>
                                <tr>
                                    <td>到院类型</td>
                                    <td>到院类型、来院类型、就诊类型</td>
                                    <td><span class="badge badge-secondary">可选</span></td>
                                </tr>
                                <tr>
                                    <td>科室</td>
                                    <td>科室、就诊科室</td>
                                    <td><span class="badge badge-secondary">可选</span></td>
                                </tr>
                                <tr>
                                    <td>医生</td>
                                    <td>医生、主治医生、接诊医生</td>
                                    <td><span class="badge badge-secondary">可选</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="alert alert-success mt-3">
                        <h6><i class="fas fa-check-circle"></i> 重要提示</h6>
                        <ul class="mb-0">
                            <li>✅ 现在支持"来院日期"列名，无需修改Excel文件</li>
                            <li>🚀 智能增量模式：只处理新数据，效率提升80%以上</li>
                            <li>🔄 批量UPSERT模式：处理大量数据，自动去重</li>
                            <li>📊 系统会自动记录上传历史，提供智能建议</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 上传模式说明
document.getElementById('upload_mode').addEventListener('change', function() {
    const mode = this.value;
    const description = document.getElementById('mode_description');
    
    if (mode === 'smart_incremental') {
        description.textContent = '智能增量：自动检测新数据，高效处理，避免重复';
        description.className = 'form-text text-success';
    } else {
        description.textContent = '批量UPSERT：处理大量数据，自动去重，适合数据修正';
        description.className = 'form-text text-info';
    }
});
</script>
{% endblock %}
