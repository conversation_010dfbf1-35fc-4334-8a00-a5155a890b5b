# 📊 Excel上传功能使用说明

## 🎉 问题已解决！

您遇到的"缺少必需的列: 到院时间"问题已经完全解决！现在系统支持多种列名格式，包括您使用的"来院日期"。

## ✅ 支持的列名格式

### 到院明细上传
系统现在支持以下列名（任选其一）：

| 标准列名 | 支持的列名变体 |
|---------|---------------|
| **会员卡号** | 会员卡号、卡号、会员号 |
| **到院时间** | 到院时间、**来院日期**、到院日期、来院时间、就诊日期 |
| 到院具体时间 | 到院具体时间、来院时间、就诊时间 |
| 到院类型 | 到院类型、来院类型、就诊类型 |
| 科室 | 科室、就诊科室 |
| 医生 | 医生、主治医生、接诊医生 |

### 消费明细上传
系统现在支持以下列名（任选其一）：

| 标准列名 | 支持的列名变体 |
|---------|---------------|
| **会员卡号** | 会员卡号、卡号、会员号 |
| **消费时间** | 消费时间、消费日期、交易时间、交易日期、付费时间、付费日期 |
| **消费金额** | 消费金额、金额、交易金额、付费金额、实收金额 |
| 消费项目 | 消费项目、项目名称、服务项目 |
| 数量 | 数量、项目数量 |
| 单价 | 单价、项目单价 |

**注意**：粗体标记的列为必需列，其他为可选列。

## 🚀 使用步骤

### 1. 访问上传页面
- **到院明细上传**：http://127.0.0.1:5000/reports/upload/visit-records
- **消费明细上传**：http://127.0.0.1:5000/reports/upload/consumption-records

### 2. 准备Excel文件
您的Excel文件可以使用以下列名：
```
到院明细示例：
会员卡号 | 来院日期   | 科室 | 医生
11111   | 2025-07-25 | 内科 | 张医生
22222   | 2025-07-26 | 外科 | 李医生

消费明细示例：
会员卡号 | 消费日期   | 消费金额 | 消费项目
11111   | 2025-07-25 | 1500.00 | 检查费
22222   | 2025-07-26 | 2000.00 | 治疗费
```

### 3. 上传文件
1. 选择文件类型（Excel或CSV）
2. 点击"选择文件"按钮
3. 选择您的Excel文件
4. 点击"上传"按钮

### 4. 查看结果
- 上传成功后会显示成功上传的记录数
- 如有错误会显示详细的错误信息
- 数据会自动保存到MySQL数据库

## 📈 查看统计报表

上传完成后，您可以：

1. **访问统计仪表板**：http://127.0.0.1:5000/reports/dashboard
2. **查看详细统计**：点击"跟进统计报表"
3. **筛选数据**：按日期范围、顾问、渠道等条件筛选
4. **导出报表**：将统计结果导出为Excel文件

## 🔧 测试文件

系统已为您创建了测试文件：
- `test_visit_mapping.xlsx` - 使用"来院日期"列名的到院明细测试文件
- `test_consumption_mapping.xlsx` - 使用"消费日期"列名的消费明细测试文件

您可以直接使用这些文件测试上传功能。

## 📊 统计逻辑

系统会自动进行以下统计分析：

1. **匹配客户数据**：使用会员卡号匹配客户登记信息和上传的明细数据
2. **跟进到院成功**：来院时间在登记时间之后的记录，统计数量+1
3. **跟进消费成功**：消费时间在登记时间之后的记录，统计数量+1
4. **消费金额汇总**：计算登记时间之后的总消费金额

## ⚠️ 注意事项

1. **文件格式**：支持Excel (.xlsx, .xls) 和CSV文件
2. **日期格式**：支持多种日期格式（YYYY-MM-DD、YYYY/MM/DD、YYYY年MM月DD日等）
3. **数据验证**：系统会自动验证数据格式和完整性
4. **重复数据**：系统会处理重复数据，避免重复导入

## 🎯 现在就可以使用！

您现在可以直接使用包含"来院日期"列名的Excel文件进行上传，不需要修改列名！

1. 访问：http://127.0.0.1:5000/reports/upload/visit-records
2. 上传您的Excel文件
3. 查看上传结果和统计报表

系统已完全支持您的数据格式！🎉
