# -*- coding: utf-8 -*-
"""
系统设置相关表单
"""

from flask_wtf import FlaskForm
from wtforms import StringField, IntegerField, TextAreaField, SelectField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Length, NumberRange, Optional, ValidationError

class SystemSettingsForm(FlaskForm):
    """系统设置表单"""
    
    # 卡号设置
    card_number_length = IntegerField('卡号长度', validators=[
        DataRequired(message='卡号长度不能为空'),
        NumberRange(min=6, max=18, message='卡号长度必须在6-18位之间')
    ], default=10)
    
    # 文本长度限制
    inquiry_content_max_length = IntegerField('咨询内容最大长度', validators=[
        DataRequired(message='咨询内容最大长度不能为空'),
        NumberRange(min=100, max=2000, message='咨询内容最大长度必须在100-2000字之间')
    ], default=500)
    
    follow_up_note_max_length = IntegerField('跟进情况最大长度', validators=[
        DataRequired(message='跟进情况最大长度不能为空'),
        NumberRange(min=100, max=2000, message='跟进情况最大长度必须在100-2000字之间')
    ], default=500)
    
    # 系统信息
    system_name = StringField('系统名称', validators=[
        DataRequired(message='系统名称不能为空'),
        Length(min=2, max=100, message='系统名称长度必须在2-100个字符之间')
    ], default='老客登记信息反馈系统')
    
    system_version = StringField('系统版本', validators=[
        DataRequired(message='系统版本不能为空'),
        Length(min=1, max=20, message='系统版本长度必须在1-20个字符之间')
    ], default='1.0.0')
    
    # 业务规则设置
    auto_generate_simple_code = BooleanField('自动生成简码', default=True)
    enable_duplicate_check = BooleanField('启用重复检查', default=True)
    require_last_visit_date = BooleanField('最近来院时间必填', default=False)
    
    # 数据保留设置
    data_retention_days = IntegerField('数据保留天数', validators=[
        Optional(),
        NumberRange(min=30, max=3650, message='数据保留天数必须在30-3650天之间')
    ], default=365)
    
    # 备注说明
    settings_note = TextAreaField('设置说明', validators=[
        Optional(),
        Length(max=1000, message='设置说明不能超过1000个字符')
    ])
    
    submit = SubmitField('保存设置')

class FieldRulesForm(FlaskForm):
    """字段规则配置表单"""
    
    # 卡号规则
    card_number_rule = TextAreaField('卡号规则说明', validators=[
        Optional(),
        Length(max=500, message='规则说明不能超过500个字符')
    ], default='纯数字，长度可在系统设置中配置（默认10位，最大18位）')
    
    # 所属现场规则
    onsite_consultant_rule = TextAreaField('所属现场规则说明', validators=[
        Optional(),
        Length(max=500, message='规则说明不能超过500个字符')
    ], default='下拉选择，选项为所有在职的"现场咨询"岗位人员')
    
    # 所属网资规则
    online_consultant_rule = TextAreaField('所属网资规则说明', validators=[
        Optional(),
        Length(max=500, message='规则说明不能超过500个字符')
    ], default='自动填充为当前操作登记的"网络咨询"人员，不可修改')
    
    # 激活渠道规则
    channel_rule = TextAreaField('激活渠道规则说明', validators=[
        Optional(),
        Length(max=500, message='规则说明不能超过500个字符')
    ], default='下拉选择，选项为"渠道管理"中所有已启用的渠道')
    
    # 咨询内容规则
    inquiry_content_rule = TextAreaField('咨询内容规则说明', validators=[
        Optional(),
        Length(max=500, message='规则说明不能超过500个字符')
    ], default='文本域，最大长度在系统设置中配置（默认500字）')
    
    # 最近来院时间规则
    last_visit_date_rule = TextAreaField('最近来院时间规则说明', validators=[
        Optional(),
        Length(max=500, message='规则说明不能超过500个字符')
    ], default='日期选择器，可选择过去的任意日期')
    
    # 咨询跟进情况规则
    follow_up_note_rule = TextAreaField('咨询跟进情况规则说明', validators=[
        Optional(),
        Length(max=500, message='规则说明不能超过500个字符')
    ], default='文本域，最大长度在系统设置中配置（默认500字）')
    
    # 咨询跟进时间规则
    follow_up_time_rule = TextAreaField('咨询跟进时间规则说明', validators=[
        Optional(),
        Length(max=500, message='规则说明不能超过500个字符')
    ], default='系统自动记录"咨询跟进情况"字段被最后一次修改时的时间戳')
    
    # 信息登记时间规则
    create_time_rule = TextAreaField('信息登记时间规则说明', validators=[
        Optional(),
        Length(max=500, message='规则说明不能超过500个字符')
    ], default='系统自动记录当前服务器时间，不可修改')
    
    submit = SubmitField('保存规则')