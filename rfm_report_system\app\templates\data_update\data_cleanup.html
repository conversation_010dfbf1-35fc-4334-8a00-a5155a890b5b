{% extends "base.html" %}

{% block title %}数据整理 - RFM报表处理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-broom me-2 text-warning"></i>数据整理</h2>
                    <p class="text-muted">按时间维度清理业务表中的错误数据</p>
                </div>
                <div>
                    <a href="{{ url_for('data_update.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>返回数据更新
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 警告提示 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-warning">
                <h6><i class="fas fa-exclamation-triangle me-2"></i>重要提示</h6>
                <ul class="mb-0">
                    <li><strong>数据清理操作不可逆</strong>，请在执行前仔细确认</li>
                    <li>建议在执行清理前<strong>备份数据库</strong></li>
                    <li>只能清理<strong>业务表</strong>数据，季度表不支持清理</li>
                    <li>清理操作基于表中的<strong>日期列</strong>进行时间筛选</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 当前数据库信息 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-server me-2"></i>当前数据库：{{ db_config.name }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-1"><strong>数据库类型：</strong>{{ db_config.db_type }}</p>
                            <p class="mb-0"><strong>服务器地址：</strong>{{ db_config.host }}:{{ db_config.port }}</p>
                        </div>
                        <div class="col-md-6">
                            <div id="databaseSummary">
                                <div class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <p class="mt-2">正在加载表信息...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 表选择区域 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-table me-2"></i>选择要整理的表</h5>
                </div>
                <div class="card-body">
                    <div id="tablesArea">
                        <div class="text-center">
                            <div class="spinner-border text-info" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2">正在加载业务表...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 清理配置区域 -->
    <div class="row mb-4" id="cleanupConfigArea" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-cog me-2"></i>清理配置</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label"><strong>选择的表：</strong></label>
                                <div id="selectedTableInfo" class="alert alert-info"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="dateColumnSelect" class="form-label">选择日期列：</label>
                                <select class="form-select" id="dateColumnSelect">
                                    <option value="">请选择日期列</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="cleanupTypeSelect" class="form-label">清理类型：</label>
                                <select class="form-select" id="cleanupTypeSelect">
                                    <option value="">请选择清理类型</option>
                                    <option value="yearly">按年度清理</option>
                                    <option value="quarterly">按季度清理</option>
                                    <option value="monthly">按月度清理</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div id="dateRangeInfo" style="display: none;">
                                <h6>日期范围信息</h6>
                                <div id="dateRangeContent"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 时间选择区域 -->
                    <div class="row" id="timeSelectionArea" style="display: none;">
                        <div class="col-12">
                            <hr>
                            <h6>时间选择</h6>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="yearSelect" class="form-label">年度：</label>
                                        <select class="form-select" id="yearSelect">
                                            <option value="">请选择年度</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-4" id="quarterSelectArea" style="display: none;">
                                    <div class="mb-3">
                                        <label for="quarterSelect" class="form-label">季度：</label>
                                        <select class="form-select" id="quarterSelect">
                                            <option value="">请选择季度</option>
                                            <option value="1">第1季度</option>
                                            <option value="2">第2季度</option>
                                            <option value="3">第3季度</option>
                                            <option value="4">第4季度</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-4" id="monthSelectArea" style="display: none;">
                                    <div class="mb-3">
                                        <label for="monthSelect" class="form-label">月份：</label>
                                        <select class="form-select" id="monthSelect">
                                            <option value="">请选择月份</option>
                                            <option value="1">1月</option>
                                            <option value="2">2月</option>
                                            <option value="3">3月</option>
                                            <option value="4">4月</option>
                                            <option value="5">5月</option>
                                            <option value="6">6月</option>
                                            <option value="7">7月</option>
                                            <option value="8">8月</option>
                                            <option value="9">9月</option>
                                            <option value="10">10月</option>
                                            <option value="11">11月</option>
                                            <option value="12">12月</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-12">
                                    <button class="btn btn-warning me-2" onclick="previewCleanup()">
                                        <i class="fas fa-eye me-2"></i>预览清理
                                    </button>
                                    <button class="btn btn-secondary" onclick="resetCleanupConfig()">
                                        <i class="fas fa-undo me-2"></i>重置配置
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 预览结果区域 -->
    <div class="row mb-4" id="previewResultArea" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-search me-2"></i>清理预览</h5>
                </div>
                <div class="card-body">
                    <div id="previewContent"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 执行结果区域 -->
    <div class="row mb-4" id="executeResultArea" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-check-circle me-2"></i>执行结果</h5>
                </div>
                <div class="card-body">
                    <div id="executeContent"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let selectedTable = null;
let tablesData = [];

$(document).ready(function() {
    loadTables();
});

// 加载业务表列表
function loadTables() {
    $.ajax({
        url: '{{ url_for("data_update.get_cleanup_tables") }}',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                tablesData = response.tables;
                renderTables(response.tables);
                renderDatabaseSummary(response.tables);
            } else {
                showError('加载表列表失败: ' + response.message);
            }
        },
        error: function() {
            showError('请求失败，请检查网络连接');
        }
    });
}

// 渲染数据库摘要
function renderDatabaseSummary(tables) {
    const totalTables = tables.length;
    const tablesWithDate = tables.filter(t => t.has_date_column).length;
    const totalRecords = tables.reduce((sum, t) => sum + t.record_count, 0);
    
    const summaryHtml = `
        <div class="row text-center">
            <div class="col-md-4">
                <h4 class="text-primary">${totalTables}</h4>
                <small class="text-muted">业务表总数</small>
            </div>
            <div class="col-md-4">
                <h4 class="text-success">${tablesWithDate}</h4>
                <small class="text-muted">可清理表数</small>
            </div>
            <div class="col-md-4">
                <h4 class="text-info">${totalRecords.toLocaleString()}</h4>
                <small class="text-muted">总记录数</small>
            </div>
        </div>
    `;
    
    $('#databaseSummary').html(summaryHtml);
}

// 渲染表列表
function renderTables(tables) {
    if (tables.length === 0) {
        $('#tablesArea').html(`
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle me-2"></i>没有找到业务表</h6>
                <p class="mb-0">当前数据库中没有可清理的业务表。</p>
            </div>
        `);
        return;
    }
    
    let html = '<div class="row">';
    
    tables.forEach(function(table) {
        const canCleanup = table.has_date_column && !table.error;
        const cardClass = canCleanup ? 'border-success' : 'border-secondary';
        const buttonClass = canCleanup ? 'btn-outline-success' : 'btn-outline-secondary';
        const buttonText = canCleanup ? '选择此表' : '无法清理';
        const buttonDisabled = canCleanup ? '' : 'disabled';
        
        html += `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card table-card h-100 ${cardClass}" ${canCleanup ? `onclick="selectTable('${table.name}')"` : ''}>
                    <div class="card-body">
                        <h6 class="card-title">
                            ${table.name}
                            ${canCleanup ? '<i class="fas fa-check-circle text-success ms-1"></i>' : '<i class="fas fa-times-circle text-secondary ms-1"></i>'}
                        </h6>
                        <p class="card-text small text-muted">
                            <i class="fas fa-list me-1"></i>${table.record_count.toLocaleString()} 行数据<br>
                            <i class="fas fa-columns me-1"></i>${table.column_count} 列<br>
                            <i class="fas fa-calendar me-1"></i>${table.date_columns.length} 个日期列
                        </p>
                        ${table.error ? `<div class="alert alert-warning p-2 mb-2"><small>${table.error}</small></div>` : ''}
                        <button class="btn ${buttonClass} btn-sm w-100" ${buttonDisabled}>
                            <i class="fas fa-arrow-right me-1"></i>${buttonText}
                        </button>
                    </div>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    $('#tablesArea').html(html);
}

// 选择表
function selectTable(tableName) {
    selectedTable = tablesData.find(t => t.name === tableName);
    
    if (!selectedTable) {
        showError('表信息不存在');
        return;
    }
    
    // 显示选择的表信息
    $('#selectedTableInfo').html(`
        <strong>${selectedTable.name}</strong><br>
        <small>记录数: ${selectedTable.record_count.toLocaleString()} | 日期列: ${selectedTable.date_columns.join(', ')}</small>
    `);
    
    // 填充日期列选择
    const dateColumnSelect = $('#dateColumnSelect');
    dateColumnSelect.empty().append('<option value="">请选择日期列</option>');
    selectedTable.date_columns.forEach(col => {
        dateColumnSelect.append(`<option value="${col}">${col}</option>`);
    });
    
    // 显示配置区域
    $('#cleanupConfigArea').show();
    
    // 滚动到配置区域
    $('html, body').animate({
        scrollTop: $('#cleanupConfigArea').offset().top - 100
    }, 500);
}

// 日期列选择变化
$('#dateColumnSelect').change(function() {
    const dateColumn = $(this).val();
    if (dateColumn && selectedTable) {
        loadDateRangeInfo(selectedTable.name, dateColumn);
    } else {
        $('#dateRangeInfo').hide();
    }
});

// 清理类型选择变化
$('#cleanupTypeSelect').change(function() {
    const cleanupType = $(this).val();
    
    $('#quarterSelectArea').hide();
    $('#monthSelectArea').hide();
    
    if (cleanupType === 'quarterly') {
        $('#quarterSelectArea').show();
    } else if (cleanupType === 'monthly') {
        $('#monthSelectArea').show();
    }
    
    if (cleanupType) {
        $('#timeSelectionArea').show();
    } else {
        $('#timeSelectionArea').hide();
    }
});

// 加载日期范围信息
function loadDateRangeInfo(tableName, dateColumn) {
    $.ajax({
        url: `{{ url_for("data_update.get_date_info", table_name="TABLE_NAME", date_column="DATE_COLUMN") }}`.replace('TABLE_NAME', tableName).replace('DATE_COLUMN', dateColumn),
        method: 'GET',
        success: function(response) {
            if (response.success) {
                renderDateRangeInfo(response.date_info);
                populateYearSelect(response.date_info.year_distribution);
            } else {
                showError('获取日期信息失败: ' + response.message);
            }
        },
        error: function() {
            showError('请求失败，请检查网络连接');
        }
    });
}

// 渲染日期范围信息
function renderDateRangeInfo(dateInfo) {
    let html = `
        <div class="alert alert-info">
            <strong>日期范围：</strong>${dateInfo.min_date} 至 ${dateInfo.max_date}<br>
            <strong>总记录数：</strong>${dateInfo.total_records.toLocaleString()}<br>
            <strong>年度分布：</strong>
    `;
    
    Object.entries(dateInfo.year_distribution).forEach(([year, count]) => {
        html += `<span class="badge bg-primary me-1">${year}: ${count.toLocaleString()}</span>`;
    });
    
    html += '</div>';
    
    $('#dateRangeContent').html(html);
    $('#dateRangeInfo').show();
}

// 填充年度选择
function populateYearSelect(yearDistribution) {
    const yearSelect = $('#yearSelect');
    yearSelect.empty().append('<option value="">请选择年度</option>');
    
    Object.keys(yearDistribution).sort().forEach(year => {
        yearSelect.append(`<option value="${year}">${year}年 (${yearDistribution[year].toLocaleString()}条记录)</option>`);
    });
}

// 预览清理
function previewCleanup() {
    const tableName = selectedTable?.name;
    const dateColumn = $('#dateColumnSelect').val();
    const cleanupType = $('#cleanupTypeSelect').val();
    const year = $('#yearSelect').val();
    const quarter = $('#quarterSelect').val();
    const month = $('#monthSelect').val();
    
    if (!tableName || !dateColumn || !cleanupType || !year) {
        showError('请完整填写清理配置');
        return;
    }
    
    const data = {
        table_name: tableName,
        date_column: dateColumn,
        cleanup_type: cleanupType,
        year: parseInt(year),
        quarter: quarter ? parseInt(quarter) : null,
        month: month ? parseInt(month) : null
    };
    
    $.ajax({
        url: '{{ url_for("data_update.preview_cleanup") }}',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(data),
        success: function(response) {
            if (response.success) {
                renderPreviewResult(response.preview, data);
            } else {
                showError('预览失败: ' + response.message);
            }
        },
        error: function() {
            showError('请求失败，请检查网络连接');
        }
    });
}

// 渲染预览结果
function renderPreviewResult(preview, config) {
    let timeDescription = `${config.year}年`;
    if (config.cleanup_type === 'quarterly' && config.quarter) {
        timeDescription = `${config.year}年第${config.quarter}季度`;
    } else if (config.cleanup_type === 'monthly' && config.month) {
        timeDescription = `${config.year}年${config.month}月`;
    }
    
    const html = `
        <div class="alert alert-warning">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>清理预览</h6>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>表名：</strong>${config.table_name}</p>
                    <p><strong>日期列：</strong>${config.date_column}</p>
                    <p><strong>清理范围：</strong>${timeDescription}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>当前总记录数：</strong>${preview.total_count.toLocaleString()}</p>
                    <p><strong>将删除记录数：</strong><span class="text-danger">${preview.delete_count.toLocaleString()}</span></p>
                    <p><strong>删除后剩余：</strong>${preview.remaining_count.toLocaleString()} (${(100 - preview.delete_percentage).toFixed(2)}%)</p>
                </div>
            </div>
            <hr>
            <div class="d-flex justify-content-between">
                <div>
                    <strong>删除比例：</strong>
                    <span class="badge ${preview.delete_percentage > 50 ? 'bg-danger' : preview.delete_percentage > 20 ? 'bg-warning' : 'bg-info'}">${preview.delete_percentage}%</span>
                </div>
                <div>
                    <button class="btn btn-danger me-2" onclick="executeCleanup()">
                        <i class="fas fa-trash me-2"></i>确认执行清理
                    </button>
                    <button class="btn btn-secondary" onclick="hidePreview()">
                        <i class="fas fa-times me-2"></i>取消
                    </button>
                </div>
            </div>
        </div>
    `;
    
    $('#previewContent').html(html);
    $('#previewResultArea').show();
    
    // 滚动到预览区域
    $('html, body').animate({
        scrollTop: $('#previewResultArea').offset().top - 100
    }, 500);
}

// 执行清理
function executeCleanup() {
    if (!confirm('确认执行数据清理操作？此操作不可逆！')) {
        return;
    }
    
    const tableName = selectedTable?.name;
    const dateColumn = $('#dateColumnSelect').val();
    const cleanupType = $('#cleanupTypeSelect').val();
    const year = $('#yearSelect').val();
    const quarter = $('#quarterSelect').val();
    const month = $('#monthSelect').val();
    
    const data = {
        table_name: tableName,
        date_column: dateColumn,
        cleanup_type: cleanupType,
        year: parseInt(year),
        quarter: quarter ? parseInt(quarter) : null,
        month: month ? parseInt(month) : null
    };
    
    // 显示执行中状态
    $('#previewContent').html(`
        <div class="text-center">
            <div class="spinner-border text-danger" role="status">
                <span class="visually-hidden">执行中...</span>
            </div>
            <p class="mt-2">正在执行数据清理，请稍候...</p>
        </div>
    `);
    
    $.ajax({
        url: '{{ url_for("data_update.execute_cleanup") }}',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(data),
        success: function(response) {
            if (response.success) {
                renderExecuteResult(response.result);
                // 重新加载表信息
                loadTables();
            } else {
                showError('执行清理失败: ' + response.message);
            }
        },
        error: function() {
            showError('请求失败，请检查网络连接');
        }
    });
}

// 渲染执行结果
function renderExecuteResult(result) {
    const html = `
        <div class="alert alert-success">
            <h6><i class="fas fa-check-circle me-2"></i>清理完成</h6>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>表名：</strong>${result.table_name}</p>
                    <p><strong>清理范围：</strong>${result.cleanup_description}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>清理前记录数：</strong>${result.records_before.toLocaleString()}</p>
                    <p><strong>删除记录数：</strong><span class="text-danger">${result.records_deleted.toLocaleString()}</span></p>
                    <p><strong>清理后记录数：</strong>${result.records_after.toLocaleString()}</p>
                </div>
            </div>
            <hr>
            <button class="btn btn-primary" onclick="resetAll()">
                <i class="fas fa-plus me-2"></i>继续清理其他数据
            </button>
        </div>
    `;
    
    $('#executeContent').html(html);
    $('#executeResultArea').show();
    $('#previewResultArea').hide();
    
    // 滚动到结果区域
    $('html, body').animate({
        scrollTop: $('#executeResultArea').offset().top - 100
    }, 500);
}

// 重置清理配置
function resetCleanupConfig() {
    $('#dateColumnSelect').val('');
    $('#cleanupTypeSelect').val('');
    $('#yearSelect').val('');
    $('#quarterSelect').val('');
    $('#monthSelect').val('');
    $('#dateRangeInfo').hide();
    $('#timeSelectionArea').hide();
    $('#quarterSelectArea').hide();
    $('#monthSelectArea').hide();
    hidePreview();
}

// 重置所有
function resetAll() {
    selectedTable = null;
    $('#cleanupConfigArea').hide();
    $('#previewResultArea').hide();
    $('#executeResultArea').hide();
    resetCleanupConfig();
}

// 隐藏预览
function hidePreview() {
    $('#previewResultArea').hide();
}

// 显示错误
function showError(message) {
    const errorHtml = `
        <div class="alert alert-danger">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>操作失败</h6>
            <p class="mb-0">${message}</p>
        </div>
    `;
    
    // 根据当前状态显示错误
    if ($('#previewResultArea').is(':visible')) {
        $('#previewContent').html(errorHtml);
    } else if ($('#executeResultArea').is(':visible')) {
        $('#executeContent').html(errorHtml);
    } else {
        $('#tablesArea').html(errorHtml);
    }
}

// 表卡片悬停效果
$(document).on('mouseenter', '.table-card:not(.border-secondary)', function() {
    $(this).addClass('border-success shadow');
});

$(document).on('mouseleave', '.table-card:not(.border-secondary)', function() {
    $(this).removeClass('border-success shadow');
});
</script>
{% endblock %}
