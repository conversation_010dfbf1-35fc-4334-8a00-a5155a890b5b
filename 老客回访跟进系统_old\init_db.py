"""
数据库初始化脚本
"""
import os
import sys
from datetime import datetime
from werkzeug.security import generate_password_hash

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import *
from config import get_config

def init_roles_and_permissions():
    """初始化角色和权限"""
    print("初始化角色和权限...")
    
    # 创建角色
    roles_data = [
        {'role_name': '系统管理员', 'role_code': 'ADMIN', 'description': '拥有系统所有功能的操作权限'},
        {'role_name': '经营院长', 'role_code': 'GENERAL_MANAGER', 'description': '可查看所有登记信息和统计报表，但不能进行登记和修改操作'},
        {'role_name': '部门主管', 'role_code': 'DEPARTMENT_MANAGER', 'description': '可查看其所属部门下所有员工登记的信息和相关统计报表'},
        {'role_name': '网络咨询', 'role_code': 'NETWORK_CONSULTANT', 'description': '负责登记客户，只能查看和修改自己名下的登记信息'},
        {'role_name': '现场咨询', 'role_code': 'FIELD_CONSULTANT', 'description': '负责跟进客户，只能查看分配给自己的客户信息，并填写跟进情况'}
    ]
    
    for role_data in roles_data:
        existing_role = Role.query.filter_by(role_code=role_data['role_code']).first()
        if not existing_role:
            role = Role(**role_data)
            db.session.add(role)
            print(f"创建角色: {role_data['role_name']}")
    
    # 创建权限
    permissions_data = [
        # 系统管理权限
        {'permission_name': '用户管理', 'permission_code': 'USER_MANAGE', 'module_name': 'SYSTEM', 'description': '创建、编辑、删除用户账号'},
        {'permission_name': '角色管理', 'permission_code': 'ROLE_MANAGE', 'module_name': 'SYSTEM', 'description': '管理系统角色和权限'},
        {'permission_name': '系统配置', 'permission_code': 'SYSTEM_CONFIG', 'module_name': 'SYSTEM', 'description': '修改系统配置参数'},
        
        # 渠道管理权限
        {'permission_name': '渠道查看', 'permission_code': 'CHANNEL_VIEW', 'module_name': 'CHANNEL', 'description': '查看渠道列表'},
        {'permission_name': '渠道管理', 'permission_code': 'CHANNEL_MANAGE', 'module_name': 'CHANNEL', 'description': '添加、修改、删除渠道'},
        {'permission_name': '渠道导入', 'permission_code': 'CHANNEL_IMPORT', 'module_name': 'CHANNEL', 'description': '批量导入渠道数据'},
        
        # 客户登记权限
        {'permission_name': '客户登记', 'permission_code': 'CUSTOMER_REGISTER', 'module_name': 'CUSTOMER', 'description': '登记新客户信息'},
        {'permission_name': '客户查看', 'permission_code': 'CUSTOMER_VIEW', 'module_name': 'CUSTOMER', 'description': '查看客户登记信息'},
        {'permission_name': '客户修改', 'permission_code': 'CUSTOMER_EDIT', 'module_name': 'CUSTOMER', 'description': '修改客户登记信息'},
        
        # 跟进管理权限
        {'permission_name': '跟进添加', 'permission_code': 'FOLLOW_UP_ADD', 'module_name': 'FOLLOW_UP', 'description': '添加客户跟进记录'},
        {'permission_name': '跟进查看', 'permission_code': 'FOLLOW_UP_VIEW', 'module_name': 'FOLLOW_UP', 'description': '查看跟进记录'},
        
        # 数据管理权限
        {'permission_name': '映射管理', 'permission_code': 'MAPPING_MANAGE', 'module_name': 'DATA', 'description': '管理现场小组映射关系'},
        {'permission_name': '消费导入', 'permission_code': 'CONSUMPTION_IMPORT', 'module_name': 'DATA', 'description': '导入客户消费数据'},
        
        # 统计报表权限
        {'permission_name': '报表查看', 'permission_code': 'REPORT_VIEW', 'module_name': 'REPORT', 'description': '查看统计报表'},
        {'permission_name': '报表导出', 'permission_code': 'REPORT_EXPORT', 'module_name': 'REPORT', 'description': '导出报表数据'},
        
        # 审计日志权限
        {'permission_name': '审计日志查看', 'permission_code': 'AUDIT_LOG_VIEW', 'module_name': 'SYSTEM', 'description': '查看系统操作审计日志'}
    ]
    
    for perm_data in permissions_data:
        existing_perm = Permission.query.filter_by(permission_code=perm_data['permission_code']).first()
        if not existing_perm:
            permission = Permission(**perm_data)
            db.session.add(permission)
            print(f"创建权限: {perm_data['permission_name']}")
    
    db.session.commit()
    
    # 分配权限给角色
    assign_permissions_to_roles()

def assign_permissions_to_roles():
    """分配权限给角色"""
    print("分配权限给角色...")
    
    # 获取所有角色和权限
    admin_role = Role.query.filter_by(role_code='ADMIN').first()
    general_manager_role = Role.query.filter_by(role_code='GENERAL_MANAGER').first()
    dept_manager_role = Role.query.filter_by(role_code='DEPARTMENT_MANAGER').first()
    network_consultant_role = Role.query.filter_by(role_code='NETWORK_CONSULTANT').first()
    field_consultant_role = Role.query.filter_by(role_code='FIELD_CONSULTANT').first()
    
    all_permissions = Permission.query.all()
    
    # 管理员拥有所有权限
    if admin_role:
        for permission in all_permissions:
            existing = RolePermission.query.filter_by(
                role_id=admin_role.id,
                permission_id=permission.id
            ).first()
            if not existing:
                role_perm = RolePermission(role_id=admin_role.id, permission_id=permission.id)
                db.session.add(role_perm)
    
    # 经营院长权限（只读）
    if general_manager_role:
        readonly_permissions = ['CHANNEL_VIEW', 'CUSTOMER_VIEW', 'FOLLOW_UP_VIEW', 'REPORT_VIEW', 'REPORT_EXPORT']
        for perm_code in readonly_permissions:
            permission = Permission.query.filter_by(permission_code=perm_code).first()
            if permission:
                existing = RolePermission.query.filter_by(
                    role_id=general_manager_role.id,
                    permission_id=permission.id
                ).first()
                if not existing:
                    role_perm = RolePermission(role_id=general_manager_role.id, permission_id=permission.id)
                    db.session.add(role_perm)
    
    # 部门主管权限
    if dept_manager_role:
        dept_permissions = ['CHANNEL_VIEW', 'CUSTOMER_VIEW', 'FOLLOW_UP_VIEW', 'REPORT_VIEW', 'REPORT_EXPORT']
        for perm_code in dept_permissions:
            permission = Permission.query.filter_by(permission_code=perm_code).first()
            if permission:
                existing = RolePermission.query.filter_by(
                    role_id=dept_manager_role.id,
                    permission_id=permission.id
                ).first()
                if not existing:
                    role_perm = RolePermission(role_id=dept_manager_role.id, permission_id=permission.id)
                    db.session.add(role_perm)
    
    # 网络咨询权限
    if network_consultant_role:
        network_permissions = ['CHANNEL_VIEW', 'CUSTOMER_REGISTER', 'CUSTOMER_VIEW', 'CUSTOMER_EDIT', 'FOLLOW_UP_VIEW']
        for perm_code in network_permissions:
            permission = Permission.query.filter_by(permission_code=perm_code).first()
            if permission:
                existing = RolePermission.query.filter_by(
                    role_id=network_consultant_role.id,
                    permission_id=permission.id
                ).first()
                if not existing:
                    role_perm = RolePermission(role_id=network_consultant_role.id, permission_id=permission.id)
                    db.session.add(role_perm)
    
    # 现场咨询权限
    if field_consultant_role:
        field_permissions = ['CUSTOMER_VIEW', 'FOLLOW_UP_ADD', 'FOLLOW_UP_VIEW']
        for perm_code in field_permissions:
            permission = Permission.query.filter_by(permission_code=perm_code).first()
            if permission:
                existing = RolePermission.query.filter_by(
                    role_id=field_consultant_role.id,
                    permission_id=permission.id
                ).first()
                if not existing:
                    role_perm = RolePermission(role_id=field_consultant_role.id, permission_id=permission.id)
                    db.session.add(role_perm)
    
    db.session.commit()

def init_departments():
    """初始化部门数据"""
    print("初始化部门数据...")
    
    # 创建默认部门
    departments_data = [
        {'name': '管理部', 'code': 'ADMIN', 'description': '系统管理部门', 'sort_order': 1},
        {'name': '网络咨询部', 'code': 'NETWORK', 'description': '网络咨询部门', 'sort_order': 2},
        {'name': '现场咨询部', 'code': 'FIELD', 'description': '现场咨询部门', 'sort_order': 3},
        {'name': '技术部', 'code': 'TECH', 'description': '技术支持部门', 'sort_order': 4},
        {'name': '市场部', 'code': 'MARKET', 'description': '市场推广部门', 'sort_order': 5},
        {'name': '客服部', 'code': 'SERVICE', 'description': '客户服务部门', 'sort_order': 6}
    ]
    
    for dept_data in departments_data:
        existing_dept = Department.query.filter_by(department_code=dept_data['code']).first()
        if not existing_dept:
            department = Department(
                department_name=dept_data['name'],
                department_code=dept_data['code'],
                description=dept_data['description'],
                sort_order=dept_data['sort_order']
            )
            db.session.add(department)
            print(f"创建部门: {dept_data['name']}")
    
    db.session.commit()

def init_system_config():
    """初始化系统配置"""
    print("初始化系统配置...")
    
    config_data = [
        {'config_key': 'card_number_max_length', 'config_value': '10', 'config_description': '卡号最大长度', 'config_type': 'integer'},
        {'config_key': 'consultation_content_max_length', 'config_value': '500', 'config_description': '咨询内容最大长度', 'config_type': 'integer'},
        {'config_key': 'follow_up_content_max_length', 'config_value': '500', 'config_description': '跟进内容最大长度', 'config_type': 'integer'},
        {'config_key': 'session_timeout_minutes', 'config_value': '30', 'config_description': '会话超时时间（分钟）', 'config_type': 'integer'},
        {'config_key': 'password_min_length', 'config_value': '8', 'config_description': '密码最小长度', 'config_type': 'integer'},
        {'config_key': 'system_name', 'config_value': '老客回访与跟进系统', 'config_description': '系统名称', 'config_type': 'string'},
        {'config_key': 'items_per_page', 'config_value': '20', 'config_description': '每页显示条数', 'config_type': 'integer'},
        {'config_key': 'max_file_size_mb', 'config_value': '16', 'config_description': '最大文件上传大小（MB）', 'config_type': 'integer'}
    ]
    
    for config_item in config_data:
        existing_config = SystemConfig.query.filter_by(config_key=config_item['config_key']).first()
        if not existing_config:
            system_config = SystemConfig(**config_item)
            db.session.add(system_config)
            print(f"创建系统配置: {config_item['config_key']}")
    
    db.session.commit()

def create_default_admin():
    """创建默认管理员账号"""
    print("检查默认管理员账号...")
    
    admin_role = Role.query.filter_by(role_code='ADMIN').first()
    if not admin_role:
        print("错误: 管理员角色不存在")
        return False
    
    # 获取管理部门
    admin_dept = Department.query.filter_by(department_code='ADMIN').first()
    if not admin_dept:
        print("错误: 管理部门不存在")
        return False
    
    # 检查是否已有管理员
    existing_admin = User.query.filter_by(role_id=admin_role.id, is_active=True).first()
    if existing_admin:
        print(f"管理员账号已存在: {existing_admin.username}")
        return True
    
    # 创建默认管理员
    admin_user = User(
        username='admin',
        password_hash=generate_password_hash('admin123'),
        real_name='系统管理员',
        department_id=admin_dept.id,
        role_id=admin_role.id,
        is_active=True
    )
    
    db.session.add(admin_user)
    db.session.commit()
    
    print("创建默认管理员账号: admin / admin123")
    print("请在首次登录后立即修改密码！")
    return True

def init_sample_data():
    """初始化示例数据"""
    print("初始化示例数据...")
    
    # 创建示例渠道
    sample_channels = [
        {'channel_category': '线上推广', 'channel_name': '百度推广'},
        {'channel_category': '线上推广', 'channel_name': '微信朋友圈'},
        {'channel_category': '线下活动', 'channel_name': '商场活动'},
        {'channel_category': '老客推荐', 'channel_name': '老客转介绍'}
    ]
    
    admin_user = User.query.filter_by(username='admin').first()
    if admin_user:
        for channel_data in sample_channels:
            existing_channel = Channel.query.filter_by(
                channel_category=channel_data['channel_category'],
                channel_name=channel_data['channel_name']
            ).first()
            if not existing_channel:
                channel = Channel(
                    channel_category=channel_data['channel_category'],
                    channel_name=channel_data['channel_name'],
                    created_by=admin_user.id
                )
                db.session.add(channel)
                print(f"创建示例渠道: {channel_data['channel_name']}")
    
    db.session.commit()

def main():
    """主函数"""
    print("=== 老客回访与跟进系统数据库初始化 ===")
    
    # 创建应用
    config_class = get_config()
    app = create_app(config_class)
    
    with app.app_context():
        try:
            # 创建所有表
            print("创建数据库表...")
            db.create_all()
            
            # 初始化基础数据
            init_roles_and_permissions()
            init_departments()
            init_system_config()
            create_default_admin()
            init_sample_data()
            
            print("\n数据库初始化完成！")
            print("默认管理员账号: admin")
            print("默认密码: admin123")
            print("请在首次登录后立即修改密码！")
            
        except Exception as e:
            print(f"数据库初始化失败: {str(e)}")
            db.session.rollback()
            return False
    
    return True

if __name__ == '__main__':
    main()
