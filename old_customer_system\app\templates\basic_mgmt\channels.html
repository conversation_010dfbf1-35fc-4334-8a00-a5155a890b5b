{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">渠道管理</h1>
    {% if current_user.has_permission('manage_channels') %}
    <div class="btn-group">
        <a href="{{ url_for('basic_mgmt.create_channel') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>添加渠道
        </a>
        <button type="button" class="btn btn-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown">
            <span class="visually-hidden">Toggle Dropdown</span>
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="{{ url_for('basic_mgmt.import_channels') }}">
                <i class="fas fa-upload me-2"></i>批量导入
            </a></li>
            <li><a class="dropdown-item" href="{{ url_for('basic_mgmt.download_channel_template') }}">
                <i class="fas fa-download me-2"></i>下载模板
            </a></li>
        </ul>
    </div>
    {% endif %}
</div>

<!-- 搜索表单 -->
<div class="card mb-3">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                {{ search_form.query.label(class="form-label") }}
                {{ search_form.query(class="form-control", placeholder="渠道名称或简码") }}
            </div>
            <div class="col-md-2">
                {{ search_form.category.label(class="form-label") }}
                {{ search_form.category(class="form-select") }}
            </div>
            <div class="col-md-2">
                {{ search_form.is_active.label(class="form-label") }}
                {{ search_form.is_active(class="form-select") }}
            </div>
            <div class="col-md-5 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-search me-1"></i>搜索
                </button>
                <a href="{{ url_for('basic_mgmt.channels') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-undo me-1"></i>重置
                </a>
            </div>
        </form>
    </div>
</div>

<!-- 渠道列表 -->
<div class="card">
    <div class="card-body">
        {% if channels %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>渠道名称</th>
                            <th>分类</th>
                            <th>简码</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            {% if current_user.has_permission('manage_channels') %}
                            <th>操作</th>
                            {% endif %}
                        </tr>
                    </thead>
                    <tbody>
                        {% for channel in channels %}
                        <tr>
                            <td><strong>{{ channel.name }}</strong></td>
                            <td>
                                {% if channel.category %}
                                    <span class="badge bg-secondary">{{ channel.category }}</span>
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>{{ channel.simple_code or '-' }}</td>
                            <td>
                                {% if channel.is_active %}
                                    <span class="badge bg-success">激活</span>
                                {% else %}
                                    <span class="badge bg-secondary">停用</span>
                                {% endif %}
                            </td>
                            <td>{{ channel.created_at.strftime('%Y-%m-%d') }}</td>
                            {% if current_user.has_permission('manage_channels') %}
                            <td>
                                <button type="button" class="btn btn-outline-{{ 'secondary' if channel.is_active else 'success' }} btn-sm" 
                                        onclick="toggleChannelStatus({{ channel.id }}, '{{ channel.name }}', {{ channel.is_active|lower }})" 
                                        title="{{ '停用' if channel.is_active else '激活' }}">
                                    <i class="fas fa-{{ 'ban' if channel.is_active else 'check' }}"></i>
                                </button>
                            </td>
                            {% endif %}
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-stream fa-3x text-muted mb-3"></i>
                <p class="text-muted">暂无渠道数据</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function toggleChannelStatus(channelId, channelName, isActive) {
    const action = isActive ? '停用' : '激活';
    if (confirm(`确定要${action}渠道 "${channelName}" 吗？`)) {
        fetch(`/basic/channels/${channelId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('操作失败: ' + data.message);
            }
        })
        .catch(error => {
            alert('操作失败: ' + error);
        });
    }
}
</script>
{% endblock %}