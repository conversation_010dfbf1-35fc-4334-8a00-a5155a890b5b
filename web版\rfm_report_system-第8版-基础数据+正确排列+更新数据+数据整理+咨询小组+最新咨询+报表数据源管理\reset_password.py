#!/usr/bin/env python3
"""
密码重置脚本
用于管理员重置用户密码
"""

import sys
from app import create_app
from app.models import User, db

def reset_password():
    """重置用户密码"""
    app = create_app()
    
    with app.app_context():
        # 获取所有用户
        users = User.query.all()
        
        if not users:
            print("系统中没有用户")
            return
        
        print("现有用户列表：")
        for i, user in enumerate(users, 1):
            print(f"{i}. {user.username} (最后登录: {user.last_login or '从未登录'})")
        
        # 选择用户
        while True:
            try:
                choice = int(input("\n请选择要重置密码的用户编号: "))
                if 1 <= choice <= len(users):
                    selected_user = users[choice - 1]
                    break
                else:
                    print("无效的选择，请重新输入")
            except ValueError:
                print("请输入有效的数字")
        
        # 输入新密码
        while True:
            new_password = input(f"\n请输入用户 '{selected_user.username}' 的新密码: ").strip()
            if len(new_password) >= 6:
                break
            print("密码长度至少6位")
        
        # 确认重置
        confirm = input(f"\n确定要重置用户 '{selected_user.username}' 的密码吗？(y/N): ").strip().lower()
        if confirm != 'y':
            print("操作已取消")
            return
        
        # 重置密码
        selected_user.set_password(new_password)
        db.session.commit()
        
        print(f"\n用户 '{selected_user.username}' 的密码已成功重置")

if __name__ == '__main__':
    try:
        reset_password()
    except KeyboardInterrupt:
        print("\n操作已取消")
        sys.exit(0)
    except Exception as e:
        print(f"\n重置密码失败: {e}")
        sys.exit(1)
