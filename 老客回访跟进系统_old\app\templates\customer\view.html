{% extends "base.html" %}

{% block title %}客户详情 - 老客回访与跟进系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-user"></i> 客户详情</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('customer.index') }}">客户管理</a></li>
                <li class="breadcrumb-item active">客户详情</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> 基本信息</h5>
            </div>
            <div class="card-body">
                <table class="table">
                    <tr>
                        <th width="150">客户卡号</th>
                        <td><strong class="text-primary">{{ registration.card_number }}</strong></td>
                    </tr>
                    <tr>
                        <th>登记人员</th>
                        <td>{{ registration.registrar.real_name }} ({{ registration.registrar.department }})</td>
                    </tr>
                    <tr>
                        <th>现场顾问</th>
                        <td>{{ registration.assigned_consultant.real_name }} ({{ registration.assigned_consultant.department }})</td>
                    </tr>
                    <tr>
                        <th>激活渠道</th>
                        <td>
                            <span class="badge bg-info">{{ registration.activation_channel.channel_category }}</span>
                            {{ registration.activation_channel.channel_name }}
                        </td>
                    </tr>
                    <tr>
                        <th>咨询内容</th>
                        <td>{{ registration.consultation_content }}</td>
                    </tr>
                    <tr>
                        <th>最近来院</th>
                        <td>{{ registration.last_visit_date.strftime('%Y-%m-%d') }}</td>
                    </tr>
                    <tr>
                        <th>登记时间</th>
                        <td>{{ registration.registration_time.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                    </tr>
                </table>
                
                <div class="mt-3">
                    {% if current_user.has_permission('FOLLOW_UP_ADD') and registration.assigned_consultant_id == current_user.id %}
                    <a href="{{ url_for('customer.add_follow_up', id=registration.id) }}" class="btn btn-success">
                        <i class="fas fa-plus"></i> 添加跟进
                    </a>
                    {% endif %}
                    <a href="{{ url_for('customer.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> 返回列表
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-bar"></i> 统计信息</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ registration.get_follow_up_count() }}</h4>
                        <p class="text-muted">跟进次数</p>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">
                            {% if registration.has_recent_consumption() %}1{% else %}0{% endif %}
                        </h4>
                        <p class="text-muted">近期消费</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-comments"></i> 跟进记录</h5>
                    <span class="badge bg-primary">共 {{ follow_ups|length }} 条记录</span>
                </div>
            </div>
            <div class="card-body">
                {% if follow_ups %}
                <div class="timeline">
                    {% for follow_up in follow_ups %}
                    <div class="timeline-item mb-4">
                        <div class="row">
                            <div class="col-md-2 text-end">
                                <small class="text-muted">
                                    {{ follow_up.follow_up_time.strftime('%Y-%m-%d') }}<br>
                                    {{ follow_up.follow_up_time.strftime('%H:%M') }}
                                </small>
                            </div>
                            <div class="col-md-1 text-center">
                                <div class="timeline-marker bg-primary"></div>
                            </div>
                            <div class="col-md-9">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="mb-0">
                                                <i class="fas fa-user"></i> {{ follow_up.consultant.real_name }}
                                                <small class="text-muted">({{ follow_up.consultant.department }})</small>
                                            </h6>
                                            <small class="text-muted">
                                                {{ follow_up.follow_up_time.strftime('%Y-%m-%d %H:%M') }}
                                            </small>
                                        </div>
                                        <p class="mb-0">{{ follow_up.follow_up_content }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-comments fa-3x mb-3"></i>
                    <p>暂无跟进记录</p>
                    {% if current_user.has_permission('FOLLOW_UP_ADD') and registration.assigned_consultant_id == current_user.id %}
                    <a href="{{ url_for('customer.add_follow_up', id=registration.id) }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> 添加第一条跟进记录
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.timeline-marker {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin: 0 auto;
}

.timeline-item:not(:last-child) .timeline-marker::after {
    content: '';
    position: absolute;
    width: 2px;
    height: 60px;
    background-color: #dee2e6;
    left: 50%;
    transform: translateX(-50%);
    margin-top: 12px;
}
</style>
{% endblock %}
