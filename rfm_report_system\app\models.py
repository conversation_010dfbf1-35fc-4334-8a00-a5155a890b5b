from datetime import datetime, timezone
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
import bcrypt
import json

# 创建db实例，将在app/__init__.py中初始化
db = SQLAlchemy()


def get_current_time():
    """获取当前时间（UTC时间，但会在显示时转换为本地时间）"""
    return datetime.utcnow()

class User(UserMixin, db.Model):
    """用户模型"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    salt = db.Column(db.String(32), nullable=False)
    created_at = db.Column(db.DateTime, default=get_current_time)
    last_login = db.Column(db.DateTime)
    is_active = db.Column(db.<PERSON>, default=True)
    is_admin = db.Column(db.<PERSON>, default=False)
    
    def set_password(self, password):
        """设置密码（加盐哈希）"""
        # 生成盐值
        salt = bcrypt.gensalt()
        self.salt = salt.decode('utf-8')
        # 生成密码哈希
        password_hash = bcrypt.hashpw(password.encode('utf-8'), salt)
        self.password_hash = password_hash.decode('utf-8')
    
    def check_password(self, password):
        """验证密码"""
        return bcrypt.checkpw(
            password.encode('utf-8'), 
            self.password_hash.encode('utf-8')
        )
    
    def update_last_login(self):
        """更新最后登录时间"""
        self.last_login = get_current_time()
        db.session.commit()
    
    def __repr__(self):
        return f'<User {self.username}>'

class DatabaseConfig(db.Model):
    """数据库配置模型"""
    __tablename__ = 'database_configs'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    host = db.Column(db.String(255), nullable=False)
    port = db.Column(db.Integer, nullable=False, default=3306)
    username = db.Column(db.String(100), nullable=False)
    password_encrypted = db.Column(db.Text, nullable=False)  # 加密存储
    database_name = db.Column(db.String(100), nullable=False)
    is_active = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # 关联关系
    creator = db.relationship('User', backref='database_configs')
    
    def set_password(self, password):
        """加密存储密码"""
        from cryptography.fernet import Fernet
        import base64
        import os
        
        # 使用应用密钥生成加密密钥
        key = base64.urlsafe_b64encode(os.environ.get('SECRET_KEY', 'default-key').ljust(32)[:32].encode())
        f = Fernet(key)
        self.password_encrypted = f.encrypt(password.encode()).decode()
    
    def get_password(self):
        """解密获取密码"""
        from cryptography.fernet import Fernet
        import base64
        import os
        
        key = base64.urlsafe_b64encode(os.environ.get('SECRET_KEY', 'default-key').ljust(32)[:32].encode())
        f = Fernet(key)
        return f.decrypt(self.password_encrypted.encode()).decode()
    
    def get_connection_string(self):
        """获取数据库连接字符串"""
        password = self.get_password()
        return f"mysql+pymysql://{self.username}:{password}@{self.host}:{self.port}/{self.database_name}?charset=utf8mb4"
    
    @classmethod
    def get_active_config(cls):
        """获取当前激活的数据库配置"""
        return cls.query.filter_by(is_active=True).first()
    
    def activate(self):
        """激活此配置（同时取消其他配置的激活状态）"""
        # 取消所有配置的激活状态
        DatabaseConfig.query.update({'is_active': False})
        # 激活当前配置
        self.is_active = True
        db.session.commit()
    
    def __repr__(self):
        return f'<DatabaseConfig {self.name}>'

class DataProcessingTask(db.Model):
    """数据处理任务模型"""
    __tablename__ = 'data_processing_tasks'
    
    id = db.Column(db.Integer, primary_key=True)
    task_name = db.Column(db.String(200), nullable=False)
    task_type = db.Column(db.String(50), nullable=False)  # 'forward' 或 'result'
    parameters = db.Column(db.Text)  # JSON格式存储任务参数
    status = db.Column(db.String(50), default='pending')  # pending, running, completed, failed
    progress = db.Column(db.Integer, default=0)  # 进度百分比
    result_summary = db.Column(db.Text)  # JSON格式存储结果摘要
    error_message = db.Column(db.Text)
    output_file_path = db.Column(db.String(500))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    started_at = db.Column(db.DateTime)
    completed_at = db.Column(db.DateTime)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # 关联关系
    creator = db.relationship('User', backref='processing_tasks')
    
    def set_parameters(self, params_dict):
        """设置任务参数"""
        self.parameters = json.dumps(params_dict, ensure_ascii=False)
    
    def get_parameters(self):
        """获取任务参数"""
        if self.parameters:
            return json.loads(self.parameters)
        return {}
    
    def set_result_summary(self, summary_dict):
        """设置结果摘要"""
        self.result_summary = json.dumps(summary_dict, ensure_ascii=False)
    
    def get_result_summary(self):
        """获取结果摘要"""
        if self.result_summary:
            return json.loads(self.result_summary)
        return {}
    
    def start_task(self):
        """开始任务"""
        self.status = 'running'
        self.started_at = get_current_time()
        db.session.commit()
    
    def complete_task(self, output_file_path=None, summary=None):
        """完成任务"""
        self.status = 'completed'
        self.progress = 100
        self.completed_at = get_current_time()
        if output_file_path:
            self.output_file_path = output_file_path
        if summary:
            self.set_result_summary(summary)
        db.session.commit()

    def fail_task(self, error_message):
        """任务失败"""
        self.status = 'failed'
        self.error_message = error_message
        self.completed_at = get_current_time()
        db.session.commit()
    
    def update_progress(self, progress, message=None, step_name=None):
        """更新进度"""
        self.progress = min(100, max(0, progress))

        # 添加进度日志
        if step_name:
            self.add_progress_log(step_name, progress, message)

        db.session.commit()

    def add_progress_log(self, step_name, progress, message=None):
        """添加进度日志"""
        current_logs = self.get_progress_logs()

        log_entry = {
            'timestamp': get_current_time().isoformat(),
            'step_name': step_name,
            'progress': progress,
            'message': message or f"完成 {step_name}",
            'status': 'completed' if progress == 100 else 'running'
        }

        current_logs.append(log_entry)

        # 更新结果摘要中的进度日志
        result_summary = self.get_result_summary() or {}
        result_summary['progress_logs'] = current_logs
        self.set_result_summary(result_summary)

    def get_progress_logs(self):
        """获取进度日志"""
        result_summary = self.get_result_summary() or {}
        return result_summary.get('progress_logs', [])
    
    @property
    def duration(self):
        """计算任务执行时长（返回timedelta对象）"""
        if self.started_at and self.completed_at:
            return self.completed_at - self.started_at
        elif self.started_at:
            return get_current_time() - self.started_at
        return None

    @property
    def duration_formatted(self):
        """获取格式化的执行时长"""
        duration = self.duration
        if duration is None:
            return '-'

        total_seconds = int(duration.total_seconds())
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        seconds = total_seconds % 60

        if hours > 0:
            return f"{hours}小时{minutes}分钟{seconds}秒"
        elif minutes > 0:
            return f"{minutes}分钟{seconds}秒"
        else:
            return f"{seconds}秒"
    
    def __repr__(self):
        return f'<DataProcessingTask {self.task_name}>'

class SystemSettings(db.Model):
    """系统设置模型"""
    __tablename__ = 'system_settings'
    
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False)
    value = db.Column(db.Text)
    description = db.Column(db.String(500))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    @classmethod
    def get_setting(cls, key, default=None):
        """获取设置值"""
        setting = cls.query.filter_by(key=key).first()
        return setting.value if setting else default
    
    @classmethod
    def set_setting(cls, key, value, description=None):
        """设置值"""
        setting = cls.query.filter_by(key=key).first()
        if setting:
            setting.value = value
            setting.updated_at = datetime.utcnow()
            if description:
                setting.description = description
        else:
            setting = cls(key=key, value=value, description=description)
            db.session.add(setting)
        db.session.commit()
        return setting


class CategoryMapping(db.Model):
    """品类映射表"""
    __tablename__ = 'category_mappings'

    id = db.Column(db.Integer, primary_key=True)
    mapping_name = db.Column(db.String(100), nullable=False, comment='映射表名称')
    mapping_type = db.Column(db.String(20), nullable=False, comment='映射类型：level1/level2/level3/field_mapping/field_group')
    original_value = db.Column(db.String(200), nullable=False, comment='原始值')
    mapped_value = db.Column(db.String(200), nullable=False, comment='映射值')
    is_active = db.Column(db.Boolean, default=True, comment='是否启用')
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系
    creator = db.relationship('User', backref='category_mappings')

    def __repr__(self):
        return f'<CategoryMapping {self.original_value} -> {self.mapped_value}>'

    @classmethod
    def get_mappings_by_type(cls, mapping_type, mapping_name=None):
        """获取指定类型的映射"""
        query = cls.query.filter_by(mapping_type=mapping_type, is_active=True)
        if mapping_name:
            query = query.filter_by(mapping_name=mapping_name)
        return {item.original_value: item.mapped_value for item in query.all()}

    @classmethod
    def clear_mappings(cls, mapping_name, mapping_type=None):
        """清除指定映射表的数据"""
        query = cls.query.filter_by(mapping_name=mapping_name)
        if mapping_type:
            query = query.filter_by(mapping_type=mapping_type)
        query.delete()
        db.session.commit()


class MappingTemplate(db.Model):
    """映射模板表"""
    __tablename__ = 'mapping_templates'

    id = db.Column(db.Integer, primary_key=True)
    template_name = db.Column(db.String(100), nullable=False, unique=True, comment='模板名称')
    description = db.Column(db.String(500), comment='模板描述')
    file_path = db.Column(db.String(500), comment='Excel文件路径')
    is_active = db.Column(db.Boolean, default=False, comment='是否启用')
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系
    creator = db.relationship('User', backref='mapping_templates')

    def __repr__(self):
        return f'<MappingTemplate {self.template_name}>'

    @classmethod
    def get_active_template(cls):
        """获取当前启用的模板"""
        return cls.query.filter_by(is_active=True).first()

    def activate(self):
        """激活此模板（同时禁用其他模板）"""
        # 禁用所有其他模板
        cls = self.__class__
        cls.query.update({'is_active': False})
        # 启用当前模板
        self.is_active = True
        db.session.commit()
    
    def __repr__(self):
        return f'<MappingTemplate {self.template_name}>'


# 导入RFM报表模型
from app.rfm_reports.models import RFMDataSource, RFMAnalysisData, RFMReportCache
