"""
数据整理管理器
负责按时间维度清理业务表数据
"""

import pandas as pd
from sqlalchemy import create_engine, text, inspect
from typing import Dict, List, Any, Optional, Tuple
import logging
import re
from datetime import datetime


class DataCleanupManager:
    """数据整理管理器"""
    
    def __init__(self, db_engine):
        self.db_engine = db_engine
        self.logger = logging.getLogger(__name__)
    
    def get_business_tables(self) -> List[Dict[str, Any]]:
        """获取所有业务表（排除季度表）"""
        try:
            inspector = inspect(self.db_engine)
            table_names = inspector.get_table_names()
            
            business_tables = []
            
            for table_name in table_names:
                # 跳过季度表
                if self._is_quarterly_table(table_name):
                    continue
                
                try:
                    # 获取表的基本信息
                    with self.db_engine.connect() as conn:
                        count_query = text(f"SELECT COUNT(*) as count FROM `{table_name}`")
                        result = conn.execute(count_query)
                        record_count = result.scalar()
                        
                        # 获取列信息
                        columns_query = text(f"SHOW COLUMNS FROM `{table_name}`")
                        columns_df = pd.read_sql(columns_query, conn)
                        column_names = [str(col) for col in columns_df['Field'].tolist()]
                        
                        # 查找日期列
                        date_columns = self._find_date_columns(column_names)
                        
                        business_tables.append({
                            'name': table_name,
                            'record_count': int(record_count),
                            'column_count': len(column_names),
                            'date_columns': date_columns,
                            'has_date_column': len(date_columns) > 0
                        })
                        
                except Exception as e:
                    self.logger.warning(f"获取表 {table_name} 信息失败: {str(e)}")
                    business_tables.append({
                        'name': table_name,
                        'record_count': 0,
                        'column_count': 0,
                        'date_columns': [],
                        'has_date_column': False,
                        'error': str(e)
                    })
            
            return sorted(business_tables, key=lambda x: x['name'])
            
        except Exception as e:
            self.logger.error(f"获取业务表列表失败: {str(e)}")
            raise Exception(f"获取业务表列表失败: {str(e)}")
    
    def _is_quarterly_table(self, table_name: str) -> bool:
        """判断是否为季度表"""
        quarterly_keywords = ['季度', 'quarterly', 'quarter', 'q1', 'q2', 'q3', 'q4']
        table_name_lower = table_name.lower()
        
        for keyword in quarterly_keywords:
            if keyword in table_name_lower:
                return True
        
        # 检查季度模式，如 2024Q1, 2024_Q2 等
        quarterly_pattern = r'(20\d{2}[_\-]?q[1-4]|q[1-4][_\-]?20\d{2})'
        if re.search(quarterly_pattern, table_name_lower):
            return True
            
        return False
    
    def _find_date_columns(self, column_names: List[str]) -> List[str]:
        """查找表中的日期列"""
        date_keywords = [
            '日期', 'date', '时间', 'time', 
            '创建时间', 'create_time', '更新时间', 'update_time',
            '执行时间', 'execution_time', '来院时间', 'visit_time',
            '消费时间', 'consumption_time', '交易时间', 'transaction_time',
            '开单时间', '完成时间', '就诊时间'
        ]
        
        date_columns = []
        for col in column_names:
            col_lower = col.lower()
            for keyword in date_keywords:
                if keyword in col_lower:
                    date_columns.append(col)
                    break
        
        return date_columns
    
    def get_date_range_info(self, table_name: str, date_column: str) -> Dict[str, Any]:
        """获取表中日期列的范围信息"""
        try:
            with self.db_engine.connect() as conn:
                # 获取日期范围
                range_query = text(f"""
                    SELECT 
                        MIN(`{date_column}`) as min_date,
                        MAX(`{date_column}`) as max_date,
                        COUNT(DISTINCT YEAR(`{date_column}`)) as year_count,
                        COUNT(*) as total_records
                    FROM `{table_name}` 
                    WHERE `{date_column}` IS NOT NULL
                """)
                
                result = pd.read_sql(range_query, conn)
                
                if result.empty:
                    return {'error': '没有找到有效的日期数据'}
                
                row = result.iloc[0]
                
                # 获取年度分布
                year_query = text(f"""
                    SELECT YEAR(`{date_column}`) as year, COUNT(*) as count
                    FROM `{table_name}` 
                    WHERE `{date_column}` IS NOT NULL
                    GROUP BY YEAR(`{date_column}`)
                    ORDER BY year
                """)
                
                year_stats = pd.read_sql(year_query, conn)
                
                return {
                    'min_date': str(row['min_date']) if pd.notna(row['min_date']) else None,
                    'max_date': str(row['max_date']) if pd.notna(row['max_date']) else None,
                    'year_count': int(row['year_count']) if pd.notna(row['year_count']) else 0,
                    'total_records': int(row['total_records']) if pd.notna(row['total_records']) else 0,
                    'year_distribution': {
                        str(int(year)): int(count) 
                        for year, count in zip(year_stats['year'], year_stats['count'])
                        if pd.notna(year) and pd.notna(count)
                    }
                }
                
        except Exception as e:
            self.logger.error(f"获取日期范围信息失败: {str(e)}")
            return {'error': str(e)}
    
    def preview_cleanup(self, table_name: str, date_column: str, cleanup_type: str, 
                       year: int, quarter: int = None, month: int = None) -> Dict[str, Any]:
        """预览清理操作，返回将要删除的记录数"""
        try:
            with self.db_engine.connect() as conn:
                # 构建WHERE条件
                where_condition = f"YEAR(`{date_column}`) = {year}"
                
                if cleanup_type == 'quarterly' and quarter:
                    where_condition += f" AND QUARTER(`{date_column}`) = {quarter}"
                elif cleanup_type == 'monthly' and month:
                    where_condition += f" AND MONTH(`{date_column}`) = {month}"
                
                # 预览查询
                preview_query = text(f"""
                    SELECT COUNT(*) as delete_count
                    FROM `{table_name}` 
                    WHERE {where_condition}
                """)
                
                result = pd.read_sql(preview_query, conn)
                delete_count = int(result.iloc[0]['delete_count']) if not result.empty else 0
                
                # 获取总记录数
                total_query = text(f"SELECT COUNT(*) as total_count FROM `{table_name}`")
                total_result = pd.read_sql(total_query, conn)
                total_count = int(total_result.iloc[0]['total_count']) if not total_result.empty else 0
                
                return {
                    'delete_count': delete_count,
                    'total_count': total_count,
                    'remaining_count': total_count - delete_count,
                    'delete_percentage': round((delete_count / total_count * 100), 2) if total_count > 0 else 0,
                    'where_condition': where_condition
                }
                
        except Exception as e:
            self.logger.error(f"预览清理操作失败: {str(e)}")
            return {'error': str(e)}
    
    def execute_cleanup(self, table_name: str, date_column: str, cleanup_type: str, 
                       year: int, quarter: int = None, month: int = None) -> Tuple[bool, str, Dict[str, Any]]:
        """执行数据清理操作"""
        try:
            with self.db_engine.begin() as conn:
                # 构建WHERE条件
                where_condition = f"YEAR(`{date_column}`) = {year}"
                cleanup_description = f"{year}年"
                
                if cleanup_type == 'quarterly' and quarter:
                    where_condition += f" AND QUARTER(`{date_column}`) = {quarter}"
                    cleanup_description = f"{year}年第{quarter}季度"
                elif cleanup_type == 'monthly' and month:
                    where_condition += f" AND MONTH(`{date_column}`) = {month}"
                    cleanup_description = f"{year}年{month}月"
                
                # 先获取删除前的记录数
                count_before_query = text(f"SELECT COUNT(*) as count FROM `{table_name}`")
                count_before = pd.read_sql(count_before_query, conn).iloc[0]['count']
                
                # 获取将要删除的记录数
                delete_count_query = text(f"""
                    SELECT COUNT(*) as count
                    FROM `{table_name}` 
                    WHERE {where_condition}
                """)
                delete_count = pd.read_sql(delete_count_query, conn).iloc[0]['count']
                
                # 执行删除操作
                delete_query = text(f"""
                    DELETE FROM `{table_name}` 
                    WHERE {where_condition}
                """)
                
                result = conn.execute(delete_query)
                
                # 获取删除后的记录数
                count_after_query = text(f"SELECT COUNT(*) as count FROM `{table_name}`")
                count_after = pd.read_sql(count_after_query, conn).iloc[0]['count']
                
                # 记录操作日志
                self.logger.info(f"数据清理完成: 表={table_name}, 条件={cleanup_description}, 删除={delete_count}条记录")
                
                return True, f"数据清理成功", {
                    'table_name': table_name,
                    'cleanup_description': cleanup_description,
                    'records_before': int(count_before),
                    'records_deleted': int(delete_count),
                    'records_after': int(count_after),
                    'where_condition': where_condition
                }
                
        except Exception as e:
            self.logger.error(f"执行数据清理失败: {str(e)}")
            return False, f"数据清理失败: {str(e)}", {}
    
    def get_cleanup_history(self, table_name: str = None) -> List[Dict[str, Any]]:
        """获取清理历史记录（这里可以扩展为从日志表中读取）"""
        # 这里可以扩展为从专门的日志表中读取历史记录
        # 目前返回空列表，后续可以实现
        return []
