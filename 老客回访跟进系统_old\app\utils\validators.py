"""
数据验证工具函数
"""
import re
from datetime import datetime, date
from app.models.system import SystemConfig

def validate_card_number(card_number):
    """验证卡号格式"""
    if not card_number:
        return False, "卡号不能为空"
    
    # 获取系统配置的最大长度
    max_length = SystemConfig.get_config('card_number_max_length', 10)
    
    # 检查是否为纯数字
    if not card_number.isdigit():
        return False, "卡号只能包含数字"
    
    # 检查长度
    if len(card_number) > max_length:
        return False, f"卡号长度不能超过{max_length}位"
    
    if len(card_number) == 0:
        return False, "卡号不能为空"
    
    return True, ""

def validate_content_length(content, content_type="内容"):
    """验证内容长度"""
    if not content:
        return False, f"{content_type}不能为空"
    
    # 获取系统配置的最大长度
    if content_type == "咨询内容":
        max_length = SystemConfig.get_config('consultation_content_max_length', 500)
    elif content_type == "跟进内容":
        max_length = SystemConfig.get_config('follow_up_content_max_length', 500)
    else:
        max_length = 500
    
    if len(content) > max_length:
        return False, f"{content_type}长度不能超过{max_length}字"
    
    return True, ""

def validate_username(username):
    """验证用户名格式"""
    if not username:
        return False, "用户名不能为空"
    
    # 用户名长度限制
    if len(username) < 3:
        return False, "用户名长度不能少于3位"
    
    if len(username) > 50:
        return False, "用户名长度不能超过50位"
    
    # 用户名格式检查（字母、数字、下划线）
    if not re.match(r'^[a-zA-Z0-9_]+$', username):
        return False, "用户名只能包含字母、数字和下划线"
    
    # 不能以数字开头
    if username[0].isdigit():
        return False, "用户名不能以数字开头"
    
    return True, ""

def validate_password(password):
    """验证密码强度"""
    if not password:
        return False, "密码不能为空"
    
    min_length = SystemConfig.get_config('password_min_length', 8)
    
    if len(password) < min_length:
        return False, f"密码长度不能少于{min_length}位"
    
    # 检查是否包含大小写字母、数字
    has_upper = any(c.isupper() for c in password)
    has_lower = any(c.islower() for c in password)
    has_digit = any(c.isdigit() for c in password)
    
    if not (has_upper and has_lower and has_digit):
        return False, "密码必须包含大写字母、小写字母和数字"
    
    return True, ""

def validate_real_name(real_name):
    """验证真实姓名"""
    if not real_name:
        return False, "真实姓名不能为空"
    
    if len(real_name) < 2:
        return False, "真实姓名长度不能少于2位"
    
    if len(real_name) > 100:
        return False, "真实姓名长度不能超过100位"
    
    # 检查是否包含特殊字符（允许中文、英文、空格）
    if not re.match(r'^[\u4e00-\u9fa5a-zA-Z\s]+$', real_name):
        return False, "真实姓名只能包含中文、英文字母和空格"
    
    return True, ""

def validate_department(department):
    """验证部门名称"""
    if not department:
        return False, "部门名称不能为空"
    
    if len(department) > 100:
        return False, "部门名称长度不能超过100位"
    
    return True, ""

def validate_date_range(start_date, end_date):
    """验证日期范围"""
    if start_date and end_date:
        if isinstance(start_date, str):
            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            except ValueError:
                return False, "开始日期格式不正确"
        
        if isinstance(end_date, str):
            try:
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            except ValueError:
                return False, "结束日期格式不正确"
        
        if start_date > end_date:
            return False, "开始日期不能大于结束日期"
        
        # 检查日期范围是否合理（不超过1年）
        if (end_date - start_date).days > 365:
            return False, "日期范围不能超过1年"
    
    return True, ""

def validate_date_format(date_str, date_name="日期"):
    """验证日期格式"""
    if not date_str:
        return False, f"{date_name}不能为空"
    
    try:
        parsed_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        
        # 检查日期是否合理（不能是未来日期）
        if parsed_date > date.today():
            return False, f"{date_name}不能是未来日期"
        
        # 检查日期是否过于久远（不超过10年前）
        from datetime import timedelta
        ten_years_ago = date.today() - timedelta(days=365*10)
        if parsed_date < ten_years_ago:
            return False, f"{date_name}不能超过10年前"
        
        return True, ""
    except ValueError:
        return False, f"{date_name}格式不正确，请使用YYYY-MM-DD格式"

def validate_channel_name(channel_name):
    """验证渠道名称"""
    if not channel_name:
        return False, "渠道名称不能为空"
    
    if len(channel_name) > 200:
        return False, "渠道名称长度不能超过200位"
    
    return True, ""

def validate_channel_category(channel_category):
    """验证渠道分类"""
    if not channel_category:
        return False, "渠道分类不能为空"
    
    if len(channel_category) > 100:
        return False, "渠道分类长度不能超过100位"
    
    return True, ""

def validate_group_name(group_name):
    """验证小组名称"""
    if not group_name:
        return False, "小组名称不能为空"
    
    if len(group_name) > 100:
        return False, "小组名称长度不能超过100位"
    
    return True, ""

def validate_consumption_amount(amount):
    """验证消费金额"""
    if amount is None:
        return True, ""  # 允许为空
    
    try:
        amount = float(amount)
        if amount < 0:
            return False, "消费金额不能为负数"
        
        if amount > 999999.99:
            return False, "消费金额不能超过999999.99"
        
        return True, ""
    except (ValueError, TypeError):
        return False, "消费金额格式不正确"

def validate_file_extension(filename, allowed_extensions=None):
    """验证文件扩展名"""
    if not filename:
        return False, "文件名不能为空"
    
    if allowed_extensions is None:
        allowed_extensions = {'xlsx', 'xls'}
    
    if '.' not in filename:
        return False, "文件必须有扩展名"
    
    extension = filename.rsplit('.', 1)[1].lower()
    if extension not in allowed_extensions:
        return False, f"只允许上传{', '.join(allowed_extensions)}格式的文件"
    
    return True, ""

def validate_pagination_params(page, per_page, max_per_page=100):
    """验证分页参数"""
    try:
        page = int(page) if page else 1
        per_page = int(per_page) if per_page else 20
        
        if page < 1:
            page = 1
        
        if per_page < 1:
            per_page = 20
        elif per_page > max_per_page:
            per_page = max_per_page
        
        return True, "", page, per_page
    except (ValueError, TypeError):
        return False, "分页参数格式不正确", 1, 20

class ValidationError(Exception):
    """验证错误异常"""
    def __init__(self, message, field=None):
        self.message = message
        self.field = field
        super().__init__(self.message)

class Validator:
    """验证器类"""
    
    def __init__(self):
        self.errors = {}
    
    def add_error(self, field, message):
        """添加验证错误"""
        if field not in self.errors:
            self.errors[field] = []
        self.errors[field].append(message)
    
    def validate_field(self, field, value, validator_func, *args, **kwargs):
        """验证单个字段"""
        is_valid, message = validator_func(value, *args, **kwargs)
        if not is_valid:
            self.add_error(field, message)
        return is_valid
    
    def is_valid(self):
        """检查是否所有验证都通过"""
        return len(self.errors) == 0
    
    def get_errors(self):
        """获取所有验证错误"""
        return self.errors
    
    def get_first_error(self):
        """获取第一个验证错误"""
        if self.errors:
            field, messages = next(iter(self.errors.items()))
            return field, messages[0]
        return None, None
