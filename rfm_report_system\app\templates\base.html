<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% if title %}{{ title }} - {% endif %}数据处理系统</title>
    
    <!-- Bootstrap CSS - 使用国内CDN -->
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome - 使用国内CDN -->
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- 备用基础样式 -->
    <style>
        /* 如果Bootstrap加载失败，提供基础样式 */
        .container-fluid { max-width: 100%; padding: 0 15px; }
        .row { display: flex; flex-wrap: wrap; margin: 0 -15px; }
        .col, .col-md-3, .col-md-6, .col-md-9, .col-12 { padding: 0 15px; flex: 1; }
        .btn { padding: 8px 16px; border: 1px solid #ccc; background: #f8f9fa; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn-primary { background: #007bff; color: white; border-color: #007bff; }
        .btn:hover { opacity: 0.8; }
        .form-select, .form-control { width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; }
        .table { width: 100%; border-collapse: collapse; }
        .table th, .table td { padding: 8px; border: 1px solid #ddd; text-align: left; }
        .table-dark th { background: #343a40; color: white; }
        .table-striped tbody tr:nth-child(odd) { background: #f8f9fa; }
        .alert { padding: 15px; margin: 15px 0; border: 1px solid transparent; border-radius: 4px; }
        .alert-info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .alert-warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .text-end { text-align: right; }
        .text-center { text-align: center; }
        .fw-bold { font-weight: bold; }
        .mb-4 { margin-bottom: 1.5rem; }
        .me-2 { margin-right: 0.5rem; }
        .d-flex { display: flex; }
        .justify-content-between { justify-content: space-between; }
        .align-items-center { align-items: center; }
    </style>
    
    <style>
        .navbar-brand {
            font-weight: bold;
        }
        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: #f8f9fa;
        }
        .main-content {
            min-height: calc(100vh - 56px);
        }
        .card-header {
            background-color: #007bff;
        }

        /* 多级下拉菜单样式 */
        .dropdown-submenu {
            position: relative;
        }

        .dropdown-submenu > .dropdown-menu {
            top: 0;
            left: 100%;
            margin-top: -6px;
            margin-left: -1px;
            border-radius: 0 6px 6px 6px;
            min-width: 200px;
        }

        .dropdown-submenu:hover > .dropdown-menu {
            display: block;
        }

        .dropdown-submenu > .dropdown-item::after {
            display: block;
            content: " ";
            float: right;
            width: 0;
            height: 0;
            border-color: transparent;
            border-style: solid;
            border-width: 5px 0 5px 5px;
            border-left-color: #ccc;
            margin-top: 5px;
            margin-right: -10px;
        }

        .dropdown-submenu:hover > .dropdown-item::after {
            border-left-color: #fff;
        }

        .dropdown-submenu.pull-left {
            float: none;
        }

        .dropdown-submenu.pull-left > .dropdown-menu {
            left: -100%;
            margin-left: 10px;
            border-radius: 6px 0 6px 6px;
        }

        /* 确保下拉菜单足够宽 */
        .dropdown-menu-lg {
            min-width: 280px;
        }

        /* 菜单项悬停效果 */
        .dropdown-item:hover {
            background-color: #f8f9fa;
        }

        .dropdown-submenu > .dropdown-item:hover {
            background-color: #e9ecef;
        }

        /* 响应式处理 */
        @media (max-width: 768px) {
            .dropdown-submenu > .dropdown-menu {
                position: static !important;
                float: none;
                width: auto;
                margin-top: 0;
                background-color: transparent;
                border: 0;
                box-shadow: none;
                padding-left: 20px;
            }

            .dropdown-submenu > .dropdown-item::after {
                display: none;
            }
        }
            color: white;
        }
        .btn-group-sm > .btn, .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        .table th {
            background-color: #f8f9fa;
        }
        .status-badge {
            font-size: 0.75rem;
        }
        .password-mask {
            font-family: monospace;
            letter-spacing: 2px;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    {% if current_user.is_authenticated %}
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('main.index') }}">
                <i class="fas fa-database me-2"></i>数据处理系统
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.index') }}">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="configDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog me-1"></i>配置管理
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('config_mgmt.list_configs') }}">
                                <i class="fas fa-database me-1"></i>数据库配置
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="dataUpdateDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-upload me-1"></i>数据更新
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('data_update.index') }}">
                                <i class="fas fa-upload me-1"></i>数据更新
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('data_update.data_preview') }}">
                                <i class="fas fa-chart-bar me-1"></i>数据预览
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('data_update.data_cleanup') }}">
                                <i class="fas fa-broom me-1"></i>数据整理
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="dataDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-chart-bar me-1"></i>数据处理
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('data_processing.new_task') }}">
                                <i class="fas fa-plus me-1"></i>新建任务
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('data_processing.task_history') }}">
                                <i class="fas fa-history me-1"></i>任务历史
                            </a></li>
                        </ul>
                    </li>


                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('mapping.index') }}">
                            <i class="fas fa-exchange-alt me-1"></i>参照表管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('excel_reports.index') }}">
                            <i class="fas fa-file-excel me-1 text-success"></i>Excel报表
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="rfmDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-chart-line me-1 text-primary"></i>RFM报表
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('rfm_reports.index') }}">
                                <i class="fas fa-home me-1 text-primary"></i>报表首页
                            </a></li>
                            {% if current_user.is_admin %}
                            <li><a class="dropdown-item" href="{{ url_for('rfm_reports.data_source_management') }}">
                                <i class="fas fa-database me-1 text-info"></i>数据源管理
                            </a></li>
                            {% endif %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('rfm_reports.forward_panel') }}">
                                <i class="fas fa-arrow-right me-1 text-success"></i>正向盘报表
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('rfm_reports.result_panel') }}">
                                <i class="fas fa-bullseye me-1 text-info"></i>结果盘报表
                            </a></li>
                        </ul>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>{{ current_user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{{ url_for('auth.change_password') }}">
                                <i class="fas fa-key me-1"></i>修改密码
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt me-1"></i>退出登录
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}
    
    <!-- 主要内容区域 -->
    <div class="container-fluid">
        <!-- Flash消息 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="row mt-3">
                    <div class="col-12">
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}
        {% endwith %}
        
        <!-- 页面内容 -->
        {% block content %}{% endblock %}
    </div>
    
    <!-- Bootstrap JS - 使用国内CDN -->
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery - 使用国内CDN -->
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

    <!-- 简化的提示功能，替代SweetAlert2 -->
    <script>
        // 如果SweetAlert2未加载，提供简单的替代方案
        if (typeof Swal === 'undefined') {
            window.Swal = {
                fire: function(options) {
                    if (typeof options === 'string') {
                        alert(options);
                    } else if (options && options.text) {
                        alert(options.text);
                    } else if (options && options.title) {
                        alert(options.title);
                    }
                }
            };
        }
    </script>



    <!-- 多级下拉菜单JavaScript -->
    <script>
        $(document).ready(function() {
            // 多级下拉菜单支持
            $('.dropdown-submenu a.dropdown-toggle').on("click", function(e) {
                var $subMenu = $(this).next(".dropdown-menu");
                if (!$subMenu.hasClass('show')) {
                    $subMenu.addClass('show');
                    $subMenu.parents('.dropdown-menu').first().find('.dropdown-submenu .dropdown-menu').not($subMenu).removeClass('show');
                } else {
                    $subMenu.removeClass('show');
                }

                $(this).parents('li.nav-item.dropdown.show').on('hidden.bs.dropdown', function(e) {
                    $('.dropdown-submenu .dropdown-menu').removeClass('show');
                });

                return false;
            });

            // 点击其他地方关闭子菜单
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.dropdown-submenu').length) {
                    $('.dropdown-submenu .dropdown-menu').removeClass('show');
                }
            });

            // 移动端处理
            if (window.innerWidth <= 768) {
                $('.dropdown-submenu').off('mouseenter mouseleave');
                $('.dropdown-submenu > .dropdown-menu').removeClass('show');
            }
        });
    </script>

    <!-- 自定义JavaScript -->
    {% block scripts %}{% endblock %}
</body>
</html>
