# RFM报表处理系统

一个专为医美行业设计的客户消费数据分析系统，能够自动化处理和分析客户数据，生成标准化的RFM分析报表和TOP业绩排名报表。

## 功能特性

### 核心功能
- **用户认证系统**: 安全的用户登录、注册、密码管理
- **数据库配置管理**: 支持多数据库连接配置，动态切换
- **数据处理引擎**: 强大的RFM数据分析处理能力
- **任务管理**: 完整的任务创建、监控、历史记录功能
- **结果导出**: 支持Excel格式的分析结果导出

### 数据处理能力
- **正向盘分析**: 基于指定季度表的会员分析
- **结果盘分析**: 基于多个季度表会员并集的分析
- **季度业绩计算**: 自动计算各季度执行业绩
- **科室业绩分析**: 支持皮肤、注射、口腔、整形、毛发等科室
- **TOP排名计算**: 自动生成品类TOP5和品项TOP15排名

## 系统要求

- Python 3.8+
- MySQL 5.7+
- 内存: 建议4GB以上
- 存储: 建议10GB以上可用空间

## 安装部署

### 1. 环境准备

```bash
# 克隆或下载项目代码
cd rfm_report_system

# 创建虚拟环境（推荐）
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑 .env 文件，配置数据库连接信息
# 注意：这里配置的是系统管理数据库，用于存储用户和配置信息
```

### 3. 初始化数据库

```bash
# 运行数据库初始化脚本
python init_db.py

# 按提示创建管理员账户
```

### 4. 启动系统

```bash
# 启动系统
python run.py

# 或者使用Flask命令
flask run
```

系统启动后，访问 http://localhost:5000

## 使用指南

### 首次使用

1. **登录系统**: 使用初始化时创建的管理员账户登录
2. **配置数据库**: 在"配置管理"中添加业务数据库连接
3. **激活配置**: 选择一个数据库配置并激活
4. **创建任务**: 在"数据处理"中创建第一个分析任务

### 数据库配置

系统支持两种数据库：
- **系统管理数据库**: 存储用户信息、配置信息、任务记录
- **业务数据库**: 存储客户数据、交易明细等业务数据

### 数据处理流程

1. **选择处理类型**:
   - 正向盘: 基于单个季度表的会员
   - 结果盘: 基于多个季度表的会员并集

2. **选择数据源**:
   - 季度客户表: 如 `24Q3_customer_data`
   - 分析季度: 如 `24Q4`, `25Q1`, `25Q2`
   - 科室选择: 皮肤、注射等

3. **执行处理**: 系统自动执行四个步骤
   - 创建基础缓存表
   - 计算季度执行业绩
   - 计算科室季度业绩
   - 计算TOP排名

4. **下载结果**: 处理完成后下载Excel格式的分析结果

## 数据表结构要求

### 季度客户表
- 表名格式: `{年份}Q{季度}_customer_data` (如: `24Q3_customer_data`)
- 必须包含: `会员卡号` 字段

### 客户执行明细表
- 表名: `客户执行明细表`
- 必须包含字段:
  - `执行日期`: 消费日期
  - `一级分类`: 科室分类
  - `二级分类`: 品类
  - `三级分类`: 品项
  - `执行业绩（真实金额）`: 消费金额
  - `会员卡号`: 会员标识

## 科室映射关系

系统内置科室映射关系：
- **皮肤**: 皮肤美容项目、形体美容项目
- **口腔**: 口腔美容项目
- **注射**: 注射美容项目
- **整形**: 检验项目、整形美容项目、麻醉项目
- **毛发**: 毛发种植项目

## 安全特性

- **密码加盐哈希**: 用户密码采用bcrypt加盐哈希存储
- **数据库密码加密**: 业务数据库密码采用Fernet对称加密
- **CSRF保护**: 表单提交包含CSRF令牌保护
- **权限控制**: 用户只能访问自己创建的任务

## 性能优化

- **批处理**: 大数据量采用分批处理
- **连接池**: 数据库连接池优化
- **异步处理**: 数据处理任务异步执行
- **进度监控**: 实时显示处理进度

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否启动
   - 验证连接参数是否正确
   - 确认网络连通性

2. **任务处理失败**
   - 检查数据表结构是否符合要求
   - 验证数据完整性
   - 查看错误日志

3. **内存不足**
   - 减少处理的数据量
   - 增加系统内存
   - 优化查询条件

### 日志文件

- 应用日志: `logs/app.log`
- 错误日志: 在任务详情页面查看

## 技术架构

- **后端**: Flask + SQLAlchemy + PyMySQL
- **前端**: Bootstrap 5 + jQuery
- **数据处理**: Pandas + NumPy
- **文件导出**: openpyxl + xlsxwriter
- **安全**: bcrypt + cryptography

## 开发团队

本系统专为医美行业数据分析需求设计开发。

## 许可证

本项目仅供内部使用。

---

如有问题或建议，请联系系统管理员。
