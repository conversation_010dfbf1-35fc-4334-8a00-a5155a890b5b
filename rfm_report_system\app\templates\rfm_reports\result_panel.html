{% extends "base.html" %}

{% block title %}结果盘报表 - RFM报表{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 页面标题 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-bullseye text-info me-2"></i>
                    结果盘报表
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">首页</a></li>
                        <li class="breadcrumb-item"><a href="{{ url_for('rfm_reports.index') }}">RFM报表</a></li>
                        <li class="breadcrumb-item active">结果盘报表</li>
                    </ol>
                </nav>
            </div>

            <!-- 数据源选择 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-database me-2"></i>
                                数据源选择
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <label for="dataSourceSelect" class="form-label">选择结果盘数据源：</label>
                                    <select class="form-select" id="dataSourceSelect">
                                        <option value="">请选择数据源...</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-primary" id="applyDataSource" disabled>
                                        <i class="fas fa-check me-1"></i> 应用数据源
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-muted small" id="dataSourceInfo">
                                        请先选择数据源
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 报表内容区域 -->
            <div id="reportContent" style="display: none;">
                <!-- 一级标签页 -->
                <ul class="nav nav-tabs nav-tabs-custom" id="mainTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="customer-overview-tab" data-bs-toggle="tab" 
                                data-bs-target="#customer-overview" type="button" role="tab">
                            <i class="fas fa-users me-1"></i> 客户资源总览
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="department-analysis-tab" data-bs-toggle="tab" 
                                data-bs-target="#department-analysis" type="button" role="tab">
                            <i class="fas fa-building me-1"></i> 科室业绩分析
                        </button>
                    </li>
                </ul>

                <!-- 标签页内容 -->
                <div class="tab-content" id="mainTabContent">
                    <!-- 客户资源总览 -->
                    <div class="tab-pane fade show active" id="customer-overview" role="tabpanel">
                        <div class="card border-0">
                            <div class="card-body">
                                <!-- 二级标签页 -->
                                <ul class="nav nav-pills nav-pills-custom mb-3" id="customerTabs" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" data-analysis="base-volume-trend">
                                            <i class="fas fa-chart-area me-1"></i> 基盘体量趋势
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" data-analysis="quarterly-upgrade-trend">
                                            <i class="fas fa-exchange-alt me-1"></i> 季度升降级趋势
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" data-analysis="upgrade-matrix-change">
                                            <i class="fas fa-th me-1"></i> 升降矩阵变化
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" data-analysis="rfm-health-assessment">
                                            <i class="fas fa-heartbeat me-1"></i> RFM健康度评估
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" data-analysis="quarterly-resource-utilization">
                                            <i class="fas fa-chart-pie me-1"></i> 季末资源利用率
                                        </button>
                                    </li>
                                    <!-- 结果盘特有的分析 -->
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" data-analysis="quarterly-new-customer-utilization">
                                            <i class="fas fa-user-plus me-1"></i> 季度次新客资源利用率
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" data-analysis="annual-quarterly-utilization">
                                            <i class="fas fa-calendar-alt me-1"></i> 年度次季度资源利用率
                                        </button>
                                    </li>
                                </ul>

                                <!-- 分析内容区域 -->
                                <div id="customerAnalysisContent">
                                    <div class="text-center py-5">
                                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">请选择分析类型</h5>
                                        <p class="text-muted">点击上方标签页查看相应的分析内容</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 科室业绩分析 -->
                    <div class="tab-pane fade" id="department-analysis" role="tabpanel">
                        <div class="card border-0">
                            <div class="card-body">
                                <!-- 二级标签页 -->
                                <ul class="nav nav-pills nav-pills-custom mb-3" id="departmentTabs" role="tablist">
                                    <li class="nav-item dropdown">
                                        <button class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                                            <i class="fas fa-building me-1"></i> 科室总盘
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#" data-analysis="department-overview/micro-plastic">
                                                <i class="fas fa-syringe me-1"></i> 微整
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" data-analysis="department-overview/dermatology">
                                                <i class="fas fa-hand-paper me-1"></i> 皮肤
                                            </a></li>
                                        </ul>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" data-analysis="quarterly-category-analysis">
                                            <i class="fas fa-tags me-1"></i> 季度重点品类业绩分析
                                        </button>
                                    </li>
                                    <!-- 结果盘特有的分析 -->
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" data-analysis="quarterly-item-analysis">
                                            <i class="fas fa-list me-1"></i> 季度重点品项业绩分析
                                        </button>
                                    </li>
                                </ul>

                                <!-- 分析内容区域 -->
                                <div id="departmentAnalysisContent">
                                    <div class="text-center py-5">
                                        <i class="fas fa-building fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">请选择分析类型</h5>
                                        <p class="text-muted">点击上方标签页查看相应的分析内容</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 无数据源提示 -->
            <div id="noDataSourceMessage" class="text-center py-5">
                <i class="fas fa-database fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">请先选择数据源</h5>
                <p class="text-muted">
                    选择一个结果盘数据源后，即可查看相应的RFM分析报表。
                    如果没有可用的数据源，请先在
                    <a href="{{ url_for('rfm_reports.data_source_management') }}">数据源管理</a>
                    中导入数据。
                </p>
            </div>
        </div>
    </div>
</div>

<script>
let currentDataSource = null;
let currentAnalysisType = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadDataSources();
    initializeEventListeners();
});

function loadDataSources() {
    fetch('/rfm/api/result-data-sources')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populateDataSourceSelect(data.data_sources);
            } else {
                showError('获取数据源失败: ' + data.error);
            }
        })
        .catch(error => {
            console.error('获取数据源失败:', error);
            showError('网络错误，请稍后重试');
        });
}

function populateDataSourceSelect(dataSources) {
    const select = document.getElementById('dataSourceSelect');
    select.innerHTML = '<option value="">请选择数据源...</option>';
    
    if (dataSources.length === 0) {
        select.innerHTML += '<option value="" disabled>暂无可用数据源</option>';
        return;
    }
    
    dataSources.forEach(ds => {
        const option = document.createElement('option');
        option.value = ds.id;
        option.textContent = `${ds.source_task_name} (${ds.imported_records.toLocaleString()} 条记录)`;
        option.dataset.dataSource = JSON.stringify(ds);
        select.appendChild(option);
    });
}

function initializeEventListeners() {
    // 数据源选择变化
    document.getElementById('dataSourceSelect').addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const applyButton = document.getElementById('applyDataSource');
        const infoDiv = document.getElementById('dataSourceInfo');
        
        if (this.value) {
            const dataSource = JSON.parse(selectedOption.dataset.dataSource);
            applyButton.disabled = false;
            infoDiv.innerHTML = `
                <strong>${dataSource.source_task_name}</strong><br>
                <small class="text-muted">
                    记录数: ${dataSource.imported_records.toLocaleString()} | 
                    季度: ${dataSource.quarters.join(', ')} | 
                    导入时间: ${new Date(dataSource.imported_at).toLocaleString()}
                </small>
            `;
        } else {
            applyButton.disabled = true;
            infoDiv.textContent = '请先选择数据源';
        }
    });
    
    // 应用数据源
    document.getElementById('applyDataSource').addEventListener('click', function() {
        const select = document.getElementById('dataSourceSelect');
        const selectedOption = select.options[select.selectedIndex];
        
        if (select.value) {
            currentDataSource = JSON.parse(selectedOption.dataset.dataSource);
            showReportContent();
        }
    });
    
    // 分析类型点击事件
    document.addEventListener('click', function(e) {
        if (e.target.matches('[data-analysis]') || e.target.closest('[data-analysis]')) {
            const button = e.target.matches('[data-analysis]') ? e.target : e.target.closest('[data-analysis]');
            const analysisType = button.dataset.analysis;
            
            if (analysisType && currentDataSource) {
                loadAnalysisContent(analysisType);
                
                // 更新按钮状态
                document.querySelectorAll('[data-analysis]').forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
            }
        }
    });
}

function showReportContent() {
    document.getElementById('noDataSourceMessage').style.display = 'none';
    document.getElementById('reportContent').style.display = 'block';
    
    // 默认加载第一个分析
    loadAnalysisContent('base-volume-trend');
    document.querySelector('[data-analysis="base-volume-trend"]').classList.add('active');
}

function loadAnalysisContent(analysisType) {
    currentAnalysisType = analysisType;
    
    // 确定内容容器
    let contentContainer;
    if (analysisType.startsWith('department-overview/') || 
        analysisType === 'quarterly-category-analysis' || 
        analysisType === 'quarterly-item-analysis') {
        contentContainer = document.getElementById('departmentAnalysisContent');
    } else {
        contentContainer = document.getElementById('customerAnalysisContent');
    }
    
    // 显示加载状态
    contentContainer.innerHTML = `
        <div class="text-center py-5">
            <i class="fas fa-spinner fa-spin fa-2x text-primary mb-3"></i>
            <h5 class="text-primary">正在加载分析数据...</h5>
        </div>
    `;
    
    // 获取分析数据
    fetch(`/rfm/api/analysis-data/${currentDataSource.id}/${analysisType}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderAnalysisContent(contentContainer, data.data);
            } else {
                showAnalysisError(contentContainer, data.error);
            }
        })
        .catch(error => {
            console.error('获取分析数据失败:', error);
            showAnalysisError(contentContainer, '网络错误，请稍后重试');
        });
}

function renderAnalysisContent(container, data) {
    // 当前显示占位符内容
    container.innerHTML = `
        <div class="alert alert-info">
            <h5 class="alert-heading">
                <i class="fas fa-info-circle me-2"></i>
                ${getAnalysisTitle(currentAnalysisType)}
            </h5>
            <p class="mb-2"><strong>数据源：</strong>${currentDataSource.source_task_name}</p>
            <p class="mb-2"><strong>分析类型：</strong>${currentAnalysisType}</p>
            <p class="mb-2"><strong>数据记录：</strong>${data.total_records.toLocaleString()} 条</p>
            <p class="mb-2"><strong>包含季度：</strong>${data.quarters.join(', ')}</p>
            <hr>
            <p class="mb-0">
                <i class="fas fa-construction me-1"></i>
                <strong>待实现功能</strong> - 此分析功能正在开发中，敬请期待！
            </p>
        </div>
    `;
}

function showAnalysisError(container, error) {
    container.innerHTML = `
        <div class="alert alert-danger">
            <h5 class="alert-heading">
                <i class="fas fa-exclamation-triangle me-2"></i>
                加载失败
            </h5>
            <p class="mb-0">${error}</p>
        </div>
    `;
}

function getAnalysisTitle(analysisType) {
    const titles = {
        'base-volume-trend': '基盘体量趋势',
        'quarterly-upgrade-trend': '季度升降级趋势',
        'upgrade-matrix-change': '升降矩阵变化',
        'rfm-health-assessment': 'RFM健康度评估',
        'quarterly-resource-utilization': '季末资源利用率',
        'quarterly-new-customer-utilization': '季度次新客资源利用率',
        'annual-quarterly-utilization': '年度次季度资源利用率',
        'department-overview/micro-plastic': '科室总盘 - 微整',
        'department-overview/dermatology': '科室总盘 - 皮肤',
        'quarterly-category-analysis': '季度重点品类业绩分析',
        'quarterly-item-analysis': '季度重点品项业绩分析'
    };
    
    return titles[analysisType] || analysisType;
}

function showError(message) {
    // TODO: 实现更好的错误提示
    alert('错误: ' + message);
}
</script>

<style>
.nav-tabs-custom .nav-link {
    border-radius: 0.375rem 0.375rem 0 0;
    border: 1px solid #dee2e6;
    border-bottom: none;
    margin-right: 2px;
}

.nav-tabs-custom .nav-link.active {
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}

.nav-pills-custom .nav-link {
    border-radius: 0.375rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

.nav-pills-custom .nav-link:hover {
    background-color: #e9ecef;
}

.nav-pills-custom .nav-link.active {
    background-color: #0d6efd;
    color: white;
}
</style>
{% endblock %}
