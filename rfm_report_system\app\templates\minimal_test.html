<!DOCTYPE html>
<html>
<head>
    <title>最小化测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>最小化JavaScript测试</h1>
        <div id="result"></div>
        <button onclick="testFunction()" class="btn btn-primary">测试函数</button>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        console.log('开始最小化测试...');
        
        function testFunction() {
            console.log('测试函数被调用');
            
            // 测试模板字符串
            const message = '测试消息';
            const html = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    ${message}
                    <br>
                    <button class="btn btn-outline-primary btn-sm mt-2" onclick="testFunction()">
                        <i class="fas fa-redo me-1"></i> 重新测试
                    </button>
                </div>
            `;
            
            document.getElementById('result').innerHTML = html;
        }
        
        // 测试API调用
        function loadTasks() {
            console.log('🚀 开始加载任务列表...');
            
            fetch('/rfm/api/available-tasks', {
                method: 'GET',
                credentials: 'same-origin',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => {
                console.log('📡 API响应状态:', response.status);
                
                // 安全地获取响应头
                try {
                    const headers = {};
                    for (const [key, value] of response.headers.entries()) {
                        headers[key] = value;
                    }
                    console.log('📋 API响应头:', headers);
                } catch (e) {
                    console.log('📋 无法获取响应头:', e.message);
                }
                
                return response.text();
            })
            .then(text => {
                console.log('📄 原始响应:', text);
                document.getElementById('result').innerHTML = `<pre>${text}</pre>`;
            })
            .catch(error => {
                console.error('❌ 错误:', error);
                document.getElementById('result').innerHTML = `<div class="alert alert-danger">错误: ${error.message}</div>`;
            });
        }
        
        // 页面加载后自动测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成');
            loadTasks();
        });
    </script>
</body>
</html>
