{% extends "base.html" %}

{% block title %}数据预览 - RFM报表处理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-chart-bar me-2 text-primary"></i>数据预览</h2>
                    <p class="text-muted">查看数据库中各表的统计信息和数据概览</p>
                </div>
                <div>
                    <a href="{{ url_for('data_update.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>返回数据更新
                    </a>
                    <button class="btn btn-primary" onclick="refreshPreview()">
                        <i class="fas fa-sync-alt me-2"></i>刷新数据
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 当前数据库信息 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-server me-2"></i>当前数据库：{{ db_config.name }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-1"><strong>数据库类型：</strong>{{ db_config.db_type }}</p>
                            <p class="mb-0"><strong>服务器地址：</strong>{{ db_config.host }}:{{ db_config.port }}</p>
                        </div>
                        <div class="col-md-6">
                            <div id="databaseSummary">
                                <div class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <p class="mt-2">正在加载数据统计...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 季度表预览 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-calendar-alt me-2 text-warning"></i>季度表数据预览</h5>
                </div>
                <div class="card-body">
                    <div id="quarterlyTablesPreview">
                        <div class="text-center">
                            <div class="spinner-border text-warning" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2">正在加载季度表数据...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 业务表预览 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-table me-2 text-info"></i>业务表数据预览</h5>
                </div>
                <div class="card-body">
                    <div id="businessTablesPreview">
                        <div class="text-center">
                            <div class="spinner-border text-info" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2">正在加载业务表数据...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    loadPreviewData();
});

// 加载预览数据
function loadPreviewData() {
    $.ajax({
        url: '{{ url_for("data_update.get_preview_data") }}',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                renderPreviewData(response.data);
            } else {
                showError('加载预览数据失败: ' + response.message);
            }
        },
        error: function() {
            showError('请求失败，请检查网络连接');
        }
    });
}

// 渲染预览数据
function renderPreviewData(data) {
    // 渲染数据库摘要
    renderDatabaseSummary(data);
    
    // 渲染季度表预览
    renderQuarterlyTables(data.quarterly_tables);
    
    // 渲染业务表预览
    renderBusinessTables(data.business_tables);
}

// 渲染数据库摘要
function renderDatabaseSummary(data) {
    const quarterlyCount = data.quarterly_tables.length;
    const businessCount = data.business_tables.length;
    const totalTables = quarterlyCount + businessCount;
    
    const summaryHtml = `
        <div class="row text-center">
            <div class="col-md-4">
                <h4 class="text-primary">${totalTables}</h4>
                <small class="text-muted">总表数</small>
            </div>
            <div class="col-md-4">
                <h4 class="text-warning">${quarterlyCount}</h4>
                <small class="text-muted">季度表</small>
            </div>
            <div class="col-md-4">
                <h4 class="text-info">${businessCount}</h4>
                <small class="text-muted">业务表</small>
            </div>
        </div>
    `;
    
    $('#databaseSummary').html(summaryHtml);
}

// 渲染季度表预览
function renderQuarterlyTables(quarterlyTables) {
    if (quarterlyTables.length === 0) {
        $('#quarterlyTablesPreview').html(`
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle me-2"></i>没有找到季度表</h6>
                <p class="mb-0">当前数据库中没有季度表数据。</p>
            </div>
        `);
        return;
    }
    
    let html = '<div class="row">';
    
    quarterlyTables.forEach(function(table) {
        if (table.error) {
            html += `
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="card border-danger">
                        <div class="card-header bg-danger text-white">
                            <h6 class="mb-0">${table.table_name}</h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-danger mb-0">
                                <small>加载失败: ${table.error}</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        } else {
            html += `
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="card border-warning">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0">
                                <i class="fas fa-calendar-alt me-2"></i>${table.table_name}
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <strong>总记录数：</strong><span class="badge bg-primary">${table.total_records.toLocaleString()}</span>
                            </div>
                            <div class="mb-3">
                                <strong>会员卡号：</strong><span class="badge bg-success">${table.member_card_count.toLocaleString()}</span>
                            </div>
                            
                            ${renderLevelStats('综合等级', table.comprehensive_levels)}
                            ${renderLevelStats('细分等级', table.detailed_levels)}
                            ${renderLevelStats('会员卡级', table.card_levels)}
                            ${renderLevelStats('科室标签', table.department_tags)}
                        </div>
                    </div>
                </div>
            `;
        }
    });
    
    html += '</div>';
    $('#quarterlyTablesPreview').html(html);
}

// 渲染等级统计
function renderLevelStats(title, stats) {
    if (!stats || Object.keys(stats).length === 0) {
        return '';
    }
    
    let html = `<div class="mb-2"><strong>${title}：</strong><br>`;
    
    Object.entries(stats).forEach(([level, count]) => {
        html += `<span class="badge bg-secondary me-1">${level}: ${count}</span>`;
    });
    
    html += '</div>';
    return html;
}

// 渲染业务表预览
function renderBusinessTables(businessTables) {
    if (businessTables.length === 0) {
        $('#businessTablesPreview').html(`
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle me-2"></i>没有找到业务表</h6>
                <p class="mb-0">当前数据库中没有业务表数据。</p>
            </div>
        `);
        return;
    }
    
    let html = '<div class="accordion" id="businessTablesAccordion">';
    
    businessTables.forEach(function(table, index) {
        const collapseId = `collapse${index}`;
        
        html += `
            <div class="accordion-item">
                <h2 class="accordion-header" id="heading${index}">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                            data-bs-target="#${collapseId}" aria-expanded="false" aria-controls="${collapseId}">
                        <i class="fas fa-table me-2 text-info"></i>
                        <strong>${table.table_name}</strong>
                        <span class="badge bg-info ms-2">${table.total_records.toLocaleString()} 行</span>
                    </button>
                </h2>
                <div id="${collapseId}" class="accordion-collapse collapse" 
                     aria-labelledby="heading${index}" data-bs-parent="#businessTablesAccordion">
                    <div class="accordion-body">
                        ${renderBusinessTableStats(table)}
                    </div>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    $('#businessTablesPreview').html(html);
}

// 渲染业务表统计
function renderBusinessTableStats(table) {
    if (table.error) {
        return `<div class="alert alert-danger">加载失败: ${table.error}</div>`;
    }

    const stats = table.statistics;
    let html = '';

    // 显示表类型
    if (stats.table_type) {
        html += `<div class="alert alert-info"><strong>表类型：</strong>${stats.table_type}</div>`;
    }

    // 医生绑定明细表统计
    if (stats.doctor_customer_counts) {
        html += '<h6><i class="fas fa-user-md me-2"></i>医生客户统计（前10名）</h6>';
        html += '<div class="row">';
        Object.entries(stats.doctor_customer_counts).forEach(([doctor, count]) => {
            html += `
                <div class="col-md-6 mb-2">
                    <span class="badge bg-primary">${doctor}: ${count} 客户</span>
                </div>
            `;
        });
        html += '</div>';
    }

    if (stats.comprehensive_level_distribution) {
        html += '<h6><i class="fas fa-chart-pie me-2"></i>综合等级分布</h6>';
        html += '<div class="row">';
        Object.entries(stats.comprehensive_level_distribution).forEach(([level, count]) => {
            html += `
                <div class="col-md-3 mb-2">
                    <span class="badge bg-info">${level}: ${count}</span>
                </div>
            `;
        });
        html += '</div>';
    }

    // 待执行明细表统计
    if (stats.total_pending_amount) {
        html += `<h6><i class="fas fa-clock me-2"></i>总待执行金额</h6>`;
        html += `<div class="alert alert-warning"><strong>¥${parseFloat(stats.total_pending_amount).toLocaleString()}</strong></div>`;
    }

    if (stats.yearly_pending_amount) {
        html += '<h6><i class="fas fa-calendar me-2"></i>年度待执行金额</h6>';
        html += '<div class="row">';
        Object.entries(stats.yearly_pending_amount).forEach(([year, amount]) => {
            html += `
                <div class="col-md-4 mb-2">
                    <span class="badge bg-warning">${year}年: ¥${parseFloat(amount).toLocaleString()}</span>
                </div>
            `;
        });
        html += '</div>';
    }

    if (stats.department_pending_amount) {
        html += '<h6><i class="fas fa-building me-2"></i>科室待执行金额（前10名）</h6>';
        html += '<div class="row">';
        Object.entries(stats.department_pending_amount).forEach(([dept, amount]) => {
            html += `
                <div class="col-md-6 mb-2">
                    <span class="badge bg-secondary">${dept}: ¥${parseFloat(amount).toLocaleString()}</span>
                </div>
            `;
        });
        html += '</div>';
    }

    // 执行明细表统计
    if (stats.total_execution_amount) {
        html += `<h6><i class="fas fa-check-circle me-2"></i>总执行金额</h6>`;
        html += `<div class="alert alert-success"><strong>¥${parseFloat(stats.total_execution_amount).toLocaleString()}</strong></div>`;
    }

    if (stats.yearly_execution_amount) {
        html += '<h6><i class="fas fa-calendar me-2"></i>年度执行金额</h6>';
        html += '<div class="row">';
        Object.entries(stats.yearly_execution_amount).forEach(([year, amount]) => {
            html += `
                <div class="col-md-4 mb-2">
                    <span class="badge bg-success">${year}年: ¥${parseFloat(amount).toLocaleString()}</span>
                </div>
            `;
        });
        html += '</div>';
    }

    if (stats.quarterly_execution_amount) {
        html += '<h6><i class="fas fa-chart-line me-2"></i>季度执行金额</h6>';
        html += '<div class="row">';
        Object.entries(stats.quarterly_execution_amount).forEach(([quarter, amount]) => {
            html += `
                <div class="col-md-3 mb-2">
                    <span class="badge bg-info">${quarter}: ¥${parseFloat(amount).toLocaleString()}</span>
                </div>
            `;
        });
        html += '</div>';
    }

    // 消费明细表统计
    if (stats.total_consumption_amount) {
        html += `<h6><i class="fas fa-shopping-cart me-2"></i>总消费金额</h6>`;
        html += `<div class="alert alert-primary"><strong>¥${parseFloat(stats.total_consumption_amount).toLocaleString()}</strong></div>`;
    }

    if (stats.yearly_consumption_amount) {
        html += '<h6><i class="fas fa-calendar me-2"></i>年度消费金额</h6>';
        html += '<div class="row">';
        Object.entries(stats.yearly_consumption_amount).forEach(([year, amount]) => {
            html += `
                <div class="col-md-4 mb-2">
                    <span class="badge bg-primary">${year}年: ¥${parseFloat(amount).toLocaleString()}</span>
                </div>
            `;
        });
        html += '</div>';
    }

    if (stats.quarterly_consumption_amount) {
        html += '<h6><i class="fas fa-chart-line me-2"></i>季度消费金额</h6>';
        html += '<div class="row">';
        Object.entries(stats.quarterly_consumption_amount).forEach(([quarter, amount]) => {
            html += `
                <div class="col-md-3 mb-2">
                    <span class="badge bg-primary">${quarter}: ¥${parseFloat(amount).toLocaleString()}</span>
                </div>
            `;
        });
        html += '</div>';
    }

    if (stats.total_currency_amount) {
        html += `<h6><i class="fas fa-coins me-2"></i>总货币金额</h6>`;
        html += `<div class="alert alert-success"><strong>¥${parseFloat(stats.total_currency_amount).toLocaleString()}</strong></div>`;
    }

    // 通用统计
    if (stats.amount_statistics) {
        const amountStats = stats.amount_statistics;
        html += `<h6><i class="fas fa-calculator me-2"></i>金额统计（${amountStats.column_name}）</h6>`;
        html += `
            <div class="row">
                <div class="col-md-6">
                    <small>总计: ¥${parseFloat(amountStats.total).toLocaleString()}</small><br>
                    <small>平均: ¥${parseFloat(amountStats.average).toLocaleString()}</small>
                </div>
                <div class="col-md-6">
                    <small>最大: ¥${parseFloat(amountStats.max).toLocaleString()}</small><br>
                    <small>最小: ¥${parseFloat(amountStats.min).toLocaleString()}</small>
                </div>
            </div>
        `;
    }

    if (stats.date_statistics) {
        const dateStats = stats.date_statistics;
        html += `<h6><i class="fas fa-calendar-alt me-2"></i>日期统计（${dateStats.column_name}）</h6>`;
        html += `
            <div class="row">
                <div class="col-md-6">
                    <small>最早: ${dateStats.min_date || 'N/A'}</small><br>
                    <small>最晚: ${dateStats.max_date || 'N/A'}</small>
                </div>
                <div class="col-md-6">
                    <small>不同日期数: ${dateStats.unique_dates}</small>
                </div>
            </div>
        `;
    }

    // 显示可用列名
    if (stats.available_columns && stats.available_columns.length > 0) {
        html += '<h6><i class="fas fa-columns me-2"></i>可用列名</h6>';
        html += '<div class="mb-3">';
        stats.available_columns.forEach(col => {
            html += `<span class="badge bg-light text-dark me-1 mb-1">${col}</span>`;
        });
        html += '</div>';
    }

    // 显示错误信息
    if (stats.error) {
        html += `<div class="alert alert-warning"><strong>部分统计失败：</strong>${stats.error}</div>`;
    }

    if (html === '') {
        html = '<div class="alert alert-info">暂无统计数据</div>';
    }

    return html;
}

// 刷新预览
function refreshPreview() {
    // 重置所有加载状态
    $('#databaseSummary').html(`
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载数据统计...</p>
        </div>
    `);
    
    $('#quarterlyTablesPreview').html(`
        <div class="text-center">
            <div class="spinner-border text-warning" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载季度表数据...</p>
        </div>
    `);
    
    $('#businessTablesPreview').html(`
        <div class="text-center">
            <div class="spinner-border text-info" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载业务表数据...</p>
        </div>
    `);
    
    // 重新加载数据
    loadPreviewData();
}

// 显示错误
function showError(message) {
    const errorHtml = `
        <div class="alert alert-danger">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>加载失败</h6>
            <p class="mb-0">${message}</p>
        </div>
    `;
    
    $('#databaseSummary').html(errorHtml);
    $('#quarterlyTablesPreview').html(errorHtml);
    $('#businessTablesPreview').html(errorHtml);
}
</script>
{% endblock %}
