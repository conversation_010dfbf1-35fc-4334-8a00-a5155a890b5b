#!/usr/bin/env python3
"""
调试季度选项生成
"""

import re
from datetime import datetime
from app import create_app
from app.models import DatabaseConfig
from app.data_processing.processor import RFMDataProcessor

def debug_quarter_options():
    """调试季度选项生成"""
    app = create_app()
    
    with app.app_context():
        # 获取激活的数据库配置
        active_config = DatabaseConfig.get_active_config()
        
        if not active_config:
            print("❌ 没有激活的数据库配置")
            return
        
        print(f"✅ 数据库配置: {active_config.name}")
        
        # 创建处理器并获取季度表
        processor = RFMDataProcessor(active_config)
        
        if not processor.connect_database():
            print("❌ 无法连接到数据库")
            return
        
        quarter_tables = processor.get_quarter_tables()
        print(f"\n📋 找到的季度表: {quarter_tables}")
        
        # 生成季度选项（模拟路由中的逻辑）
        quarters = set()
        
        print(f"\n🔍 从表名提取季度信息:")
        for table in quarter_tables:
            print(f"   表名: {table}")
            # 从表名提取季度信息，如 24q3 -> 24Q3
            match = re.match(r'^(\d{2})[qQ]([1-4])$', table)
            if match:
                year, quarter = match.groups()
                quarter_str = f"{year}Q{quarter}"
                quarters.add(quarter_str)
                print(f"     -> 提取到季度: {quarter_str}")
            else:
                print(f"     -> 无法提取季度信息")
        
        # 添加额外的季度选项
        print(f"\n📅 添加额外的季度选项:")
        current_year = datetime.now().year % 100
        print(f"   当前年份: 20{current_year}")
        
        for year in range(current_year - 2, current_year + 2):
            for q in range(1, 5):
                quarter_str = f"{year:02d}Q{q}"
                quarters.add(quarter_str)
                print(f"     添加: {quarter_str}")
        
        # 最终的季度选项
        sorted_quarters = sorted(quarters)
        print(f"\n📊 最终的季度选项 ({len(sorted_quarters)} 个):")
        for i, quarter in enumerate(sorted_quarters):
            print(f"   {i+1:2d}. {quarter}")
        
        # 检查特定季度是否存在
        target_quarters = ['24Q4', '25Q1', '25Q2', '25Q3', '25Q4']
        print(f"\n🎯 检查目标季度:")
        for target in target_quarters:
            status = "✅" if target in quarters else "❌"
            print(f"   {status} {target}")
        
        # 检查是否有23Q1
        if '23Q1' in quarters:
            print(f"\n⚠️  发现 23Q1 在选项中")
        else:
            print(f"\n✅ 23Q1 不在选项中")

if __name__ == '__main__':
    try:
        debug_quarter_options()
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
