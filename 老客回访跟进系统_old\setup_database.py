"""
数据库配置向导
"""
import os
import sys
import getpass
import pymysql

def test_mysql_connection(host, port, user, password, database=None):
    """测试MySQL连接"""
    try:
        connection = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        connection.close()
        return True, "连接成功"
    except Exception as e:
        return False, str(e)

def create_database_if_not_exists(host, port, user, password, database_name):
    """创建数据库（如果不存在）"""
    try:
        connection = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            charset='utf8mb4'
        )
        cursor = connection.cursor()
        
        # 检查数据库是否存在
        cursor.execute("SHOW DATABASES LIKE %s", (database_name,))
        if cursor.fetchone():
            print(f"✅ 数据库 {database_name} 已存在")
        else:
            # 创建数据库
            cursor.execute(f"CREATE DATABASE `{database_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print(f"✅ 数据库 {database_name} 创建成功")
        
        connection.close()
        return True
    except Exception as e:
        print(f"❌ 数据库操作失败: {e}")
        return False

def update_config_file(host, port, user, password, database):
    """更新配置文件"""
    config_content = f'''"""
系统配置文件
"""
import os
from datetime import timedelta
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """基础配置类"""
    
    # 基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-change-in-production'
    
    # 数据库配置
    MYSQL_HOST = os.environ.get('MYSQL_HOST') or '{host}'
    MYSQL_PORT = int(os.environ.get('MYSQL_PORT') or {port})
    MYSQL_USER = os.environ.get('MYSQL_USER') or '{user}'
    MYSQL_PASSWORD = os.environ.get('MYSQL_PASSWORD') or '{password}'
    MYSQL_DATABASE = os.environ.get('MYSQL_DATABASE') or '{database}'
    
    # SQLAlchemy 配置
    SQLALCHEMY_DATABASE_URI = (
        f"mysql+pymysql://{{MYSQL_USER}}:{{MYSQL_PASSWORD}}@"
        f"{{MYSQL_HOST}}:{{MYSQL_PORT}}/{{MYSQL_DATABASE}}?charset=utf8mb4"
    )
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {{
        'pool_size': 10,
        'pool_timeout': 20,
        'pool_recycle': -1,
        'max_overflow': 0,
        'echo': False
    }}
    
    # 会话配置
    PERMANENT_SESSION_LIFETIME = timedelta(minutes=30)
    SESSION_COOKIE_SECURE = False  # 生产环境设为 True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # 文件上传配置
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
    ALLOWED_EXTENSIONS = {{'xlsx', 'xls'}}
    
    # 分页配置
    ITEMS_PER_PAGE = 20
    MAX_ITEMS_PER_PAGE = 100
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    LOG_FILE = os.environ.get('LOG_FILE') or 'app.log'
    
    # 缓存配置（可选）
    CACHE_TYPE = os.environ.get('CACHE_TYPE') or 'simple'
    CACHE_DEFAULT_TIMEOUT = 300
    
    # Redis 配置（如果使用）
    REDIS_URL = os.environ.get('REDIS_URL') or 'redis://localhost:6379/0'
    
    # 邮件配置（可选）
    MAIL_SERVER = os.environ.get('MAIL_SERVER')
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    
    # 系统默认配置
    DEFAULT_CARD_NUMBER_MAX_LENGTH = 10
    DEFAULT_CONTENT_MAX_LENGTH = 500
    DEFAULT_PASSWORD_MIN_LENGTH = 8
    
    @staticmethod
    def init_app(app):
        """初始化应用配置"""
        # 创建上传目录
        upload_folder = app.config.get('UPLOAD_FOLDER')
        if upload_folder and not os.path.exists(upload_folder):
            os.makedirs(upload_folder)

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    SQLALCHEMY_ENGINE_OPTIONS = {{
        **Config.SQLALCHEMY_ENGINE_OPTIONS,
        'echo': True  # 开发环境显示SQL语句
    }}

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    SESSION_COOKIE_SECURE = True
    
    # 生产环境安全配置
    SQLALCHEMY_ENGINE_OPTIONS = {{
        **Config.SQLALCHEMY_ENGINE_OPTIONS,
        'pool_pre_ping': True,  # 连接池预检查
        'pool_recycle': 3600,   # 1小时回收连接
    }}
    
    @classmethod
    def init_app(cls, app):
        Config.init_app(app)
        
        # 生产环境日志配置
        import logging
        from logging.handlers import RotatingFileHandler
        
        if not app.debug:
            file_handler = RotatingFileHandler(
                cls.LOG_FILE, 
                maxBytes=10240000, 
                backupCount=10
            )
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
            ))
            file_handler.setLevel(logging.INFO)
            app.logger.addHandler(file_handler)
            app.logger.setLevel(logging.INFO)
            app.logger.info('老客回访跟进系统启动')

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False

# 配置字典
config = {{
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}}

def get_config():
    """获取当前配置"""
    return config[os.environ.get('FLASK_ENV') or 'default']
'''
    
    try:
        with open('config.py', 'w', encoding='utf-8') as f:
            f.write(config_content)
        print("✅ 配置文件更新成功")
        return True
    except Exception as e:
        print(f"❌ 配置文件更新失败: {e}")
        return False

def main():
    """主函数"""
    print("老客回访与跟进系统 - 数据库配置向导")
    print("="*50)
    
    print("请输入MySQL数据库连接信息:")
    print()
    
    # 获取数据库连接信息
    host = input("数据库主机 [localhost]: ").strip() or 'localhost'
    port = input("数据库端口 [3306]: ").strip() or '3306'
    try:
        port = int(port)
    except ValueError:
        port = 3306
    
    user = input("数据库用户名 [root]: ").strip() or 'root'
    password = getpass.getpass("数据库密码: ")
    database = input("数据库名 [Old_Customer_System]: ").strip() or 'Old_Customer_System'
    
    print()
    print("测试数据库连接...")
    
    # 测试连接（不指定数据库）
    success, message = test_mysql_connection(host, port, user, password)
    if not success:
        print(f"❌ 数据库连接失败: {message}")
        print()
        print("请检查:")
        print("1. MySQL服务是否正在运行")
        print("2. 用户名和密码是否正确")
        print("3. 主机和端口是否正确")
        return False
    
    print("✅ 数据库连接成功")
    
    # 创建数据库
    print(f"检查/创建数据库 {database}...")
    if not create_database_if_not_exists(host, port, user, password, database):
        return False
    
    # 测试连接到指定数据库
    success, message = test_mysql_connection(host, port, user, password, database)
    if not success:
        print(f"❌ 连接到数据库 {database} 失败: {message}")
        return False
    
    print(f"✅ 成功连接到数据库 {database}")
    
    # 更新配置文件
    print("更新配置文件...")
    if not update_config_file(host, port, user, password, database):
        return False
    
    print()
    print("="*50)
    print("🎉 数据库配置完成！")
    print("="*50)
    print()
    print("下一步:")
    print("1. 初始化数据库: python init_db.py")
    print("2. 启动系统: python quick_start.py")
    print()
    
    # 询问是否立即初始化数据库
    choice = input("是否现在初始化数据库？(y/n): ").lower().strip()
    if choice == 'y':
        print()
        print("正在初始化数据库...")
        try:
            from init_db import main as init_main
            if init_main():
                print()
                print("🎉 数据库初始化成功！")
                print("现在可以运行: python quick_start.py")
            else:
                print("❌ 数据库初始化失败")
        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
    
    return True

if __name__ == '__main__':
    main()
