"""
修复配置文件
"""
import os
import getpass

def fix_config_file():
    """修复配置文件中的数据库连接问题"""
    print("修复配置文件...")
    
    # 重新获取密码
    print("请重新输入MySQL密码:")
    password = getpass.getpass("数据库密码: ")
    
    # 读取当前配置文件
    try:
        with open('config.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找并替换密码行
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if 'MYSQL_PASSWORD' in line and 'os.environ.get' in line:
                # 替换密码行
                lines[i] = f"    MYSQL_PASSWORD = os.environ.get('MYSQL_PASSWORD') or '{password}'"
                break
        
        # 写回文件
        with open('config.py', 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        
        print("✅ 配置文件修复成功")
        return True
        
    except Exception as e:
        print(f"❌ 配置文件修复失败: {e}")
        return False

def test_config():
    """测试修复后的配置"""
    try:
        from config import get_config
        config = get_config()
        
        print(f"数据库主机: {config.MYSQL_HOST}")
        print(f"数据库端口: {config.MYSQL_PORT}")
        print(f"数据库用户: {config.MYSQL_USER}")
        print(f"数据库名称: {config.MYSQL_DATABASE}")
        print(f"密码长度: {len(config.MYSQL_PASSWORD)} 字符")
        
        # 测试连接字符串
        print(f"连接字符串: {config.SQLALCHEMY_DATABASE_URI}")
        
        return True
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def main():
    """主函数"""
    print("配置文件修复工具")
    print("="*30)
    
    if fix_config_file():
        print("\n测试修复后的配置:")
        print("-"*30)
        if test_config():
            print("\n✅ 配置修复完成！")
            print("现在可以运行: python init_db.py")
        else:
            print("\n❌ 配置仍有问题")
    else:
        print("\n❌ 配置修复失败")

if __name__ == '__main__':
    main()
