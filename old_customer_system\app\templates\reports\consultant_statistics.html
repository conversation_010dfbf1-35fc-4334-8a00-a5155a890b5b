{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">现场咨询师统计报表</h1>
    <div class="btn-group">
        <a href="{{ url_for('reports.dashboard') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>返回仪表板
        </a>
        {% if consultant_data.consultant_stats %}
        <a href="{{ url_for('reports.export_consultant_statistics', start_date=start_date, end_date=end_date) }}" 
           class="btn btn-success">
            <i class="fas fa-download me-2"></i>导出Excel
        </a>
        {% endif %}
    </div>
</div>

<!-- 筛选条件 -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-filter me-2"></i>筛选条件
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="start_date" class="form-label">开始日期</label>
                <input type="date" class="form-control" id="start_date" name="start_date" 
                       value="{{ start_date or '' }}">
            </div>
            <div class="col-md-4">
                <label for="end_date" class="form-label">结束日期</label>
                <input type="date" class="form-control" id="end_date" name="end_date" 
                       value="{{ end_date or '' }}">
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-2"></i>查询
                </button>
                <a href="{{ url_for('reports.consultant_statistics') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-refresh me-2"></i>重置
                </a>
            </div>
        </form>
    </div>
</div>

<!-- 总体统计 -->
{% if consultant_data.total_stats %}
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">总客户量</h5>
                <h2 class="text-primary">{{ consultant_data.total_stats.total_customers }}</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">跟进数量</h5>
                <h2 class="text-info">{{ consultant_data.total_stats.followed_customers }}</h2>
                <small class="text-muted">跟进率: {{ consultant_data.total_stats.follow_up_rate }}%</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">跟进到院量</h5>
                <h2 class="text-success">{{ consultant_data.total_stats.visit_success_count }}</h2>
                <small class="text-muted">到院成功率: {{ consultant_data.total_stats.visit_success_rate }}%</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">跟进消费金额</h5>
                <h2 class="text-warning">¥{{ "%.2f"|format(consultant_data.total_stats.total_consumption_amount) }}</h2>
                <small class="text-muted">平均: ¥{{ "%.2f"|format(consultant_data.total_stats.avg_consumption) }}</small>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- 详细统计表格 -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-users me-2"></i>现场咨询师详细统计
        </h5>
    </div>
    <div class="card-body">
        {% if consultant_data.consultant_stats %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>现场咨询师</th>
                        <th class="text-center">客户量</th>
                        <th class="text-center">跟进数量</th>
                        <th class="text-center">跟进率</th>
                        <th class="text-center">跟进到院量</th>
                        <th class="text-center">到院成功率</th>
                        <th class="text-center">跟进消费金额</th>
                        <th class="text-center">平均消费金额</th>
                    </tr>
                </thead>
                <tbody>
                    {% for stat in consultant_data.consultant_stats %}
                    <tr>
                        <td>
                            <strong>{{ stat.consultant.real_name }}</strong>
                            <small class="text-muted d-block">{{ stat.consultant.username }}</small>
                        </td>
                        <td class="text-center">
                            <span class="badge bg-primary">{{ stat.total_customers }}</span>
                        </td>
                        <td class="text-center">
                            <span class="badge bg-info">{{ stat.followed_customers }}</span>
                        </td>
                        <td class="text-center">
                            <span class="badge bg-{% if stat.follow_up_rate >= 80 %}success{% elif stat.follow_up_rate >= 60 %}warning{% else %}danger{% endif %}">
                                {{ stat.follow_up_rate }}%
                            </span>
                        </td>
                        <td class="text-center">
                            <span class="badge bg-success">{{ stat.visit_success_count }}</span>
                        </td>
                        <td class="text-center">
                            <span class="badge bg-{% if stat.visit_success_rate >= 70 %}success{% elif stat.visit_success_rate >= 50 %}warning{% else %}danger{% endif %}">
                                {{ stat.visit_success_rate }}%
                            </span>
                        </td>
                        <td class="text-center">
                            <span class="text-success fw-bold">¥{{ "%.2f"|format(stat.total_consumption_amount) }}</span>
                        </td>
                        <td class="text-center">
                            <span class="text-warning fw-bold">¥{{ "%.2f"|format(stat.avg_consumption) }}</span>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot class="table-secondary">
                    <tr class="fw-bold">
                        <td>总计</td>
                        <td class="text-center">{{ consultant_data.total_stats.total_customers }}</td>
                        <td class="text-center">{{ consultant_data.total_stats.followed_customers }}</td>
                        <td class="text-center">{{ consultant_data.total_stats.follow_up_rate }}%</td>
                        <td class="text-center">{{ consultant_data.total_stats.visit_success_count }}</td>
                        <td class="text-center">{{ consultant_data.total_stats.visit_success_rate }}%</td>
                        <td class="text-center">¥{{ "%.2f"|format(consultant_data.total_stats.total_consumption_amount) }}</td>
                        <td class="text-center">¥{{ "%.2f"|format(consultant_data.total_stats.avg_consumption) }}</td>
                    </tr>
                </tfoot>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">暂无统计数据</h5>
            <p class="text-muted">请检查筛选条件或确认是否有客户数据</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- 统计说明 -->
<div class="card mt-4">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="fas fa-info-circle me-2"></i>统计说明
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <ul class="list-unstyled">
                    <li><strong>客户量：</strong>该现场咨询师负责的客户总数</li>
                    <li><strong>跟进数量：</strong>已进行跟进的客户数量</li>
                    <li><strong>跟进率：</strong>跟进数量 ÷ 客户量 × 100%</li>
                    <li><strong>跟进到院量：</strong>跟进后成功到院的客户数量</li>
                </ul>
            </div>
            <div class="col-md-6">
                <ul class="list-unstyled">
                    <li><strong>到院成功率：</strong>跟进到院量 ÷ 跟进数量 × 100%</li>
                    <li><strong>跟进消费金额：</strong>跟进后客户的总消费金额</li>
                    <li><strong>平均消费金额：</strong>跟进消费金额 ÷ 跟进到院量</li>
                    <li><strong>统计范围：</strong>基于客户登记时间进行筛选</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 页面初始化
$(document).ready(function() {
    console.log('现场咨询师统计页面初始化完成');
    
    // 设置默认日期范围（最近30天）
    if (!$('#start_date').val() && !$('#end_date').val()) {
        const today = new Date();
        const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
        
        $('#end_date').val(today.toISOString().split('T')[0]);
        $('#start_date').val(thirtyDaysAgo.toISOString().split('T')[0]);
    }
});
</script>
{% endblock %}
