#!/usr/bin/env python3
"""
统计表迁移脚本
"""

import os
import sys
from app import create_app, db
from app.models import VisitRecord, ConsumptionRecord


def migrate_statistics_tables():
    """执行统计表迁移"""
    print("开始执行统计表迁移...")
    
    app = create_app('development')
    
    with app.app_context():
        try:
            # 创建新表
            print("创建统计相关表...")
            db.create_all()
            
            # 执行SQL脚本
            sql_file = os.path.join(os.path.dirname(__file__), 'migrations', 'add_statistics_tables.sql')
            
            if os.path.exists(sql_file):
                print("执行SQL迁移脚本...")
                with open(sql_file, 'r', encoding='utf-8') as f:
                    sql_content = f.read()
                
                # 分割SQL语句并执行
                sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
                
                for stmt in sql_statements:
                    if stmt:
                        try:
                            db.session.execute(stmt)
                            print(f"✅ 执行成功: {stmt[:50]}...")
                        except Exception as e:
                            print(f"⚠️ 执行警告: {stmt[:50]}... - {str(e)}")
                
                db.session.commit()
                print("✅ SQL脚本执行完成")
            else:
                print("⚠️ 未找到SQL迁移脚本")
            
            # 验证表是否创建成功
            print("验证表结构...")
            
            # 检查到院明细表
            visit_count = VisitRecord.query.count()
            print(f"✅ 到院明细表创建成功，当前记录数: {visit_count}")
            
            # 检查消费明细表
            consumption_count = ConsumptionRecord.query.count()
            print(f"✅ 消费明细表创建成功，当前记录数: {consumption_count}")
            
            print("🎉 统计表迁移完成！")
            return True
            
        except Exception as e:
            print(f"❌ 迁移失败: {str(e)}")
            db.session.rollback()
            return False


if __name__ == "__main__":
    success = migrate_statistics_tables()
    sys.exit(0 if success else 1)
