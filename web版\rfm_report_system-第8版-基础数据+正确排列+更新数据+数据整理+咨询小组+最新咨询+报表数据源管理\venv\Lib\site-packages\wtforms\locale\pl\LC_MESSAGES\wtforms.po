# Polish translations for WTForms.
# Copyright (C) 2020 WTForms Team
# This file is distributed under the same license as the WTForms project.
# <AUTHOR> <EMAIL>, 2020.
#
msgid ""
msgstr ""
"Project-Id-Version: WTForms 1.0\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2020-04-25 11:34-0700\n"
"PO-Revision-Date: 2012-05-05 23:20+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language: pl\n"
"Language-Team: pl <<EMAIL>>\n"
"Plural-Forms: nplurals=3; plural=(n==1 ? 0 : n%10>=2 && n%10<=4 && "
"(n%100<10 || n%100>=20) ? 1 : 2)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.8.0\n"

#: src/wtforms/validators.py:87
#, python-format
msgid "Invalid field name '%s'."
msgstr "Nieprawidłowa nazwa pola '%s'."

#: src/wtforms/validators.py:98
#, python-format
msgid "Field must be equal to %(other_name)s."
msgstr "Wartość pola musi być równa %(other_name)s."

#: src/wtforms/validators.py:134
#, python-format
msgid "Field must be at least %(min)d character long."
msgid_plural "Field must be at least %(min)d characters long."
msgstr[0] "Pole musi mieć przynajmniej %(min)d znak."
msgstr[1] "Pole musi mieć przynajmniej %(min)d znaki."
msgstr[2] "Pole musi mieć przynajmniej %(min)d znaków."

#: src/wtforms/validators.py:140
#, python-format
msgid "Field cannot be longer than %(max)d character."
msgid_plural "Field cannot be longer than %(max)d characters."
msgstr[0] "Wartość w polu nie może mieć więcej niż %(max)d znak."
msgstr[1] "Wartość w polu nie może mieć więcej niż %(max)d znaki."
msgstr[2] "Wartość w polu nie może mieć więcej niż %(max)d znaków."

#: src/wtforms/validators.py:146
#, python-format
msgid "Field must be exactly %(max)d character long."
msgid_plural "Field must be exactly %(max)d characters long."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: src/wtforms/validators.py:152
#, python-format
msgid "Field must be between %(min)d and %(max)d characters long."
msgstr "Wartość musi być długa na od %(min)d do %(max)d znaków."

#: src/wtforms/validators.py:197
#, python-format
msgid "Number must be at least %(min)s."
msgstr "Liczba musi być większa lub równa %(min)s."

#: src/wtforms/validators.py:199
#, python-format
msgid "Number must be at most %(max)s."
msgstr "Liczba musi być mniejsza lub równa %(max)s."

#: src/wtforms/validators.py:201
#, python-format
msgid "Number must be between %(min)s and %(max)s."
msgstr "Liczba musi być z zakresu %(min)s i %(max)s."

#: src/wtforms/validators.py:269 src/wtforms/validators.py:294
msgid "This field is required."
msgstr "To pole jest wymagane."

#: src/wtforms/validators.py:327
msgid "Invalid input."
msgstr "Nieprawidłowa wartość."

#: src/wtforms/validators.py:387
msgid "Invalid email address."
msgstr "Nieprawidłowy adres e-mail."

#: src/wtforms/validators.py:423
msgid "Invalid IP address."
msgstr "Nieprawidłowy adres IP."

#: src/wtforms/validators.py:466
msgid "Invalid Mac address."
msgstr "Nieprawidłowy adres Mac."

#: src/wtforms/validators.py:501
msgid "Invalid URL."
msgstr "Nieprawidłowy URL."

#: src/wtforms/validators.py:522
msgid "Invalid UUID."
msgstr "Nieprawidłowy UUID."

#: src/wtforms/validators.py:553
#, python-format
msgid "Invalid value, must be one of: %(values)s."
msgstr "Wartość musi być jedną z: %(values)s."

#: src/wtforms/validators.py:588
#, python-format
msgid "Invalid value, can't be any of: %(values)s."
msgstr "Wartość nie może być żadną z: %(values)s."

#: src/wtforms/csrf/core.py:96
msgid "Invalid CSRF Token."
msgstr "Nieprawidłowy token CSRF"

#: src/wtforms/csrf/session.py:63
msgid "CSRF token missing."
msgstr "Brak tokena CSRF"

#: src/wtforms/csrf/session.py:71
msgid "CSRF failed."
msgstr "błąd CSRF"

#: src/wtforms/csrf/session.py:76
msgid "CSRF token expired."
msgstr "Wygasł token CSRF"

#: src/wtforms/fields/core.py:534
msgid "Invalid Choice: could not coerce."
msgstr "Nieprawidłowy wybór: nie można skonwertować"

#: src/wtforms/fields/core.py:538
msgid "Choices cannot be None."
msgstr ""

#: src/wtforms/fields/core.py:545
msgid "Not a valid choice."
msgstr "Nieprawidłowy wybór"

#: src/wtforms/fields/core.py:573
msgid "Invalid choice(s): one or more data inputs could not be coerced."
msgstr "Nieprawidłowy wybór: nie można skonwertować przynajmniej jednej wartości"

#: src/wtforms/fields/core.py:584
#, python-format
msgid "'%(value)s' is not a valid choice for this field."
msgstr "'%(value)s' nie jest poprawnym wyborem dla tego pola"

#: src/wtforms/fields/core.py:679 src/wtforms/fields/core.py:689
msgid "Not a valid integer value."
msgstr "Nieprawidłowa liczba całkowita"

#: src/wtforms/fields/core.py:760
msgid "Not a valid decimal value."
msgstr "Nieprawidłowa liczba dziesiętna"

#: src/wtforms/fields/core.py:788
msgid "Not a valid float value."
msgstr "Nieprawidłowa liczba zmiennoprzecinkowa"

#: src/wtforms/fields/core.py:853
msgid "Not a valid datetime value."
msgstr "Nieprawidłowa data i czas"

#: src/wtforms/fields/core.py:871
msgid "Not a valid date value."
msgstr "Nieprawidłowa data"

#: src/wtforms/fields/core.py:889
msgid "Not a valid time value."
msgstr ""
