"""
权限验证工具函数
"""
from functools import wraps
from flask import session, jsonify, request, redirect, url_for, flash
from flask_login import current_user, login_required

def require_login(f):
    """要求用户登录的装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            if request.is_json:
                return jsonify({'error': '未登录', 'code': 'UNAUTHORIZED'}), 401
            else:
                flash('请先登录', 'warning')
                return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function

def require_permission(permission_code):
    """要求特定权限的装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 首先检查是否登录
            if not current_user.is_authenticated:
                if request.is_json:
                    return jsonify({'error': '未登录', 'code': 'UNAUTHORIZED'}), 401
                else:
                    flash('请先登录', 'warning')
                    return redirect(url_for('auth.login'))
            
            # 检查用户是否有指定权限
            if not current_user.has_permission(permission_code):
                if request.is_json:
                    return jsonify({'error': '权限不足', 'code': 'FORBIDDEN'}), 403
                else:
                    flash('您没有执行此操作的权限', 'error')
                    return redirect(url_for('main.index'))
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_role(role_code):
    """要求特定角色的装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                if request.is_json:
                    return jsonify({'error': '未登录', 'code': 'UNAUTHORIZED'}), 401
                else:
                    flash('请先登录', 'warning')
                    return redirect(url_for('auth.login'))
            
            if current_user.role.role_code != role_code:
                if request.is_json:
                    return jsonify({'error': '角色权限不足', 'code': 'FORBIDDEN'}), 403
                else:
                    flash('您的角色无权访问此功能', 'error')
                    return redirect(url_for('main.index'))
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_admin(f):
    """要求管理员权限的装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            if request.is_json:
                return jsonify({'error': '未登录', 'code': 'UNAUTHORIZED'}), 401
            else:
                flash('请先登录', 'warning')
                return redirect(url_for('auth.login'))
        
        if not current_user.is_admin():
            if request.is_json:
                return jsonify({'error': '需要管理员权限', 'code': 'FORBIDDEN'}), 403
            else:
                flash('需要管理员权限', 'error')
                return redirect(url_for('main.index'))
        
        return f(*args, **kwargs)
    return decorated_function

def check_permission(permission_code, user=None):
    """检查用户是否有指定权限"""
    if user is None:
        user = current_user
    
    if not user.is_authenticated:
        return False
    
    return user.has_permission(permission_code)

def check_data_permission(resource, user=None):
    """检查用户是否有访问特定数据的权限"""
    if user is None:
        user = current_user
    
    if not user.is_authenticated:
        return False
    
    # 管理员和经营院长可以访问所有数据
    if user.role.role_code in ['ADMIN', 'GENERAL_MANAGER']:
        return True
    
    # 部门主管可以访问本部门数据
    if user.role.role_code == 'DEPARTMENT_MANAGER':
        if hasattr(resource, 'registrar'):
            return user.department == resource.registrar.department
        elif hasattr(resource, 'created_by'):
            creator = resource.creator
            return creator and user.department == creator.department
    
    # 网络咨询只能访问自己创建的数据
    if user.role.role_code == 'NETWORK_CONSULTANT':
        if hasattr(resource, 'registrar_id'):
            return resource.registrar_id == user.id
        elif hasattr(resource, 'created_by'):
            return resource.created_by == user.id
    
    # 现场咨询只能访问分配给自己的客户
    if user.role.role_code == 'FIELD_CONSULTANT':
        if hasattr(resource, 'assigned_consultant_id'):
            return resource.assigned_consultant_id == user.id
        elif hasattr(resource, 'consultant_id'):
            return resource.consultant_id == user.id
    
    return False

def get_accessible_users(current_user):
    """获取当前用户可以访问的用户列表"""
    from app.models.user import User
    
    if current_user.role.role_code in ['ADMIN', 'GENERAL_MANAGER']:
        # 管理员和经营院长可以访问所有用户
        return User.query.filter_by(is_active=True).all()
    elif current_user.role.role_code == 'DEPARTMENT_MANAGER':
        # 部门主管只能访问本部门用户
        return User.query.filter_by(
            department=current_user.department,
            is_active=True
        ).all()
    else:
        # 其他角色只能访问自己
        return [current_user]

def filter_query_by_permission(query, model_class, user=None):
    """根据用户权限过滤查询"""
    if user is None:
        user = current_user
    
    if not user.is_authenticated:
        return query.filter(False)
    
    # 使用模型的权限过滤方法
    if hasattr(model_class, 'filter_by_permission'):
        return model_class.filter_by_permission(query, user)
    
    # 默认权限过滤逻辑
    if user.role.role_code in ['ADMIN', 'GENERAL_MANAGER']:
        return query
    elif user.role.role_code == 'DEPARTMENT_MANAGER':
        # 部门主管只能看本部门数据
        from app.models.user import User
        dept_users = User.query.filter_by(department=user.department).all()
        user_ids = [u.id for u in dept_users]
        
        if hasattr(model_class, 'registrar_id'):
            return query.filter(model_class.registrar_id.in_(user_ids))
        elif hasattr(model_class, 'created_by'):
            return query.filter(model_class.created_by.in_(user_ids))
    elif user.role.role_code == 'NETWORK_CONSULTANT':
        # 网络咨询只能看自己的数据
        if hasattr(model_class, 'registrar_id'):
            return query.filter(model_class.registrar_id == user.id)
        elif hasattr(model_class, 'created_by'):
            return query.filter(model_class.created_by == user.id)
    elif user.role.role_code == 'FIELD_CONSULTANT':
        # 现场咨询看分配给自己的客户
        if hasattr(model_class, 'assigned_consultant_id'):
            return query.filter(model_class.assigned_consultant_id == user.id)
        elif hasattr(model_class, 'consultant_id'):
            return query.filter(model_class.consultant_id == user.id)
    
    return query.filter(False)

def get_accessible_users(user):
    """获取用户可访问的用户列表"""
    from app.models.user import User

    if user.role.role_code == 'ADMIN':
        return User.query.filter_by(is_active=True).all()
    elif user.role.role_code == 'GENERAL_MANAGER':
        return User.query.filter_by(is_active=True).all()
    elif user.role.role_code == 'DEPARTMENT_MANAGER':
        return User.query.filter_by(department=user.department, is_active=True).all()
    else:
        return [user]

def check_data_permission(data_obj, user):
    """检查用户是否有权限访问特定数据对象"""
    if user.role.role_code == 'ADMIN':
        return True
    elif user.role.role_code == 'GENERAL_MANAGER':
        return True
    elif user.role.role_code == 'DEPARTMENT_MANAGER':
        # 检查是否为本部门数据
        if hasattr(data_obj, 'registrar'):
            return data_obj.registrar.department == user.department
        elif hasattr(data_obj, 'assigned_consultant'):
            return data_obj.assigned_consultant.department == user.department
        elif hasattr(data_obj, 'department'):
            return data_obj.department == user.department
    elif user.role.role_code == 'NETWORK_CONSULTANT':
        # 只能访问自己登记的数据
        if hasattr(data_obj, 'registrar_id'):
            return data_obj.registrar_id == user.id
        elif hasattr(data_obj, 'created_by'):
            return data_obj.created_by == user.id
    elif user.role.role_code == 'FIELD_CONSULTANT':
        # 只能访问分配给自己的数据
        if hasattr(data_obj, 'assigned_consultant_id'):
            return data_obj.assigned_consultant_id == user.id
        elif hasattr(data_obj, 'consultant_id'):
            return data_obj.consultant_id == user.id

    return False

class PermissionChecker:
    """权限检查器类"""
    
    def __init__(self, user=None):
        self.user = user or current_user
    
    def can_create(self, resource_type):
        """检查是否可以创建资源"""
        permission_map = {
            'user': 'USER_MANAGE',
            'channel': 'CHANNEL_MANAGE',
            'customer': 'CUSTOMER_REGISTER',
            'follow_up': 'FOLLOW_UP_ADD'
        }
        
        permission = permission_map.get(resource_type)
        return permission and self.user.has_permission(permission)
    
    def can_read(self, resource_type, resource=None):
        """检查是否可以读取资源"""
        permission_map = {
            'user': 'USER_MANAGE',
            'channel': 'CHANNEL_VIEW',
            'customer': 'CUSTOMER_VIEW',
            'follow_up': 'FOLLOW_UP_VIEW',
            'report': 'REPORT_VIEW'
        }
        
        permission = permission_map.get(resource_type)
        if not permission or not self.user.has_permission(permission):
            return False
        
        # 如果提供了具体资源，检查数据权限
        if resource:
            return check_data_permission(resource, self.user)
        
        return True
    
    def can_update(self, resource_type, resource=None):
        """检查是否可以更新资源"""
        permission_map = {
            'user': 'USER_MANAGE',
            'channel': 'CHANNEL_MANAGE',
            'customer': 'CUSTOMER_EDIT',
            'follow_up': 'FOLLOW_UP_ADD'
        }
        
        permission = permission_map.get(resource_type)
        if not permission or not self.user.has_permission(permission):
            return False
        
        if resource:
            return check_data_permission(resource, self.user)
        
        return True
    
    def can_delete(self, resource_type, resource=None):
        """检查是否可以删除资源"""
        # 只有管理员可以删除大部分资源
        if not self.user.is_admin():
            return False
        
        if resource:
            return check_data_permission(resource, self.user)
        
        return True
