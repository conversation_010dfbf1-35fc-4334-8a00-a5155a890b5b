"""
Excel文件处理工具
"""
import pandas as pd
import os
from datetime import datetime
from io import BytesIO
from flask import current_app
from .helpers import generate_batch_id, validate_and_convert_excel_date, safe_str, safe_float
from .validators import ValidationError

class ExcelHandler:
    """Excel文件处理器"""
    
    def __init__(self):
        self.supported_formats = ['.xlsx', '.xls']
        self.max_file_size = 16 * 1024 * 1024  # 16MB
    
    def validate_file(self, file):
        """验证上传的文件"""
        if not file:
            raise ValidationError("未选择文件")
        
        if file.filename == '':
            raise ValidationError("文件名不能为空")
        
        # 检查文件扩展名
        _, ext = os.path.splitext(file.filename.lower())
        if ext not in self.supported_formats:
            raise ValidationError(f"不支持的文件格式，请上传 {', '.join(self.supported_formats)} 文件")
        
        # 检查文件大小（如果可能）
        if hasattr(file, 'content_length') and file.content_length:
            if file.content_length > self.max_file_size:
                raise ValidationError(f"文件大小不能超过 {self.max_file_size // (1024*1024)}MB")
        
        return True
    
    def read_excel_file(self, file, sheet_name=0, header=0):
        """读取Excel文件"""
        try:
            self.validate_file(file)
            
            # 读取Excel文件
            df = pd.read_excel(file, sheet_name=sheet_name, header=header)
            
            # 基本验证
            if df.empty:
                raise ValidationError("Excel文件为空")
            
            if len(df.columns) == 0:
                raise ValidationError("Excel文件没有列")
            
            return df
            
        except pd.errors.EmptyDataError:
            raise ValidationError("Excel文件为空或格式不正确")
        except pd.errors.ParserError as e:
            raise ValidationError(f"Excel文件解析失败: {str(e)}")
        except Exception as e:
            current_app.logger.error(f"读取Excel文件失败: {str(e)}")
            raise ValidationError(f"读取Excel文件失败: {str(e)}")
    
    def process_channel_import(self, file):
        """处理渠道导入"""
        df = self.read_excel_file(file)
        
        # 验证必需的列
        required_columns = ['渠道分类', '渠道名称']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValidationError(f"缺少必需的列: {', '.join(missing_columns)}")
        
        results = {
            'total_rows': len(df),
            'success_data': [],
            'error_data': [],
            'batch_id': generate_batch_id('CHANNEL_IMPORT')
        }
        
        for index, row in df.iterrows():
            try:
                # 数据清理和验证
                channel_category = safe_str(row['渠道分类']).strip()
                channel_name = safe_str(row['渠道名称']).strip()
                
                if not channel_category:
                    raise ValueError("渠道分类不能为空")
                
                if not channel_name:
                    raise ValueError("渠道名称不能为空")
                
                if len(channel_category) > 100:
                    raise ValueError("渠道分类长度不能超过100字符")
                
                if len(channel_name) > 200:
                    raise ValueError("渠道名称长度不能超过200字符")
                
                results['success_data'].append({
                    'channel_category': channel_category,
                    'channel_name': channel_name,
                    'row_number': index + 2  # Excel行号从2开始（包含表头）
                })
                
            except Exception as e:
                results['error_data'].append({
                    'row_number': index + 2,
                    'error': str(e),
                    'data': row.to_dict()
                })
        
        return results
    
    def process_consumption_import(self, file):
        """处理消费数据导入"""
        df = self.read_excel_file(file)
        
        # 验证必需的列
        required_columns = ['卡号']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValidationError(f"缺少必需的列: {', '.join(missing_columns)}")
        
        results = {
            'total_rows': len(df),
            'success_data': [],
            'error_data': [],
            'batch_id': generate_batch_id('CONSUMPTION_IMPORT')
        }
        
        for index, row in df.iterrows():
            try:
                # 数据清理和验证
                card_number = safe_str(row['卡号']).strip()
                
                if not card_number:
                    raise ValueError("卡号不能为空")
                
                if not card_number.isdigit():
                    raise ValueError("卡号只能包含数字")
                
                if len(card_number) > 10:
                    raise ValueError("卡号长度不能超过10位")
                
                # 处理到院日期
                visit_date = None
                if '到院日期' in row and pd.notna(row['到院日期']):
                    visit_date = validate_and_convert_excel_date(row['到院日期'])
                    if visit_date is None:
                        raise ValueError("到院日期格式不正确")
                
                # 处理消费金额
                consumption_amount = 0.0
                if '消费金额' in row and pd.notna(row['消费金额']):
                    consumption_amount = safe_float(row['消费金额'])
                    if consumption_amount < 0:
                        raise ValueError("消费金额不能为负数")
                    if consumption_amount > 999999.99:
                        raise ValueError("消费金额不能超过999999.99")
                
                results['success_data'].append({
                    'card_number': card_number,
                    'visit_date': visit_date,
                    'consumption_amount': consumption_amount,
                    'row_number': index + 2
                })
                
            except Exception as e:
                results['error_data'].append({
                    'row_number': index + 2,
                    'error': str(e),
                    'data': row.to_dict()
                })
        
        return results
    
    def process_consultant_mapping_import(self, file):
        """处理现场小组映射导入"""
        df = self.read_excel_file(file)
        
        # 验证必需的列
        required_columns = ['现场咨询员', '现场小组']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValidationError(f"缺少必需的列: {', '.join(missing_columns)}")
        
        results = {
            'total_rows': len(df),
            'success_data': [],
            'error_data': [],
            'batch_id': generate_batch_id('MAPPING_IMPORT')
        }
        
        for index, row in df.iterrows():
            try:
                # 数据清理和验证
                consultant_name = safe_str(row['现场咨询员']).strip()
                group_name = safe_str(row['现场小组']).strip()
                
                if not consultant_name:
                    raise ValueError("现场咨询员不能为空")
                
                if not group_name:
                    raise ValueError("现场小组不能为空")
                
                if len(consultant_name) > 100:
                    raise ValueError("现场咨询员名称长度不能超过100字符")
                
                if len(group_name) > 100:
                    raise ValueError("现场小组名称长度不能超过100字符")
                
                results['success_data'].append({
                    'consultant_name': consultant_name,
                    'group_name': group_name,
                    'row_number': index + 2
                })
                
            except Exception as e:
                results['error_data'].append({
                    'row_number': index + 2,
                    'error': str(e),
                    'data': row.to_dict()
                })
        
        return results
    
    def create_template_file(self, template_type):
        """创建模板文件"""
        templates = {
            'channel': {
                'columns': ['渠道分类', '渠道名称'],
                'sample_data': [
                    ['线上推广', '百度推广'],
                    ['线上推广', '微信朋友圈'],
                    ['线下活动', '商场活动'],
                    ['老客推荐', '老客转介绍']
                ]
            },
            'consumption': {
                'columns': ['卡号', '到院日期', '消费金额'],
                'sample_data': [
                    ['1001', '2024-01-15', 1500.00],
                    ['1002', '2024-01-16', 2800.50],
                    ['1003', '2024-01-17', 0.00]
                ]
            },
            'consultant_mapping': {
                'columns': ['现场咨询员', '现场小组'],
                'sample_data': [
                    ['张三', '第一小组'],
                    ['李四', '第一小组'],
                    ['王五', '第二小组'],
                    ['赵六', '第二小组']
                ]
            }
        }
        
        if template_type not in templates:
            raise ValidationError(f"不支持的模板类型: {template_type}")
        
        template = templates[template_type]
        
        # 创建DataFrame
        df = pd.DataFrame(template['sample_data'], columns=template['columns'])
        
        # 创建Excel文件
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='模板', index=False)
            
            # 获取工作表并设置列宽
            worksheet = writer.sheets['模板']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width
        
        output.seek(0)
        return output
    
    def export_data_to_excel(self, data, columns, sheet_name='数据导出'):
        """导出数据到Excel"""
        try:
            # 创建DataFrame
            df = pd.DataFrame(data, columns=columns)
            
            # 创建Excel文件
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name=sheet_name, index=False)
                
                # 设置列宽
                worksheet = writer.sheets[sheet_name]
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            output.seek(0)
            return output
            
        except Exception as e:
            current_app.logger.error(f"导出Excel失败: {str(e)}")
            raise ValidationError(f"导出Excel失败: {str(e)}")
