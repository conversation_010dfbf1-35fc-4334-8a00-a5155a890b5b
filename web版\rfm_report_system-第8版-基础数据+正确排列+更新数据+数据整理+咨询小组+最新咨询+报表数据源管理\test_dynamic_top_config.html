<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动态TOP配置测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2><i class="fas fa-cog me-2"></i>动态TOP配置测试</h2>
        <p class="text-muted">测试用户可控的科室和TOP数量配置功能</p>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>配置选项</h5>
                    </div>
                    <div class="card-body">
                        <!-- 科室选择 -->
                        <div class="mb-3">
                            <label class="form-label">科室选择</label>
                            <div class="row">
                                <div class="col-md-6 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="dept_skin" value="皮肤" checked>
                                        <label class="form-check-label" for="dept_skin">皮肤科</label>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="dept_injection" value="注射" checked>
                                        <label class="form-check-label" for="dept_injection">注射科</label>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="dept_oral" value="口腔">
                                        <label class="form-check-label" for="dept_oral">口腔科</label>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="dept_plastic" value="整形">
                                        <label class="form-check-label" for="dept_plastic">整形科</label>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="dept_hair" value="毛发">
                                        <label class="form-check-label" for="dept_hair">毛发科</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- TOP数量配置 -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="categoryTopCount" class="form-label">品类TOP数</label>
                                    <input type="number" class="form-control" id="categoryTopCount" value="5" min="1" max="20">
                                    <small class="form-text text-muted">每个科室显示的品类TOP数量</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="itemTopCount" class="form-label">品项TOP数</label>
                                    <input type="number" class="form-control" id="itemTopCount" value="15" min="1" max="50">
                                    <small class="form-text text-muted">每个科室显示的品项TOP数量</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-eye me-2"></i>配置预览</h5>
                    </div>
                    <div class="card-body">
                        <div id="configPreview">
                            <!-- 预览内容将在这里显示 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 列名预览 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-columns me-2"></i>生成的列名预览</h5>
                    </div>
                    <div class="card-body">
                        <div id="columnPreview">
                            <!-- 列名预览将在这里显示 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        $(document).ready(function() {
            updatePreview();
            
            // 监听所有配置变化
            $('input[type="checkbox"], input[type="number"]').on('change input', function() {
                updatePreview();
            });
        });
        
        function updatePreview() {
            // 获取选中的科室
            var selectedDepartments = [];
            $('input[type="checkbox"]:checked').each(function() {
                selectedDepartments.push({
                    value: $(this).val(),
                    text: $(this).next('label').text()
                });
            });
            
            var categoryTopCount = parseInt($('#categoryTopCount').val()) || 5;
            var itemTopCount = parseInt($('#itemTopCount').val()) || 15;
            
            // 更新配置预览
            updateConfigPreview(selectedDepartments, categoryTopCount, itemTopCount);
            
            // 更新列名预览
            updateColumnPreview(selectedDepartments, categoryTopCount, itemTopCount);
        }
        
        function updateConfigPreview(departments, categoryTop, itemTop) {
            if (departments.length === 0) {
                $('#configPreview').html('<div class="alert alert-warning">请至少选择一个科室</div>');
                return;
            }
            
            var html = '<div class="row">';
            
            // 品类配置
            html += '<div class="col-md-6">';
            html += '<h6><i class="fas fa-tags me-2 text-primary"></i>品类配置</h6>';
            html += '<ul class="list-unstyled">';
            departments.forEach(function(dept) {
                html += '<li><i class="fas fa-check-circle text-success me-1"></i>' + dept.text + ' TOP1-' + categoryTop + '</li>';
            });
            html += '</ul>';
            html += '</div>';
            
            // 品项配置
            html += '<div class="col-md-6">';
            html += '<h6><i class="fas fa-cube me-2 text-info"></i>品项配置</h6>';
            html += '<ul class="list-unstyled">';
            departments.forEach(function(dept) {
                html += '<li><i class="fas fa-check-circle text-success me-1"></i>' + dept.text + ' TOP1-' + itemTop + '</li>';
            });
            html += '</ul>';
            html += '</div>';
            
            html += '</div>';
            
            // 统计信息
            var totalCategoryColumns = departments.length * categoryTop;
            var totalItemColumns = departments.length * itemTop;
            var totalColumns = totalCategoryColumns + totalItemColumns;
            
            html += '<hr>';
            html += '<div class="row text-center">';
            html += '<div class="col-md-3">';
            html += '<h5 class="text-primary">' + totalCategoryColumns + '</h5>';
            html += '<small class="text-muted">品类列数</small>';
            html += '</div>';
            html += '<div class="col-md-3">';
            html += '<h5 class="text-info">' + totalItemColumns + '</h5>';
            html += '<small class="text-muted">品项列数</small>';
            html += '</div>';
            html += '<div class="col-md-3">';
            html += '<h5 class="text-success">' + totalColumns + '</h5>';
            html += '<small class="text-muted">总列数</small>';
            html += '</div>';
            html += '<div class="col-md-3">';
            html += '<h5 class="text-warning">' + departments.length + '</h5>';
            html += '<small class="text-muted">科室数</small>';
            html += '</div>';
            html += '</div>';
            
            $('#configPreview').html(html);
        }
        
        function updateColumnPreview(departments, categoryTop, itemTop) {
            if (departments.length === 0) {
                $('#columnPreview').html('<div class="alert alert-warning">请至少选择一个科室</div>');
                return;
            }
            
            var html = '';
            
            departments.forEach(function(dept) {
                html += '<div class="mb-4">';
                html += '<h6 class="text-primary">' + dept.text + '</h6>';
                
                // 品类列
                html += '<div class="mb-3">';
                html += '<strong class="text-success">品类列 (TOP1-' + categoryTop + '):</strong><br>';
                for (var i = 1; i <= categoryTop; i++) {
                    html += '<span class="badge bg-success me-1 mb-1">' + dept.value + '_TOP品类' + i + '_[品类名]</span>';
                }
                html += '</div>';
                
                // 品项列
                html += '<div class="mb-3">';
                html += '<strong class="text-info">品项列 (TOP1-' + itemTop + '):</strong><br>';
                for (var i = 1; i <= itemTop; i++) {
                    html += '<span class="badge bg-info me-1 mb-1">' + dept.value + '_TOP品项' + i + '_[品项名]</span>';
                    if (i % 5 === 0) html += '<br>';
                }
                html += '</div>';
                
                html += '</div>';
            });
            
            $('#columnPreview').html(html);
        }
    </script>
</body>
</html>
