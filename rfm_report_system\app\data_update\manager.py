"""
数据更新管理器
处理数据追加、更新和新表创建的核心逻辑
"""

import pandas as pd
import logging
import re
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime
from sqlalchemy import create_engine, text, inspect, MetaData, Table, Column
from sqlalchemy.exc import SQLAlchemyError
import os
import tempfile
import numpy as np


class DataUpdateManager:
    """数据更新管理器"""
    
    def __init__(self, db_engine, logger=None):
        self.db_engine = db_engine
        self.logger = logger or logging.getLogger(__name__)
        self.temp_files = []  # 跟踪临时文件

        # 映射相关属性
        self.mapping_options = {}
        self.level1_mappings = {}
        self.level2_mappings = {}
        self.level3_mappings = {}
        self.field_mappings = {}
        self.field_group_mappings = {}

    def _convert_to_json_serializable(self, obj):
        """将pandas/numpy数据类型转换为JSON可序列化的类型"""
        if isinstance(obj, (np.integer, np.int64, np.int32)):
            return int(obj)
        elif isinstance(obj, (np.floating, np.float64, np.float32)):
            return float(obj)
        elif isinstance(obj, dict):
            return {key: self._convert_to_json_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_to_json_serializable(item) for item in obj]
        elif pd.isna(obj):
            return None
        else:
            return obj
        
    def __del__(self):
        """清理临时文件"""
        self.cleanup_temp_files()
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        for temp_file in self.temp_files:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            except Exception as e:
                self.logger.warning(f"清理临时文件失败: {temp_file}, 错误: {str(e)}")
        self.temp_files.clear()
    
    def get_database_tables(self, operation_type: str = None) -> List[Dict]:
        """获取数据库中的表，根据操作类型进行过滤"""
        try:
            inspector = inspect(self.db_engine)
            tables = []

            for table_name in inspector.get_table_names():
                # 根据操作类型过滤表
                if operation_type == 'append' and self._is_quarterly_table(table_name):
                    # 追加模式下跳过季度表
                    continue

                # 获取表的基本信息
                try:
                    # 获取记录数量
                    count_query = text(f"SELECT COUNT(*) FROM `{table_name}`")
                    with self.db_engine.connect() as conn:
                        result = conn.execute(count_query)
                        record_count = result.scalar()

                    # 获取表的列信息
                    columns = inspector.get_columns(table_name)

                    tables.append({
                        'name': table_name,
                        'record_count': record_count,
                        'column_count': len(columns),
                        'columns': [col['name'] for col in columns],
                        'is_quarterly': self._is_quarterly_table(table_name)
                    })
                except Exception as e:
                    self.logger.warning(f"获取表 {table_name} 信息失败: {str(e)}")
                    tables.append({
                        'name': table_name,
                        'record_count': 0,
                        'column_count': 0,
                        'columns': [],
                        'is_quarterly': self._is_quarterly_table(table_name)
                    })

            return sorted(tables, key=lambda x: x['name'])

        except Exception as e:
            self.logger.error(f"获取数据库表列表失败: {str(e)}")
            raise

    def _is_quarterly_table(self, table_name: str) -> bool:
        """判断是否为季度表"""
        quarterly_keywords = ['季度', 'quarterly', 'quarter', 'q1', 'q2', 'q3', 'q4']
        table_name_lower = table_name.lower()

        for keyword in quarterly_keywords:
            if keyword in table_name_lower:
                return True

        # 检查是否包含季度模式，如 2024Q1, 2024_Q2 等
        import re
        quarterly_pattern = r'(20\d{2}[_\-]?q[1-4]|q[1-4][_\-]?20\d{2})'
        if re.search(quarterly_pattern, table_name_lower):
            return True

        return False
    
    def get_table_structure(self, table_name: str) -> Dict:
        """获取表结构详细信息"""
        try:
            inspector = inspect(self.db_engine)
            
            # 获取列信息
            columns = inspector.get_columns(table_name)
            
            # 获取主键信息
            primary_keys = inspector.get_pk_constraint(table_name)
            
            # 获取示例数据
            sample_query = text(f"SELECT * FROM `{table_name}` LIMIT 5")
            with self.db_engine.connect() as conn:
                sample_df = pd.read_sql(sample_query, conn)
            
            return {
                'table_name': table_name,
                'columns': columns,
                'primary_keys': primary_keys.get('constrained_columns', []),
                'sample_data': sample_df.to_dict('records') if not sample_df.empty else [],
                'column_names': [col['name'] for col in columns]
            }
            
        except Exception as e:
            self.logger.error(f"获取表 {table_name} 结构失败: {str(e)}")
            raise
    
    def validate_excel_file(self, file_path: str) -> Tuple[bool, str, Optional[pd.DataFrame]]:
        """验证Excel文件格式和内容"""
        try:
            self.logger.info(f"开始验证Excel文件: {file_path}")

            # 检查文件是否存在
            if not os.path.exists(file_path):
                return False, "文件不存在", None

            # 检查文件大小（最大50MB）
            file_size = os.path.getsize(file_path)
            if file_size > 50 * 1024 * 1024:
                return False, "文件大小超过50MB限制", None

            self.logger.info(f"文件大小: {file_size / 1024 / 1024:.2f}MB")

            # 尝试读取Excel文件
            try:
                self.logger.info("正在读取Excel文件...")
                df = pd.read_excel(file_path)
                self.logger.info(f"Excel文件读取完成，行数: {len(df)}, 列数: {len(df.columns)}")
            except Exception as e:
                return False, f"Excel文件格式错误: {str(e)}", None

            # 检查是否为空文件
            if df.empty:
                return False, "Excel文件为空", None

            # 检查是否有列名
            if df.columns.empty:
                return False, "Excel文件没有列名", None

            # 检查数据行数限制 - 调整为更合理的限制
            if len(df) > 500000:  # 限制50万行
                return False, f"数据行数过多({len(df)}行)，请分批处理（建议每次不超过50万行）", None
            elif len(df) > 200000:  # 超过20万行给出警告但允许处理
                self.logger.warning(f"数据量较大({len(df)}行)，处理可能需要较长时间")
                print(f"⚠️ 警告：数据量较大({len(df)}行)，处理可能需要较长时间")

            # 预处理数据，确保列名和数据类型正确
            df = self._preprocess_dataframe(df)

            self.logger.info("Excel文件验证通过")
            return True, "文件验证通过", df

        except Exception as e:
            self.logger.error(f"验证Excel文件失败: {str(e)}")
            return False, f"文件验证失败: {str(e)}", None
    
    def validate_columns_match(self, excel_df: pd.DataFrame, table_name: str) -> Tuple[bool, str, Dict]:
        """验证Excel列名与数据库表列名是否匹配"""
        try:
            # 获取数据库表结构
            table_structure = self.get_table_structure(table_name)
            db_columns = table_structure['column_names']

            # 安全地获取Excel列名，处理各种数据类型
            excel_columns = []
            for col in excel_df.columns:
                if isinstance(col, str):
                    excel_columns.append(col)
                else:
                    # 将非字符串类型转换为字符串
                    excel_columns.append(str(col))

            self.logger.info(f"数据库列名: {db_columns}")
            self.logger.info(f"Excel列名: {excel_columns}")
            
            # 创建详细的比较结果
            comparison = {
                'db_columns': db_columns,
                'excel_columns': excel_columns,
                'missing_in_excel': [],
                'extra_in_excel': [],
                'order_mismatch': False
            }
            
            # 检查缺失的列
            for col in db_columns:
                if col not in excel_columns:
                    comparison['missing_in_excel'].append(col)
            
            # 检查多余的列
            for col in excel_columns:
                if col not in db_columns:
                    comparison['extra_in_excel'].append(col)
            
            # 检查顺序是否一致
            if excel_columns != db_columns:
                comparison['order_mismatch'] = True
            
            # 判断是否匹配
            is_match = (len(comparison['missing_in_excel']) == 0 and 
                       len(comparison['extra_in_excel']) == 0 and 
                       not comparison['order_mismatch'])
            
            if is_match:
                return True, "列名完全匹配", comparison
            else:
                error_msg = self._generate_column_mismatch_message(comparison)
                return False, error_msg, comparison
                
        except Exception as e:
            self.logger.error(f"验证列名匹配失败: {str(e)}")
            return False, f"列名验证失败: {str(e)}", {}
    
    def _generate_column_mismatch_message(self, comparison: Dict) -> str:
        """生成列名不匹配的详细错误信息"""
        messages = []
        
        if comparison['missing_in_excel']:
            messages.append(f"Excel中缺失的列: {', '.join(comparison['missing_in_excel'])}")
        
        if comparison['extra_in_excel']:
            messages.append(f"Excel中多余的列: {', '.join(comparison['extra_in_excel'])}")
        
        if comparison['order_mismatch'] and not comparison['missing_in_excel'] and not comparison['extra_in_excel']:
            messages.append("列的顺序不匹配")
        
        return "; ".join(messages)
    
    def infer_data_types(self, df: pd.DataFrame) -> Dict[str, str]:
        """推断DataFrame各列的数据类型"""
        type_mapping = {}
        
        for column in df.columns:
            series = df[column].dropna()  # 去除空值进行类型推断
            
            if series.empty:
                type_mapping[column] = 'VARCHAR(255)'
                continue
            
            # 尝试推断数据类型
            if pd.api.types.is_integer_dtype(series):
                type_mapping[column] = 'INT'
            elif pd.api.types.is_float_dtype(series):
                type_mapping[column] = 'DECIMAL(10,2)'
            elif pd.api.types.is_datetime64_any_dtype(series):
                type_mapping[column] = 'DATE'
            elif pd.api.types.is_bool_dtype(series):
                type_mapping[column] = 'BOOLEAN'
            else:
                # 文本类型，根据最大长度确定
                max_length = series.astype(str).str.len().max()
                if max_length <= 255:
                    type_mapping[column] = f'VARCHAR({max(max_length, 50)})'
                else:
                    type_mapping[column] = 'TEXT'
        
        return type_mapping
    
    def validate_table_name(self, table_name: str) -> Tuple[bool, str]:
        """验证新表名的合法性"""
        try:
            # 检查表名格式
            if not re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', table_name):
                return False, "表名只能包含字母、数字和下划线，且必须以字母开头"
            
            # 检查长度
            if len(table_name) < 3 or len(table_name) > 64:
                return False, "表名长度必须在3-64个字符之间"
            
            # 检查是否为保留字（简化版）
            reserved_words = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'TABLE', 'INDEX']
            if table_name.upper() in reserved_words:
                return False, f"表名不能使用保留字: {table_name}"
            
            # 检查是否已存在
            inspector = inspect(self.db_engine)
            existing_tables = inspector.get_table_names()
            if table_name in existing_tables:
                return False, f"表名 '{table_name}' 已存在"
            
            return True, "表名验证通过"
            
        except Exception as e:
            self.logger.error(f"验证表名失败: {str(e)}")
            return False, f"表名验证失败: {str(e)}"

    def append_data(self, excel_df: pd.DataFrame, table_name: str, progress_callback=None) -> Tuple[bool, str, Dict]:
        """追加数据到现有表"""
        try:
            self.logger.info(f"开始追加数据到表 {table_name}")

            # 验证列名匹配
            is_match, match_msg, comparison = self.validate_columns_match(excel_df, table_name)
            if not is_match:
                return False, f"列名不匹配: {match_msg}", {}

            # 开始事务
            with self.db_engine.begin() as conn:
                # 记录操作前的行数
                count_before = pd.read_sql(text(f"SELECT COUNT(*) as count FROM `{table_name}`"), conn).iloc[0]['count']

                # 分批插入数据以提高性能
                self._batch_insert_data(excel_df, table_name, conn, progress_callback)

                # 记录操作后的行数
                count_after = pd.read_sql(text(f"SELECT COUNT(*) as count FROM `{table_name}`"), conn).iloc[0]['count']

                result = {
                    'operation': 'append',
                    'table_name': table_name,
                    'rows_before': int(count_before),
                    'rows_after': int(count_after),
                    'rows_inserted': int(count_after - count_before),
                    'timestamp': datetime.now().isoformat()
                }

                self.logger.info(f"数据追加成功: {result}")
                return True, f"成功追加 {result['rows_inserted']} 行数据", self._convert_to_json_serializable(result)

        except Exception as e:
            self.logger.error(f"追加数据失败: {str(e)}")
            return False, f"追加数据失败: {str(e)}", {}

    def update_data(self, excel_df: pd.DataFrame, table_name: str, progress_callback=None) -> Tuple[bool, str, Dict]:
        """更新表数据（清空后重新插入）"""
        try:
            self.logger.info(f"开始更新表 {table_name} 的数据")

            # 验证列名匹配
            is_match, match_msg, comparison = self.validate_columns_match(excel_df, table_name)
            if not is_match:
                return False, f"列名不匹配: {match_msg}", {}

            # 开始事务
            with self.db_engine.begin() as conn:
                # 记录操作前的行数
                count_before = pd.read_sql(text(f"SELECT COUNT(*) as count FROM `{table_name}`"), conn).iloc[0]['count']

                # 清空表数据
                conn.execute(text(f"DELETE FROM `{table_name}`"))
                if progress_callback:
                    progress_callback(f"🗑️ 已清空表 {table_name}，删除了 {count_before} 行数据")

                # 分批插入新数据
                self._batch_insert_data(excel_df, table_name, conn, progress_callback)

                # 记录操作后的行数
                count_after = pd.read_sql(text(f"SELECT COUNT(*) as count FROM `{table_name}`"), conn).iloc[0]['count']

                result = {
                    'operation': 'update',
                    'table_name': table_name,
                    'rows_before': int(count_before),
                    'rows_after': int(count_after),
                    'rows_deleted': int(count_before),
                    'rows_inserted': int(count_after),
                    'timestamp': datetime.now().isoformat()
                }

                self.logger.info(f"数据更新成功: {result}")
                return True, f"成功更新数据：删除 {result['rows_deleted']} 行，插入 {result['rows_inserted']} 行", self._convert_to_json_serializable(result)

        except Exception as e:
            self.logger.error(f"更新数据失败: {str(e)}")
            return False, f"更新数据失败: {str(e)}", {}

    def create_new_table(self, excel_df: pd.DataFrame, table_name: str, column_types: Dict[str, str] = None, progress_callback=None) -> Tuple[bool, str, Dict]:
        """创建新表并插入数据"""
        try:
            self.logger.info(f"开始创建新表 {table_name}")

            # 验证表名
            is_valid, validation_msg = self.validate_table_name(table_name)
            if not is_valid:
                return False, validation_msg, {}

            # 推断或使用指定的数据类型
            if column_types is None:
                column_types = self.infer_data_types(excel_df)
                if progress_callback:
                    progress_callback(f"🔍 已推断 {len(column_types)} 个列的数据类型")

            # 开始事务
            with self.db_engine.begin() as conn:
                # 生成CREATE TABLE语句
                create_sql = self._generate_create_table_sql(table_name, excel_df.columns, column_types)

                # 创建表
                conn.execute(text(create_sql))
                if progress_callback:
                    progress_callback(f"✅ 表 {table_name} 创建成功")

                # 分批插入数据
                self._batch_insert_data(excel_df, table_name, conn, progress_callback)

                # 记录操作结果
                count_after = pd.read_sql(text(f"SELECT COUNT(*) as count FROM `{table_name}`"), conn).iloc[0]['count']

                result = {
                    'operation': 'create_table',
                    'table_name': table_name,
                    'columns_created': len(excel_df.columns),
                    'rows_inserted': int(count_after),
                    'column_types': column_types,
                    'timestamp': datetime.now().isoformat()
                }

                self.logger.info(f"新表创建成功: {result}")
                return True, f"成功创建表 '{table_name}' 并插入 {result['rows_inserted']} 行数据", self._convert_to_json_serializable(result)

        except Exception as e:
            self.logger.error(f"创建新表失败: {str(e)}")
            return False, f"创建新表失败: {str(e)}", {}

    def _generate_create_table_sql(self, table_name: str, columns: List[str], column_types: Dict[str, str]) -> str:
        """生成CREATE TABLE SQL语句"""
        column_definitions = []

        for column in columns:
            col_type = column_types.get(column, 'VARCHAR(255)')
            # 处理列名中的特殊字符
            safe_column_name = f"`{column}`"
            column_definitions.append(f"{safe_column_name} {col_type}")

        sql = f"CREATE TABLE `{table_name}` (\n"
        sql += ",\n".join(column_definitions)
        sql += "\n)"

        return sql

    def get_operation_preview(self, operation_type: str, excel_df: pd.DataFrame, table_name: str = None) -> Dict:
        """获取操作预览信息"""
        try:
            preview = {
                'operation_type': operation_type,
                'excel_rows': len(excel_df),
                'excel_columns': len(excel_df.columns),
                'excel_column_names': list(excel_df.columns),
                'sample_data': excel_df.head(10).to_dict('records'),
                'timestamp': datetime.now().isoformat()
            }

            if table_name:
                try:
                    # 获取目标表信息
                    table_structure = self.get_table_structure(table_name)
                    preview['target_table'] = {
                        'name': table_name,
                        'current_rows': len(table_structure['sample_data']),
                        'columns': table_structure['column_names']
                    }

                    # 验证列名匹配
                    is_match, match_msg, comparison = self.validate_columns_match(excel_df, table_name)
                    preview['column_validation'] = {
                        'is_match': is_match,
                        'message': match_msg,
                        'comparison': comparison
                    }

                except Exception as e:
                    preview['target_table_error'] = str(e)

            if operation_type == 'create_table':
                # 推断数据类型
                inferred_types = self.infer_data_types(excel_df)
                preview['inferred_types'] = inferred_types

            return preview

        except Exception as e:
            self.logger.error(f"生成操作预览失败: {str(e)}")
            return {'error': str(e)}

    def save_uploaded_file(self, file_obj, filename: str) -> str:
        """保存上传的文件到临时目录"""
        try:
            # 创建临时文件
            temp_dir = tempfile.gettempdir()
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            safe_filename = f"{timestamp}_{filename}"
            temp_path = os.path.join(temp_dir, safe_filename)

            # 保存文件
            file_obj.save(temp_path)

            # 记录临时文件路径
            self.temp_files.append(temp_path)

            self.logger.info(f"文件保存到临时路径: {temp_path}")
            return temp_path

        except Exception as e:
            self.logger.error(f"保存上传文件失败: {str(e)}")
            raise

    def _batch_insert_data(self, excel_df, table_name, conn, progress_callback=None):
        """分批插入数据以提高性能"""
        batch_size = 10000  # 每批1万行
        total_rows = len(excel_df)

        if total_rows > batch_size:
            self.logger.info(f"数据量较大({total_rows}行)，将分批插入，每批{batch_size}行")
            print(f"📊 数据量较大({total_rows}行)，将分批插入，每批{batch_size}行")
            if progress_callback:
                progress_callback(f"📊 数据量较大({total_rows}行)，将分批插入，每批{batch_size}行")

            for i in range(0, total_rows, batch_size):
                batch_end = min(i + batch_size, total_rows)
                batch_df = excel_df.iloc[i:batch_end]
                batch_df.to_sql(table_name, conn, if_exists='append', index=False, method='multi')

                progress = int((batch_end / total_rows) * 100)
                progress_msg = f"📈 已插入 {batch_end}/{total_rows} 行 ({progress}%)"
                self.logger.info(progress_msg)
                print(progress_msg)
                if progress_callback:
                    progress_callback(progress_msg)
        else:
            excel_df.to_sql(table_name, conn, if_exists='append', index=False, method='multi')

    def _preprocess_dataframe(self, df):
        """预处理DataFrame，确保数据类型和列名正确"""
        try:
            # 处理列名，确保都是字符串
            new_columns = []
            for col in df.columns:
                if isinstance(col, str):
                    new_columns.append(col.strip())  # 去除空格
                else:
                    new_columns.append(str(col).strip())

            df.columns = new_columns

            # 处理数据中的特殊类型
            for col in df.columns:
                # 处理日期时间类型
                if df[col].dtype == 'object':
                    # 尝试检测是否为日期时间字符串
                    try:
                        # 如果是日期时间类型，转换为字符串
                        if pd.api.types.is_datetime64_any_dtype(df[col]):
                            df[col] = df[col].astype(str)
                    except:
                        pass

                # 处理NaN值
                df[col] = df[col].fillna('')

            return df

        except Exception as e:
            self.logger.error(f"数据预处理失败: {str(e)}")
            return df  # 如果预处理失败，返回原始数据
