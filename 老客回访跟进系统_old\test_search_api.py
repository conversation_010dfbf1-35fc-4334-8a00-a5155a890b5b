#!/usr/bin/env python3
"""
测试搜索API功能
"""
import requests
import json

def test_api_endpoints():
    """测试API接口"""
    base_url = "http://localhost:5001"
    
    # 首先登录获取session
    session = requests.Session()
    
    print("1. 测试登录...")
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    try:
        login_response = session.post(f"{base_url}/auth/login", data=login_data)
        if login_response.status_code == 200:
            print("✅ 登录成功")
        else:
            print(f"❌ 登录失败: {login_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
        return False
    
    # 测试渠道API
    print("\n2. 测试渠道API...")
    try:
        # 不带搜索参数
        response = session.get(f"{base_url}/api/channels")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✅ 获取渠道列表成功，共 {len(data['data'])} 个渠道")
                if data['data']:
                    first_channel = data['data'][0]
                    print(f"   第一个渠道: {first_channel.get('text', 'N/A')}")
                    print(f"   拼音: {first_channel.get('pinyin', 'N/A')}")
            else:
                print(f"❌ 渠道API返回失败: {data}")
        else:
            print(f"❌ 渠道API请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 渠道API请求异常: {e}")
    
    # 测试渠道搜索
    print("\n3. 测试渠道搜索...")
    try:
        # 搜索"推广"
        response = session.get(f"{base_url}/api/channels?search=推广")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✅ 搜索'推广'成功，找到 {len(data['data'])} 个结果")
                for item in data['data'][:3]:  # 显示前3个结果
                    print(f"   - {item.get('text', 'N/A')}")
            else:
                print(f"❌ 渠道搜索返回失败: {data}")
        else:
            print(f"❌ 渠道搜索请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 渠道搜索请求异常: {e}")
    
    # 测试拼音搜索
    print("\n4. 测试拼音搜索...")
    try:
        # 搜索"tg"（推广的拼音首字母）
        response = session.get(f"{base_url}/api/channels?search=tg")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✅ 搜索'tg'成功，找到 {len(data['data'])} 个结果")
                for item in data['data'][:3]:  # 显示前3个结果
                    print(f"   - {item.get('text', 'N/A')}")
            else:
                print(f"❌ 拼音搜索返回失败: {data}")
        else:
            print(f"❌ 拼音搜索请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 拼音搜索请求异常: {e}")
    
    # 测试现场顾问API
    print("\n5. 测试现场顾问API...")
    try:
        response = session.get(f"{base_url}/api/consultants")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✅ 获取现场顾问列表成功，共 {len(data['data'])} 个顾问")
                if data['data']:
                    first_consultant = data['data'][0]
                    print(f"   第一个顾问: {first_consultant.get('text', 'N/A')}")
                    print(f"   拼音: {first_consultant.get('pinyin', 'N/A')}")
            else:
                print(f"❌ 现场顾问API返回失败: {data}")
        else:
            print(f"❌ 现场顾问API请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 现场顾问API请求异常: {e}")
    
    # 测试网络咨询员API
    print("\n6. 测试网络咨询员API...")
    try:
        response = session.get(f"{base_url}/api/registrars")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✅ 获取网络咨询员列表成功，共 {len(data['data'])} 个咨询员")
                if data['data']:
                    first_registrar = data['data'][0]
                    print(f"   第一个咨询员: {first_registrar.get('text', 'N/A')}")
                    print(f"   拼音: {first_registrar.get('pinyin', 'N/A')}")
            else:
                print(f"❌ 网络咨询员API返回失败: {data}")
        else:
            print(f"❌ 网络咨询员API请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 网络咨询员API请求异常: {e}")
    
    return True

if __name__ == "__main__":
    print("测试搜索API功能")
    print("=" * 50)
    
    success = test_api_endpoints()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 API测试完成！")
        print("\n现在可以访问以下页面测试搜索功能:")
        print("- 客户登记: http://localhost:5001/customer/register")
        print("- 客户管理: http://localhost:5001/customer/")
        print("\n搜索功能说明:")
        print("✅ 支持中文搜索")
        print("✅ 支持拼音首字母搜索")
        print("✅ 支持部分匹配")
        print("✅ 实时搜索（输入时自动搜索）")
    else:
        print("❌ API测试失败，请检查系统状态")
