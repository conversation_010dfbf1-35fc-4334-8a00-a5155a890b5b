{% extends "base.html" %}

{% block title %}修改密码 - 老客回访与跟进系统{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-key"></i> 修改密码</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="current_password" class="form-label">当前密码</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                            <input type="password" class="form-control" id="current_password" 
                                   name="current_password" placeholder="请输入当前密码" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="new_password" class="form-label">新密码</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-key"></i></span>
                            <input type="password" class="form-control" id="new_password" 
                                   name="new_password" placeholder="请输入新密码" required>
                        </div>
                        <div class="form-text">
                            密码长度至少8位，必须包含大写字母、小写字母和数字
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">确认新密码</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-check"></i></span>
                            <input type="password" class="form-control" id="confirm_password" 
                                   name="confirm_password" placeholder="请再次输入新密码" required>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 修改密码
                        </button>
                        <a href="{{ url_for('main.profile') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-shield-alt"></i> 密码安全提示</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> 使用强密码，包含大小写字母、数字</li>
                    <li><i class="fas fa-check text-success"></i> 定期更换密码</li>
                    <li><i class="fas fa-check text-success"></i> 不要与他人分享密码</li>
                    <li><i class="fas fa-check text-success"></i> 不要在多个系统使用相同密码</li>
                </ul>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
// 密码强度检查
document.getElementById('new_password').addEventListener('input', function() {
    const password = this.value;
    const strengthIndicator = document.getElementById('password-strength');
    
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    
    // 可以添加密码强度显示逻辑
});

// 确认密码检查
document.getElementById('confirm_password').addEventListener('input', function() {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = this.value;
    
    if (newPassword !== confirmPassword) {
        this.setCustomValidity('两次输入的密码不一致');
    } else {
        this.setCustomValidity('');
    }
});
</script>
{% endblock %}
