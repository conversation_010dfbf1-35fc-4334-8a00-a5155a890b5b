"""
数据更新模块路由
处理数据更新相关的HTTP请求
"""

from flask import render_template, request, jsonify, flash, redirect, url_for, session
from flask_login import login_required, current_user
import os
from datetime import datetime
import json

from app.models import DatabaseConfig
from .manager import DataUpdateManager
from .preview_manager import DataPreviewManager
from .cleanup_manager import DataCleanupManager
from . import bp
from sqlalchemy import create_engine, text


def get_database_engine():
    """获取数据库引擎"""
    db_config = DatabaseConfig.get_active_config()
    if not db_config:
        return None

    try:
        connection_string = db_config.get_connection_string()
        engine = create_engine(connection_string)
        # 测试连接
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        return engine
    except Exception as e:
        print(f"创建数据库引擎失败: {str(e)}")
        return None


@bp.route('/')
@login_required
def index():
    """数据更新主页"""
    # 获取当前活跃的数据库配置
    db_config = DatabaseConfig.get_active_config()

    if not db_config:
        flash('请先在数据库配置中设置并启用一个数据库连接', 'warning')
        return redirect(url_for('config_mgmt.list_configs'))

    # 测试数据库连接
    try:
        engine = create_engine(db_config.get_connection_string())
        manager = DataUpdateManager(engine)
        # 获取所有表（不进行过滤，在前端根据操作模式过滤）
        tables = manager.get_database_tables()
        connection_status = 'connected'
    except Exception as e:
        tables = []
        connection_status = 'error'
        flash(f'数据库连接失败: {str(e)}', 'error')

    return render_template('data_update/index.html',
                         db_config=db_config,
                         tables=tables,
                         connection_status=connection_status)


@bp.route('/api/table_structure/<table_name>')
@login_required
def get_table_structure(table_name):
    """获取表结构信息"""
    try:
        engine = get_database_engine()
        if not engine:
            return jsonify({'success': False, 'message': '数据库配置不存在或连接失败'})

        manager = DataUpdateManager(engine)
        structure = manager.get_table_structure(table_name)

        return jsonify({'success': True, 'structure': structure})

    except Exception as e:
        return jsonify({'success': False, 'message': f'获取表结构失败: {str(e)}'})


@bp.route('/task/create', methods=['GET', 'POST'])
@login_required
def create_task():
    """创建数据更新任务"""
    if request.method == 'GET':
        operation = request.args.get('operation')
        table_name = request.args.get('table_name')

        if not operation:
            flash('缺少操作模式参数', 'error')
            return redirect(url_for('data_update.index'))

        # 验证操作模式
        if operation not in ['append', 'update', 'create_table']:
            flash('无效的操作模式', 'error')
            return redirect(url_for('data_update.index'))

        # 对于追加和更新操作，需要表名
        if operation in ['append', 'update'] and not table_name:
            flash('追加和更新操作需要选择目标表', 'error')
            return redirect(url_for('data_update.index'))

        try:
            db_config = DatabaseConfig.get_active_config()
            if not db_config:
                flash('数据库配置不存在', 'error')
                return redirect(url_for('data_update.index'))

            engine = get_database_engine()
            if not engine:
                flash('数据库连接失败', 'error')
                return redirect(url_for('data_update.index'))

            manager = DataUpdateManager(engine)

            # 根据操作类型获取不同的信息
            table_structure = None
            if operation in ['append', 'update']:
                # 获取现有表结构
                table_structure = manager.get_table_structure(table_name)

            # 存储任务信息到session
            session['current_task'] = {
                'db_name': db_config.name,
                'operation': operation,
                'table_name': table_name if table_name else None,
                'created_at': datetime.now().isoformat()
            }

            return render_template('data_update/create_task.html',
                                 db_config=db_config,
                                 table_structure=table_structure,
                                 operation=operation)

        except Exception as e:
            flash(f'创建任务失败: {str(e)}', 'error')
            return redirect(url_for('data_update.index'))
    
    # POST请求处理文件上传和操作选择
    try:
        operation_type = request.form.get('operation_type')
        
        if not operation_type:
            return jsonify({'success': False, 'message': '请选择操作类型'})
        
        # 检查是否有上传文件
        if 'excel_file' not in request.files:
            return jsonify({'success': False, 'message': '请上传Excel文件'})
        
        file = request.files['excel_file']
        if file.filename == '':
            return jsonify({'success': False, 'message': '请选择文件'})
        
        # 验证文件格式
        if not file.filename.lower().endswith(('.xlsx', '.xls')):
            return jsonify({'success': False, 'message': '只支持Excel文件格式(.xlsx, .xls)'})
        
        # 获取任务信息
        task_info = session.get('current_task')
        if not task_info:
            return jsonify({'success': False, 'message': '任务信息丢失，请重新创建任务'})
        
        # 获取数据库连接
        engine = get_database_engine()
        if not engine:
            return jsonify({'success': False, 'message': '数据库连接失败'})

        manager = DataUpdateManager(engine)
        
        # 保存上传的文件
        temp_file_path = manager.save_uploaded_file(file, file.filename)
        
        # 验证Excel文件
        is_valid, validation_msg, excel_df = manager.validate_excel_file(temp_file_path)
        if not is_valid:
            return jsonify({'success': False, 'message': validation_msg})
        
        # 直接执行操作
        execution_log = []

        # 创建进度回调函数
        def progress_callback(message):
            execution_log.append(message)
            print(message)  # 同时输出到终端

        try:
            print(f"开始执行数据操作: {operation_type}")  # 终端调试信息
            execution_log.append(f"开始执行{operation_type}操作")

            if operation_type == 'create_table':
                new_table_name = request.form.get('new_table_name')
                if not new_table_name:
                    return jsonify({'success': False, 'message': '请输入新表名称'})

                # 验证表名
                execution_log.append(f"验证表名: {new_table_name}")
                is_valid_name, name_msg = manager.validate_table_name(new_table_name)
                if not is_valid_name:
                    return jsonify({'success': False, 'message': name_msg, 'execution_log': execution_log})

                execution_log.append(f"开始创建新表: {new_table_name}")
                success, message, result = manager.create_new_table(excel_df, new_table_name, progress_callback=progress_callback)
                if not success:
                    raise Exception(message)
                execution_log.append(f"表创建成功，插入了 {result.get('rows_inserted', 0)} 行数据")

            elif operation_type == 'append':
                table_name = task_info['table_name']
                execution_log.append(f"开始向表 {table_name} 追加数据")
                success, message, result = manager.append_data(excel_df, table_name, progress_callback=progress_callback)
                if not success:
                    raise Exception(message)
                execution_log.append(f"数据追加成功，新增了 {result.get('rows_inserted', 0)} 行数据")

            elif operation_type == 'update':
                table_name = task_info['table_name']
                execution_log.append(f"开始更新表 {table_name} 的数据")
                success, message, result = manager.update_data(excel_df, table_name, progress_callback=progress_callback)
                if not success:
                    raise Exception(message)
                execution_log.append(f"数据更新成功，删除了 {result.get('rows_deleted', 0)} 行，插入了 {result.get('rows_inserted', 0)} 行")

            execution_log.append("操作执行完成！")

            # 清理临时文件
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
                execution_log.append("临时文件已清理")

            # 清理session
            session.pop('current_task', None)

            print(f"操作执行成功，返回结果: {result}")  # 终端调试信息
            print(f"执行日志: {execution_log}")  # 终端调试信息

            return jsonify({
                'success': True,
                'message': '数据操作执行成功',
                'result': result,
                'execution_log': execution_log
            })

        except Exception as e:
            execution_log.append(f"操作失败: {str(e)}")

            # 清理临时文件
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
                execution_log.append("临时文件已清理")

            return jsonify({
                'success': False,
                'message': f'数据操作失败: {str(e)}',
                'execution_log': execution_log
            })
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'处理请求失败: {str(e)}'})


@bp.route('/task/preview')
@login_required
def preview_task():
    """预览任务操作"""
    preview_info = session.get('operation_preview')
    task_info = session.get('current_task')

    if not preview_info or not task_info:
        flash('预览信息丢失，请重新创建任务', 'error')
        return redirect(url_for('data_update.index'))

    try:
        # 重新生成完整的预览数据（避免在session中存储大量数据）
        engine = get_database_engine()
        if not engine:
            flash('数据库连接失败', 'error')
            return redirect(url_for('data_update.index'))

        manager = DataUpdateManager(engine)

        # 重新读取Excel文件生成预览
        temp_file_path = preview_info['temp_file_path']
        is_valid, validation_msg, excel_df = manager.validate_excel_file(temp_file_path)
        if not is_valid:
            flash(f'文件验证失败: {validation_msg}', 'error')
            return redirect(url_for('data_update.index'))

        # 生成完整预览
        operation_type = preview_info['operation_type']
        if operation_type == 'create_table':
            preview = manager.get_operation_preview(operation_type, excel_df)
            preview['new_table_name'] = preview_info['new_table_name']
        else:
            preview = manager.get_operation_preview(operation_type, excel_df, task_info['table_name'])

        # 更新预览信息
        preview_info['preview'] = preview

        return render_template('data_update/preview_task.html',
                             preview_info=preview_info,
                             task_info=task_info)

    except Exception as e:
        flash(f'生成预览失败: {str(e)}', 'error')
        return redirect(url_for('data_update.index'))


@bp.route('/task/execute', methods=['POST'])
@login_required
def execute_task():
    """执行数据更新任务"""
    try:
        preview_info = session.get('operation_preview')
        task_info = session.get('current_task')
        
        if not preview_info or not task_info:
            return jsonify({'success': False, 'message': '任务信息丢失，请重新创建任务'})
        
        # 获取数据库连接
        engine = get_database_engine()
        if not engine:
            return jsonify({'success': False, 'message': '数据库连接失败'})

        manager = DataUpdateManager(engine)
        
        # 重新读取Excel文件
        temp_file_path = preview_info['temp_file_path']
        is_valid, validation_msg, excel_df = manager.validate_excel_file(temp_file_path)
        if not is_valid:
            return jsonify({'success': False, 'message': f'文件验证失败: {validation_msg}'})
        
        # 根据操作类型执行相应操作
        operation_type = preview_info['operation_type']
        
        if operation_type == 'append':
            success, message, result = manager.append_data(excel_df, task_info['table_name'])
        elif operation_type == 'update':
            success, message, result = manager.update_data(excel_df, task_info['table_name'])
        elif operation_type == 'create_table':
            new_table_name = preview_info['new_table_name']
            success, message, result = manager.create_new_table(excel_df, new_table_name)
        else:
            return jsonify({'success': False, 'message': '不支持的操作类型'})
        
        # 清理临时文件
        manager.cleanup_temp_files()
        
        # 清理session
        session.pop('current_task', None)
        session.pop('operation_preview', None)
        
        if success:
            return jsonify({'success': True, 'message': message, 'result': result})
        else:
            return jsonify({'success': False, 'message': message})
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'执行任务失败: {str(e)}'})


@bp.route('/task/cancel', methods=['POST'])
@login_required
def cancel_task():
    """取消当前任务"""
    try:
        preview_info = session.get('operation_preview')
        
        # 清理临时文件
        if preview_info and 'temp_file_path' in preview_info:
            temp_file_path = preview_info['temp_file_path']
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
        
        # 清理session
        session.pop('current_task', None)
        session.pop('operation_preview', None)
        
        return jsonify({'success': True, 'message': '任务已取消'})
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'取消任务失败: {str(e)}'})


@bp.route('/download_template/<table_name>')
@login_required
def download_template(table_name):
    """下载表结构模板文件"""
    try:
        engine = get_database_engine()
        if not engine:
            flash('数据库连接失败', 'error')
            return redirect(url_for('data_update.index'))

        manager = DataUpdateManager(engine)
        
        # 获取表结构
        table_structure = manager.get_table_structure(table_name)
        
        # 创建模板DataFrame（只有列名，没有数据）
        import pandas as pd
        template_df = pd.DataFrame(columns=table_structure['column_names'])
        
        # 生成临时文件
        import tempfile
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
        template_df.to_excel(temp_file.name, index=False)
        
        # 返回文件下载
        from flask import send_file
        return send_file(temp_file.name, 
                        as_attachment=True, 
                        download_name=f'{table_name}_template.xlsx',
                        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        
    except Exception as e:
        flash(f'下载模板失败: {str(e)}', 'error')
        return redirect(url_for('data_update.index'))


@bp.route('/api/progress')
@login_required
def get_progress():
    """获取当前操作的进度"""
    progress_data = session.get('operation_progress', {})
    return jsonify(progress_data)


@bp.route('/api/clear_progress', methods=['POST'])
@login_required
def clear_progress():
    """清除进度数据"""
    session.pop('operation_progress', None)
    return jsonify({'success': True})


@bp.route('/api/tables/<operation_type>')
@login_required
def get_filtered_tables(operation_type):
    """根据操作类型获取过滤后的表列表"""
    try:
        engine = get_database_engine()
        if not engine:
            return jsonify({'success': False, 'message': '数据库连接失败'})

        manager = DataUpdateManager(engine)
        tables = manager.get_database_tables(operation_type)

        return jsonify({'success': True, 'tables': tables})

    except Exception as e:
        return jsonify({'success': False, 'message': f'获取表列表失败: {str(e)}'})


@bp.route('/preview')
@login_required
def data_preview():
    """数据预览页面"""
    db_config = DatabaseConfig.get_active_config()

    if not db_config:
        flash('请先在数据库配置中设置并启用一个数据库连接', 'warning')
        return redirect(url_for('config_mgmt.list_configs'))

    return render_template('data_update/data_preview.html', db_config=db_config)


@bp.route('/api/preview_data')
@login_required
def get_preview_data():
    """获取数据预览信息"""
    try:
        engine = get_database_engine()
        if not engine:
            return jsonify({'success': False, 'message': '数据库连接失败'})

        preview_manager = DataPreviewManager(engine)
        preview_data = preview_manager.get_all_tables_preview()

        return jsonify({'success': True, 'data': preview_data})

    except Exception as e:
        return jsonify({'success': False, 'message': f'获取预览数据失败: {str(e)}'})


@bp.route('/cleanup')
@login_required
def data_cleanup():
    """数据整理页面"""
    db_config = DatabaseConfig.get_active_config()

    if not db_config:
        flash('请先在数据库配置中设置并启用一个数据库连接', 'warning')
        return redirect(url_for('config_mgmt.list_configs'))

    return render_template('data_update/data_cleanup.html', db_config=db_config)


@bp.route('/api/cleanup/tables')
@login_required
def get_cleanup_tables():
    """获取可清理的业务表列表"""
    try:
        engine = get_database_engine()
        if not engine:
            return jsonify({'success': False, 'message': '数据库连接失败'})

        cleanup_manager = DataCleanupManager(engine)
        tables = cleanup_manager.get_business_tables()

        return jsonify({'success': True, 'tables': tables})

    except Exception as e:
        return jsonify({'success': False, 'message': f'获取表列表失败: {str(e)}'})


@bp.route('/api/cleanup/date_info/<table_name>/<date_column>')
@login_required
def get_date_info(table_name, date_column):
    """获取表的日期范围信息"""
    try:
        engine = get_database_engine()
        if not engine:
            return jsonify({'success': False, 'message': '数据库连接失败'})

        cleanup_manager = DataCleanupManager(engine)
        date_info = cleanup_manager.get_date_range_info(table_name, date_column)

        if 'error' in date_info:
            return jsonify({'success': False, 'message': date_info['error']})

        return jsonify({'success': True, 'date_info': date_info})

    except Exception as e:
        return jsonify({'success': False, 'message': f'获取日期信息失败: {str(e)}'})


@bp.route('/api/cleanup/preview', methods=['POST'])
@login_required
def preview_cleanup():
    """预览清理操作"""
    try:
        data = request.get_json()
        table_name = data.get('table_name')
        date_column = data.get('date_column')
        cleanup_type = data.get('cleanup_type')  # yearly, quarterly, monthly
        year = data.get('year')
        quarter = data.get('quarter')
        month = data.get('month')

        if not all([table_name, date_column, cleanup_type, year]):
            return jsonify({'success': False, 'message': '缺少必要参数'})

        engine = get_database_engine()
        if not engine:
            return jsonify({'success': False, 'message': '数据库连接失败'})

        cleanup_manager = DataCleanupManager(engine)
        preview_result = cleanup_manager.preview_cleanup(
            table_name, date_column, cleanup_type, year, quarter, month
        )

        if 'error' in preview_result:
            return jsonify({'success': False, 'message': preview_result['error']})

        return jsonify({'success': True, 'preview': preview_result})

    except Exception as e:
        return jsonify({'success': False, 'message': f'预览失败: {str(e)}'})


@bp.route('/api/cleanup/execute', methods=['POST'])
@login_required
def execute_cleanup():
    """执行清理操作"""
    try:
        data = request.get_json()
        table_name = data.get('table_name')
        date_column = data.get('date_column')
        cleanup_type = data.get('cleanup_type')
        year = data.get('year')
        quarter = data.get('quarter')
        month = data.get('month')

        if not all([table_name, date_column, cleanup_type, year]):
            return jsonify({'success': False, 'message': '缺少必要参数'})

        engine = get_database_engine()
        if not engine:
            return jsonify({'success': False, 'message': '数据库连接失败'})

        cleanup_manager = DataCleanupManager(engine)
        success, message, result = cleanup_manager.execute_cleanup(
            table_name, date_column, cleanup_type, year, quarter, month
        )

        if success:
            return jsonify({'success': True, 'message': message, 'result': result})
        else:
            return jsonify({'success': False, 'message': message})

    except Exception as e:
        return jsonify({'success': False, 'message': f'执行清理失败: {str(e)}'})
