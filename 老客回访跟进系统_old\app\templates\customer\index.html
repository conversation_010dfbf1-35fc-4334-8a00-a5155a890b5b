{% extends "base.html" %}

{% block title %}客户管理 - 老客回访与跟进系统{% endblock %}

{% block extra_css %}
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
<style>
.select2-container--bootstrap-5 .select2-selection {
    min-height: 38px;
}
.select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
    line-height: 36px;
}
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h2><i class="fas fa-users"></i> 客户管理</h2>
            {% if current_user.has_permission('CUSTOMER_REGISTER') %}
            <a href="{{ url_for('customer.register') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> 客户登记
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- 搜索筛选 -->
<div class="row mb-3">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="card_number" class="form-label">卡号</label>
                        <input type="text" class="form-control" id="card_number" name="card_number" 
                               placeholder="请输入卡号" value="{{ request.args.get('card_number', '') }}">
                    </div>
                    <div class="col-md-3">
                        <label for="consultant_id" class="form-label">现场顾问</label>
                        <select class="form-select select2-consultant" id="consultant_id" name="consultant_id">
                            <option value="">全部</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="registrar_id" class="form-label">网络咨询</label>
                        <select class="form-select select2-registrar" id="registrar_id" name="registrar_id">
                            <option value="">全部</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="channel_id" class="form-label">激活渠道</label>
                        <select class="form-select select2-channel" id="channel_id" name="channel_id">
                            <option value="">全部</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="date_range" class="form-label">登记时间</label>
                        <input type="date" class="form-control" id="date_range" name="date_range">
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                        <a href="{{ url_for('customer.index') }}" class="btn btn-secondary">
                            <i class="fas fa-redo"></i> 重置
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 客户列表 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>卡号</th>
                                <th>登记人</th>
                                <th>现场顾问</th>
                                <th>激活渠道</th>
                                <th>咨询内容</th>
                                <th>最近来院</th>
                                <th>登记时间</th>
                                <th>跟进状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for customer in customers.items %}
                            <tr>
                                <td><strong>{{ customer.card_number }}</strong></td>
                                <td>{{ customer.registrar.real_name }}</td>
                                <td>{{ customer.assigned_consultant.real_name }}</td>
                                <td>
                                    <span class="badge bg-info">{{ customer.activation_channel.channel_name }}</span>
                                </td>
                                <td>
                                    <span title="{{ customer.consultation_content }}">
                                        {{ customer.consultation_content[:30] }}{% if customer.consultation_content|length > 30 %}...{% endif %}
                                    </span>
                                </td>
                                <td>{{ customer.last_visit_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ customer.registration_time.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>
                                    {% set follow_up_count = customer.get_follow_up_count() %}
                                    {% if follow_up_count > 0 %}
                                        <span class="badge bg-success">已跟进({{ follow_up_count }})</span>
                                    {% else %}
                                        <span class="badge bg-warning">待跟进</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('customer.view', id=customer.id) }}" 
                                           class="btn btn-outline-primary" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if current_user.has_permission('FOLLOW_UP_ADD') and customer.assigned_consultant_id == current_user.id %}
                                        <a href="{{ url_for('customer.add_follow_up', id=customer.id) }}" 
                                           class="btn btn-outline-success" title="添加跟进">
                                            <i class="fas fa-plus"></i>
                                        </a>
                                        {% endif %}
                                        {% if current_user.has_permission('CUSTOMER_EDIT') %}
                                        <a href="{{ url_for('customer.edit', id=customer.id) }}"
                                           class="btn btn-outline-warning" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="9" class="text-center text-muted">
                                    <i class="fas fa-inbox fa-2x mb-2"></i><br>
                                    暂无客户数据
                                    {% if current_user.has_permission('CUSTOMER_REGISTER') %}
                                    <br><a href="{{ url_for('customer.register') }}" class="btn btn-primary btn-sm mt-2">
                                        <i class="fas fa-plus"></i> 立即登记
                                    </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                {% if customers.pages > 1 %}
                <nav aria-label="客户列表分页">
                    <ul class="pagination justify-content-center">
                        {% if customers.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('customer.index', page=customers.prev_num) }}">上一页</a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in customers.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != customers.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('customer.index', page=page_num) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">…</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if customers.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('customer.index', page=customers.next_num) }}">下一页</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
$(document).ready(function() {
    // 获取当前选中的值
    var currentConsultantId = '{{ request.args.get("consultant_id", "") }}';
    var currentRegistrarId = '{{ request.args.get("registrar_id", "") }}';
    var currentChannelId = '{{ request.args.get("channel_id", "") }}';

    // 初始化现场顾问选择框
    $('.select2-consultant').select2({
        theme: 'bootstrap-5',
        placeholder: '全部',
        allowClear: true,
        ajax: {
            url: '{{ url_for("api.get_consultants") }}',
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    search: params.term || ''
                };
            },
            processResults: function (data) {
                if (data.success) {
                    var results = [{id: '', text: '全部'}];
                    results = results.concat(data.data.map(function(item) {
                        return {
                            id: item.id,
                            text: item.text,
                            name: item.name,
                            department: item.department,
                            pinyin: item.pinyin
                        };
                    }));
                    return { results: results };
                }
                return { results: [{id: '', text: '全部'}] };
            },
            cache: true
        },
        minimumInputLength: 0
    });

    // 初始化网络咨询选择框
    $('.select2-registrar').select2({
        theme: 'bootstrap-5',
        placeholder: '全部',
        allowClear: true,
        ajax: {
            url: '{{ url_for("api.get_registrars") }}',
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    search: params.term || ''
                };
            },
            processResults: function (data) {
                if (data.success) {
                    var results = [{id: '', text: '全部'}];
                    results = results.concat(data.data.map(function(item) {
                        return {
                            id: item.id,
                            text: item.text,
                            name: item.name,
                            department: item.department,
                            pinyin: item.pinyin
                        };
                    }));
                    return { results: results };
                }
                return { results: [{id: '', text: '全部'}] };
            },
            cache: true
        },
        minimumInputLength: 0
    });

    // 初始化激活渠道选择框
    $('.select2-channel').select2({
        theme: 'bootstrap-5',
        placeholder: '全部',
        allowClear: true,
        ajax: {
            url: '{{ url_for("api.get_channels") }}',
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    search: params.term || ''
                };
            },
            processResults: function (data) {
                if (data.success) {
                    var results = [{id: '', text: '全部'}];
                    results = results.concat(data.data.map(function(item) {
                        return {
                            id: item.id,
                            text: item.text,
                            category: item.category,
                            name: item.name,
                            pinyin: item.pinyin
                        };
                    }));
                    return { results: results };
                }
                return { results: [{id: '', text: '全部'}] };
            },
            cache: true
        },
        minimumInputLength: 0
    });

    // 设置默认选中值
    if (currentConsultantId) {
        $('.select2-consultant').val(currentConsultantId).trigger('change');
    }
    if (currentRegistrarId) {
        $('.select2-registrar').val(currentRegistrarId).trigger('change');
    }
    if (currentChannelId) {
        $('.select2-channel').val(currentChannelId).trigger('change');
    }
});
</script>
{% endblock %}
