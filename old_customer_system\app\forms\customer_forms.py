# -*- coding: utf-8 -*-
"""
客户管理相关表单
"""

from flask_wtf import FlaskForm
from wtforms import StringField, SelectField, DateField, TextAreaField, HiddenField, SubmitField
from wtforms.validators import DataRequired, Length, Optional, ValidationError, Regexp
from datetime import date
from app.models import Customer, User, Channel
from app.repositories.system_setting_repository import SystemSettingRepository
from datetime import datetime
from app import db

class CustomerForm(FlaskForm):
    """客户登记表单"""
    card_number = StringField('卡号', validators=[
        DataRequired(message='卡号不能为空'),
        Regexp(r'^\d+$', message='卡号只能包含数字')
    ])
    onsite_consultant_id = SelectField('所属现场', coerce=int, validators=[
        DataRequired(message='请选择所属现场咨询师')
    ])
    channel_id = SelectField('激活渠道', coerce=int, validators=[
        DataRequired(message='请选择激活渠道')
    ])
    registration_date = DateField('登记时间', format='%Y-%m-%d', default=datetime.utcnow)
    inquiry_content = TextAreaField('咨询内容', validators=[
        Optional(),
        Length(max=500, message='咨询内容长度不能超过500个字符')
    ])
    last_visit_date = DateField('最近来院时间', validators=[Optional()])
    submit = SubmitField('保存')
    
    def __init__(self, customer=None, *args, **kwargs):
        super(CustomerForm, self).__init__(*args, **kwargs)
        self.customer = customer
        
        # 加载现场咨询师选项
        onsite_consultants = User.query.filter_by(
            role='onsite_consultant', is_active=True
        ).all()
        self.onsite_consultant_id.choices = [
            (0, '请选择现场咨询师')
        ] + [(u.id, u.real_name) for u in onsite_consultants]
        
        # 加载渠道选项
        channels = Channel.query.filter_by(is_active=True).order_by(Channel.name).all()
        self.channel_id.choices = [
            (0, '请选择激活渠道')
        ] + [(c.id, c.name) for c in channels]
    
    def validate_card_number(self, card_number):
        """验证卡号"""
        # 获取卡号长度设置
        setting_repo = SystemSettingRepository()
        card_length = setting_repo.get_card_number_length()
        
        if len(card_number.data) != card_length:
            raise ValidationError(f'卡号长度必须为{card_length}位')
        
        # 检查卡号唯一性
        customer = Customer.query.filter_by(card_number=card_number.data).first()
        if customer and (not self.customer or customer.id != self.customer.id):
            raise ValidationError('卡号已存在，请检查输入')
    
    def validate_onsite_consultant_id(self, onsite_consultant_id):
        """验证现场咨询师选择"""
        if onsite_consultant_id.data == 0:
            raise ValidationError('请选择所属现场咨询师')
    
    def validate_channel_id(self, channel_id):
        """验证渠道选择"""
        if channel_id.data == 0:
            raise ValidationError('请选择激活渠道')
    
    def validate_last_visit_date(self, last_visit_date):
        """验证来院时间"""
        if last_visit_date.data and last_visit_date.data > date.today():
            raise ValidationError('来院时间不能是未来日期')

class CustomerSearchForm(FlaskForm):
    """客户搜索表单"""
    query = StringField('搜索', validators=[Optional()])
    onsite_consultant_id = SelectField('现场咨询师', coerce=int, validators=[Optional()])
    online_consultant_id = SelectField('网络咨询', coerce=int, validators=[Optional()])
    channel_id = SelectField('激活渠道', coerce=int, validators=[Optional()])
    channel_category = SelectField('渠道分类', validators=[Optional()])
    start_date = DateField('开始日期', validators=[Optional()])
    end_date = DateField('结束日期', validators=[Optional()])
    has_follow_up = SelectField('跟进状态', choices=[
        ('', '全部状态'),
        ('1', '已跟进'),
        ('0', '待跟进')
    ], validators=[Optional()])
    submit = SubmitField('搜索')
    
    def __init__(self, *args, **kwargs):
        super(CustomerSearchForm, self).__init__(*args, **kwargs)
        
        # 加载现场咨询师选项
        onsite_consultants = User.query.filter_by(
            role='onsite_consultant', is_active=True
        ).all()
        self.onsite_consultant_id.choices = [
            (0, '全部现场咨询师')
        ] + [(u.id, u.real_name) for u in onsite_consultants]
        
        # 加载网络咨询师选项
        online_consultants = User.query.filter_by(
            role='online_consultant', is_active=True
        ).all()
        self.online_consultant_id.choices = [
            (0, '全部网络咨询')
        ] + [(u.id, u.real_name) for u in online_consultants]
        
        # 加载渠道选项
        channels = Channel.query.filter_by(is_active=True).order_by(Channel.name).all()
        self.channel_id.choices = [
            (0, '全部渠道')
        ] + [(c.id, c.name) for c in channels]
        
        # 加载渠道分类选项
        categories = db.session.query(Channel.category).distinct().all()
        self.channel_category.choices = [
            ('', '全部分类')
        ] + [(c[0], c[0]) for c in categories if c[0]]

class FollowUpForm(FlaskForm):
    """跟进信息表单"""
    follow_up_note = TextAreaField('跟进情况', validators=[
        DataRequired(message='跟进情况不能为空'),
        Length(max=500, message='跟进情况长度不能超过500个字符')
    ])
    submit = SubmitField('保存跟进')
