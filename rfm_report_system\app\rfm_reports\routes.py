"""
RFM报表路由
"""

from flask import render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from app.rfm_reports import bp
from app.rfm_reports.data_manager import rfm_data_manager
from app.rfm_reports.models import RFMDataSource


@bp.route('/')
@login_required
def index():
    """RFM报表主页"""
    return render_template('rfm_reports/index.html')


# ==================== 数据源管理 ====================

@bp.route('/data-source')
@login_required
def data_source_management():
    """数据源管理页面"""
    # 检查权限（仅管理员可访问）
    if not current_user.is_admin:
        flash('仅管理员可访问数据源管理功能', 'error')
        return redirect(url_for('rfm_reports.index'))
    
    return render_template('rfm_reports/data_source_management.html')


@bp.route('/api/available-tasks')
@login_required
def get_available_tasks():
    """获取可用的数据处理任务API"""
    if not current_user.is_admin:
        return jsonify({'success': False, 'error': '权限不足'}), 403

    try:
        tasks = rfm_data_manager.get_available_tasks()
        return jsonify({
            'success': True,
            'tasks': tasks
        })
    except Exception as e:
        print(f"获取任务列表API错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/api/import-data-source', methods=['POST'])
@login_required
def import_data_source():
    """导入数据源API"""
    if not current_user.is_admin:
        return jsonify({'error': '权限不足'}), 403
    
    try:
        data = request.get_json()
        task_id = data.get('task_id')
        
        if not task_id:
            return jsonify({'success': False, 'message': '缺少任务ID'})
        
        result = rfm_data_manager.import_data_source(task_id, current_user.id)

        # 如果成功，添加数据源信息到返回结果
        if result.get('success') and 'data_source_id' in result:
            data_source = RFMDataSource.query.get(result['data_source_id'])
            if data_source:
                result['data_source'] = data_source.get_summary()

        return jsonify(result)
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'导入失败: {str(e)}'})


@bp.route('/api/delete-data-source', methods=['POST'])
@login_required
def delete_data_source():
    """删除数据源API"""
    if not current_user.is_admin:
        return jsonify({'error': '权限不足'}), 403
    
    try:
        data = request.get_json()
        data_source_id = data.get('data_source_id')
        
        if not data_source_id:
            return jsonify({'success': False, 'message': '缺少数据源ID'})
        
        result = rfm_data_manager.delete_data_source(data_source_id, current_user.id)
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'删除失败: {str(e)}'})


# ==================== 正向盘报表 ====================

@bp.route('/forward-panel')
@login_required
def forward_panel():
    """正向盘报表页面"""
    return render_template('rfm_reports/forward_panel.html')


@bp.route('/api/forward-data-sources')
@login_required
def get_forward_data_sources():
    """获取正向盘数据源API"""
    try:
        data_sources = rfm_data_manager.get_data_sources_by_type('forward')
        return jsonify({
            'success': True,
            'data_sources': data_sources
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@bp.route('/forward-panel/<path:analysis_path>')
@login_required
def forward_panel_analysis(analysis_path):
    """正向盘报表分析页面"""
    # 解析分析路径
    path_parts = analysis_path.split('/')
    
    if len(path_parts) < 2:
        flash('无效的分析路径', 'error')
        return redirect(url_for('rfm_reports.forward_panel'))
    
    section = path_parts[0]  # customer-overview 或 department-analysis
    analysis_type = path_parts[1]  # 具体分析类型
    sub_analysis = path_parts[2] if len(path_parts) > 2 else None
    
    # 获取数据源ID
    data_source_id = request.args.get('data_source_id')
    if not data_source_id:
        flash('请先选择数据源', 'error')
        return redirect(url_for('rfm_reports.forward_panel'))
    
    # 验证数据源
    data_source = RFMDataSource.query.get(data_source_id)
    if not data_source or data_source.report_type != 'forward':
        flash('数据源不存在或类型不匹配', 'error')
        return redirect(url_for('rfm_reports.forward_panel'))
    
    return render_template('rfm_reports/analysis_placeholder.html',
                         report_type='forward',
                         section=section,
                         analysis_type=analysis_type,
                         sub_analysis=sub_analysis,
                         data_source=data_source.get_summary())


# ==================== 结果盘报表 ====================

@bp.route('/result-panel')
@login_required
def result_panel():
    """结果盘报表页面"""
    return render_template('rfm_reports/result_panel.html')


@bp.route('/api/result-data-sources')
@login_required
def get_result_data_sources():
    """获取结果盘数据源API"""
    try:
        data_sources = rfm_data_manager.get_data_sources_by_type('result')
        return jsonify({
            'success': True,
            'data_sources': data_sources
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@bp.route('/result-panel/<path:analysis_path>')
@login_required
def result_panel_analysis(analysis_path):
    """结果盘报表分析页面"""
    # 解析分析路径
    path_parts = analysis_path.split('/')
    
    if len(path_parts) < 2:
        flash('无效的分析路径', 'error')
        return redirect(url_for('rfm_reports.result_panel'))
    
    section = path_parts[0]  # customer-overview 或 department-analysis
    analysis_type = path_parts[1]  # 具体分析类型
    sub_analysis = path_parts[2] if len(path_parts) > 2 else None
    
    # 获取数据源ID
    data_source_id = request.args.get('data_source_id')
    if not data_source_id:
        flash('请先选择数据源', 'error')
        return redirect(url_for('rfm_reports.result_panel'))
    
    # 验证数据源
    data_source = RFMDataSource.query.get(data_source_id)
    if not data_source or data_source.report_type != 'result':
        flash('数据源不存在或类型不匹配', 'error')
        return redirect(url_for('rfm_reports.result_panel'))
    
    return render_template('rfm_reports/analysis_placeholder.html',
                         report_type='result',
                         section=section,
                         analysis_type=analysis_type,
                         sub_analysis=sub_analysis,
                         data_source=data_source.get_summary())


# ==================== 通用API ====================

@bp.route('/api/analysis-data/<int:data_source_id>/<analysis_type>')
@login_required
def get_analysis_data(data_source_id, analysis_type):
    """获取分析数据API"""
    try:
        # 验证数据源
        data_source = RFMDataSource.query.get(data_source_id)
        if not data_source or not data_source.is_available:
            return jsonify({'error': '数据源不可用'}), 404

        # 根据analysis_type生成相应的分析数据
        if analysis_type == 'base-volume-trend':
            # 基盘体量趋势 - 返回3个报表的数据
            from app.rfm_reports.analysis import BaseVolumeTrendAnalysis
            analyzer = BaseVolumeTrendAnalysis(data_source)
            analysis_data = analyzer.generate_reports()

            return jsonify({
                'success': True,
                'data': analysis_data
            })
        else:
            # 其他分析类型返回占位符数据
            placeholder_data = {
                'analysis_type': analysis_type,
                'data_source_id': data_source_id,
                'message': '待实现功能',
                'quarters': data_source.quarters,
                'total_records': data_source.imported_records
            }

            return jsonify({
                'success': True,
                'data': placeholder_data
            })

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@bp.route('/api/import-progress/<int:data_source_id>')
@login_required
def get_import_progress(data_source_id):
    """获取导入进度API"""
    try:
        data_source = RFMDataSource.query.get(data_source_id)
        if not data_source:
            return jsonify({'error': '数据源不存在'}), 404

        return jsonify({
            'success': True,
            'status': data_source.status,
            'imported_records': data_source.imported_records or 0,
            'total_records': data_source.total_records or 0,
            'progress_percent': round((data_source.imported_records or 0) / max(data_source.total_records or 1, 1) * 100, 1)
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@bp.route('/api/delete-progress/<int:data_source_id>')
@login_required
def get_delete_progress(data_source_id):
    """获取删除进度API"""
    try:
        data_source = RFMDataSource.query.get(data_source_id)

        # 如果数据源不存在，说明删除已完成
        if not data_source:
            return jsonify({
                'success': True,
                'status': 'completed',
                'remaining_records': 0,
                'deleted_records': 0,
                'total_records': 0,
                'progress_percent': 100,
                'message': '删除已完成，数据源已被移除'
            })

        # 如果数据源状态不是删除中，返回完成状态
        if data_source.status != 'deleting':
            return jsonify({
                'success': True,
                'status': 'completed',
                'remaining_records': 0,
                'deleted_records': data_source.total_records or 0,
                'total_records': data_source.total_records or 0,
                'progress_percent': 100
            })

        # 计算删除进度
        current_records = data_source.imported_records or 0
        total_records = data_source.total_records or 1
        deleted_records = total_records - current_records
        progress_percent = round(deleted_records / total_records * 100, 1)

        return jsonify({
            'success': True,
            'status': data_source.status,
            'remaining_records': current_records,
            'deleted_records': deleted_records,
            'total_records': total_records,
            'progress_percent': progress_percent
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@bp.route('/test-api')
@login_required
def test_api_page():
    """API测试页面"""
    return render_template('test_api.html')


@bp.route('/simple-test')
@login_required
def simple_test_page():
    """简单测试页面"""
    return render_template('simple_test.html')


@bp.route('/minimal-test')
@login_required
def minimal_test_page():
    """最小化测试页面"""
    return render_template('minimal_test.html')


@bp.route('/test-data-sources')
@login_required
def test_data_sources_page():
    """数据源API测试页面"""
    return render_template('test_data_sources.html')


@bp.route('/api/system-resources')
@login_required
def get_system_resources():
    """获取系统资源使用情况API"""
    try:
        from app.utils.resource_monitor import resource_monitor

        resources = resource_monitor.get_system_resources()
        performance_stats = resource_monitor.get_performance_stats()

        return jsonify({
            'success': True,
            'resources': resources,
            'performance_stats': performance_stats,
            'thresholds': {
                'cpu_threshold': resource_monitor.cpu_threshold,
                'memory_threshold': resource_monitor.memory_threshold
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/api/consultant-attribution-filter/<int:data_source_id>', methods=['POST'])
@login_required
def filter_consultant_attribution(data_source_id):
    """咨询归属报表筛选API"""
    try:
        # 验证数据源
        data_source = RFMDataSource.query.get(data_source_id)
        if not data_source or not data_source.is_available:
            return jsonify({
                'success': False, 
                'error': '数据源不可用',
                'error_code': 'DATA_SOURCE_UNAVAILABLE'
            }), 404

        # 获取和验证筛选条件
        if not request.is_json:
            return jsonify({
                'success': False,
                'error': '请求必须是JSON格式',
                'error_code': 'INVALID_REQUEST_FORMAT'
            }), 400

        filter_data = request.get_json()
        if not filter_data:
            return jsonify({
                'success': False,
                'error': '筛选条件不能为空',
                'error_code': 'EMPTY_FILTER_DATA'
            }), 400

        subdivision_levels = filter_data.get('subdivision_levels', [])
        member_card_levels = filter_data.get('member_card_levels', [])

        # 验证筛选条件
        if not isinstance(subdivision_levels, list) or not isinstance(member_card_levels, list):
            return jsonify({
                'success': False,
                'error': '筛选条件格式错误',
                'error_code': 'INVALID_FILTER_FORMAT'
            }), 400

        if not subdivision_levels or not member_card_levels:
            return jsonify({
                'success': False,
                'error': '筛选条件不能为空，请至少选择一个选项',
                'error_code': 'EMPTY_FILTER_CONDITIONS'
            }), 400

        # 生成筛选后的报表数据
        from app.rfm_reports.analysis import BaseVolumeTrendAnalysis
        from app.rfm_reports.models import RFMAnalysisData
        
        analyzer = BaseVolumeTrendAnalysis(data_source)

        # 获取所有分析数据
        analysis_data = RFMAnalysisData.query.filter_by(
            data_source_id=data_source.id
        ).all()

        if not analysis_data:
            return jsonify({
                'success': True,
                'data': {'data': [], 'summary': {}},
                'quarters': analyzer.quarters,
                'filter_applied': {
                    'subdivision_levels': subdivision_levels,
                    'member_card_levels': member_card_levels
                },
                'total_records': 0,
                'filtered_records': 0
            })

        # 转换为DataFrame
        df = analyzer._to_dataframe(analysis_data)

        # 生成筛选后的咨询归属数据
        filtered_data = analyzer._generate_consultant_data(df, subdivision_levels, member_card_levels)

        # 计算筛选前后的记录数
        total_records = len(analysis_data)
        filtered_records = len(filtered_data['data']) if filtered_data['data'] else 0

        return jsonify({
            'success': True,
            'data': filtered_data,
            'quarters': analyzer.quarters,
            'filter_applied': {
                'subdivision_levels': subdivision_levels,
                'member_card_levels': member_card_levels
            },
            'total_records': total_records,
            'filtered_records': filtered_records
        })

    except Exception as e:
        import traceback
        print(f"筛选API错误: {str(e)}")
        traceback.print_exc()
        return jsonify({
            'success': False, 
            'error': f'服务器内部错误: {str(e)}',
            'error_code': 'INTERNAL_SERVER_ERROR'
        }), 500


@bp.route('/api/filter-options/<int:data_source_id>')
@login_required
def get_filter_options(data_source_id):
    """获取筛选选项API"""
    try:
        # 验证数据源
        data_source = RFMDataSource.query.get(data_source_id)
        if not data_source or not data_source.is_available:
            return jsonify({
                'success': False,
                'error': '数据源不可用',
                'error_code': 'DATA_SOURCE_UNAVAILABLE'
            }), 404

        # 获取筛选选项
        from app.rfm_reports.analysis import BaseVolumeTrendAnalysis
        from app.rfm_reports.models import RFMAnalysisData
        
        analyzer = BaseVolumeTrendAnalysis(data_source)

        # 获取所有分析数据
        analysis_data = RFMAnalysisData.query.filter_by(
            data_source_id=data_source.id
        ).all()

        if not analysis_data:
            return jsonify({
                'success': True,
                'subdivision_levels': [],
                'member_card_levels': [],
                'data_source_info': {
                    'id': data_source.id,
                    'name': data_source.source_task_name,
                    'total_records': 0
                }
            })

        # 转换为DataFrame
        df = analyzer._to_dataframe(analysis_data)

        # 获取筛选选项
        filter_options = analyzer._get_filter_options(df)

        return jsonify({
            'success': True,
            'subdivision_levels': filter_options['subdivision_levels'],
            'member_card_levels': filter_options['member_card_levels'],
            'data_source_info': {
                'id': data_source.id,
                'name': data_source.source_task_name,
                'total_records': len(analysis_data)
            }
        })

    except Exception as e:
        import traceback
        print(f"获取筛选选项API错误: {str(e)}")
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': f'服务器内部错误: {str(e)}',
            'error_code': 'INTERNAL_SERVER_ERROR'
        }), 500
