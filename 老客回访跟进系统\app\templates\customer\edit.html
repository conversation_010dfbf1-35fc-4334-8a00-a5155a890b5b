{% extends "base.html" %}

{% block title %}编辑客户信息 - 老客回访与跟进系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-edit"></i> 编辑客户信息</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('customer.index') }}">客户管理</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('customer.view', id=registration.id) }}">客户详情</a></li>
                <li class="breadcrumb-item active">编辑信息</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-edit"></i> 编辑客户信息</h5>
            </div>
            <div class="card-body">
                <form method="POST" id="editForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="card_number" class="form-label">
                                    <i class="fas fa-id-card"></i> 客户卡号 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="card_number" name="card_number" 
                                       value="{{ registration.card_number }}" required maxlength="10"
                                       placeholder="请输入客户卡号">
                                <div class="form-text">只能包含数字，最多10位</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="last_visit_date" class="form-label">
                                    <i class="fas fa-calendar"></i> 最近来院日期 <span class="text-danger">*</span>
                                </label>
                                <input type="date" class="form-control" id="last_visit_date" name="last_visit_date" 
                                       value="{{ registration.last_visit_date.strftime('%Y-%m-%d') }}" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="assigned_consultant_id" class="form-label">
                                    <i class="fas fa-user-tie"></i> 分配现场顾问 <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="assigned_consultant_id" name="assigned_consultant_id" required>
                                    <option value="">请选择现场顾问</option>
                                    {% for consultant in consultants %}
                                    <option value="{{ consultant.id }}" 
                                            {% if consultant.id == registration.assigned_consultant_id %}selected{% endif %}>
                                        {{ consultant.real_name }} - {{ consultant.department.department_name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="activation_channel_id" class="form-label">
                                    <i class="fas fa-broadcast-tower"></i> 激活渠道 <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="activation_channel_id" name="activation_channel_id" required>
                                    <option value="">请选择激活渠道</option>
                                    {% for channel in channels %}
                                    <option value="{{ channel.id }}" 
                                            {% if channel.id == registration.activation_channel_id %}selected{% endif %}>
                                        {{ channel.channel_name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="consultation_content" class="form-label">
                            <i class="fas fa-comment"></i> 咨询内容
                        </label>
                        <textarea class="form-control" id="consultation_content" name="consultation_content" 
                                  rows="4" maxlength="500" placeholder="请输入咨询内容">{{ registration.consultation_content }}</textarea>
                        <div class="form-text">最多500字</div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 保存修改
                            </button>
                            <a href="{{ url_for('customer.view', id=registration.id) }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>
                        <div class="text-muted small">
                            <i class="fas fa-info-circle"></i> 带 <span class="text-danger">*</span> 的字段为必填项
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-info-circle"></i> 原始信息</h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>登记人员</strong></td>
                        <td>{{ registration.registrar.real_name }}</td>
                    </tr>
                    <tr>
                        <td><strong>登记部门</strong></td>
                        <td>{{ registration.registrar.department.department_name }}</td>
                    </tr>
                    <tr>
                        <td><strong>登记时间</strong></td>
                        <td>{{ registration.registration_time.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                    </tr>
                    <tr>
                        <td><strong>跟进次数</strong></td>
                        <td>{{ registration.get_follow_up_count() }} 次</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-exclamation-triangle text-warning"></i> 编辑说明</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li><i class="fas fa-check text-success"></i> 可以修改客户卡号、咨询内容</li>
                    <li><i class="fas fa-check text-success"></i> 可以重新分配现场顾问</li>
                    <li><i class="fas fa-check text-success"></i> 可以更改激活渠道</li>
                    <li><i class="fas fa-check text-success"></i> 可以调整最近来院日期</li>
                    <li><i class="fas fa-times text-danger"></i> 登记人员和登记时间不可修改</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
$(document).ready(function() {
    // 初始化Select2
    $('#assigned_consultant_id').select2({
        theme: 'bootstrap-5',
        placeholder: '请选择现场顾问',
        allowClear: true,
        ajax: {
            url: '{{ url_for("api.get_consultants") }}',
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    search: params.term,
                    page: params.page || 1
                };
            },
            processResults: function (data) {
                return {
                    results: data.data.map(function(item) {
                        return {
                            id: item.id,
                            text: item.text,
                            pinyin: item.pinyin
                        };
                    })
                };
            },
            cache: true
        }
    });
    
    $('#activation_channel_id').select2({
        theme: 'bootstrap-5',
        placeholder: '请选择激活渠道',
        allowClear: true,
        ajax: {
            url: '{{ url_for("api.get_channels") }}',
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    search: params.term,
                    page: params.page || 1
                };
            },
            processResults: function (data) {
                return {
                    results: data.data.map(function(item) {
                        return {
                            id: item.id,
                            text: item.text,
                            pinyin: item.pinyin
                        };
                    })
                };
            },
            cache: true
        }
    });
    
    // 表单验证
    $('#editForm').on('submit', function(e) {
        var cardNumber = $('#card_number').val().trim();
        var consultantId = $('#assigned_consultant_id').val();
        var channelId = $('#activation_channel_id').val();
        var visitDate = $('#last_visit_date').val();
        
        if (!cardNumber) {
            alert('请输入客户卡号');
            e.preventDefault();
            return false;
        }
        
        if (!/^\d+$/.test(cardNumber)) {
            alert('卡号只能包含数字');
            e.preventDefault();
            return false;
        }
        
        if (cardNumber.length > 10) {
            alert('卡号不能超过10位');
            e.preventDefault();
            return false;
        }
        
        if (!consultantId) {
            alert('请选择现场顾问');
            e.preventDefault();
            return false;
        }
        
        if (!channelId) {
            alert('请选择激活渠道');
            e.preventDefault();
            return false;
        }
        
        if (!visitDate) {
            alert('请选择最近来院日期');
            e.preventDefault();
            return false;
        }
        
        return true;
    });
    
    // 字符计数
    $('#consultation_content').on('input', function() {
        var length = $(this).val().length;
        var maxLength = 500;
        var remaining = maxLength - length;
        
        if (remaining < 0) {
            $(this).val($(this).val().substring(0, maxLength));
            remaining = 0;
        }
        
        $(this).next('.form-text').text('最多500字，还可输入 ' + remaining + ' 字');
    });
});
</script>
{% endblock %}
