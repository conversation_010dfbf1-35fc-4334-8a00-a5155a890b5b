{% extends "base.html" %}

{% block content %}
<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">数据清除</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">首页</a></li>
                        <li class="breadcrumb-item"><a href="{{ url_for('reports.dashboard') }}">统计报表</a></li>
                        <li class="breadcrumb-item active">数据清除</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            
            <!-- 警告提示 -->
            <div class="row">
                <div class="col-12">
                    <div class="alert alert-danger">
                        <h5><i class="icon fas fa-exclamation-triangle"></i> 重要警告！</h5>
                        <p><strong>数据清除操作不可逆！</strong>请在执行前确保：</p>
                        <ul>
                            <li>已备份重要数据</li>
                            <li>确认需要重新导入数据</li>
                            <li>了解清除操作的影响范围</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- 清除选项 -->
            <div class="row">
                <div class="col-md-4">
                    <div class="card card-danger">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-trash-alt"></i> 清除所有数据
                            </h3>
                        </div>
                        <div class="card-body">
                            <p><strong>清除范围：</strong></p>
                            <ul>
                                <li>所有消费记录</li>
                                <li>所有到院记录</li>
                                <li>所有上传历史</li>
                            </ul>
                            <p class="text-danger"><strong>适用场景：</strong>完全重新开始，重新导入所有数据</p>
                            
                            <button type="button" class="btn btn-danger btn-block" 
                                    onclick="confirmClear('all', '清除所有数据')">
                                <i class="fas fa-trash-alt"></i> 清除所有数据
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card card-warning">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-shopping-cart"></i> 清除消费记录
                            </h3>
                        </div>
                        <div class="card-body">
                            <p><strong>清除范围：</strong></p>
                            <ul>
                                <li>所有消费记录</li>
                                <li>消费相关上传历史</li>
                            </ul>
                            <p class="text-warning"><strong>适用场景：</strong>只重新导入消费明细数据</p>
                            
                            <button type="button" class="btn btn-warning btn-block" 
                                    onclick="confirmClear('consumption', '清除消费记录')">
                                <i class="fas fa-shopping-cart"></i> 清除消费记录
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card card-info">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-hospital"></i> 清除到院记录
                            </h3>
                        </div>
                        <div class="card-body">
                            <p><strong>清除范围：</strong></p>
                            <ul>
                                <li>所有到院记录</li>
                                <li>到院相关上传历史</li>
                            </ul>
                            <p class="text-info"><strong>适用场景：</strong>只重新导入到院明细数据</p>
                            
                            <button type="button" class="btn btn-info btn-block" 
                                    onclick="confirmClear('visit', '清除到院记录')">
                                <i class="fas fa-hospital"></i> 清除到院记录
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 当前数据统计 -->
            <div class="row">
                <div class="col-12">
                    <div class="card card-secondary">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-database"></i> 当前数据统计
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="info-box bg-gradient-success">
                                        <span class="info-box-icon">
                                            <i class="fas fa-shopping-cart"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">消费记录</span>
                                            <span class="info-box-number" id="consumption-count">加载中...</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="info-box bg-gradient-info">
                                        <span class="info-box-icon">
                                            <i class="fas fa-hospital"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">到院记录</span>
                                            <span class="info-box-number" id="visit-count">加载中...</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="info-box bg-gradient-secondary">
                                        <span class="info-box-icon">
                                            <i class="fas fa-history"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">上传历史</span>
                                            <span class="info-box-number" id="upload-count">加载中...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
        </div>
    </section>
</div>

<!-- 确认清除模态框 -->
<div class="modal fade" id="confirmClearModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-danger">
                <h4 class="modal-title">
                    <i class="fas fa-exclamation-triangle"></i> 确认清除操作
                </h4>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <h5><i class="fas fa-exclamation-triangle"></i> 最后确认</h5>
                    <p>您即将执行：<strong id="clear-action-text"></strong></p>
                    <p class="text-danger"><strong>此操作不可撤销！</strong></p>
                </div>
                
                <div class="form-group">
                    <label>请输入 <code>确认清除</code> 来确认操作：</label>
                    <input type="text" class="form-control" id="confirm-text" placeholder="请输入：确认清除">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="final-confirm-btn" disabled onclick="executeClear()">
                    <i class="fas fa-trash-alt"></i> 确认清除
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 隐藏的表单 -->
<form id="clear-form" method="POST" action="{{ url_for('reports.clear_data_confirm') }}" style="display: none;">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
    <input type="hidden" name="clear_type" id="clear-type-input">
</form>

<script>
let currentClearType = '';

// 确认清除操作
function confirmClear(clearType, actionText) {
    currentClearType = clearType;
    document.getElementById('clear-action-text').textContent = actionText;
    document.getElementById('confirm-text').value = '';
    document.getElementById('final-confirm-btn').disabled = true;
    $('#confirmClearModal').modal('show');
}

// 监听确认文本输入
document.getElementById('confirm-text').addEventListener('input', function() {
    const confirmBtn = document.getElementById('final-confirm-btn');
    if (this.value === '确认清除') {
        confirmBtn.disabled = false;
    } else {
        confirmBtn.disabled = true;
    }
});

// 执行清除
function executeClear() {
    document.getElementById('clear-type-input').value = currentClearType;
    document.getElementById('clear-form').submit();
}

// 加载数据统计
$(document).ready(function() {
    // 这里可以通过AJAX加载当前数据统计
    // 暂时显示静态文本
    document.getElementById('consumption-count').textContent = '{{ validation_summary.consumption_stats.year_total.count if validation_summary else "0" }}条';
    document.getElementById('visit-count').textContent = '{{ validation_summary.visit_stats.year_total.count if validation_summary else "0" }}条';
    document.getElementById('upload-count').textContent = '加载中...';
});
</script>
{% endblock %}
