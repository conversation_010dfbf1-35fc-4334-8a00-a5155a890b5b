# -*- coding: utf-8 -*-
"""
系统设置Repository
"""

from typing import Optional, Dict, Any
from app.models import SystemSetting
from .base_repository import BaseRepository

class SystemSettingRepository(BaseRepository):
    """系统设置数据访问类"""
    
    def __init__(self):
        super().__init__(SystemSetting)
    
    def get_setting(self, key: str, default: Any = None) -> Any:
        """获取系统设置值"""
        setting = SystemSetting.query.filter_by(setting_key=key).first()
        return setting.setting_value if setting else default
    
    def set_setting(self, key: str, value: str, description: str = None) -> SystemSetting:
        """设置系统设置值"""
        setting = SystemSetting.query.filter_by(setting_key=key).first()
        if setting:
            setting.setting_value = value
            if description:
                setting.description = description
            from datetime import datetime
            setting.updated_at = datetime.utcnow()
        else:
            setting = SystemSetting(
                setting_key=key,
                setting_value=value,
                description=description
            )
            from app import db
            db.session.add(setting)
        
        from app import db
        db.session.commit()
        return setting
    
    def get_all_settings(self) -> Dict[str, str]:
        """获取所有系统设置"""
        settings = SystemSetting.query.all()
        return {setting.setting_key: setting.setting_value for setting in settings}
    
    def get_settings_by_prefix(self, prefix: str) -> Dict[str, str]:
        """根据前缀获取系统设置"""
        settings = SystemSetting.query.filter(
            SystemSetting.setting_key.like(f'{prefix}%')
        ).all()
        return {setting.setting_key: setting.setting_value for setting in settings}
    
    def delete_setting(self, key: str) -> bool:
        """删除系统设置"""
        setting = SystemSetting.query.filter_by(setting_key=key).first()
        if setting:
            from app import db
            db.session.delete(setting)
            db.session.commit()
            return True
        return False
    
    def get_card_number_length(self) -> int:
        """获取卡号长度设置"""
        length = self.get_setting('card_number_length', '10')
        try:
            return int(length)
        except (ValueError, TypeError):
            return 10
    
    def set_card_number_length(self, length: int) -> bool:
        """设置卡号长度"""
        if 10 <= length <= 18:
            self.set_setting('card_number_length', str(length), '卡号长度设置')
            return True
        return False
    
    def get_system_info(self) -> Dict[str, str]:
        """获取系统信息"""
        return {
            'system_name': self.get_setting('system_name', '老客登记信息反馈系统'),
            'version': self.get_setting('version', '1.0.0'),
            'card_number_length': self.get_setting('card_number_length', '10')
        }