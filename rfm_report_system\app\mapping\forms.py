from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileRequired, FileAllowed
from wtforms import StringField, TextAreaField, BooleanField, SubmitField, SelectField
from wtforms.validators import DataRequired, Length


class MappingTemplateForm(FlaskForm):
    """映射模板表单"""
    template_name = StringField('模板名称', validators=[
        DataRequired(message='请输入模板名称'),
        Length(min=1, max=100, message='模板名称长度应在1-100字符之间')
    ])
    
    description = TextAreaField('模板描述', validators=[
        Length(max=500, message='描述长度不能超过500字符')
    ])
    
    excel_file = FileField('Excel文件', validators=[
        FileRequired(message='请选择Excel文件'),
        FileAllowed(['xlsx', 'xls'], message='只支持Excel文件格式(.xlsx, .xls)')
    ])
    
    submit = SubmitField('上传模板')


class MappingEditForm(FlaskForm):
    """映射编辑表单"""
    mapping_type = SelectField('映射类型', choices=[
        ('level1', '一级分类映射'),
        ('level2', '二级分类映射'),
        ('level3', '三级分类映射')
    ], validators=[DataRequired(message='请选择映射类型')])
    
    original_value = StringField('原始值', validators=[
        DataRequired(message='请输入原始值'),
        Length(min=1, max=200, message='原始值长度应在1-200字符之间')
    ])
    
    mapped_value = StringField('映射值', validators=[
        DataRequired(message='请输入映射值'),
        Length(min=1, max=200, message='映射值长度应在1-200字符之间')
    ])
    
    is_active = BooleanField('启用', default=True)
    
    submit = SubmitField('保存映射')


class ActivateTemplateForm(FlaskForm):
    """激活模板表单"""
    submit = SubmitField('激活模板')
