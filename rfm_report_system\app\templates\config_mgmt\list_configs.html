{% extends "base.html" %}

{% block content %}
<div class="row mt-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2><i class="fas fa-database me-2"></i>数据库配置管理</h2>
            <a href="{{ url_for('config_mgmt.add_config') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>添加配置
            </a>
        </div>
    </div>
</div>

<div class="row mt-3">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if configs %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>配置名称</th>
                                    <th>数据库地址</th>
                                    <th>用户名</th>
                                    <th>密码</th>
                                    <th>数据库名</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for config in configs %}
                                <tr>
                                    <td>
                                        <strong>{{ config.name }}</strong>
                                        {% if config.is_active %}
                                            <span class="badge bg-success ms-2">当前激活</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ config.host }}:{{ config.port }}</td>
                                    <td>{{ config.username }}</td>
                                    <td><span class="password-mask">********</span></td>
                                    <td>{{ config.database_name }}</td>
                                    <td>
                                        {% if config.is_active %}
                                            <span class="badge bg-success">激活</span>
                                        {% else %}
                                            <span class="badge bg-secondary">未激活</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ config.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            {% if not config.is_active %}
                                                <form method="POST" action="{{ url_for('config_mgmt.activate_config', config_id=config.id) }}" style="display: inline;">
                                                    <button type="submit" class="btn btn-outline-success" 
                                                            onclick="return confirm('确定要激活此配置吗？')">
                                                        <i class="fas fa-play"></i>
                                                    </button>
                                                </form>
                                            {% endif %}
                                            <a href="{{ url_for('config_mgmt.edit_config', config_id=config.id) }}" 
                                               class="btn btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% if not config.is_active %}
                                                <form method="POST" action="{{ url_for('config_mgmt.delete_config', config_id=config.id) }}" style="display: inline;">
                                                    <button type="submit" class="btn btn-outline-danger" 
                                                            onclick="return confirm('确定要删除此配置吗？此操作不可恢复！')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-database fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无数据库配置</h5>
                        <p class="text-muted">请添加第一个数据库配置以开始使用系统</p>
                        <a href="{{ url_for('config_mgmt.add_config') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>添加配置
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 使用说明 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-info-circle me-2"></i>使用说明</h6>
            </div>
            <div class="card-body">
                <ul class="mb-0">
                    <li>每次只能激活一个数据库配置，激活的配置将用于所有数据处理任务</li>
                    <li>激活配置前系统会自动测试连接，确保配置有效</li>
                    <li>无法删除当前激活的配置，请先激活其他配置</li>
                    <li>密码采用加密存储，页面上显示为掩码</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
