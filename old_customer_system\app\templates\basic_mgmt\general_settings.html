{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">通用设置</h1>
    <a href="{{ url_for('basic_mgmt.system_settings') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>返回系统设置
    </a>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cog me-2"></i>系统通用设置
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <!-- 系统信息设置 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2">系统信息</h6>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.system_name.label(class="form-label") }}
                                {{ form.system_name(class="form-control") }}
                                {% if form.system_name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.system_name.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.system_version.label(class="form-label") }}
                                {{ form.system_version(class="form-control") }}
                                {% if form.system_version.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.system_version.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- 字段长度设置 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2">字段长度设置</h6>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                {{ form.card_number_length.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.card_number_length(class="form-control") }}
                                    <span class="input-group-text">位</span>
                                </div>
                                {% if form.card_number_length.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.card_number_length.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">卡号必须为纯数字，长度范围：6-18位</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                {{ form.inquiry_content_max_length.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.inquiry_content_max_length(class="form-control") }}
                                    <span class="input-group-text">字</span>
                                </div>
                                {% if form.inquiry_content_max_length.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.inquiry_content_max_length.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                {{ form.follow_up_note_max_length.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.follow_up_note_max_length(class="form-control") }}
                                    <span class="input-group-text">字</span>
                                </div>
                                {% if form.follow_up_note_max_length.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.follow_up_note_max_length.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- 业务规则设置 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2">业务规则</h6>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    {{ form.auto_generate_simple_code(class="form-check-input") }}
                                    {{ form.auto_generate_simple_code.label(class="form-check-label") }}
                                </div>
                                <div class="form-text">为渠道和用户自动生成拼音简码</div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    {{ form.enable_duplicate_check(class="form-check-input") }}
                                    {{ form.enable_duplicate_check.label(class="form-check-label") }}
                                </div>
                                <div class="form-text">检查重复的卡号和渠道名称</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    {{ form.require_last_visit_date(class="form-check-input") }}
                                    {{ form.require_last_visit_date.label(class="form-check-label") }}
                                </div>
                                <div class="form-text">客户登记时必须填写最近来院时间</div>
                            </div>
                            <div class="mb-3">
                                {{ form.data_retention_days.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.data_retention_days(class="form-control") }}
                                    <span class="input-group-text">天</span>
                                </div>
                                {% if form.data_retention_days.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.data_retention_days.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">数据保留时间，超过此时间的数据可能被清理</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 设置说明 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2">设置说明</h6>
                        </div>
                        <div class="col-12">
                            <div class="mb-3">
                                {{ form.settings_note.label(class="form-label") }}
                                {{ form.settings_note(class="form-control", rows="3", placeholder="可以在这里添加关于当前设置的说明或备注...") }}
                                {% if form.settings_note.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.settings_note.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('basic_mgmt.system_settings') }}" class="btn btn-outline-secondary me-md-2">取消</a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>设置说明
                </h5>
            </div>
            <div class="card-body">
                <h6>卡号长度设置</h6>
                <p class="small text-muted">
                    设置客户卡号的固定长度。卡号必须为纯数字，长度范围为6-18位。
                    修改此设置后，新登记的客户卡号将按新长度验证。
                </p>
                
                <h6>内容长度限制</h6>
                <p class="small text-muted">
                    设置咨询内容和跟进情况字段的最大字符数。
                    建议根据实际业务需要调整，避免过长或过短。
                </p>
                
                <h6>业务规则</h6>
                <p class="small text-muted">
                    配置系统的业务处理规则，包括是否自动生成简码、
                    是否启用重复检查等功能。
                </p>
                
                <div class="alert alert-warning">
                    <small>
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        修改这些设置可能会影响系统的正常运行，请谨慎操作。
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}