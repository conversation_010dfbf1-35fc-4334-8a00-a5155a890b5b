# -*- coding: utf-8 -*-
"""
系统初始化相关表单
"""

from flask_wtf import FlaskForm
from wtforms import StringField, IntegerField, PasswordField, SubmitField, SelectField
from wtforms.validators import DataRequired, Length, NumberRange, EqualTo, ValidationError
from app.models import User

class DatabaseConfigForm(FlaskForm):
    """数据库配置表单"""
    host = StringField('数据库主机', validators=[DataRequired()], default='localhost')
    port = IntegerField('端口', validators=[DataRequired(), NumberRange(min=1, max=65535)], default=3306)
    username = StringField('用户名', validators=[DataRequired(), Length(min=1, max=50)])
    password = PasswordField('密码')
    database = StringField('数据库名', validators=[DataRequired(), Length(min=1, max=64)], default='Old_Customer_System')
    submit = SubmitField('测试连接')

class AdminRegistrationForm(FlaskForm):
    """管理员注册表单"""
    username = StringField('用户名', validators=[
        DataRequired(message='用户名不能为空'),
        Length(min=3, max=50, message='用户名长度必须在3-50个字符之间')
    ])
    real_name = StringField('真实姓名', validators=[
        DataRequired(message='真实姓名不能为空'),
        Length(min=2, max=100, message='真实姓名长度必须在2-100个字符之间')
    ])
    password = PasswordField('密码', validators=[
        DataRequired(message='密码不能为空'),
        Length(min=6, max=128, message='密码长度必须在6-128个字符之间')
    ])
    password2 = PasswordField('确认密码', validators=[
        DataRequired(message='请确认密码'),
        EqualTo('password', message='两次输入的密码不一致')
    ])
    submit = SubmitField('创建管理员账号')
    
    def validate_username(self, username):
        """验证用户名唯一性"""
        user = User.query.filter_by(username=username.data).first()
        if user:
            raise ValidationError('用户名已存在，请选择其他用户名。')