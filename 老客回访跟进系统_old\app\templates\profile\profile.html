{% extends "base.html" %}

{% block title %}个人资料 - 老客回访与跟进系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-user"></i> 个人资料</h5>
            </div>
            <div class="card-body">
                <table class="table">
                    <tr>
                        <th width="150">用户名</th>
                        <td>{{ current_user.username }}</td>
                    </tr>
                    <tr>
                        <th>真实姓名</th>
                        <td>{{ current_user.real_name }}</td>
                    </tr>
                    <tr>
                        <th>所属部门</th>
                        <td>{{ current_user.department }}</td>
                    </tr>
                    <tr>
                        <th>用户角色</th>
                        <td>
                            <span class="badge bg-primary">{{ current_user.role.role_name }}</span>
                        </td>
                    </tr>
                    <tr>
                        <th>账号状态</th>
                        <td>
                            {% if current_user.is_active %}
                                <span class="badge bg-success">正常</span>
                            {% else %}
                                <span class="badge bg-danger">已停用</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th>创建时间</th>
                        <td>{{ current_user.created_time.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                    </tr>
                    <tr>
                        <th>最后登录</th>
                        <td>
                            {% if current_user.last_login_time %}
                                {{ current_user.last_login_time.strftime('%Y-%m-%d %H:%M:%S') }}
                            {% else %}
                                首次登录
                            {% endif %}
                        </td>
                    </tr>
                </table>
                
                <div class="mt-3">
                    <a href="{{ url_for('auth.change_password') }}" class="btn btn-primary">
                        <i class="fas fa-key"></i> 修改密码
                    </a>
                    <a href="{{ url_for('main.dashboard') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> 返回首页
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-shield-alt"></i> 权限信息</h5>
            </div>
            <div class="card-body">
                <h6>角色描述</h6>
                <p class="text-muted">{{ current_user.role.description }}</p>
                
                <h6>拥有权限</h6>
                <div class="permissions-list">
                    {% for permission in current_user.get_permissions() %}
                        <span class="badge bg-secondary me-1 mb-1">{{ permission.permission_name }}</span>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
