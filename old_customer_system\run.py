# -*- coding: utf-8 -*-
"""
老客登记信息反馈系统启动脚本
"""

import os
from app import create_app, db
from app.models import User, Department, Channel, Customer, OnsiteGroup, SystemSetting

app = create_app(os.getenv('FLASK_CONFIG') or 'default')

@app.shell_context_processor
def make_shell_context():
    """Shell上下文处理器"""
    return {
        'db': db,
        'User': User,
        'Department': Department,
        'Channel': Channel,
        'Customer': Customer,
        'OnsiteGroup': OnsiteGroup,
        'SystemSetting': SystemSetting
    }

@app.cli.command()
def init_db():
    """初始化数据库"""
    db.create_all()
    print('数据库初始化完成。')

@app.cli.command()
def create_admin():
    """创建管理员账号"""
    from app.services.user_service import UserService
    
    username = input('请输入管理员用户名: ')
    password = input('请输入管理员密码: ')
    real_name = input('请输入管理员真实姓名: ')
    
    try:
        admin_user = UserService.create_user({
            'username': username,
            'password': password,
            'real_name': real_name,
            'role': 'admin',
            'is_active': True
        })
        print(f'管理员账号 {username} 创建成功！')
    except Exception as e:
        print(f'创建管理员账号失败: {str(e)}')

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)