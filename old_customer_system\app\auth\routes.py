# -*- coding: utf-8 -*-
"""
认证路由
"""

from flask import render_template, redirect, url_for, flash, request
from flask_login import current_user, login_required
from app.auth import bp
from app.forms.auth_forms import LoginForm, ChangePasswordForm
from app.services.user_service import UserService

@bp.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录"""
    # 如果用户已登录，重定向到首页
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))
    
    form = LoginForm()
    if form.validate_on_submit():
        user_service = UserService()
        
        # 用户认证
        success, user, message = user_service.authenticate_user(
            form.username.data, form.password.data
        )
        
        if success:
            # 登录用户
            if user_service.login_user_session(user, form.remember_me.data):
                flash(f'欢迎回来，{user.real_name}！', 'success')
                
                # 重定向到用户原本想访问的页面
                next_page = request.args.get('next')
                if next_page:
                    return redirect(next_page)
                return redirect(url_for('main.index'))
            else:
                flash('登录失败，请重试', 'error')
        else:
            flash(message, 'error')
    
    return render_template('auth/login.html', form=form, title='登录')

@bp.route('/logout')
@login_required
def logout():
    """用户登出"""
    user_service = UserService()
    if user_service.logout_user_session():
        flash('您已成功退出登录', 'info')
    return redirect(url_for('main.index'))

@bp.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    """修改密码"""
    form = ChangePasswordForm()
    if form.validate_on_submit():
        user_service = UserService()
        
        success, message = user_service.change_password(
            current_user.id,
            form.old_password.data,
            form.new_password.data
        )
        
        if success:
            flash(message, 'success')
            return redirect(url_for('main.dashboard'))
        else:
            flash(message, 'error')
    
    return render_template('auth/change_password.html', form=form, title='修改密码')