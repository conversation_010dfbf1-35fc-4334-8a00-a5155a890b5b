{% extends "base.html" %}

{% block title %}审计日志 - 老客回访与跟进系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-history"></i> 审计日志</h2>
        <p class="text-muted">查看系统操作记录</p>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>用户</th>
                                <th>操作</th>
                                <th>资源类型</th>
                                <th>资源ID</th>
                                <th>IP地址</th>
                                <th>详情</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for log in logs.items %}
                            <tr>
                                <td>{{ log.created_time.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                <td>
                                    {% if log.user %}
                                        {{ log.user.real_name }}
                                        <small class="text-muted">({{ log.user.username }})</small>
                                    {% else %}
                                        <span class="text-muted">系统</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if log.action == 'LOGIN' %}
                                        <span class="badge bg-success">登录</span>
                                    {% elif log.action == 'LOGOUT' %}
                                        <span class="badge bg-secondary">登出</span>
                                    {% elif log.action == 'LOGIN_FAILED' %}
                                        <span class="badge bg-danger">登录失败</span>
                                    {% elif log.action == 'CREATE_USER' %}
                                        <span class="badge bg-primary">创建用户</span>
                                    {% elif log.action == 'CHANGE_PASSWORD' %}
                                        <span class="badge bg-warning">修改密码</span>
                                    {% else %}
                                        <span class="badge bg-info">{{ log.action }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ log.resource_type }}</td>
                                <td>{{ log.resource_id or '-' }}</td>
                                <td>{{ log.ip_address or '-' }}</td>
                                <td>
                                    {% if log.details %}
                                        <button class="btn btn-sm btn-outline-info" 
                                                onclick="showDetails('{{ log.details|replace("'", "\\'") }}')">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="7" class="text-center text-muted">暂无日志记录</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                {% if logs.pages > 1 %}
                <nav aria-label="日志分页">
                    <ul class="pagination justify-content-center">
                        {% if logs.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.audit_logs', page=logs.prev_num) }}">上一页</a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in logs.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != logs.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('admin.audit_logs', page=page_num) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">…</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if logs.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.audit_logs', page=logs.next_num) }}">下一页</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 详情模态框 -->
<div class="modal fade" id="detailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-info-circle"></i> 操作详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <pre id="detailsContent" class="bg-light p-3"></pre>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showDetails(details) {
    document.getElementById('detailsContent').textContent = details;
    new bootstrap.Modal(document.getElementById('detailsModal')).show();
}
</script>
{% endblock %}
