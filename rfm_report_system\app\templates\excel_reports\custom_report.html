{% extends "base.html" %}

{% block title %}自定义Excel报表{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-plus me-2 text-primary"></i>自定义Excel报表</h2>
                    <p class="text-muted">基于Excel公式创建自定义报表</p>
                </div>
                <div>
                    <a href="{{ url_for('excel_reports.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>返回列表
                    </a>
                    <button class="btn btn-info" onclick="loadSampleConfig()">
                        <i class="fas fa-lightbulb me-2"></i>加载示例
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 配置表单 -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-cog me-2"></i>报表配置</h5>
                </div>
                <div class="card-body">
                    <form id="customReportForm">
                        <!-- 基本信息 -->
                        <div class="mb-4">
                            <h6><i class="fas fa-info-circle me-2"></i>基本信息</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">报表名称</label>
                                    <input type="text" class="form-control" id="reportName" placeholder="请输入报表名称" required>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">报表描述</label>
                                    <input type="text" class="form-control" id="reportDescription" placeholder="请输入报表描述">
                                </div>
                            </div>
                        </div>

                        <!-- 数据源配置 -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6><i class="fas fa-database me-2"></i>数据源配置</h6>
                                <button type="button" class="btn btn-sm btn-primary" onclick="addDataSource()">
                                    <i class="fas fa-plus me-1"></i>添加数据源
                                </button>
                            </div>
                            <div id="dataSourcesContainer">
                                <!-- 数据源将在这里动态添加 -->
                            </div>
                        </div>

                        <!-- 计算列配置 -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6><i class="fas fa-columns me-2"></i>计算列配置</h6>
                                <button type="button" class="btn btn-sm btn-success" onclick="addColumn()">
                                    <i class="fas fa-plus me-1"></i>添加列
                                </button>
                            </div>
                            <div id="columnsContainer">
                                <!-- 计算列将在这里动态添加 -->
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-primary" onclick="previewCustomReport()">
                                <i class="fas fa-eye me-2"></i>预览数据
                            </button>
                            <button type="button" class="btn btn-success" onclick="generateCustomReport()">
                                <i class="fas fa-download me-2"></i>生成报表
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                                <i class="fas fa-undo me-2"></i>重置
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 帮助面板 -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-question-circle me-2"></i>帮助信息</h5>
                </div>
                <div class="card-body">
                    <!-- 可用表格 -->
                    <div class="mb-4">
                        <h6><i class="fas fa-table me-2"></i>可用数据表</h6>
                        <div class="list-group list-group-flush" style="max-height: 200px; overflow-y: auto;">
                            {% for table in tables %}
                            <div class="list-group-item border-0 px-0 py-1">
                                <small class="text-primary">{{ table }}</small>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- 公式示例 -->
                    <div class="mb-4">
                        <h6><i class="fas fa-code me-2"></i>公式示例</h6>
                        {% for category, examples in formula_examples.items() %}
                        <div class="mb-3">
                            <strong class="small">{{ category }}：</strong>
                            {% for example in examples %}
                            <div class="small text-muted mb-1">
                                <code>{{ example }}</code>
                            </div>
                            {% endfor %}
                        </div>
                        {% endfor %}
                    </div>

                    <!-- 使用说明 -->
                    <div class="alert alert-info">
                        <h6>使用说明</h6>
                        <ul class="small mb-0">
                            <li>数据源：编写SQL查询获取数据</li>
                            <li>计算列：使用Excel公式计算新列</li>
                            <li>列引用：使用[列名]引用其他列</li>
                            <li>预览：生成前可预览数据结构</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 预览模态框 -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">报表预览</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="previewContent">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在生成预览...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-success" onclick="generateCustomReport()">生成完整报表</button>
            </div>
        </div>
    </div>
</div>

<!-- 加载提示模态框 -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">处理中...</span>
                </div>
                <p id="loadingMessage">正在生成报表，请稍候...</p>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
let dataSourceCounter = 0;
let columnCounter = 0;

// 示例配置
const sampleConfig = {{ sample_config | tojson }};

// 页面加载完成后初始化
$(document).ready(function() {
    // 添加第一个数据源
    addDataSource();
    // 添加第一个列
    addColumn();
});

// 加载示例配置
function loadSampleConfig() {
    if (!sampleConfig) return;

    // 填充基本信息
    $('#reportName').val(sampleConfig.name || '');
    $('#reportDescription').val(sampleConfig.description || '');

    // 清空现有配置
    $('#dataSourcesContainer').empty();
    $('#columnsContainer').empty();
    dataSourceCounter = 0;
    columnCounter = 0;

    // 加载数据源
    if (sampleConfig.data_sources) {
        sampleConfig.data_sources.forEach(ds => {
            addDataSource(ds);
        });
    }

    // 加载列配置
    if (sampleConfig.columns) {
        sampleConfig.columns.forEach(col => {
            addColumn(col);
        });
    }

    Swal.fire({
        icon: 'success',
        title: '示例配置已加载',
        text: '您可以基于示例配置进行修改',
        timer: 2000
    });
}

// 添加数据源
function addDataSource(config = null) {
    dataSourceCounter++;
    const dsId = `dataSource_${dataSourceCounter}`;

    const html = `
        <div class="card mb-3" id="${dsId}">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">数据源 ${dataSourceCounter}</h6>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeDataSource('${dsId}')">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">数据源名称</label>
                    <input type="text" class="form-control ds-name" placeholder="例：会员数据" value="${config?.name || ''}" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">SQL查询</label>
                    <textarea class="form-control ds-query" rows="6" placeholder="SELECT * FROM 表名 WHERE 条件" required>${config?.query || ''}</textarea>
                    <div class="form-text">
                        <button type="button" class="btn btn-sm btn-outline-primary mt-2" onclick="previewQuery('${dsId}')">
                            <i class="fas fa-eye me-1"></i>预览查询结果
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    $('#dataSourcesContainer').append(html);
}

// 移除数据源
function removeDataSource(dsId) {
    $(`#${dsId}`).remove();
}

// 添加计算列
function addColumn(config = null) {
    columnCounter++;
    const colId = `column_${columnCounter}`;

    const html = `
        <div class="card mb-3" id="${colId}">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">列 ${columnCounter}</h6>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeColumn('${colId}')">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">列名</label>
                        <input type="text" class="form-control col-name" placeholder="例：客户等级" value="${config?.name || ''}" required>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">源列名（可选）</label>
                        <input type="text" class="form-control col-source" placeholder="直接复制的列名" value="${config?.source_column || ''}">
                    </div>
                </div>
                <div class="mt-3">
                    <label class="form-label">Excel公式（可选）</label>
                    <input type="text" class="form-control col-formula" placeholder="例：=IF([金额]>1000, &quot;高消费&quot;, &quot;普通消费&quot;)" value="${config?.formula || ''}">
                    <div class="form-text">
                        <button type="button" class="btn btn-sm btn-outline-success mt-2" onclick="validateFormula('${colId}')">
                            <i class="fas fa-check me-1"></i>验证公式
                        </button>
                    </div>
                </div>
                <div class="mt-3">
                    <label class="form-label">列描述</label>
                    <input type="text" class="form-control col-description" placeholder="列的说明" value="${config?.description || ''}">
                </div>
            </div>
        </div>
    `;

    $('#columnsContainer').append(html);
}

// 移除计算列
function removeColumn(colId) {
    $(`#${colId}`).remove();
}

// 收集表单数据
function collectFormData() {
    const config = {
        name: $('#reportName').val(),
        description: $('#reportDescription').val(),
        data_sources: [],
        columns: []
    };

    // 收集数据源
    $('#dataSourcesContainer .card').each(function() {
        const name = $(this).find('.ds-name').val();
        const query = $(this).find('.ds-query').val();

        if (name && query) {
            config.data_sources.push({name, query});
        }
    });

    // 收集列配置
    $('#columnsContainer .card').each(function() {
        const name = $(this).find('.col-name').val();
        const source_column = $(this).find('.col-source').val();
        const formula = $(this).find('.col-formula').val();
        const description = $(this).find('.col-description').val();

        if (name) {
            const col = {name, description};
            if (source_column) col.source_column = source_column;
            if (formula) col.formula = formula;
            config.columns.push(col);
        }
    });

    return config;
}

// 生成自定义报表
function generateCustomReport() {
    const config = collectFormData();

    if (!config.name) {
        Swal.fire('错误', '请输入报表名称', 'error');
        return;
    }

    if (config.data_sources.length === 0) {
        Swal.fire('错误', '请至少配置一个数据源', 'error');
        return;
    }

    if (config.columns.length === 0) {
        Swal.fire('错误', '请至少配置一个输出列', 'error');
        return;
    }

    $('#previewModal').modal('hide');
    $('#loadingModal').modal('show');

    $.ajax({
        url: '{{ url_for("excel_reports.generate_custom_report") }}',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(config),
        success: function(response) {
            $('#loadingModal').modal('hide');

            if (response.success) {
                Swal.fire({
                    icon: 'success',
                    title: '报表生成成功！',
                    text: response.message,
                    showCancelButton: true,
                    confirmButtonText: '立即下载',
                    cancelButtonText: '稍后下载'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = response.download_url;
                    }
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: '生成失败',
                    text: response.message
                });
            }
        },
        error: function() {
            $('#loadingModal').modal('hide');
            Swal.fire({
                icon: 'error',
                title: '请求失败',
                text: '报表生成请求失败，请稍后重试'
            });
        }
    });
}

// 预览自定义报表
function previewCustomReport() {
    const config = collectFormData();

    if (!config.name) {
        Swal.fire('错误', '请输入报表名称', 'error');
        return;
    }

    if (config.data_sources.length === 0) {
        Swal.fire('错误', '请至少配置一个数据源', 'error');
        return;
    }

    if (config.columns.length === 0) {
        Swal.fire('错误', '请至少配置一个输出列', 'error');
        return;
    }

    $('#previewModal').modal('show');
    $('#previewContent').html(`
        <div class="alert alert-info">
            <h6>配置预览</h6>
            <p><strong>报表名称：</strong>${config.name}</p>
            <p><strong>数据源数量：</strong>${config.data_sources.length}</p>
            <p><strong>输出列数量：</strong>${config.columns.length}</p>
            <p class="mb-0">点击"生成完整报表"开始处理数据</p>
        </div>
    `);
}

// 重置表单
function resetForm() {
    Swal.fire({
        title: '确认重置',
        text: '这将清空所有配置，确定要重置吗？',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: '确定重置',
        cancelButtonText: '取消'
    }).then((result) => {
        if (result.isConfirmed) {
            $('#customReportForm')[0].reset();
            $('#dataSourcesContainer').empty();
            $('#columnsContainer').empty();
            dataSourceCounter = 0;
            columnCounter = 0;
            addDataSource();
            addColumn();
        }
    });
}
</script>
{% endblock %}
