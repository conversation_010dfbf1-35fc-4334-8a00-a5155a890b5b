"""
创建正确的配置文件
"""
import getpass

def create_config():
    """创建配置文件"""
    print("创建配置文件...")
    
    # 获取数据库信息
    host = input("数据库主机 [127.0.0.1]: ").strip() or '127.0.0.1'
    port = input("数据库端口 [3306]: ").strip() or '3306'
    user = input("数据库用户名 [root]: ").strip() or 'root'
    password = getpass.getpass("数据库密码: ")
    database = input("数据库名 [Old_Customer_System]: ").strip() or 'Old_Customer_System'
    
    # 创建配置文件内容
    config_content = f'''"""
系统配置文件
"""
import os
from datetime import timedelta

class Config:
    """基础配置类"""
    
    # 基础配置
    SECRET_KEY = 'your-secret-key-change-in-production'
    
    # 数据库配置
    MYSQL_HOST = '{host}'
    MYSQL_PORT = {port}
    MYSQL_USER = '{user}'
    MYSQL_PASSWORD = '{password}'
    MYSQL_DATABASE = '{database}'
    
    # SQLAlchemy 配置
    SQLALCHEMY_DATABASE_URI = f"mysql+pymysql://{user}:{password}@{host}:{port}/{database}?charset=utf8mb4"
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {{
        'pool_size': 10,
        'pool_timeout': 20,
        'pool_recycle': -1,
        'max_overflow': 0,
        'echo': False
    }}
    
    # 会话配置
    PERMANENT_SESSION_LIFETIME = timedelta(minutes=30)
    SESSION_COOKIE_SECURE = False
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # 文件上传配置
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
    ALLOWED_EXTENSIONS = {{'xlsx', 'xls'}}
    
    # 分页配置
    ITEMS_PER_PAGE = 20
    MAX_ITEMS_PER_PAGE = 100
    
    # 日志配置
    LOG_LEVEL = 'INFO'
    LOG_FILE = 'app.log'
    
    # 系统默认配置
    DEFAULT_CARD_NUMBER_MAX_LENGTH = 10
    DEFAULT_CONTENT_MAX_LENGTH = 500
    DEFAULT_PASSWORD_MIN_LENGTH = 8
    
    @staticmethod
    def init_app(app):
        """初始化应用配置"""
        upload_folder = app.config.get('UPLOAD_FOLDER')
        if upload_folder and not os.path.exists(upload_folder):
            os.makedirs(upload_folder)

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    SESSION_COOKIE_SECURE = True

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'

# 配置字典
config = {{
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}}

def get_config():
    """获取当前配置"""
    return config.get(os.environ.get('FLASK_ENV', 'default'), DevelopmentConfig)
'''
    
    try:
        # 备份原配置文件
        if os.path.exists('config.py'):
            os.rename('config.py', 'config.py.backup')
            print("✅ 原配置文件已备份为 config.py.backup")
        
        # 写入新配置文件
        with open('config.py', 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        print("✅ 新配置文件创建成功")
        return True
        
    except Exception as e:
        print(f"❌ 配置文件创建失败: {e}")
        return False

def test_config():
    """测试配置"""
    try:
        import importlib
        import sys
        
        # 重新加载配置模块
        if 'config' in sys.modules:
            importlib.reload(sys.modules['config'])
        
        from config import get_config
        config = get_config()
        
        print("配置信息:")
        print(f"  数据库主机: {config.MYSQL_HOST}")
        print(f"  数据库端口: {config.MYSQL_PORT}")
        print(f"  数据库用户: {config.MYSQL_USER}")
        print(f"  数据库名称: {config.MYSQL_DATABASE}")
        print(f"  连接字符串: {config.SQLALCHEMY_DATABASE_URI}")
        
        return True
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def main():
    """主函数"""
    print("配置文件创建工具")
    print("="*30)
    
    if create_config():
        print("\n测试新配置:")
        print("-"*30)
        if test_config():
            print("\n✅ 配置创建完成！")
            print("现在可以运行:")
            print("  python test_system.py  # 测试系统")
            print("  python init_db.py      # 初始化数据库")
            print("  python quick_start.py  # 启动系统")
        else:
            print("\n❌ 配置有问题，请检查")
    else:
        print("\n❌ 配置创建失败")

if __name__ == '__main__':
    main()
