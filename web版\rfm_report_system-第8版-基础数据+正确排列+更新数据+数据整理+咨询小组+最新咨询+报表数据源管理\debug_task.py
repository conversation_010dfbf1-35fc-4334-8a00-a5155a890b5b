#!/usr/bin/env python3
"""
调试任务执行问题
"""

import logging
from app import create_app
from app.models import DataProcessingTask, DatabaseConfig
from app.data_processing.processor import RFMDataProcessor

def debug_task_execution():
    """调试任务执行"""
    app = create_app()
    
    with app.app_context():
        # 获取最新的任务
        latest_task = DataProcessingTask.query.order_by(DataProcessingTask.created_at.desc()).first()
        
        if not latest_task:
            print("❌ 没有找到任务")
            return
        
        print(f"📋 最新任务信息:")
        print(f"   ID: {latest_task.id}")
        print(f"   名称: {latest_task.task_name}")
        print(f"   状态: {latest_task.status}")
        print(f"   进度: {latest_task.progress}%")
        print(f"   创建时间: {latest_task.created_at}")
        print(f"   开始时间: {latest_task.started_at}")
        print(f"   错误信息: {latest_task.error_message}")
        
        # 获取任务参数
        params = latest_task.get_parameters()
        print(f"\n📊 任务参数:")
        print(f"   类型: {latest_task.task_type}")
        print(f"   参数: {params}")
        
        # 检查数据库配置
        active_config = DatabaseConfig.get_active_config()
        if not active_config:
            print("❌ 没有激活的数据库配置")
            return
        
        print(f"\n🔗 数据库配置:")
        print(f"   名称: {active_config.name}")
        print(f"   数据库: {active_config.database_name}")
        
        # 尝试手动执行任务
        if latest_task.status == 'pending':
            print(f"\n🚀 尝试手动执行任务...")
            
            try:
                # 开始任务
                latest_task.start_task()
                print(f"   ✅ 任务已标记为开始")
                
                # 创建处理器
                logger = logging.getLogger(f'debug_task_{latest_task.id}')
                processor = RFMDataProcessor(active_config, logger)
                
                # 检查数据库连接
                if not processor.connect_database():
                    print(f"   ❌ 数据库连接失败")
                    return
                
                print(f"   ✅ 数据库连接成功")
                
                # 获取季度表
                quarter_tables = processor.get_quarter_tables()
                print(f"   📋 找到季度表: {quarter_tables}")
                
                # 更新进度
                latest_task.update_progress(10, step_name="调试测试", message="手动调试执行")
                print(f"   ✅ 进度更新成功")
                
                # 检查进度日志
                logs = latest_task.get_progress_logs()
                print(f"   📝 进度日志: {len(logs)} 条")
                for log in logs:
                    print(f"      - {log['step_name']}: {log['message']} ({log['progress']}%)")
                
            except Exception as e:
                print(f"   ❌ 手动执行失败: {e}")
                import traceback
                traceback.print_exc()
        
        else:
            print(f"\n⚠️  任务状态不是 pending，无法手动执行")

def test_background_task():
    """测试后台任务执行"""
    app = create_app()
    
    with app.app_context():
        # 获取最新的任务
        latest_task = DataProcessingTask.query.order_by(DataProcessingTask.created_at.desc()).first()
        
        if not latest_task:
            print("❌ 没有找到任务")
            return
        
        print(f"🧪 测试后台任务执行 (任务ID: {latest_task.id})")
        
        # 导入后台处理函数
        from app.data_processing.routes import process_data_background
        
        try:
            # 直接调用后台处理函数
            process_data_background(latest_task.id)
            print(f"✅ 后台任务执行完成")
            
            # 重新查询任务状态
            latest_task = DataProcessingTask.query.get(latest_task.id)
            print(f"📊 任务最终状态:")
            print(f"   状态: {latest_task.status}")
            print(f"   进度: {latest_task.progress}%")
            print(f"   错误信息: {latest_task.error_message}")
            
        except Exception as e:
            print(f"❌ 后台任务执行失败: {e}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'test':
        test_background_task()
    else:
        debug_task_execution()
