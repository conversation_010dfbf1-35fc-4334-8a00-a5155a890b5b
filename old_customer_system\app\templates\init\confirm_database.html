{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-10 col-lg-8">
            <div class="card mt-5">
                <div class="card-header text-center">
                    <h4><i class="fas fa-exclamation-triangle me-2"></i>确认数据库操作</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>数据库连接信息</h6>
                        <ul class="mb-0">
                            <li><strong>主机:</strong> {{ db_config.host }}:{{ db_config.port }}</li>
                            <li><strong>用户名:</strong> {{ db_config.username }}</li>
                            <li><strong>数据库:</strong> {{ db_config.database }}</li>
                        </ul>
                    </div>
                    
                    {% if db_exists %}
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-database me-2"></i>数据库已存在</h6>
                            <p>{{ db_message }}</p>
                            
                            {% if db_info and not db_info.get('error') %}
                                <div class="mt-3">
                                    <h6>数据库信息:</h6>
                                    <ul class="mb-0">
                                        <li><strong>表数量:</strong> {{ db_info.table_count }}</li>
                                        <li><strong>数据库大小:</strong> {{ db_info.database_size }} MB</li>
                                        {% if db_info.tables %}
                                            <li><strong>现有表:</strong> {{ db_info.tables | join(', ') }}</li>
                                        {% endif %}
                                    </ul>
                                </div>
                            {% endif %}
                        </div>
                        
                        <p>请选择以下操作之一:</p>
                        
                        <form method="POST" class="d-inline">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            <button type="submit" name="action" value="use_existing" class="btn btn-success me-2">
                                <i class="fas fa-check me-2"></i>使用现有数据库
                            </button>
                        </form>
                        
                        <form method="POST" class="d-inline">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            <button type="submit" name="action" value="reset" class="btn btn-danger" 
                                    onclick="return confirm('警告：此操作将删除现有数据库中的所有数据！确定要继续吗？')">
                                <i class="fas fa-trash me-2"></i>清空并重建数据库
                            </button>
                        </form>
                        
                    {% else %}
                        <div class="alert alert-success">
                            <h6><i class="fas fa-plus-circle me-2"></i>创建新数据库</h6>
                            <p>{{ db_message }}</p>
                            <p>系统将创建新的数据库并初始化所需的表结构。</p>
                        </div>
                        
                        <form method="POST">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            <button type="submit" name="action" value="initialize" class="btn btn-primary">
                                <i class="fas fa-play me-2"></i>初始化数据库
                            </button>
                        </form>
                    {% endif %}
                    
                    <div class="mt-4 pt-3 border-top">
                        <a href="{{ url_for('init.database_config') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>返回修改配置
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}