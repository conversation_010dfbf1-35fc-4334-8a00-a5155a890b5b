# -*- coding: utf-8 -*-
"""
权限控制测试
"""

import unittest
from app import create_app, db
from app.models import User, Department, Channel, Customer
from app.services.permission_service import PermissionService

class PermissionTestCase(unittest.TestCase):
    """权限控制测试"""
    
    def setUp(self):
        """测试前准备"""
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        
        # 创建测试数据
        self.create_test_data()
    
    def tearDown(self):
        """测试后清理"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def create_test_data(self):
        """创建测试数据"""
        # 创建部门
        self.dept1 = Department(name='部门1')
        self.dept2 = Department(name='部门2')
        db.session.add_all([self.dept1, self.dept2])
        
        # 创建用户
        self.admin = User(username='admin', real_name='管理员', role='admin', is_active=True)
        self.admin.set_password('admin123')
        
        self.director = User(username='director', real_name='经营院长', role='director', is_active=True)
        self.director.set_password('director123')
        
        self.manager1 = User(username='manager1', real_name='部门主管1', role='manager', 
                            department_id=1, is_active=True)
        self.manager1.set_password('manager123')
        
        self.online1 = User(username='online1', real_name='网络咨询1', role='online_consultant',
                           department_id=1, is_active=True)
        self.online1.set_password('online123')
        
        self.online2 = User(username='online2', real_name='网络咨询2', role='online_consultant',
                           department_id=2, is_active=True)
        self.online2.set_password('online123')
        
        self.onsite1 = User(username='onsite1', real_name='现场咨询1', role='onsite_consultant',
                           department_id=1, is_active=True)
        self.onsite1.set_password('onsite123')
        
        db.session.add_all([self.admin, self.director, self.manager1, 
                           self.online1, self.online2, self.onsite1])
        
        # 创建渠道
        self.channel = Channel(name='测试渠道', is_active=True)
        db.session.add(self.channel)
        
        db.session.commit()
        
        # 创建客户
        self.customer1 = Customer(
            card_number='1234567890',
            onsite_consultant_id=self.onsite1.id,
            online_consultant_id=self.online1.id,
            channel_id=self.channel.id,
            created_by=self.online1.id
        )
        
        self.customer2 = Customer(
            card_number='0987654321',
            onsite_consultant_id=self.onsite1.id,
            online_consultant_id=self.online2.id,
            channel_id=self.channel.id,
            created_by=self.online2.id
        )
        
        db.session.add_all([self.customer1, self.customer2])
        db.session.commit()
    
    def test_admin_permissions(self):
        """测试管理员权限"""
        # 管理员应该有所有权限
        self.assertTrue(PermissionService.has_permission(self.admin, 'manage_users'))
        self.assertTrue(PermissionService.has_permission(self.admin, 'view_all_customers'))
        self.assertTrue(PermissionService.has_permission(self.admin, 'manage_system'))
        
        # 管理员可以访问所有客户
        self.assertTrue(PermissionService.can_access_customer(self.admin, self.customer1))
        self.assertTrue(PermissionService.can_access_customer(self.admin, self.customer2))
        
        # 管理员可以编辑所有客户
        self.assertTrue(PermissionService.can_edit_customer(self.admin, self.customer1))
        self.assertTrue(PermissionService.can_edit_customer(self.admin, self.customer2))
    
    def test_director_permissions(self):
        """测试经营院长权限"""
        # 经营院长可以查看所有客户和报表
        self.assertTrue(PermissionService.has_permission(self.director, 'view_all_customers'))
        self.assertTrue(PermissionService.has_permission(self.director, 'view_all_reports'))
        
        # 经营院长不能管理用户
        self.assertFalse(PermissionService.has_permission(self.director, 'manage_users'))
        
        # 经营院长可以访问所有客户
        self.assertTrue(PermissionService.can_access_customer(self.director, self.customer1))
        self.assertTrue(PermissionService.can_access_customer(self.director, self.customer2))
        
        # 经营院长不能编辑客户
        self.assertFalse(PermissionService.can_edit_customer(self.director, self.customer1))
    
    def test_manager_permissions(self):
        """测试部门主管权限"""
        # 部门主管可以查看部门客户和报表
        self.assertTrue(PermissionService.has_permission(self.manager1, 'view_department_customers'))
        self.assertTrue(PermissionService.has_permission(self.manager1, 'view_department_reports'))
        
        # 部门主管不能管理用户
        self.assertFalse(PermissionService.has_permission(self.manager1, 'manage_users'))
        
        # 部门主管只能访问本部门的客户
        self.assertTrue(PermissionService.can_access_customer(self.manager1, self.customer1))
        self.assertFalse(PermissionService.can_access_customer(self.manager1, self.customer2))
    
    def test_online_consultant_permissions(self):
        """测试网络咨询权限"""
        # 网络咨询可以创建和查看自己的客户
        self.assertTrue(PermissionService.has_permission(self.online1, 'create_customer'))
        self.assertTrue(PermissionService.has_permission(self.online1, 'view_own_customers'))
        
        # 网络咨询不能查看所有客户
        self.assertFalse(PermissionService.has_permission(self.online1, 'view_all_customers'))
        
        # 网络咨询只能访问自己创建的客户
        self.assertTrue(PermissionService.can_access_customer(self.online1, self.customer1))
        self.assertFalse(PermissionService.can_access_customer(self.online1, self.customer2))
        
        # 网络咨询可以编辑自己创建的客户
        self.assertTrue(PermissionService.can_edit_customer(self.online1, self.customer1))
        self.assertFalse(PermissionService.can_edit_customer(self.online1, self.customer2))
    
    def test_onsite_consultant_permissions(self):
        """测试现场咨询权限"""
        # 现场咨询可以查看分配的客户和更新跟进
        self.assertTrue(PermissionService.has_permission(self.onsite1, 'view_assigned_customers'))
        self.assertTrue(PermissionService.has_permission(self.onsite1, 'update_follow_up'))
        
        # 现场咨询不能创建客户
        self.assertFalse(PermissionService.has_permission(self.onsite1, 'create_customer'))
        
        # 现场咨询只能访问分配给自己的客户
        self.assertTrue(PermissionService.can_access_customer(self.onsite1, self.customer1))
        self.assertTrue(PermissionService.can_access_customer(self.onsite1, self.customer2))
        
        # 现场咨询不能编辑客户基本信息
        self.assertFalse(PermissionService.can_edit_customer(self.onsite1, self.customer1))
        
        # 现场咨询可以更新跟进信息
        self.assertTrue(PermissionService.can_update_follow_up(self.onsite1, self.customer1))
    
    def test_filter_customers_by_permission(self):
        """测试根据权限过滤客户"""
        customers = [self.customer1, self.customer2]
        
        # 管理员可以看到所有客户
        filtered = PermissionService.filter_customers_by_permission(self.admin, customers)
        self.assertEqual(len(filtered), 2)
        
        # 部门主管只能看到本部门的客户
        filtered = PermissionService.filter_customers_by_permission(self.manager1, customers)
        self.assertEqual(len(filtered), 1)
        self.assertEqual(filtered[0].id, self.customer1.id)
        
        # 网络咨询只能看到自己创建的客户
        filtered = PermissionService.filter_customers_by_permission(self.online1, customers)
        self.assertEqual(len(filtered), 1)
        self.assertEqual(filtered[0].id, self.customer1.id)
        
        filtered = PermissionService.filter_customers_by_permission(self.online2, customers)
        self.assertEqual(len(filtered), 1)
        self.assertEqual(filtered[0].id, self.customer2.id)
    
    def test_inactive_user_permissions(self):
        """测试非激活用户权限"""
        # 停用用户
        self.online1.is_active = False
        db.session.commit()
        
        # 非激活用户没有任何权限
        self.assertFalse(PermissionService.has_permission(self.online1, 'create_customer'))
        self.assertFalse(PermissionService.can_access_customer(self.online1, self.customer1))

if __name__ == '__main__':
    unittest.main()