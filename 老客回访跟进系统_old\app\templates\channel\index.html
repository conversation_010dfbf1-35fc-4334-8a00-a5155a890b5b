{% extends "base.html" %}

{% block title %}渠道管理 - 老客回访与跟进系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h2><i class="fas fa-tags"></i> 渠道管理</h2>
            <div class="btn-group">
                {% if current_user.has_permission('CHANNEL_MANAGE') %}
                <a href="{{ url_for('channel.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> 新增渠道
                </a>
                <a href="{{ url_for('channel.import_channels') }}" class="btn btn-success">
                    <i class="fas fa-upload"></i> 批量导入
                </a>
                {% endif %}
                <a href="{{ url_for('channel.export_channels') }}" class="btn btn-info">
                    <i class="fas fa-download"></i> 导出列表
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 搜索筛选 -->
<div class="row mb-3">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="category" class="form-label">渠道分类</label>
                        <select class="form-select" id="category" name="category">
                            <option value="">全部分类</option>
                            {% for cat in categories %}
                            <option value="{{ cat }}" {% if request.args.get('category') == cat %}selected{% endif %}>
                                {{ cat }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="name" class="form-label">渠道名称</label>
                        <input type="text" class="form-control" id="name" name="name" 
                               placeholder="请输入渠道名称" value="{{ request.args.get('name', '') }}">
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">状态</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">全部状态</option>
                            <option value="active" {% if request.args.get('status') == 'active' %}selected{% endif %}>启用</option>
                            <option value="inactive" {% if request.args.get('status') == 'inactive' %}selected{% endif %}>停用</option>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                        <a href="{{ url_for('channel.index') }}" class="btn btn-secondary">
                            <i class="fas fa-redo"></i> 重置
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 渠道列表 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-list"></i> 渠道列表</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>渠道分类</th>
                                <th>渠道名称</th>
                                <th>状态</th>
                                <th>创建人</th>
                                <th>创建时间</th>
                                <th>更新时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for channel in channels.items %}
                            <tr>
                                <td>{{ channel.id }}</td>
                                <td>
                                    <span class="badge bg-info">{{ channel.channel_category }}</span>
                                </td>
                                <td><strong>{{ channel.channel_name }}</strong></td>
                                <td>
                                    {% if channel.is_active %}
                                        <span class="badge bg-success">启用</span>
                                    {% else %}
                                        <span class="badge bg-secondary">停用</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if channel.creator %}
                                        {{ channel.creator.real_name }}
                                    {% else %}
                                        <span class="text-muted">系统</span>
                                    {% endif %}
                                </td>
                                <td>{{ channel.created_time.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>
                                    {% if channel.updated_time != channel.created_time %}
                                        {{ channel.updated_time.strftime('%Y-%m-%d %H:%M') }}
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if current_user.has_permission('CHANNEL_MANAGE') %}
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('channel.edit', id=channel.id) }}" 
                                           class="btn btn-outline-primary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="btn btn-outline-danger" title="删除"
                                                onclick="confirmDelete({{ channel.id }}, '{{ channel.channel_name }}')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                    {% else %}
                                    <span class="text-muted">无权限</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="8" class="text-center text-muted">
                                    <i class="fas fa-inbox fa-2x mb-2"></i><br>
                                    暂无渠道数据
                                    {% if current_user.has_permission('CHANNEL_MANAGE') %}
                                    <br><a href="{{ url_for('channel.create') }}" class="btn btn-primary btn-sm mt-2">
                                        <i class="fas fa-plus"></i> 立即创建
                                    </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                {% if channels.pages > 1 %}
                <nav aria-label="渠道列表分页">
                    <ul class="pagination justify-content-center">
                        {% if channels.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('channel.index', page=channels.prev_num, category=request.args.get('category', ''), name=request.args.get('name', ''), status=request.args.get('status', '')) }}">上一页</a>
                        </li>
                        {% endif %}

                        {% for page_num in channels.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != channels.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('channel.index', page=page_num, category=request.args.get('category', ''), name=request.args.get('name', ''), status=request.args.get('status', '')) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">…</span>
                            </li>
                            {% endif %}
                        {% endfor %}

                        {% if channels.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('channel.index', page=channels.next_num, category=request.args.get('category', ''), name=request.args.get('name', ''), status=request.args.get('status', '')) }}">下一页</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-exclamation-triangle text-warning"></i> 确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>您确定要删除渠道 "<span id="channelName"></span>" 吗？</p>
                <p class="text-danger small">
                    <i class="fas fa-warning"></i> 
                    注意：如果有客户正在使用此渠道，将无法删除。
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(channelId, channelName) {
    document.getElementById('channelName').textContent = channelName;
    document.getElementById('deleteForm').action = '/channel/' + channelId + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
