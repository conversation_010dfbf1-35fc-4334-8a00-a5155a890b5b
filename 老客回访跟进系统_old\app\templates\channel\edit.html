{% extends "base.html" %}

{% block title %}编辑渠道 - 老客回访与跟进系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-edit"></i> 编辑渠道</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('channel.index') }}">渠道管理</a></li>
                <li class="breadcrumb-item active">编辑渠道</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-tag"></i> 渠道信息</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="channel_category" class="form-label">渠道分类 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="channel_category" name="channel_category" 
                                       value="{{ channel.channel_category }}" placeholder="请输入渠道分类" required maxlength="100">
                                <div class="form-text">如：线上推广、线下活动、老客推荐等</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="channel_name" class="form-label">渠道名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="channel_name" name="channel_name" 
                                       value="{{ channel.channel_name }}" placeholder="请输入渠道名称" required maxlength="200">
                                <div class="form-text">如：百度推广、地推活动、老客户介绍等</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                   {% if channel.is_active %}checked{% endif %}>
                            <label class="form-check-label" for="is_active">
                                启用此渠道
                            </label>
                            <div class="form-text">停用后，此渠道将不会在客户登记时显示</div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('channel.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 保存修改
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-info-circle"></i> 渠道信息</h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>ID</strong></td>
                        <td>{{ channel.id }}</td>
                    </tr>
                    <tr>
                        <td><strong>创建人</strong></td>
                        <td>
                            {% if channel.creator %}
                                {{ channel.creator.real_name }}
                            {% else %}
                                <span class="text-muted">系统</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>创建时间</strong></td>
                        <td>{{ channel.created_time.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                    </tr>
                    <tr>
                        <td><strong>更新时间</strong></td>
                        <td>
                            {% if channel.updated_time != channel.created_time %}
                                {{ channel.updated_time.strftime('%Y-%m-%d %H:%M:%S') }}
                            {% else %}
                                <span class="text-muted">未更新</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>当前状态</strong></td>
                        <td>
                            {% if channel.is_active %}
                                <span class="badge bg-success">启用</span>
                            {% else %}
                                <span class="badge bg-secondary">停用</span>
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-users"></i> 使用情况</h6>
            </div>
            <div class="card-body">
                {% set customer_count = channel.customer_registrations|length %}
                <div class="text-center">
                    <h3 class="text-primary">{{ customer_count }}</h3>
                    <p class="text-muted">个客户使用此渠道</p>
                </div>
                
                {% if customer_count > 0 %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <small>此渠道正在被客户使用，请谨慎修改或停用</small>
                </div>
                {% endif %}
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-exclamation-triangle"></i> 注意事项</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li><i class="fas fa-check text-success"></i> 修改渠道信息会影响统计报表</li>
                    <li><i class="fas fa-check text-success"></i> 停用渠道不会影响已有客户数据</li>
                    <li><i class="fas fa-check text-success"></i> 相同分类和名称的渠道不能重复</li>
                    <li><i class="fas fa-check text-success"></i> 有客户使用的渠道无法删除</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 字符计数和验证
document.getElementById('channel_category').addEventListener('input', function() {
    const maxLength = 100;
    const currentLength = this.value.length;
    
    if (currentLength > maxLength) {
        this.value = this.value.substring(0, maxLength);
    }
});

document.getElementById('channel_name').addEventListener('input', function() {
    const maxLength = 200;
    const currentLength = this.value.length;
    
    if (currentLength > maxLength) {
        this.value = this.value.substring(0, maxLength);
    }
});
</script>
{% endblock %}
