{% extends "base.html" %}

{% block title %}编辑部门 - 老客回访与跟进系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-edit"></i> 编辑部门</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('admin.index') }}">系统管理</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('admin.departments') }}">部门管理</a></li>
                <li class="breadcrumb-item active">编辑部门</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-building"></i> 部门信息</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="department_name" class="form-label">部门名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="department_name" name="department_name" 
                                       value="{{ department.department_name }}" placeholder="请输入部门名称" required>
                                <div class="form-text">部门的显示名称，如"技术部"、"销售部"</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="department_code" class="form-label">部门代码 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="department_code" name="department_code" 
                                       value="{{ department.department_code }}" placeholder="请输入部门代码" required>
                                <div class="form-text">部门的唯一标识，如"TECH"、"SALES"</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="parent_id" class="form-label">上级部门</label>
                                <select class="form-select" id="parent_id" name="parent_id">
                                    <option value="">无上级部门（根部门）</option>
                                    {% for parent_dept in parent_departments %}
                                    <option value="{{ parent_dept.id }}" 
                                            {% if department.parent_id == parent_dept.id %}selected{% endif %}>
                                        {{ parent_dept.full_name }}
                                    </option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">选择该部门的上级部门</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sort_order" class="form-label">排序顺序</label>
                                <input type="number" class="form-control" id="sort_order" name="sort_order" 
                                       value="{{ department.sort_order }}" min="0" max="999">
                                <div class="form-text">数字越小排序越靠前</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">部门描述</label>
                        <textarea class="form-control" id="description" name="description" rows="3" 
                                  placeholder="请输入部门描述（可选）">{{ department.description or '' }}</textarea>
                        <div class="form-text">描述该部门的职能和作用</div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                   {% if department.is_active %}checked{% endif %}>
                            <label class="form-check-label" for="is_active">
                                启用部门
                            </label>
                            <div class="form-text">停用后该部门将不可选择，但不影响已有用户</div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('admin.departments') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 保存修改
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-info-circle"></i> 部门信息</h6>
            </div>
            <div class="card-body">
                <dl class="row small">
                    <dt class="col-sm-4">创建时间：</dt>
                    <dd class="col-sm-8">{{ department.created_time.strftime('%Y-%m-%d %H:%M') }}</dd>
                    
                    <dt class="col-sm-4">更新时间：</dt>
                    <dd class="col-sm-8">{{ department.updated_time.strftime('%Y-%m-%d %H:%M') }}</dd>
                    
                    <dt class="col-sm-4">用户数量：</dt>
                    <dd class="col-sm-8">{{ department.users|length }} 个用户</dd>
                    
                    <dt class="col-sm-4">子部门：</dt>
                    <dd class="col-sm-8">{{ department.children|length }} 个子部门</dd>
                </dl>
                
                {% if department.children %}
                <div class="mt-3">
                    <strong class="small">子部门列表：</strong>
                    <ul class="list-unstyled mt-2 small">
                        {% for child in department.children %}
                        <li class="mb-1">
                            <i class="fas fa-building text-muted"></i> {{ child.department_name }}
                            {% if not child.is_active %}
                            <span class="badge bg-secondary">停用</span>
                            {% endif %}
                        </li>
                        {% endfor %}
                    </ul>
                </div>
                {% endif %}
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-exclamation-triangle"></i> 注意事项</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li><i class="fas fa-check text-success"></i> 部门名称和代码必须唯一</li>
                    <li><i class="fas fa-check text-success"></i> 不能将自己设为上级部门</li>
                    <li><i class="fas fa-check text-success"></i> 不能将子部门设为上级部门</li>
                    <li><i class="fas fa-warning text-warning"></i> 停用部门不影响已有用户</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}