"""
自定义报表管理器
支持用户自定义数据源、计算列和Excel公式
"""

import pandas as pd
import logging
import re
from typing import Dict, List, Any, Union
from datetime import datetime, timedelta
import json


class CustomReportManager:
    """自定义报表管理器"""
    
    def __init__(self, db_engine, logger=None):
        self.db_engine = db_engine
        self.logger = logger or logging.getLogger(__name__)
        
    def validate_sql_query(self, query: str) -> tuple:
        """验证SQL查询的安全性"""
        try:
            # 基本的SQL注入防护
            dangerous_keywords = [
                'DROP', 'DELETE', 'UPDATE', 'INSERT', 'ALTER', 'CREATE',
                'TRUNCATE', 'EXEC', 'EXECUTE', 'UNION', '--', '/*', '*/',
                'xp_', 'sp_', 'INFORMATION_SCHEMA'
            ]
            
            query_upper = query.upper()
            for keyword in dangerous_keywords:
                if keyword in query_upper:
                    return False, f"查询包含危险关键词: {keyword}"
            
            # 检查是否只包含SELECT语句
            if not query_upper.strip().startswith('SELECT'):
                return False, "只允许SELECT查询语句"
            
            # 尝试执行查询（限制1行）
            test_query = f"SELECT * FROM ({query}) AS test_query LIMIT 1"
            pd.read_sql(test_query, self.db_engine)
            
            return True, "查询验证通过"
            
        except Exception as e:
            return False, f"查询验证失败: {str(e)}"
    
    def execute_data_source(self, data_source_config: Dict) -> pd.DataFrame:
        """执行数据源查询"""
        try:
            query = data_source_config.get('query', '')
            name = data_source_config.get('name', 'unnamed')
            
            # 验证查询
            is_valid, message = self.validate_sql_query(query)
            if not is_valid:
                raise ValueError(f"数据源 {name} 查询验证失败: {message}")
            
            # 执行查询
            df = pd.read_sql(query, self.db_engine)
            self.logger.info(f"数据源 {name} 查询成功，返回 {len(df)} 行数据")
            
            return df
            
        except Exception as e:
            self.logger.error(f"执行数据源查询失败: {str(e)}")
            raise
    
    def apply_simple_formula(self, df: pd.DataFrame, formula: str, row_index: int = None) -> Any:
        """应用简化的Excel公式"""
        try:
            # 如果指定了行索引，获取该行数据
            if row_index is not None and row_index < len(df):
                row_data = df.iloc[row_index].to_dict()
            else:
                row_data = {}
            
            # 替换列引用 [列名] 为实际值
            processed_formula = formula
            for col_name in df.columns:
                if f'[{col_name}]' in processed_formula:
                    if row_index is not None:
                        value = row_data.get(col_name, 0)
                        # 如果是数值，直接替换；如果是字符串，加引号
                        if isinstance(value, (int, float)):
                            processed_formula = processed_formula.replace(f'[{col_name}]', str(value))
                        else:
                            processed_formula = processed_formula.replace(f'[{col_name}]', f'"{value}"')
            
            # 移除等号
            if processed_formula.startswith('='):
                processed_formula = processed_formula[1:]
            
            # 处理简单的Excel函数
            processed_formula = self._convert_excel_functions(processed_formula, df, row_data)
            
            # 安全执行
            result = self._safe_eval(processed_formula)
            return result
            
        except Exception as e:
            self.logger.error(f"公式执行失败: {formula}, 错误: {str(e)}")
            return None
    
    def _convert_excel_functions(self, formula: str, df: pd.DataFrame, row_data: Dict) -> str:
        """转换Excel函数为Python代码"""
        # IF函数
        if_pattern = r'IF\s*\(\s*([^,]+)\s*,\s*([^,]+)\s*,\s*([^)]+)\s*\)'
        def replace_if(match):
            condition, true_val, false_val = match.groups()
            return f"({true_val} if ({condition}) else {false_val})"
        formula = re.sub(if_pattern, replace_if, formula, flags=re.IGNORECASE)
        
        # CONCATENATE函数
        concat_pattern = r'CONCATENATE\s*\(\s*([^)]+)\s*\)'
        def replace_concat(match):
            args = match.group(1).split(',')
            args = [arg.strip() for arg in args]
            return f"str({args[0]}) + " + " + ".join([f"str({arg})" for arg in args[1:]])
        formula = re.sub(concat_pattern, replace_concat, formula, flags=re.IGNORECASE)
        
        # SUM函数（简化版，只支持单列）
        sum_pattern = r'SUM\s*\(\s*([^)]+)\s*\)'
        def replace_sum(match):
            arg = match.group(1).strip()
            if arg.startswith('[') and arg.endswith(']'):
                col_name = arg[1:-1]
                if col_name in df.columns:
                    return str(df[col_name].sum())
            return "0"
        formula = re.sub(sum_pattern, replace_sum, formula, flags=re.IGNORECASE)
        
        # COUNT函数
        count_pattern = r'COUNT\s*\(\s*([^)]+)\s*\)'
        def replace_count(match):
            arg = match.group(1).strip()
            if arg.startswith('[') and arg.endswith(']'):
                col_name = arg[1:-1]
                if col_name in df.columns:
                    return str(df[col_name].count())
            return "0"
        formula = re.sub(count_pattern, replace_count, formula, flags=re.IGNORECASE)
        
        # AVERAGE函数
        avg_pattern = r'AVERAGE\s*\(\s*([^)]+)\s*\)'
        def replace_avg(match):
            arg = match.group(1).strip()
            if arg.startswith('[') and arg.endswith(']'):
                col_name = arg[1:-1]
                if col_name in df.columns:
                    return str(df[col_name].mean())
            return "0"
        formula = re.sub(avg_pattern, replace_avg, formula, flags=re.IGNORECASE)
        
        return formula
    
    def _safe_eval(self, expression: str) -> Any:
        """安全地执行表达式"""
        try:
            # 只允许安全的内置函数
            safe_dict = {
                '__builtins__': {},
                'abs': abs,
                'round': round,
                'max': max,
                'min': min,
                'len': len,
                'str': str,
                'int': int,
                'float': float,
                'bool': bool,
            }
            
            result = eval(expression, safe_dict)
            return result
            
        except Exception as e:
            self.logger.error(f"表达式执行失败: {expression}, 错误: {str(e)}")
            return None
    
    def generate_custom_report(self, report_config: Dict) -> pd.DataFrame:
        """生成自定义报表"""
        try:
            self.logger.info("开始生成自定义报表")
            
            # 执行数据源查询
            data_sources = {}
            for ds_config in report_config.get('data_sources', []):
                ds_name = ds_config['name']
                df = self.execute_data_source(ds_config)
                data_sources[ds_name] = df
            
            if not data_sources:
                raise ValueError("没有配置有效的数据源")
            
            # 获取主数据源
            main_source_name = report_config.get('main_source')
            if main_source_name and main_source_name in data_sources:
                main_df = data_sources[main_source_name].copy()
            else:
                # 使用第一个数据源作为主数据源
                main_df = list(data_sources.values())[0].copy()
            
            # 处理计算列
            for col_config in report_config.get('columns', []):
                col_name = col_config['name']
                formula = col_config.get('formula', '')
                
                if formula:
                    # 为每一行计算公式值
                    values = []
                    for idx in range(len(main_df)):
                        value = self.apply_simple_formula(main_df, formula, idx)
                        values.append(value)
                    main_df[col_name] = values
                else:
                    # 如果没有公式，检查是否是从其他列复制
                    source_col = col_config.get('source_column')
                    if source_col and source_col in main_df.columns:
                        main_df[col_name] = main_df[source_col]
            
            # 选择输出列
            output_columns = [col['name'] for col in report_config.get('columns', [])]
            if output_columns:
                # 确保所有输出列都存在
                existing_columns = [col for col in output_columns if col in main_df.columns]
                if existing_columns:
                    main_df = main_df[existing_columns]
            
            self.logger.info(f"自定义报表生成完成，共 {len(main_df)} 行数据")
            return main_df
            
        except Exception as e:
            self.logger.error(f"生成自定义报表失败: {str(e)}")
            raise
    
    def get_available_tables(self) -> List[str]:
        """获取可用的数据表列表"""
        try:
            query = "SHOW TABLES"
            df = pd.read_sql(query, self.db_engine)
            tables = df.iloc[:, 0].tolist()
            return tables
        except Exception as e:
            self.logger.error(f"获取数据表列表失败: {str(e)}")
            return []
    
    def get_table_columns(self, table_name: str) -> List[Dict]:
        """获取指定表的列信息"""
        try:
            query = f"DESCRIBE `{table_name}`"
            df = pd.read_sql(query, self.db_engine)
            
            columns = []
            for _, row in df.iterrows():
                columns.append({
                    'name': row['Field'],
                    'type': row['Type'],
                    'null': row['Null'],
                    'key': row['Key'],
                    'default': row['Default']
                })
            
            return columns
            
        except Exception as e:
            self.logger.error(f"获取表 {table_name} 列信息失败: {str(e)}")
            return []
    
    def preview_query_result(self, query: str, limit: int = 10) -> Dict:
        """预览查询结果"""
        try:
            # 验证查询
            is_valid, message = self.validate_sql_query(query)
            if not is_valid:
                return {'success': False, 'message': message}
            
            # 添加LIMIT限制
            limited_query = f"SELECT * FROM ({query}) AS preview_query LIMIT {limit}"
            df = pd.read_sql(limited_query, self.db_engine)
            
            return {
                'success': True,
                'columns': list(df.columns),
                'data': df.values.tolist(),
                'row_count': len(df)
            }
            
        except Exception as e:
            return {'success': False, 'message': f"查询预览失败: {str(e)}"}


def create_sample_custom_config():
    """创建示例自定义报表配置"""
    return {
        "name": "会员消费等级分析",
        "description": "基于消费金额和频次的会员等级分析",
        "main_source": "会员统计",
        "data_sources": [
            {
                "name": "会员统计",
                "query": """
                SELECT 
                    会员卡号,
                    COUNT(*) as 消费次数,
                    SUM(执行业绩（真实金额）) as 总消费金额,
                    AVG(执行业绩（真实金额）) as 平均消费,
                    MAX(执行日期) as 最近消费日期
                FROM 客户执行明细表 
                WHERE 会员卡号 IS NOT NULL 
                  AND 执行业绩（真实金额） > 0
                  AND 执行日期 >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR)
                GROUP BY 会员卡号
                ORDER BY 总消费金额 DESC
                LIMIT 1000
                """
            }
        ],
        "columns": [
            {
                "name": "会员卡号",
                "source_column": "会员卡号",
                "description": "会员卡号"
            },
            {
                "name": "消费次数",
                "source_column": "消费次数", 
                "description": "年度消费次数"
            },
            {
                "name": "总消费金额",
                "source_column": "总消费金额",
                "description": "年度总消费金额"
            },
            {
                "name": "消费等级",
                "formula": "=IF([总消费金额]>=100000, \"钻石会员\", IF([总消费金额]>=50000, \"金牌会员\", IF([总消费金额]>=20000, \"银牌会员\", \"普通会员\")))",
                "description": "基于消费金额的等级分类"
            },
            {
                "name": "活跃度",
                "formula": "=IF([消费次数]>=20, \"高活跃\", IF([消费次数]>=10, \"中活跃\", \"低活跃\"))",
                "description": "基于消费频次的活跃度"
            },
            {
                "name": "客户标签",
                "formula": "=CONCATENATE([消费等级], \"-\", [活跃度])",
                "description": "综合客户标签"
            }
        ]
    }
