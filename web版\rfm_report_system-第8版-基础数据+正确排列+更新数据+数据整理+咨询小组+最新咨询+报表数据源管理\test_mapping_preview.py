#!/usr/bin/env python3
"""
测试映射预览功能
"""

import requests
import json

def test_mapping_preview_api():
    """测试映射预览API"""
    
    print("=== 测试映射预览API ===")
    
    # 测试API端点
    url = "http://localhost:5000/data/api/mapping_preview"
    
    try:
        response = requests.get(url)
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success'):
                print("✅ API调用成功")
                print(f"参照表名称: {data.get('template_name')}")
                
                mappings = data.get('mappings', {})
                
                # 检查各种映射类型
                mapping_types = [
                    ('level1', '一级分类映射'),
                    ('level2', '二级分类映射'),
                    ('level3', '三级分类映射'),
                    ('field_mapping', '现场映射'),
                    ('field_group', '现场小组映射')
                ]
                
                print("\n映射数据:")
                for mapping_type, display_name in mapping_types:
                    mapping_data = mappings.get(mapping_type, {})
                    count = len(mapping_data)
                    
                    if count > 0:
                        print(f"✅ {display_name}: {count} 个映射")
                        # 显示前3个映射示例
                        for i, (key, value) in enumerate(mapping_data.items()):
                            if i >= 3:
                                print(f"   ...")
                                break
                            print(f"   {key} → {value}")
                    else:
                        print(f"⚪ {display_name}: 无映射数据")
                
                return True
                
            else:
                print(f"❌ API返回错误: {data.get('message')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保系统已启动")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return False

def test_frontend_display():
    """测试前端显示逻辑"""
    
    print("\n=== 测试前端显示逻辑 ===")
    
    # 模拟映射数据
    mock_mappings = {
        'level1': {
            '美容项目': '美容',
            '医疗项目': '医疗'
        },
        'level2': {
            '皮肤美容项目': '皮肤美容',
            '注射美容项目': '注射美容',
            '口腔美容项目': '口腔美容'
        },
        'level3': {
            '五代热玛吉': '热玛吉',
            '超皮秒': '皮秒激光'
        },
        'field_mapping': {
            '张三': '李四',
            '王五': '赵六'
        },
        'field_group': {
            '李四': '王婆小组',
            '赵六': '李婆小组'
        }
    }
    
    # 模拟用户选择
    user_selections = {
        'use_level1_mapping': True,
        'use_level2_mapping': True,
        'use_level3_mapping': False,
        'use_field_mapping': True,
        'use_field_group': True
    }
    
    print("用户选择的映射类型:")
    for key, value in user_selections.items():
        status = "✅ 已启用" if value else "⚪ 未启用"
        print(f"  {key}: {status}")
    
    print("\n前端预览效果:")
    
    # 模拟前端HTML生成
    html_parts = []
    
    if user_selections['use_level1_mapping'] and mock_mappings['level1']:
        html_parts.append("📊 一级分类映射")
        for key, value in mock_mappings['level1'].items():
            html_parts.append(f"   {key} → {value}")
    
    if user_selections['use_level2_mapping'] and mock_mappings['level2']:
        html_parts.append("📊 二级分类映射")
        for key, value in mock_mappings['level2'].items():
            html_parts.append(f"   {key} → {value}")
    
    if user_selections['use_level3_mapping'] and mock_mappings['level3']:
        html_parts.append("📊 三级分类映射")
        for key, value in mock_mappings['level3'].items():
            html_parts.append(f"   {key} → {value}")
    
    if user_selections['use_field_mapping'] and mock_mappings['field_mapping']:
        html_parts.append("👤 现场映射")
        for key, value in mock_mappings['field_mapping'].items():
            html_parts.append(f"   {key} → {value}")
    
    if user_selections['use_field_group'] and mock_mappings['field_group']:
        html_parts.append("👥 现场小组映射")
        for key, value in mock_mappings['field_group'].items():
            html_parts.append(f"   {key} → {value}")
    
    for part in html_parts:
        print(part)
    
    print("\n✅ 前端显示逻辑测试完成")

def test_mapping_description():
    """测试映射说明"""
    
    print("\n=== 测试映射说明 ===")
    
    descriptions = [
        "一级分类映射：科室分类合并（如：皮肤美容项目 → 皮肤）",
        "二级分类映射：品类合并（如：衡力+Botox → 肉毒素）",
        "三级分类映射：品项合并（如：四代+五代微针 → 黄金微针）",
        "现场映射：现场人员名称统一（如：张三 → 李四）",
        "         添加"最新现场"列，跳过"不在盘内"",
        "现场小组映射：现场人员归组（如：李四 → 王婆小组）",
        "             添加"现场小组"列，基于最新现场"
    ]
    
    print("映射说明内容:")
    for desc in descriptions:
        print(f"  • {desc}")
    
    print("\n✅ 映射说明测试完成")

if __name__ == "__main__":
    # 测试API
    api_success = test_mapping_preview_api()
    
    # 测试前端逻辑
    test_frontend_display()
    
    # 测试映射说明
    test_mapping_description()
    
    print("\n🎉 所有测试完成！")
    
    if api_success:
        print("现在新建数据处理任务页面应该显示:")
        print("1. ✅ 完整的映射说明（包含现场映射和现场小组映射）")
        print("2. ✅ 映射预览（包含现场映射和现场小组映射数据）")
        print("3. ✅ 实时预览更新（勾选现场映射选项时显示预览）")
    else:
        print("⚠️  请确保:")
        print("1. 系统已启动 (python start_server.py)")
        print("2. 已上传并激活参照表")
        print("3. 参照表包含现场映射和现场小组映射工作表")
