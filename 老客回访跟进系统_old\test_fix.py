#!/usr/bin/env python3
"""
测试修复后的分页功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.customer import Channel
from app.models.user import User
from config import get_config

def test_pagination_fix():
    """测试分页修复"""
    print("测试分页修复...")
    
    config = get_config()
    app = create_app(config)
    
    with app.app_context():
        # 模拟第2页请求
        with app.test_request_context('/?page=2'):
            try:
                from flask_login import login_user
                from app.views.channel import index
                
                # 模拟登录
                admin_user = User.query.filter_by(username='admin').first()
                if admin_user:
                    login_user(admin_user)
                
                # 调用视图函数
                result = index()
                print(f"视图函数返回类型: {type(result)}")
                
                if isinstance(result, str):
                    print("✅ 模板渲染成功")
                    # 检查是否包含渠道数据
                    if "推广3000" in result:  # 第2页第一条记录的名称
                        print("✅ 第2页数据正确显示")
                    else:
                        print("❌ 第2页数据未显示")
                        # 检查是否显示了"暂无数据"
                        if "暂无渠道数据" in result:
                            print("❌ 显示了'暂无渠道数据'")
                        else:
                            print("? 未知状态")
                else:
                    print("❌ 视图函数返回异常")
                
            except Exception as e:
                print(f"❌ 测试失败: {e}")
                import traceback
                traceback.print_exc()

if __name__ == "__main__":
    test_pagination_fix()
