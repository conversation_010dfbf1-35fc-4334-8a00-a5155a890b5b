# 老客回访与跟进系统

## 项目简介

本系统是为高端服务机构（如医美诊所）设计的老客户激活、咨询和跟进管理系统。系统采用基于角色的权限控制（RBAC），确保数据安全、权限分明且流程高效。

## 技术栈

- **后端**: Python Flask
- **数据库**: MySQL 8.0+
- **ORM**: SQLAlchemy
- **前端**: HTML5 + CSS3 + JavaScript + Bootstrap
- **认证**: Flask-Login + Session管理

## 系统特性

- 🔐 基于RBAC的权限管理系统
- 👥 多角色支持（管理员、经营院长、部门主管、网络咨询、现场咨询）
- 📊 完整的客户登记与跟进流程
- 📈 统计报表与数据分析
- 📁 Excel批量导入/导出功能
- 🔒 数据安全与操作审计
- 🎯 灵活的渠道管理

## 快速开始

### 环境要求

- Python 3.8+
- MySQL 8.0+
- pip

### 安装步骤

#### 方法一：一键启动（推荐新手）
```bash
# 下载项目后，直接运行
python start.py
```
脚本会自动检查并安装依赖、初始化数据库、启动系统。

#### 方法二：自动安装
```bash
# 1. 进入项目目录
cd 老客回访跟进系统

# 2. 运行安装脚本
# Windows用户：
install.bat

# 或者：
python install.py

# 3. 配置数据库连接（编辑 config.py）

# 4. 初始化数据库
python init_db.py

# 5. 启动系统
python run.py
```

#### 方法三：手动安装
```bash
# 1. 创建虚拟环境（推荐）
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows

# 2. 安装依赖
pip install -r requirements.txt
# 如果失败，尝试使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 3. 配置数据库连接（编辑 config.py）

# 4. 初始化数据库
python init_db.py

# 5. 启动系统
python run.py
```

访问 http://localhost:5000 开始使用系统
默认管理员账号：admin / admin123

## 项目结构

```
老客回访跟进系统/
├── app/                    # 应用主目录
│   ├── __init__.py        # Flask应用初始化
│   ├── models/            # 数据模型
│   │   ├── __init__.py
│   │   ├── user.py        # 用户相关模型
│   │   ├── customer.py    # 客户相关模型
│   │   └── system.py      # 系统配置模型
│   ├── views/             # 视图控制器
│   │   ├── __init__.py
│   │   ├── auth.py        # 认证相关
│   │   ├── customer.py    # 客户管理
│   │   ├── admin.py       # 系统管理
│   │   └── report.py      # 报表统计
│   ├── templates/         # HTML模板
│   │   ├── base.html      # 基础模板
│   │   ├── auth/          # 认证页面
│   │   ├── customer/      # 客户管理页面
│   │   └── admin/         # 管理页面
│   ├── static/            # 静态资源
│   │   ├── css/
│   │   ├── js/
│   │   └── images/
│   └── utils/             # 工具函数
│       ├── __init__.py
│       ├── permissions.py # 权限验证
│       ├── validators.py  # 数据验证
│       └── helpers.py     # 辅助函数
├── config.py              # 配置文件
├── requirements.txt       # 依赖包列表
├── app.py                # 应用入口
├── init_db.py            # 数据库初始化
└── README.md             # 项目说明
```

## 功能模块

### 1. 用户认证与权限管理
- 用户登录/登出
- 密码修改
- 基于角色的权限控制
- 会话管理

### 2. 账号管理（管理员）
- 创建用户账号
- 分配角色权限
- 重置用户密码
- 启用/停用账号

### 3. 渠道管理
- 渠道分类管理
- 渠道信息维护
- Excel批量导入
- 渠道状态管理

### 4. 客户登记与跟进
- 网络咨询登记客户
- 分配现场顾问
- 跟进记录管理
- 客户信息查看

### 5. 数据管理
- 现场小组映射
- 客户消费数据导入
- 数据更新与维护

### 6. 统计报表
- 多维度数据统计
- 报表导出功能
- 数据可视化展示

## 角色权限说明

### 系统管理员
- 拥有系统所有功能的操作权限
- 用户账号管理
- 系统配置管理

### 经营院长
- 查看所有登记信息和统计报表
- 不能进行登记和修改操作

### 部门主管
- 查看本部门员工登记的信息
- 查看相关统计报表

### 网络咨询
- 登记客户信息
- 查看和修改自己名下的登记信息

### 现场咨询
- 查看分配给自己的客户信息
- 填写客户跟进情况

## 数据库设计

系统使用MySQL数据库，主要包含以下核心表：

- `user_accounts` - 用户账号表
- `user_roles` - 角色表
- `permissions` - 权限表
- `role_permissions` - 角色权限关联表
- `channels` - 渠道管理表
- `customer_registrations` - 客户登记表
- `follow_up_records` - 跟进记录表
- `consultant_group_mapping` - 现场小组映射表
- `customer_consumption` - 客户消费记录表
- `system_config` - 系统配置表

详细的数据库设计请参考 [系统设计方案.md](./系统设计方案.md)

## API接口

系统提供RESTful API接口，支持前后端分离开发：

- 认证接口：`/api/v1/auth/*`
- 用户管理：`/api/v1/users/*`
- 渠道管理：`/api/v1/channels/*`
- 客户登记：`/api/v1/registrations/*`
- 跟进管理：`/api/v1/follow-ups/*`
- 统计报表：`/api/v1/reports/*`

详细的API文档请参考 [系统设计方案.md](./系统设计方案.md)

## 安全特性

- 密码哈希加密存储
- 会话超时自动登出
- 操作审计日志
- 数据权限隔离
- SQL注入防护
- XSS攻击防护

## 开发指南

### 添加新功能

1. 在 `app/models/` 中定义数据模型
2. 在 `app/views/` 中实现业务逻辑
3. 在 `app/templates/` 中创建页面模板
4. 在 `app/static/` 中添加静态资源
5. 更新权限配置

### 数据库迁移

```bash
# 生成迁移文件
flask db migrate -m "描述信息"

# 执行迁移
flask db upgrade
```

### 运行测试

```bash
python -m pytest tests/
```

## 部署说明

### 生产环境部署

1. 使用 Gunicorn 作为 WSGI 服务器
2. 配置 Nginx 作为反向代理
3. 使用 MySQL 主从复制提高可用性
4. 配置 Redis 缓存提高性能

### Docker 部署

```bash
# 构建镜像
docker build -t old-customer-system .

# 运行容器
docker-compose up -d
```

## 维护与监控

- 定期备份数据库
- 监控系统性能指标
- 查看操作审计日志
- 更新安全补丁

## 技术支持

如有问题或建议，请联系开发团队。

## 许可证

本项目采用 MIT 许可证。
