from flask import render_template, redirect, url_for
from flask_login import login_required, current_user
from app.main import bp
from app.models import DatabaseConfig, DataProcessingTask

@bp.route('/')
@bp.route('/index')
@login_required
def index():
    """主页"""
    # 获取系统状态信息
    active_db_config = DatabaseConfig.get_active_config()
    recent_tasks = DataProcessingTask.query.filter_by(
        created_by=current_user.id
    ).order_by(DataProcessingTask.created_at.desc()).limit(5).all()
    
    # 统计信息
    stats = {
        'total_tasks': DataProcessingTask.query.filter_by(created_by=current_user.id).count(),
        'completed_tasks': DataProcessingTask.query.filter_by(
            created_by=current_user.id, status='completed'
        ).count(),
        'failed_tasks': DataProcessingTask.query.filter_by(
            created_by=current_user.id, status='failed'
        ).count(),
        'db_configs': DatabaseConfig.query.count()
    }
    
    return render_template('main/index.html', 
                         title='首页',
                         active_db_config=active_db_config,
                         recent_tasks=recent_tasks,
                         stats=stats)

@bp.route('/dashboard')
@login_required
def dashboard():
    """仪表板"""
    return render_template('main/dashboard.html', title='仪表板')
