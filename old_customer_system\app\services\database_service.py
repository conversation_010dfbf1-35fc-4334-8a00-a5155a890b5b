# -*- coding: utf-8 -*-
"""
数据库服务
"""

import pymysql
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
from typing import Dict, Tuple, Optional

class DatabaseService:
    """数据库连接和管理服务"""
    
    @staticmethod
    def test_connection(host: str, port: int, username: str, password: str, database: str = None) -> <PERSON><PERSON>[bool, str]:
        """测试数据库连接"""
        try:
            # 构建连接字符串
            if database:
                connection_string = f"mysql+pymysql://{username}:{password}@{host}:{port}/{database}?charset=utf8mb4"
            else:
                connection_string = f"mysql+pymysql://{username}:{password}@{host}:{port}/?charset=utf8mb4"
            
            # 创建引擎并测试连接
            engine = create_engine(connection_string, pool_pre_ping=True)
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            
            return True, "数据库连接成功"
            
        except Exception as e:
            return False, f"数据库连接失败: {str(e)}"
    
    @staticmethod
    def check_database_exists(host: str, port: int, username: str, password: str, database: str) -> Tuple[bool, str]:
        """检查数据库是否存在"""
        try:
            # 先连接到MySQL服务器（不指定数据库）
            success, message = DatabaseService.test_connection(host, port, username, password)
            if not success:
                return False, message
            
            # 检查数据库是否存在
            connection_string = f"mysql+pymysql://{username}:{password}@{host}:{port}/?charset=utf8mb4"
            engine = create_engine(connection_string)
            
            with engine.connect() as conn:
                result = conn.execute(text(f"SHOW DATABASES LIKE '{database}'"))
                exists = result.fetchone() is not None
            
            if exists:
                return True, f"数据库 '{database}' 已存在"
            else:
                return False, f"数据库 '{database}' 不存在"
                
        except Exception as e:
            return False, f"检查数据库失败: {str(e)}"
    
    @staticmethod
    def create_database(host: str, port: int, username: str, password: str, database: str) -> Tuple[bool, str]:
        """创建数据库"""
        try:
            # 连接到MySQL服务器
            connection_string = f"mysql+pymysql://{username}:{password}@{host}:{port}/?charset=utf8mb4"
            engine = create_engine(connection_string)
            
            with engine.connect() as conn:
                # 创建数据库
                conn.execute(text(f"CREATE DATABASE IF NOT EXISTS {database} DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"))
                conn.commit()
            
            return True, f"数据库 '{database}' 创建成功"
            
        except Exception as e:
            return False, f"创建数据库失败: {str(e)}"
    
    @staticmethod
    def drop_database(host: str, port: int, username: str, password: str, database: str) -> Tuple[bool, str]:
        """删除数据库"""
        try:
            # 连接到MySQL服务器
            connection_string = f"mysql+pymysql://{username}:{password}@{host}:{port}/?charset=utf8mb4"
            engine = create_engine(connection_string)
            
            with engine.connect() as conn:
                # 删除数据库
                conn.execute(text(f"DROP DATABASE IF EXISTS {database}"))
                conn.commit()
            
            return True, f"数据库 '{database}' 删除成功"
            
        except Exception as e:
            return False, f"删除数据库失败: {str(e)}"
    
    @staticmethod
    def get_database_info(host: str, port: int, username: str, password: str, database: str) -> Dict:
        """获取数据库信息"""
        try:
            connection_string = f"mysql+pymysql://{username}:{password}@{host}:{port}/{database}?charset=utf8mb4"
            engine = create_engine(connection_string)
            
            info = {
                'tables': [],
                'table_count': 0,
                'database_size': 0
            }
            
            with engine.connect() as conn:
                # 获取表列表
                result = conn.execute(text("SHOW TABLES"))
                tables = [row[0] for row in result.fetchall()]
                info['tables'] = tables
                info['table_count'] = len(tables)
                
                # 获取数据库大小
                result = conn.execute(text(f"""
                    SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'DB Size in MB'
                    FROM information_schema.tables 
                    WHERE table_schema='{database}'
                """))
                size_result = result.fetchone()
                if size_result and size_result[0]:
                    info['database_size'] = float(size_result[0])
            
            return info
            
        except Exception as e:
            return {'error': str(e)}
    
    @staticmethod
    def execute_sql_file(host: str, port: int, username: str, password: str, database: str, sql_file_path: str) -> Tuple[bool, str]:
        """执行SQL文件"""
        try:
            connection_string = f"mysql+pymysql://{username}:{password}@{host}:{port}/{database}?charset=utf8mb4"
            engine = create_engine(connection_string)
            
            # 读取SQL文件
            with open(sql_file_path, 'r', encoding='utf-8') as file:
                sql_content = file.read()
            
            # 分割SQL语句（简单的分割，按分号分割）
            sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
            
            with engine.connect() as conn:
                for statement in sql_statements:
                    if statement:
                        conn.execute(text(statement))
                conn.commit()
            
            return True, f"SQL文件 '{sql_file_path}' 执行成功"
            
        except Exception as e:
            return False, f"执行SQL文件失败: {str(e)}"