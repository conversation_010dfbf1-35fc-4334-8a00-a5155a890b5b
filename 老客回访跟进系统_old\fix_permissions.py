#!/usr/bin/env python3
"""
专门修复权限问题的脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.user import User, Role, Permission, RolePermission
from sqlalchemy import text

def fix_permissions():
    """修复权限问题"""
    print("=" * 50)
    print("修复权限问题")
    print("=" * 50)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 确保所有必需的权限存在
            print("1. 检查必需权限...")
            required_permissions = [
                ('用户管理', 'USER_MANAGE', 'SYSTEM'),
                ('角色管理', 'ROLE_MANAGE', 'SYSTEM'),
                ('系统配置', 'SYSTEM_CONFIG', 'SYSTEM'),
                ('渠道查看', 'CHANNEL_VIEW', 'CHANNEL'),
                ('渠道管理', 'CHANNEL_MANAGE', 'CHANNEL'),
                ('渠道导入', 'CHANNEL_IMPORT', 'CHANNEL'),
                ('客户登记', 'CUSTOMER_REGISTER', 'CUSTOMER'),
                ('客户查看', 'CUSTOMER_VIEW', 'CUSTOMER'),
                ('客户修改', 'CUSTOMER_EDIT', 'CUSTOMER'),
                ('跟进添加', 'FOLLOW_UP_ADD', 'FOLLOW_UP'),
                ('跟进查看', 'FOLLOW_UP_VIEW', 'FOLLOW_UP'),
                ('映射管理', 'MAPPING_MANAGE', 'DATA'),
                ('消费导入', 'CONSUMPTION_IMPORT', 'DATA'),
                ('报表查看', 'REPORT_VIEW', 'REPORT'),
                ('报表导出', 'REPORT_EXPORT', 'REPORT'),
                ('审计日志查看', 'AUDIT_LOG_VIEW', 'SYSTEM')
            ]
            
            added_permissions = 0
            for perm_name, perm_code, module_name in required_permissions:
                existing = Permission.query.filter_by(permission_code=perm_code).first()
                if not existing:
                    permission = Permission(
                        permission_name=perm_name,
                        permission_code=perm_code,
                        module_name=module_name,
                        description=f'{perm_name}权限'
                    )
                    db.session.add(permission)
                    added_permissions += 1
                    print(f"  添加权限: {perm_name}")
            
            if added_permissions > 0:
                db.session.commit()
                print(f"✓ 添加了 {added_permissions} 个权限")
            else:
                print("✓ 所有权限都已存在")
            
            # 2. 确保管理员角色存在并拥有所有权限
            print("\n2. 检查管理员权限...")
            admin_role = Role.query.filter_by(role_code='ADMIN').first()
            if not admin_role:
                print("❌ 管理员角色不存在")
                return
            
            all_permissions = Permission.query.all()
            added_role_perms = 0
            
            for permission in all_permissions:
                existing = RolePermission.query.filter_by(
                    role_id=admin_role.id,
                    permission_id=permission.id
                ).first()
                if not existing:
                    role_perm = RolePermission(
                        role_id=admin_role.id,
                        permission_id=permission.id
                    )
                    db.session.add(role_perm)
                    added_role_perms += 1
            
            if added_role_perms > 0:
                db.session.commit()
                print(f"✓ 为管理员添加了 {added_role_perms} 个权限")
            else:
                print("✓ 管理员已拥有所有权限")
            
            # 3. 检查其他角色的基本权限
            print("\n3. 检查其他角色权限...")
            
            # 经营院长权限
            general_manager = Role.query.filter_by(role_code='GENERAL_MANAGER').first()
            if general_manager:
                readonly_perms = ['CHANNEL_VIEW', 'CUSTOMER_VIEW', 'FOLLOW_UP_VIEW', 'REPORT_VIEW', 'REPORT_EXPORT']
                for perm_code in readonly_perms:
                    permission = Permission.query.filter_by(permission_code=perm_code).first()
                    if permission:
                        existing = RolePermission.query.filter_by(
                            role_id=general_manager.id,
                            permission_id=permission.id
                        ).first()
                        if not existing:
                            role_perm = RolePermission(
                                role_id=general_manager.id,
                                permission_id=permission.id
                            )
                            db.session.add(role_perm)
            
            # 部门主管权限
            dept_manager = Role.query.filter_by(role_code='DEPARTMENT_MANAGER').first()
            if dept_manager:
                dept_perms = ['CHANNEL_VIEW', 'CUSTOMER_VIEW', 'FOLLOW_UP_VIEW', 'REPORT_VIEW', 'REPORT_EXPORT']
                for perm_code in dept_perms:
                    permission = Permission.query.filter_by(permission_code=perm_code).first()
                    if permission:
                        existing = RolePermission.query.filter_by(
                            role_id=dept_manager.id,
                            permission_id=permission.id
                        ).first()
                        if not existing:
                            role_perm = RolePermission(
                                role_id=dept_manager.id,
                                permission_id=permission.id
                            )
                            db.session.add(role_perm)
            
            db.session.commit()
            print("✓ 其他角色权限检查完成")
            
            print("\n" + "=" * 50)
            print("权限修复完成！")
            print("=" * 50)
            print("现在可以重新启动系统了: python start.py")
            
        except Exception as e:
            print(f"❌ 修复过程中出现错误: {e}")
            db.session.rollback()
            raise

if __name__ == '__main__':
    fix_permissions()