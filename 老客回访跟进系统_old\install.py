"""
依赖包安装脚本
"""
import subprocess
import sys
import os

def run_command(command):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def install_requirements():
    """安装核心依赖包"""
    print("=== 安装核心依赖包 ===")
    
    # 升级pip
    print("升级pip...")
    success, stdout, stderr = run_command(f"{sys.executable} -m pip install --upgrade pip")
    if not success:
        print(f"升级pip失败: {stderr}")
    
    # 安装核心依赖
    print("安装核心依赖包...")
    success, stdout, stderr = run_command(f"{sys.executable} -m pip install -r requirements.txt")
    
    if success:
        print("✅ 核心依赖包安装成功")
        return True
    else:
        print(f"❌ 核心依赖包安装失败: {stderr}")
        
        # 尝试使用国内镜像
        print("尝试使用清华大学镜像...")
        success, stdout, stderr = run_command(
            f"{sys.executable} -m pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/"
        )
        
        if success:
            print("✅ 使用镜像安装成功")
            return True
        else:
            print(f"❌ 使用镜像安装也失败: {stderr}")
            return False

def install_optional_requirements():
    """安装可选依赖包"""
    print("\n=== 安装可选依赖包 ===")
    
    if not os.path.exists('requirements-optional.txt'):
        print("未找到可选依赖文件")
        return
    
    choice = input("是否安装可选依赖包？(y/n): ").lower().strip()
    if choice != 'y':
        print("跳过可选依赖包安装")
        return
    
    print("安装可选依赖包...")
    success, stdout, stderr = run_command(f"{sys.executable} -m pip install -r requirements-optional.txt")
    
    if success:
        print("✅ 可选依赖包安装成功")
    else:
        print(f"⚠️ 部分可选依赖包安装失败: {stderr}")
        print("这不会影响系统核心功能")

def check_python_version():
    """检查Python版本"""
    print("=== 检查Python环境 ===")
    
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python版本过低，需要Python 3.8或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True

def create_virtual_env():
    """创建虚拟环境"""
    print("\n=== 虚拟环境设置 ===")
    
    if os.path.exists('venv'):
        print("虚拟环境已存在")
        return True
    
    choice = input("是否创建虚拟环境？(推荐) (y/n): ").lower().strip()
    if choice != 'y':
        print("跳过虚拟环境创建")
        return True
    
    print("创建虚拟环境...")
    success, stdout, stderr = run_command(f"{sys.executable} -m venv venv")
    
    if success:
        print("✅ 虚拟环境创建成功")
        print("请运行以下命令激活虚拟环境:")
        if os.name == 'nt':  # Windows
            print("  venv\\Scripts\\activate")
        else:  # Linux/Mac
            print("  source venv/bin/activate")
        print("然后重新运行此安装脚本")
        return True
    else:
        print(f"❌ 虚拟环境创建失败: {stderr}")
        return False

def main():
    """主函数"""
    print("老客回访与跟进系统 - 依赖包安装脚本")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return
    
    # 检查是否在虚拟环境中
    in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    
    if not in_venv:
        print("⚠️ 建议在虚拟环境中安装依赖包")
        create_virtual_env()
        if os.path.exists('venv'):
            print("请先激活虚拟环境，然后重新运行此脚本")
            return
    else:
        print("✅ 已在虚拟环境中")
    
    # 安装核心依赖
    if not install_requirements():
        print("\n❌ 安装失败，请检查网络连接或Python环境")
        return
    
    # 安装可选依赖
    install_optional_requirements()
    
    print("\n" + "=" * 50)
    print("🎉 依赖包安装完成！")
    print("\n下一步:")
    print("1. 配置数据库连接信息 (编辑 config.py)")
    print("2. 初始化数据库: python init_db.py")
    print("3. 启动系统: python run.py")

if __name__ == '__main__':
    main()
