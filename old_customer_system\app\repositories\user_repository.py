# -*- coding: utf-8 -*-
"""
用户Repository
"""

from typing import List, Optional
from sqlalchemy import or_
from app.models import User
from .base_repository import BaseRepository

class UserRepository(BaseRepository):
    """用户数据访问类"""
    
    def __init__(self):
        super().__init__(User)
    
    def find_by_username(self, username: str) -> Optional[User]:
        """根据用户名查找用户"""
        return User.query.filter_by(username=username).first()
    
    def find_by_role(self, role: str) -> List[User]:
        """根据角色查找用户"""
        return User.query.filter_by(role=role, is_active=True).all()
    
    def find_by_department(self, department_id: int) -> List[User]:
        """根据部门查找用户"""
        return User.query.filter_by(department_id=department_id, is_active=True).all()
    
    def search_by_simple_code(self, query: str) -> List[User]:
        """根据简码搜索用户（支持中文、全拼、首字母）"""
        if not query:
            return []
        
        # 搜索用户名、真实姓名、简码
        search_filter = or_(
            User.username.like(f'%{query}%'),
            User.real_name.like(f'%{query}%'),
            User.simple_code.like(f'%{query}%')
        )
        
        return User.query.filter(search_filter, User.is_active == True).all()
    
    def get_online_consultants(self) -> List[User]:
        """获取所有网络咨询人员"""
        return self.find_by_role('online_consultant')
    
    def get_onsite_consultants(self) -> List[User]:
        """获取所有现场咨询人员"""
        return self.find_by_role('onsite_consultant')
    
    def get_managers(self) -> List[User]:
        """获取所有部门主管"""
        return self.find_by_role('manager')
    
    def get_directors(self) -> List[User]:
        """获取所有经营院长"""
        return self.find_by_role('director')
    
    def get_admins(self) -> List[User]:
        """获取所有管理员"""
        return self.find_by_role('admin')
    
    def update_last_login(self, user_id: int) -> bool:
        """更新最后登录时间"""
        from datetime import datetime
        return self.update(user_id, {'last_login': datetime.utcnow()})
    
    def change_password(self, user_id: int, new_password: str) -> bool:
        """修改用户密码"""
        try:
            user = self.get_by_id(user_id)
            if not user:
                return False
            
            user.set_password(new_password)
            from app import db
            db.session.commit()
            return True
        except Exception as e:
            from app import db
            db.session.rollback()
            raise e
    
    def activate_user(self, user_id: int) -> bool:
        """激活用户"""
        return self.update(user_id, {'is_active': True})
    
    def deactivate_user(self, user_id: int) -> bool:
        """停用用户"""
        return self.update(user_id, {'is_active': False})
    
    def get_user_statistics(self) -> dict:
        """获取用户统计信息"""
        total_users = User.query.count()
        active_users = User.query.filter_by(is_active=True).count()
        
        role_stats = {}
        roles = ['admin', 'director', 'manager', 'online_consultant', 'onsite_consultant']
        for role in roles:
            role_stats[role] = User.query.filter_by(role=role, is_active=True).count()
        
        return {
            'total_users': total_users,
            'active_users': active_users,
            'inactive_users': total_users - active_users,
            'role_statistics': role_stats
        }