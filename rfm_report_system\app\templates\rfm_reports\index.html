{% extends "base.html" %}

{% block title %}RFM报表{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 页面标题 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-chart-line text-primary me-2"></i>
                    RFM报表
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">首页</a></li>
                        <li class="breadcrumb-item active">RFM报表</li>
                    </ol>
                </nav>
            </div>

            <!-- 功能介绍 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="alert alert-info">
                        <h5 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i>
                            RFM报表系统
                        </h5>
                        <p class="mb-0">
                            基于RFM模型的客户价值分析报表系统，提供数据源管理、正向盘报表和结果盘报表三大功能模块。
                            通过多维度分析帮助您深入了解客户价值分布、行为趋势和业务表现。
                        </p>
                    </div>
                </div>
            </div>

            <!-- 功能模块卡片 -->
            <div class="row">
                <!-- 数据源管理 -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 border-primary">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-database me-2"></i>
                                数据源管理
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text">
                                管理报表分析所需的数据源，支持从数据处理任务中导入数据，
                                并提供数据源的导入、删除和状态管理功能。
                            </p>
                            <ul class="list-unstyled small text-muted">
                                <li><i class="fas fa-check text-success me-1"></i> 数据导入与删除</li>
                                <li><i class="fas fa-check text-success me-1"></i> 状态监控</li>
                                <li><i class="fas fa-check text-success me-1"></i> 权限控制</li>
                                <li><i class="fas fa-check text-success me-1"></i> 任务锁定保护</li>
                            </ul>
                        </div>
                        <div class="card-footer">
                            {% if current_user.is_admin %}
                            <a href="{{ url_for('rfm_reports.data_source_management') }}" 
                               class="btn btn-primary w-100">
                                <i class="fas fa-cog me-1"></i> 管理数据源
                            </a>
                            {% else %}
                            <button class="btn btn-secondary w-100" disabled>
                                <i class="fas fa-lock me-1"></i> 仅管理员可访问
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- 正向盘报表 -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 border-success">
                        <div class="card-header bg-success text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-arrow-right me-2"></i>
                                正向盘报表
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text">
                                基于正向盘数据的RFM分析报表，包含客户资源总览和科室业绩分析，
                                提供多维度的客户价值分析视图。
                            </p>
                            <ul class="list-unstyled small text-muted">
                                <li><i class="fas fa-chart-area text-info me-1"></i> 基盘体量趋势</li>
                                <li><i class="fas fa-exchange-alt text-warning me-1"></i> 季度升降级趋势</li>
                                <li><i class="fas fa-th text-primary me-1"></i> 升降矩阵变化</li>
                                <li><i class="fas fa-heartbeat text-danger me-1"></i> RFM健康度评估</li>
                                <li><i class="fas fa-chart-pie text-success me-1"></i> 科室业绩分析</li>
                            </ul>
                        </div>
                        <div class="card-footer">
                            <a href="{{ url_for('rfm_reports.forward_panel') }}" 
                               class="btn btn-success w-100">
                                <i class="fas fa-chart-line me-1"></i> 查看报表
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 结果盘报表 -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 border-info">
                        <div class="card-header bg-info text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-bullseye me-2"></i>
                                结果盘报表
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text">
                                基于结果盘数据的RFM分析报表，在正向盘基础上增加了次新客资源利用率等
                                更深入的分析维度。
                            </p>
                            <ul class="list-unstyled small text-muted">
                                <li><i class="fas fa-chart-area text-info me-1"></i> 完整客户资源分析</li>
                                <li><i class="fas fa-user-plus text-success me-1"></i> 季度次新客分析</li>
                                <li><i class="fas fa-calendar-alt text-warning me-1"></i> 年度次季度分析</li>
                                <li><i class="fas fa-building text-primary me-1"></i> 科室业绩深度分析</li>
                                <li><i class="fas fa-tags text-secondary me-1"></i> 重点品项分析</li>
                            </ul>
                        </div>
                        <div class="card-footer">
                            <a href="{{ url_for('rfm_reports.result_panel') }}" 
                               class="btn btn-info w-100">
                                <i class="fas fa-chart-bar me-1"></i> 查看报表
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 使用说明 -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-question-circle me-2"></i>
                                使用说明
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <h6 class="text-primary">
                                        <i class="fas fa-step-forward me-1"></i>
                                        第一步：数据源管理
                                    </h6>
                                    <p class="small text-muted">
                                        在数据源管理中选择已完成的数据处理任务，点击"应用数据"将数据导入到RFM分析系统中。
                                        导入后的数据将锁定原始任务，防止误删除。
                                    </p>
                                </div>
                                <div class="col-md-4">
                                    <h6 class="text-success">
                                        <i class="fas fa-step-forward me-1"></i>
                                        第二步：选择报表类型
                                    </h6>
                                    <p class="small text-muted">
                                        根据数据类型选择正向盘报表或结果盘报表。正向盘提供基础分析，
                                        结果盘提供更深入的客户价值分析。
                                    </p>
                                </div>
                                <div class="col-md-4">
                                    <h6 class="text-info">
                                        <i class="fas fa-step-forward me-1"></i>
                                        第三步：分析与导出
                                    </h6>
                                    <p class="small text-muted">
                                        在报表页面选择数据源，通过多层级标签页浏览不同维度的分析结果，
                                        支持数据导出和图表展示。
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
