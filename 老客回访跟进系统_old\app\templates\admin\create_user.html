{% extends "base.html" %}

{% block title %}新增用户 - 老客回访与跟进系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-user-plus"></i> 新增用户</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('admin.index') }}">系统管理</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('admin.users') }}">用户管理</a></li>
                <li class="breadcrumb-item active">新增用户</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-user"></i> 用户信息</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">用户名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       placeholder="请输入用户名" required>
                                <div class="form-text">用户名用于登录，只能包含字母、数字和下划线</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="real_name" class="form-label">真实姓名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="real_name" name="real_name" 
                                       placeholder="请输入真实姓名" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">密码 <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="password" name="password" 
                                       placeholder="请输入密码" required>
                                <div class="form-text">密码长度至少8位，包含大小写字母和数字</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="department_id" class="form-label">所属部门 <span class="text-danger">*</span></label>
                                <select class="form-select" id="department_id" name="department_id" required>
                                    <option value="">请选择部门</option>
                                    {% for department in departments %}
                                    <option value="{{ department.id }}">{{ department.full_name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="role_id" class="form-label">用户角色 <span class="text-danger">*</span></label>
                        <select class="form-select" id="role_id" name="role_id" required>
                            <option value="">请选择角色</option>
                            {% for role in roles %}
                            <option value="{{ role.id }}">{{ role.role_name }} - {{ role.description }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('admin.users') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 创建用户
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-info-circle"></i> 角色说明</h6>
            </div>
            <div class="card-body">
                {% for role in roles %}
                <div class="mb-3">
                    <h6><span class="badge bg-primary">{{ role.role_name }}</span></h6>
                    <p class="text-muted small">{{ role.description }}</p>
                </div>
                {% endfor %}
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-exclamation-triangle"></i> 注意事项</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li><i class="fas fa-check text-success"></i> 用户名创建后不可修改</li>
                    <li><i class="fas fa-check text-success"></i> 密码必须符合安全要求</li>
                    <li><i class="fas fa-check text-success"></i> 角色决定用户的功能权限</li>
                    <li><i class="fas fa-check text-success"></i> 建议用户首次登录后修改密码</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
