{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">字段规则设置</h1>
    <a href="{{ url_for('basic_mgmt.system_settings') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>返回系统设置
    </a>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list-ul me-2"></i>客户登记字段规则配置
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-12">
                            <div class="mb-4">
                                {{ form.card_number_rule.label(class="form-label") }}
                                {{ form.card_number_rule(class="form-control", rows="2") }}
                                {% if form.card_number_rule.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.card_number_rule.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-4">
                                {{ form.onsite_consultant_rule.label(class="form-label") }}
                                {{ form.onsite_consultant_rule(class="form-control", rows="2") }}
                                {% if form.onsite_consultant_rule.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.onsite_consultant_rule.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-4">
                                {{ form.online_consultant_rule.label(class="form-label") }}
                                {{ form.online_consultant_rule(class="form-control", rows="2") }}
                                {% if form.online_consultant_rule.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.online_consultant_rule.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-4">
                                {{ form.channel_rule.label(class="form-label") }}
                                {{ form.channel_rule(class="form-control", rows="2") }}
                                {% if form.channel_rule.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.channel_rule.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-4">
                                {{ form.inquiry_content_rule.label(class="form-label") }}
                                {{ form.inquiry_content_rule(class="form-control", rows="2") }}
                                {% if form.inquiry_content_rule.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.inquiry_content_rule.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-4">
                                {{ form.last_visit_date_rule.label(class="form-label") }}
                                {{ form.last_visit_date_rule(class="form-control", rows="2") }}
                                {% if form.last_visit_date_rule.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.last_visit_date_rule.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-4">
                                {{ form.follow_up_note_rule.label(class="form-label") }}
                                {{ form.follow_up_note_rule(class="form-control", rows="2") }}
                                {% if form.follow_up_note_rule.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.follow_up_note_rule.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-4">
                                {{ form.follow_up_time_rule.label(class="form-label") }}
                                {{ form.follow_up_time_rule(class="form-control", rows="2") }}
                                {% if form.follow_up_time_rule.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.follow_up_time_rule.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-4">
                                {{ form.create_time_rule.label(class="form-label") }}
                                {{ form.create_time_rule(class="form-control", rows="2") }}
                                {% if form.create_time_rule.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.create_time_rule.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('basic_mgmt.system_settings') }}" class="btn btn-outline-secondary me-md-2">取消</a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>字段规则说明
                </h5>
            </div>
            <div class="card-body">
                <p class="small text-muted">
                    这里配置的是客户登记表单中各个字段的规则说明，
                    这些说明将显示在表单中，帮助用户正确填写信息。
                </p>
                
                <h6>主要字段</h6>
                <ul class="small text-muted">
                    <li><strong>卡号:</strong> 客户的唯一标识</li>
                    <li><strong>所属现场:</strong> 负责跟进的现场咨询师</li>
                    <li><strong>所属网资:</strong> 登记信息的网络咨询师</li>
                    <li><strong>激活渠道:</strong> 客户来源渠道</li>
                    <li><strong>咨询内容:</strong> 客户咨询的具体内容</li>
                    <li><strong>最近来院时间:</strong> 客户最后一次到院时间</li>
                </ul>
                
                <h6>跟进相关</h6>
                <ul class="small text-muted">
                    <li><strong>咨询跟进情况:</strong> 现场咨询师填写的跟进记录</li>
                    <li><strong>咨询跟进时间:</strong> 系统自动记录的跟进时间</li>
                    <li><strong>信息登记时间:</strong> 系统自动记录的登记时间</li>
                </ul>
                
                <div class="alert alert-info">
                    <small>
                        <i class="fas fa-lightbulb me-1"></i>
                        修改这些规则说明可以帮助用户更好地理解各个字段的用途和填写要求。
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}