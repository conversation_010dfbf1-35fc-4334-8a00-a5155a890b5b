"""
Excel报表处理器
集成Excel公式引擎到数据处理流程中
"""

import pandas as pd
import logging
from typing import Dict, List, Any
from .formula_engine import ExcelFormulaEngine, ExcelReportEngine
from .report_templates import get_template, REPORT_TEMPLATES


class ExcelReportProcessor:
    """Excel报表处理器"""
    
    def __init__(self, db_engine, logger=None):
        self.db_engine = db_engine
        self.logger = logger or logging.getLogger(__name__)
        
        # 初始化Excel引擎
        self.formula_engine = ExcelFormulaEngine(logger)
        self.report_engine = ExcelReportEngine(self.formula_engine, logger)
        
        # 加载所有报表模板
        for template_name, template_config in REPORT_TEMPLATES.items():
            self.report_engine.load_report_template(template_name, template_config)
    
    def process_excel_report(self, template_name: str, data_context: Dict[str, pd.DataFrame] = None) -> pd.DataFrame:
        """处理Excel报表"""
        try:
            self.logger.info(f"开始处理Excel报表: {template_name}")
            
            # 如果没有提供数据上下文，从数据库加载
            if data_context is None:
                data_context = self._load_data_context(template_name)
            
            # 生成报表
            result_df = self.report_engine.generate_report(template_name, data_context)
            
            self.logger.info(f"Excel报表处理完成: {template_name}, 生成 {len(result_df)} 行数据")
            return result_df
            
        except Exception as e:
            self.logger.error(f"Excel报表处理失败: {template_name}, 错误: {str(e)}")
            raise
    
    def _load_data_context(self, template_name: str) -> Dict[str, pd.DataFrame]:
        """加载数据上下文"""
        template = get_template(template_name)
        if not template:
            raise ValueError(f"模板 {template_name} 不存在")
        
        data_context = {}
        
        # 根据模板需要的数据表加载数据
        for table_name in template.get('source_tables', []):
            try:
                # 这里可以根据实际需要调整SQL查询
                if table_name == "会员基础数据":
                    query = """
                    SELECT DISTINCT 会员卡号, 
                           MAX(执行日期) as 最近消费日期
                    FROM 客户执行明细表 
                    WHERE 会员卡号 IS NOT NULL 
                    GROUP BY 会员卡号
                    """
                elif table_name == "消费明细":
                    query = """
                    SELECT 会员卡号, 执行日期, 执行业绩（真实金额） as 消费金额
                    FROM 客户执行明细表 
                    WHERE 会员卡号 IS NOT NULL
                    """
                elif table_name == "执行明细":
                    query = """
                    SELECT 会员卡号, 科室, 二级分类, 三级分类, 
                           执行业绩（真实金额） as 执行业绩, 执行日期
                    FROM 客户执行明细表 
                    WHERE 会员卡号 IS NOT NULL
                    """
                else:
                    # 默认查询
                    query = f"SELECT * FROM `{table_name}` LIMIT 1000"
                
                df = pd.read_sql(query, self.db_engine)
                data_context[table_name] = df
                self.logger.info(f"加载数据表 {table_name}: {len(df)} 行")
                
            except Exception as e:
                self.logger.warning(f"加载数据表 {table_name} 失败: {str(e)}")
                data_context[table_name] = pd.DataFrame()
        
        return data_context
    
    def create_custom_report(self, report_config: Dict) -> pd.DataFrame:
        """创建自定义报表"""
        try:
            self.logger.info("开始处理自定义Excel报表")
            
            # 加载数据
            data_context = {}
            for source_config in report_config.get('data_sources', []):
                table_name = source_config['name']
                query = source_config['query']
                
                df = pd.read_sql(query, self.db_engine)
                data_context[table_name] = df
                self.logger.info(f"加载自定义数据源 {table_name}: {len(df)} 行")
            
            # 创建临时模板
            template_name = "custom_report"
            self.report_engine.load_report_template(template_name, report_config)
            
            # 生成报表
            result_df = self.report_engine.generate_report(template_name, data_context)
            
            self.logger.info(f"自定义Excel报表处理完成，生成 {len(result_df)} 行数据")
            return result_df
            
        except Exception as e:
            self.logger.error(f"自定义Excel报表处理失败: {str(e)}")
            raise
    
    def execute_single_formula(self, formula: str, data_row: Dict = None) -> Any:
        """执行单个Excel公式"""
        try:
            return self.formula_engine.execute_formula(formula, data_row)
        except Exception as e:
            self.logger.error(f"公式执行失败: {formula}, 错误: {str(e)}")
            return None
    
    def validate_formula(self, formula: str) -> tuple:
        """验证Excel公式"""
        try:
            # 尝试执行公式
            test_data = {'测试列': 100, '测试文本': 'test'}
            result = self.formula_engine.execute_formula(formula, test_data)
            return True, f"公式验证成功，测试结果: {result}"
        except Exception as e:
            return False, f"公式验证失败: {str(e)}"
    
    def get_available_functions(self) -> Dict[str, List[str]]:
        """获取可用的Excel函数列表"""
        return {
            "逻辑函数": ["IF", "AND", "OR"],
            "数学函数": ["SUM", "SUMIF", "SUMIFS", "AVERAGE", "COUNT", "COUNTIF", "COUNTIFS", "MAX", "MIN", "ROUND", "ABS"],
            "文本函数": ["CONCATENATE", "LEFT", "RIGHT", "MID", "LEN", "UPPER", "LOWER"],
            "日期函数": ["TODAY", "NOW", "YEAR", "MONTH", "DAY"],
            "查找函数": ["VLOOKUP", "INDEX", "MATCH"],
            "其他函数": ["RANK"]
        }


def create_sample_report_config():
    """创建示例报表配置"""
    return {
        "description": "会员消费分析报表",
        "data_sources": [
            {
                "name": "会员数据",
                "query": """
                SELECT 会员卡号, 
                       COUNT(*) as 消费次数,
                       SUM(执行业绩（真实金额）) as 总消费金额,
                       MAX(执行日期) as 最近消费日期,
                       MIN(执行日期) as 首次消费日期
                FROM 客户执行明细表 
                WHERE 会员卡号 IS NOT NULL 
                GROUP BY 会员卡号
                """
            }
        ],
        "columns": [
            {
                "name": "会员卡号",
                "formula": "[会员卡号]",
                "source_data": "会员数据",
                "description": "会员卡号"
            },
            {
                "name": "消费等级",
                "formula": "=IF([总消费金额]>50000, \"VIP\", IF([总消费金额]>20000, \"高级\", IF([总消费金额]>5000, \"中级\", \"普通\")))",
                "source_data": "会员数据",
                "description": "基于消费金额的等级划分"
            },
            {
                "name": "活跃度",
                "formula": "=IF([消费次数]>10, \"高活跃\", IF([消费次数]>5, \"中活跃\", \"低活跃\"))",
                "source_data": "会员数据", 
                "description": "基于消费次数的活跃度"
            },
            {
                "name": "客户价值",
                "formula": "=CONCATENATE([消费等级], \"-\", [活跃度])",
                "source_data": "会员数据",
                "description": "综合客户价值标签"
            }
        ]
    }
