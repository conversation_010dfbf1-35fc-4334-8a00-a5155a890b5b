"""
简化的列排序测试
"""

import pandas as pd
import re

def get_column_sort_order():
    """定义列的排序顺序"""
    base_field_order = [
        '会员卡号',
        '现场', '综合等级', '细分等级', '科室标签', '会员等级',
        'R1', 'R2', 'F1', 'F2', 'M1', 'M2',
        'R1区间', 'R2区间', 'F1区间', 'F2区间', 'M1区间', 'M2区间',
        'R1分值', 'R2分值', 'F1分值', 'F2分值', 'M1分值', 'M2分值', '加权总分',  # 修正分值顺序
        '年龄', '模型', '消费年限', 'RFM',
        '执行业绩', '皮肤_执行业绩', '注射_执行业绩',
        # 按类型分组的TOP字段
        '皮肤_TOP品类1', '皮肤_TOP品类2', '皮肤_TOP品类3', '皮肤_TOP品类4', '皮肤_TOP品类5',
        '注射_TOP品类1', '注射_TOP品类2', '注射_TOP品类3', '注射_TOP品类4', '注射_TOP品类5',
        '皮肤_TOP品项1', '皮肤_TOP品项2', '皮肤_TOP品项3', '皮肤_TOP品项4', '皮肤_TOP品项5',
        '皮肤_TOP品项6', '皮肤_TOP品项7', '皮肤_TOP品项8', '皮肤_TOP品项9', '皮肤_TOP品项10',
        '皮肤_TOP品项11', '皮肤_TOP品项12', '皮肤_TOP品项13', '皮肤_TOP品项14', '皮肤_TOP品项15',
        '注射_TOP品项1', '注射_TOP品项2', '注射_TOP品项3', '注射_TOP品项4', '注射_TOP品项5',
        '注射_TOP品项6', '注射_TOP品项7', '注射_TOP品项8', '注射_TOP品项9', '注射_TOP品项10',
        '注射_TOP品项11', '注射_TOP品项12', '注射_TOP品项13', '注射_TOP品项14', '注射_TOP品项15'
    ]
    return base_field_order

def column_matches_field(column_name, base_field):
    """判断列名是否匹配基础字段"""
    # 精确匹配：季度_字段名 格式
    if column_name.endswith(f'_{base_field}'):
        return True

    # 直接匹配字段名
    if column_name == base_field:
        return True

    # 特殊处理业绩相关字段
    if base_field == '执行业绩':
        # 匹配 24Q4_执行业绩 格式
        if column_name.endswith('_执行业绩') and '皮肤' not in column_name and '注射' not in column_name:
            return True
    elif base_field == '皮肤_执行业绩':
        # 匹配 24Q4_皮肤_执行业绩 格式
        if '皮肤_执行业绩' in column_name:
            return True
    elif base_field == '注射_执行业绩':
        # 匹配 24Q4_注射_执行业绩 格式
        if '注射_执行业绩' in column_name:
            return True

    # 对于一些特殊情况的模糊匹配
    if base_field in ['现场', '现场小组']:
        if base_field in column_name:
            # 确保不是其他字段的一部分
            if base_field == '现场' and '现场小组' in column_name:
                return False
            return True

    # TOP品类和TOP品项的精确匹配
    if base_field.startswith('皮肤_TOP品类') or base_field.startswith('注射_TOP品类'):
        # 精确匹配，如 皮肤_TOP品类1_紧致塑形 匹配 皮肤_TOP品类1
        if base_field in column_name:
            return True
    elif base_field.startswith('皮肤_TOP品项') or base_field.startswith('注射_TOP品项'):
        # 精确匹配，如 皮肤_TOP品项1_五代热玛吉 匹配 皮肤_TOP品项1
        if base_field in column_name:
            return True

    return False

def extract_quarter_for_sorting(column_name):
    """从列名中提取季度信息用于排序"""
    # 匹配季度模式，如 24Q4, 25Q1 等
    quarter_pattern = r'(\d{2})Q(\d)'
    match = re.search(quarter_pattern, column_name)
    
    if match:
        year = int(match.group(1))
        quarter = int(match.group(2))
        # 返回年份和季度的元组用于排序
        return (year, quarter)
    else:
        # 如果没有季度信息，放在最后
        return (99, 9)

def sort_columns_by_field_type(df):
    """按字段类型对列进行排序"""
    base_order = get_column_sort_order()
    current_columns = list(df.columns)
    sorted_columns = []
    used_columns = set()
    
    # 首先添加会员卡号
    if '会员卡号' in current_columns:
        sorted_columns.append('会员卡号')
        used_columns.add('会员卡号')
    
    # 按字段类型分组排序
    for base_field in base_order[1:]:  # 跳过会员卡号，已经添加
        # 找到所有包含该字段的列
        matching_columns = []
        for col in current_columns:
            if col not in used_columns and column_matches_field(col, base_field):
                matching_columns.append(col)
        
        # 按季度排序匹配的列
        matching_columns.sort(key=extract_quarter_for_sorting)
        sorted_columns.extend(matching_columns)
        used_columns.update(matching_columns)
    
    # 添加任何未匹配的列
    remaining_columns = [col for col in current_columns if col not in used_columns]
    remaining_columns.sort()  # 对剩余列进行字母排序
    sorted_columns.extend(remaining_columns)
    
    # 重新排列DataFrame的列
    df_sorted = df[sorted_columns]
    
    print(f"列排序完成，排序后列数: {len(sorted_columns)}")
    return df_sorted

def test_performance_columns():
    """测试业绩相关列的排序"""
    
    # 创建包含业绩数据的测试列（使用实际的列名格式）
    test_columns = [
        '会员卡号',
        '24Q4_现场', '25Q1_现场', '25Q2_现场', '25Q3_现场',
        '24Q4_综合等级', '25Q1_综合等级', '25Q2_综合等级', '25Q3_综合等级',
        '24Q4_R1', '25Q1_R1', '25Q2_R1', '25Q3_R1',
        '24Q4_R1区间', '25Q1_R1区间', '25Q2_R1区间', '25Q3_R1区间',
        # 测试分值顺序
        '24Q4_F1分值', '25Q1_F1分值', '24Q4_R1分值', '25Q1_R1分值',
        '24Q4_会员等级', '25Q1_会员等级', '25Q2_会员等级', '25Q3_会员等级',
        '24Q4_加权总分', '25Q1_加权总分', '25Q2_加权总分', '25Q3_加权总分',
        '24Q4_年龄', '25Q1_年龄', '25Q2_年龄', '25Q3_年龄',
        '24Q4_RFM', '25Q1_RFM', '25Q2_RFM', '25Q3_RFM',
        # 业绩数据 - 使用实际格式
        '24Q4_执行业绩', '25Q1_执行业绩', '25Q2_执行业绩', '25Q3_执行业绩',
        '24Q4_皮肤_执行业绩', '25Q1_皮肤_执行业绩', '25Q2_皮肤_执行业绩', '25Q3_皮肤_执行业绩',
        '24Q4_注射_执行业绩', '25Q1_注射_执行业绩', '25Q2_注射_执行业绩', '25Q3_注射_执行业绩',
        # TOP数据 - 测试分组排序
        '注射_TOP品类1_再生材料', '皮肤_TOP品类1_紧致塑形', '注射_TOP品类2_肌肉塑形', '皮肤_TOP品类2_美白嫩肤',
        '注射_TOP品项1_濡白天使', '皮肤_TOP品项1_五代热玛吉', '注射_TOP品项2_爱贝芙', '皮肤_TOP品项2_四代热玛吉',
    ]
    
    # 创建测试DataFrame
    test_data = {col: [f'test_{i}' for i in range(3)] for col in test_columns}
    df = pd.DataFrame(test_data)
    
    print("原始列顺序:")
    for i, col in enumerate(df.columns):
        print(f"{i+1:2d}. {col}")
    
    # 应用排序
    sorted_df = sort_columns_by_field_type(df)
    
    print("\n排序后列顺序:")
    for i, col in enumerate(sorted_df.columns):
        print(f"{i+1:2d}. {col}")
    
    # 验证关键排序规则
    print("\n验证关键排序规则:")

    # 1. 检查分值字段顺序
    score_columns = [col for col in sorted_df.columns if '分值' in col]
    print(f"分值字段: {score_columns}")

    # 检查R1分值是否在F1分值之前
    r1_score_cols = [col for col in score_columns if 'R1分值' in col]
    f1_score_cols = [col for col in score_columns if 'F1分值' in col]

    if r1_score_cols and f1_score_cols:
        columns_list = list(sorted_df.columns)
        r1_index = columns_list.index(r1_score_cols[0])
        f1_index = columns_list.index(f1_score_cols[0])

        if r1_index < f1_index:
            print("✓ R1分值在F1分值之前")
        else:
            print("✗ R1分值不在F1分值之前")

    # 2. 检查执行业绩列（不包含皮肤和注射）
    performance_columns = [col for col in sorted_df.columns if '执行业绩' in col and '皮肤' not in col and '注射' not in col]
    expected_performance = ['24Q4_执行业绩', '25Q1_执行业绩', '25Q2_执行业绩', '25Q3_执行业绩']
    print(f"执行业绩列: {performance_columns}")
    print(f"期望顺序: {expected_performance}")
    print(f"✓ 执行业绩排序正确" if performance_columns == expected_performance else "✗ 执行业绩排序错误")

    # 3. 检查皮肤执行业绩列
    skin_performance_columns = [col for col in sorted_df.columns if '皮肤_执行业绩' in col]
    expected_skin_performance = ['24Q4_皮肤_执行业绩', '25Q1_皮肤_执行业绩', '25Q2_皮肤_执行业绩', '25Q3_皮肤_执行业绩']
    print(f"皮肤执行业绩列: {skin_performance_columns}")
    print(f"期望顺序: {expected_skin_performance}")
    print(f"✓ 皮肤执行业绩排序正确" if skin_performance_columns == expected_skin_performance else "✗ 皮肤执行业绩排序错误")

    # 4. 检查注射执行业绩列
    injection_performance_columns = [col for col in sorted_df.columns if '注射_执行业绩' in col]
    expected_injection_performance = ['24Q4_注射_执行业绩', '25Q1_注射_执行业绩', '25Q2_注射_执行业绩', '25Q3_注射_执行业绩']
    print(f"注射执行业绩列: {injection_performance_columns}")
    print(f"期望顺序: {expected_injection_performance}")
    print(f"✓ 注射执行业绩排序正确" if injection_performance_columns == expected_injection_performance else "✗ 注射执行业绩排序错误")

    # 检查业绩数据的相对位置
    columns_list = list(sorted_df.columns)

    if performance_columns and skin_performance_columns:
        # 执行业绩应该在皮肤执行业绩之前
        if columns_list.index(performance_columns[0]) < columns_list.index(skin_performance_columns[0]):
            print("✓ 执行业绩在皮肤执行业绩之前")
        else:
            print("✗ 执行业绩不在皮肤执行业绩之前")

    if skin_performance_columns and injection_performance_columns:
        # 皮肤执行业绩应该在注射执行业绩之前
        if columns_list.index(skin_performance_columns[0]) < columns_list.index(injection_performance_columns[0]):
            print("✓ 皮肤执行业绩在注射执行业绩之前")
        else:
            print("✗ 皮肤执行业绩不在注射执行业绩之前")

    # 检查分值字段位置
    score_columns = [col for col in sorted_df.columns if '分值' in col]
    interval_columns = [col for col in sorted_df.columns if '区间' in col]

    if score_columns and interval_columns:
        # 分值字段应该在区间字段之后
        last_interval_index = max([columns_list.index(col) for col in interval_columns])
        first_score_index = min([columns_list.index(col) for col in score_columns])

        if last_interval_index < first_score_index:
            print("✓ 分值字段在区间字段之后")
        else:
            print("✗ 分值字段位置错误")

    # 检查会员等级字段位置
    member_level_columns = [col for col in sorted_df.columns if '会员等级' in col]
    rfm_columns = [col for col in sorted_df.columns if col.endswith('_R1') or col.endswith('_R2') or col.endswith('_F1') or col.endswith('_F2') or col.endswith('_M1') or col.endswith('_M2')]

    if member_level_columns and rfm_columns:
        # 会员等级应该在RFM字段之前
        last_member_level_index = max([columns_list.index(col) for col in member_level_columns])
        first_rfm_index = min([columns_list.index(col) for col in rfm_columns])

        if last_member_level_index < first_rfm_index:
            print("✓ 会员等级在RFM字段之前")
        else:
            print("✗ 会员等级位置错误")

    # 5. 检查TOP字段分组
    skin_top_category_cols = [col for col in sorted_df.columns if '皮肤_TOP品类' in col]
    injection_top_category_cols = [col for col in sorted_df.columns if '注射_TOP品类' in col]

    print(f"皮肤TOP品类: {skin_top_category_cols}")
    print(f"注射TOP品类: {injection_top_category_cols}")

    if skin_top_category_cols and injection_top_category_cols:
        columns_list = list(sorted_df.columns)
        last_skin_category_index = max([columns_list.index(col) for col in skin_top_category_cols])
        first_injection_category_index = min([columns_list.index(col) for col in injection_top_category_cols])

        if last_skin_category_index < first_injection_category_index:
            print("✓ 所有皮肤TOP品类在所有注射TOP品类之前")
        else:
            print("✗ 皮肤和注射TOP品类交错排列")

    # 6. 检查TOP品项分组
    skin_top_item_cols = [col for col in sorted_df.columns if '皮肤_TOP品项' in col]
    injection_top_item_cols = [col for col in sorted_df.columns if '注射_TOP品项' in col]

    print(f"皮肤TOP品项: {skin_top_item_cols}")
    print(f"注射TOP品项: {injection_top_item_cols}")

    if skin_top_item_cols and injection_top_item_cols:
        columns_list = list(sorted_df.columns)
        last_skin_item_index = max([columns_list.index(col) for col in skin_top_item_cols])
        first_injection_item_index = min([columns_list.index(col) for col in injection_top_item_cols])

        if last_skin_item_index < first_injection_item_index:
            print("✓ 所有皮肤TOP品项在所有注射TOP品项之前")
        else:
            print("✗ 皮肤和注射TOP品项交错排列")

if __name__ == "__main__":
    test_performance_columns()
