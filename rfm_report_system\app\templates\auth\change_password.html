{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h4><i class="fas fa-key me-2"></i>修改密码</h4>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.current_password.label(class="form-label") }}
                        {{ form.current_password(class="form-control" + (" is-invalid" if form.current_password.errors else "")) }}
                        {% if form.current_password.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.current_password.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.new_password.label(class="form-label") }}
                        {{ form.new_password(class="form-control" + (" is-invalid" if form.new_password.errors else "")) }}
                        {% if form.new_password.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.new_password.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.new_password2.label(class="form-label") }}
                        {{ form.new_password2(class="form-control" + (" is-invalid" if form.new_password2.errors else "")) }}
                        {% if form.new_password2.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.new_password2.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('main.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>返回
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
