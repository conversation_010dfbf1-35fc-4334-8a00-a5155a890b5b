{% extends "base.html" %}

{% block title %}数据源管理 - RFM报表{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 页面标题 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-database text-primary me-2"></i>
                    数据源管理
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">首页</a></li>
                        <li class="breadcrumb-item"><a href="{{ url_for('rfm_reports.index') }}">RFM报表</a></li>
                        <li class="breadcrumb-item active">数据源管理</li>
                    </ol>
                </nav>
            </div>

            <!-- 操作提示 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="alert alert-info">
                        <h6 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i>
                            数据源管理说明
                        </h6>
                        <ul class="mb-0 small">
                            <li><strong>应用数据：</strong>将数据处理任务的结果导入到RFM分析系统中</li>
                            <li><strong>删除数据：</strong>仅删除RFM系统中的数据副本，不影响原始任务</li>
                            <li><strong>任务锁定：</strong>已导入的数据会锁定原始任务，防止误删除</li>
                            <li><strong>权限控制：</strong>仅管理员可以进行数据源管理操作</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 统计信息 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-primary">
                            <i class="fas fa-tasks"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">可用任务</span>
                            <span class="info-box-number" id="totalTasks">-</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-success">
                            <i class="fas fa-check-circle"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">已导入</span>
                            <span class="info-box-number" id="importedTasks">-</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-warning">
                            <i class="fas fa-arrow-right"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">正向盘</span>
                            <span class="info-box-number" id="forwardTasks">-</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-info">
                            <i class="fas fa-bullseye"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">结果盘</span>
                            <span class="info-box-number" id="resultTasks">-</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统资源监控 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                系统资源监控
                                <small class="text-muted">(动态批次处理)</small>
                            </h6>
                        </div>
                        <div class="card-body py-2">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <small class="text-muted">CPU使用率</small>
                                        <div class="progress mt-1" style="height: 8px;">
                                            <div class="progress-bar bg-info" id="cpuProgress" style="width: 0%"></div>
                                        </div>
                                        <small id="cpuText">0%</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <small class="text-muted">内存使用率</small>
                                        <div class="progress mt-1" style="height: 8px;">
                                            <div class="progress-bar bg-warning" id="memoryProgress" style="width: 0%"></div>
                                        </div>
                                        <small id="memoryText">0%</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <small class="text-muted">当前批次大小</small>
                                        <div class="mt-1">
                                            <span class="badge bg-primary" id="batchSizeText">50</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <small class="text-muted">处理延迟</small>
                                        <div class="mt-1">
                                            <span class="badge bg-secondary" id="delayText">50ms</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据源列表 -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-list me-2"></i>
                                数据处理任务列表
                            </h5>
                            <div class="card-tools">
                                <button class="btn btn-sm btn-primary" onclick="refreshTasks()">
                                    <i class="fas fa-sync"></i> 刷新
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 正向盘数据 -->
                            <div class="mb-4">
                                <h6 class="text-primary">
                                    <i class="fas fa-arrow-right me-2"></i>
                                    正向盘数据
                                </h6>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th>任务名称</th>
                                                <th>创建时间</th>
                                                <th>应用状态</th>
                                                <th>导入进度</th>
                                                <th>导入记录数</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="forwardTasksBody">
                                            <tr>
                                                <td colspan="6" class="text-center">
                                                    <i class="fas fa-spinner fa-spin"></i> 正在加载...
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- 结果盘数据 -->
                            <div>
                                <h6 class="text-info">
                                    <i class="fas fa-bullseye me-2"></i>
                                    结果盘数据
                                </h6>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th>任务名称</th>
                                                <th>创建时间</th>
                                                <th>应用状态</th>
                                                <th>导入进度</th>
                                                <th>导入记录数</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="resultTasksBody">
                                            <tr>
                                                <td colspan="6" class="text-center">
                                                    <i class="fas fa-spinner fa-spin"></i> 正在加载...
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 确认对话框 -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmModalTitle">确认操作</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="confirmModalBody">
                确认要执行此操作吗？
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmButton">确认</button>
            </div>
        </div>
    </div>
</div>

<!-- 进度对话框 -->
<div class="modal fade" id="progressModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="progressModalTitle">
                    <i class="fas fa-cog fa-spin me-2"></i>
                    正在处理...
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span id="progressText">准备开始...</span>
                        <span id="progressPercent">0%</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                             id="progressBar" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
                <div class="small text-muted" id="progressDetails">
                    请稍候，正在处理您的请求...
                </div>
                <div class="mt-2">
                    <div class="alert alert-light border-0 mb-0 small">
                        <i class="fas fa-info-circle text-info me-1"></i>
                        您可以关闭此窗口，操作将在后台继续进行
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i> 关闭进度窗口
                </button>
                <button type="button" class="btn btn-warning" id="cancelProgress" style="display: none;">
                    <i class="fas fa-stop me-1"></i> 取消操作
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 结果对话框 -->
<div class="modal fade" id="resultModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="resultModalTitle">操作结果</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="resultModalBody">
                <!-- 结果内容将动态填充 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">确定</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentAction = null;
let currentTaskId = null;
let currentDataSourceId = null;

// 页面加载完成后获取任务列表
document.addEventListener('DOMContentLoaded', function() {
    loadTasks();

    // 启动定时刷新，检查导入进度
    startProgressPolling();

    // 启动系统资源监控
    startSystemResourceMonitoring();

    // 监听进度对话框关闭事件
    const progressModal = document.getElementById('progressModal');
    progressModal.addEventListener('hidden.bs.modal', function() {
        onProgressModalClosed();
    });

    // 监听键盘事件
    document.addEventListener('keydown', function(e) {
        // ESC键关闭进度对话框
        if (e.key === 'Escape') {
            const modal = bootstrap.Modal.getInstance(progressModal);
            if (modal) {
                modal.hide();
            }
        }
    });

    // 页面卸载时清理计数器
    window.addEventListener('beforeunload', function() {
        stopProgressPolling();
    });

    // 页面隐藏时暂停计数器，显示时恢复
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            // 页面隐藏，暂停计数器
            importingCounters.forEach(counter => {
                if (counter.interval) {
                    clearInterval(counter.interval);
                    counter.interval = null;
                }
            });
        } else {
            // 页面显示，重新启动真实进度轮询
            importingCounters.forEach((counter, taskId) => {
                if (!counter.interval && counter.dataSourceId && counter.type) {
                    startRealProgressPolling(taskId, counter.dataSourceId, counter.type);
                }
            });
        }
    });
});

// 定时检查导入进度
let progressPollingInterval = null;
let importingCounters = new Map(); // 存储导入中任务的计数器

function startProgressPolling() {
    // 每5秒检查一次进度
    progressPollingInterval = setInterval(() => {
        checkImportingTasks();
    }, 5000);
}

function stopProgressPolling() {
    if (progressPollingInterval) {
        clearInterval(progressPollingInterval);
        progressPollingInterval = null;
    }

    // 清理所有计数器
    importingCounters.forEach(counter => {
        if (counter.interval) {
            clearInterval(counter.interval);
        }
    });
    importingCounters.clear();
}

function checkImportingTasks() {
    // 检查是否有正在导入的任务
    const importingRows = document.querySelectorAll('[id^="task-row-"] .progress-bar-animated');

    if (importingRows.length > 0) {
        // 有正在导入的任务，刷新任务列表
        loadTasks();
    }
}

// 启动导入中和删除中任务的动态计数器
function startImportingCounters(tasks) {
    // 清理旧的计数器
    importingCounters.forEach(counter => {
        if (counter.interval) {
            clearInterval(counter.interval);
        }
    });
    importingCounters.clear();

    // 为导入中和删除中的任务启动真实进度轮询
    tasks.forEach(task => {
        if (task.data_source && task.data_source.status === 'importing') {
            startRealProgressPolling(task.id, task.data_source.id, 'importing');
        } else if (task.data_source && task.data_source.status === 'deleting') {
            startRealProgressPolling(task.id, task.data_source.id, 'deleting');
        }
    });
}

// 注意：旧的startImportingCounter函数已被移除，现在统一使用startRealProgressPolling

// 注意：旧的startDeletingCounter函数已被移除，现在统一使用startRealProgressPolling

function loadTasks() {
    console.log('🚀 开始加载任务列表...');

    fetch('/rfm/api/available-tasks', {
        method: 'GET',
        credentials: 'same-origin',  // 确保发送cookies
        headers: {
            'Content-Type': 'application/json',
        }
    })
        .then(response => {
            console.log('📡 API响应状态:', response.status);

            // 安全地获取响应头
            try {
                const headers = {};
                for (const [key, value] of response.headers.entries()) {
                    headers[key] = value;
                }
                console.log('📋 API响应头:', headers);
            } catch (e) {
                console.log('📋 无法获取响应头:', e.message);
            }

            if (response.status === 302) {
                console.error('🔐 被重定向到登录页面，用户未登录');
                showTaskLoadError('用户未登录，请刷新页面重新登录');
                return;
            }

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return response.text().then(text => {
                console.log('📄 原始响应文本:', text);
                try {
                    return JSON.parse(text);
                } catch (e) {
                    console.error('❌ JSON解析失败:', e);
                    throw new Error('响应不是有效的JSON格式');
                }
            });
        })
        .then(data => {
            if (!data) return; // 如果是重定向，data为undefined

            console.log('✅ API响应数据:', data);
            if (data.success) {
                console.log(`📋 获取到 ${data.tasks.length} 个任务`);

                // 保存任务数据到全局变量
                window.currentTasksData = data.tasks;

                renderTasks(data.tasks);
                updateStatistics(data.tasks);
            } else {
                console.error('❌ API返回错误:', data.error);
                showTaskLoadError('获取任务列表失败: ' + data.error);
            }
        })
        .catch(error => {
            console.error('❌ 获取任务列表失败:', error);
            showTaskLoadError('网络错误: ' + error.message);
        });
}

function renderTasks(tasks) {
    const forwardTasks = tasks.filter(task => task.type === 'forward');
    const resultTasks = tasks.filter(task => task.type === 'result');

    renderTaskTable('forwardTasksBody', forwardTasks);
    renderTaskTable('resultTasksBody', resultTasks);

    // 启动导入中任务的动态计数
    startImportingCounters(tasks);
}

function renderTaskTable(bodyId, tasks) {
    const tbody = document.getElementById(bodyId);

    if (tasks.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">暂无数据</td></tr>';
        return;
    }

    tbody.innerHTML = '';

    tasks.forEach(task => {
        const row = document.createElement('tr');
        row.id = `task-row-${task.id}`;

        // 根据数据源状态确定显示的状态标签
        let statusBadge;
        if (task.data_source) {
            switch (task.data_source.status) {
                case 'importing':
                    statusBadge = '<span class="badge bg-warning">导入中</span>';
                    break;
                case 'deleting':
                    statusBadge = '<span class="badge bg-danger">删除中</span>';
                    break;
                case 'completed':
                    statusBadge = '<span class="badge bg-success">已导入</span>';
                    break;
                case 'failed':
                    statusBadge = '<span class="badge bg-danger">操作失败</span>';
                    break;
                default:
                    statusBadge = '<span class="badge bg-secondary">未导入</span>';
            }
        } else {
            statusBadge = '<span class="badge bg-secondary">未导入</span>';
        }

        // 根据数据源状态确定导入记录数显示
        let importedRecords;
        if (task.data_source && task.data_source.status === 'completed') {
            importedRecords = task.data_source.imported_records.toLocaleString();
        } else if (task.data_source && task.data_source.status === 'importing') {
            // 导入中状态显示真实的导入记录数
            const currentCount = task.data_source.imported_records || 0;
            importedRecords = `
                <span class="text-primary">
                    <i class="fas fa-arrow-up me-1"></i>
                    <span id="importing-count-${task.id}">${currentCount.toLocaleString()}</span>
                </span>
            `;
        } else if (task.data_source && task.data_source.status === 'deleting') {
            // 删除中状态显示真实的剩余记录数
            const remainingCount = task.data_source.imported_records || 0;
            importedRecords = `
                <span class="text-warning">
                    <i class="fas fa-arrow-down me-1"></i>
                    <span id="deleting-count-${task.id}">${remainingCount.toLocaleString()}</span>
                </span>
            `;
        } else {
            importedRecords = '-';
        }

        // 生成进度列内容
        const progressColumn = generateProgressColumn(task);

        // 根据数据源状态确定操作按钮
        let actionButtons;
        if (task.data_source) {
            switch (task.data_source.status) {
                case 'importing':
                    actionButtons = `
                        <button class="btn btn-outline-warning btn-sm me-1" disabled>
                            <i class="fas fa-spinner fa-spin"></i> 导入中
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" disabled>
                            <i class="fas fa-trash"></i> 删除数据
                        </button>
                    `;
                    break;
                case 'deleting':
                    actionButtons = `
                        <button class="btn btn-outline-secondary btn-sm me-1" disabled>
                            <i class="fas fa-check"></i> 已应用
                        </button>
                        <button class="btn btn-outline-warning btn-sm" disabled>
                            <i class="fas fa-spinner fa-spin"></i> 删除中
                        </button>
                    `;
                    break;
                case 'completed':
                    actionButtons = `
                        <button class="btn btn-outline-primary btn-sm me-1" disabled>
                            <i class="fas fa-check"></i> 已应用
                        </button>
                        <button class="btn btn-outline-danger btn-sm" onclick="confirmDeleteDataSource(${task.data_source.id})">
                            <i class="fas fa-trash"></i> 删除数据
                        </button>
                    `;
                    break;
                case 'failed':
                    actionButtons = `
                        <button class="btn btn-outline-success btn-sm me-1" onclick="confirmImportDataSource(${task.id}, '${task.name}')">
                            <i class="fas fa-redo"></i> 重新导入
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" disabled>
                            <i class="fas fa-trash"></i> 删除数据
                        </button>
                    `;
                    break;
                default:
                    actionButtons = `
                        <button class="btn btn-outline-success btn-sm me-1" onclick="confirmImportDataSource(${task.id}, '${task.name}')">
                            <i class="fas fa-download"></i> 应用数据
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" disabled>
                            <i class="fas fa-trash"></i> 删除数据
                        </button>
                    `;
            }
        } else {
            actionButtons = `
                <button class="btn btn-outline-success btn-sm me-1" onclick="confirmImportDataSource(${task.id}, '${task.name}')">
                    <i class="fas fa-download"></i> 应用数据
                </button>
                <button class="btn btn-outline-secondary btn-sm" disabled>
                    <i class="fas fa-trash"></i> 删除数据
                </button>
            `;
        }

        row.innerHTML = `
            <td><strong>${task.name}</strong></td>
            <td>${new Date(task.created_at).toLocaleString()}</td>
            <td>${statusBadge}</td>
            <td>${progressColumn}</td>
            <td>${importedRecords}</td>
            <td>${actionButtons}</td>
        `;

        tbody.appendChild(row);
    });
}

function generateProgressColumn(task) {
    if (!task.data_source) {
        return '<span class="text-muted">-</span>';
    }

    const status = task.data_source.status;

    switch (status) {
        case 'importing':
            // 使用真实进度数据
            const importProgress = getRealProgressPercent(task);
            return `
                <div class="progress" style="height: 20px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-primary"
                         role="progressbar" style="width: ${importProgress}%" id="progress-bar-${task.id}">
                        导入中 ${importProgress}%
                    </div>
                </div>
            `;
        case 'deleting':
            // 使用真实删除进度数据
            const deleteProgress = getRealProgressPercent(task);
            return `
                <div class="progress" style="height: 20px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-warning"
                         role="progressbar" style="width: ${deleteProgress}%" id="progress-bar-${task.id}">
                        删除中 ${deleteProgress}%
                    </div>
                </div>
            `;
        case 'completed':
            return `
                <div class="progress" style="height: 20px;">
                    <div class="progress-bar bg-success" role="progressbar" style="width: 100%">
                        已完成
                    </div>
                </div>
            `;
        case 'failed':
            return `
                <div class="progress" style="height: 20px;">
                    <div class="progress-bar bg-danger" role="progressbar" style="width: 100%">
                        操作失败
                    </div>
                </div>
            `;
        case 'pending':
        default:
            return '<span class="text-muted">待导入</span>';
    }
}

function updateStatistics(tasks) {
    const totalTasks = tasks.length;
    const importedTasks = tasks.filter(task => task.is_imported).length;
    const forwardTasks = tasks.filter(task => task.type === 'forward').length;
    const resultTasks = tasks.filter(task => task.type === 'result').length;
    
    document.getElementById('totalTasks').textContent = totalTasks;
    document.getElementById('importedTasks').textContent = importedTasks;
    document.getElementById('forwardTasks').textContent = forwardTasks;
    document.getElementById('resultTasks').textContent = resultTasks;
}

function confirmImportDataSource(taskId, taskName) {
    currentAction = 'import';
    currentTaskId = taskId;
    
    document.getElementById('confirmModalTitle').textContent = '确认导入数据源';
    document.getElementById('confirmModalBody').innerHTML = `
        <p>确认要导入任务 <strong>"${taskName}"</strong> 的数据吗？</p>
        <div class="alert alert-warning small">
            <i class="fas fa-exclamation-triangle me-1"></i>
            导入后该任务将被锁定，无法在数据处理模块中删除，直到在此处删除数据源。
        </div>
    `;
    
    const confirmButton = document.getElementById('confirmButton');
    confirmButton.className = 'btn btn-success';
    confirmButton.innerHTML = '<i class="fas fa-download me-1"></i> 确认导入';
    
    new bootstrap.Modal(document.getElementById('confirmModal')).show();
}

function confirmDeleteDataSource(dataSourceId) {
    currentAction = 'delete';
    currentDataSourceId = dataSourceId;
    
    document.getElementById('confirmModalTitle').textContent = '确认删除数据源';
    document.getElementById('confirmModalBody').innerHTML = `
        <p>确认要删除此数据源吗？</p>
        <div class="alert alert-danger small">
            <i class="fas fa-exclamation-triangle me-1"></i>
            删除后RFM系统中的相关数据将被清除，但不会影响原始的数据处理任务。
        </div>
    `;
    
    const confirmButton = document.getElementById('confirmButton');
    confirmButton.className = 'btn btn-danger';
    confirmButton.innerHTML = '<i class="fas fa-trash me-1"></i> 确认删除';
    
    new bootstrap.Modal(document.getElementById('confirmModal')).show();
}

// 确认按钮点击事件
document.getElementById('confirmButton').addEventListener('click', function() {
    if (currentAction === 'import') {
        importDataSource(currentTaskId);
    } else if (currentAction === 'delete') {
        deleteDataSource(currentDataSourceId);
    }
    
    bootstrap.Modal.getInstance(document.getElementById('confirmModal')).hide();
});

function importDataSource(taskId) {
    // 立即更新任务行状态为导入中
    updateTaskRowStatus(taskId, 'importing');
    updateTaskRowButtons(taskId, 'importing');
    updateTaskRowProgress(taskId, 'importing', 5); // 显示初始进度条
    updateTaskRowRecords(taskId, 'importing', 0); // 显示初始记录数

    // 显示进度对话框
    showProgressModal('导入数据源', '正在准备导入数据...');

    // 清理可能存在的旧轮询
    if (importingCounters.has(taskId)) {
        const oldCounter = importingCounters.get(taskId);
        if (oldCounter.interval) {
            clearInterval(oldCounter.interval);
        }
        importingCounters.delete(taskId);
    }

    // 使用真实进度API更新
    let dataSourceId = null;
    const progressInterval = setInterval(() => {
        // 如果还没有数据源ID，先等待
        if (!dataSourceId) {
            console.log('⏳ 等待数据源ID...');
            return;
        }

        console.log(`🔄 轮询进度API: /rfm/api/import-progress/${dataSourceId}`);
        // 获取真实进度
        fetch(`/rfm/api/import-progress/${dataSourceId}`)
            .then(response => response.json())
            .then(data => {
                console.log('📡 API响应:', data);
                if (data.success) {
                    const progress = data.progress_percent;
                    const importedCount = data.imported_records;
                    const totalCount = data.total_records;

                    // 更新弹出框和任务行进度
                    console.log(`📊 更新进度: ${progress}%, 记录数: ${importedCount}/${totalCount}`);
                    updateProgress(progress, `正在处理数据... 已导入 ${importedCount.toLocaleString()} / ${totalCount.toLocaleString()} 条记录`);
                    updateTaskRowProgress(taskId, 'importing', progress);
                    updateTaskRowRecords(taskId, 'importing', importedCount);
                    updateProgressBar(taskId, progress, 'importing', `导入中 ${Math.round(progress)}%`);

                    // 如果完成了，停止轮询
                    if (data.status === 'completed') {
                        clearInterval(progressInterval);
                    }
                }
            })
            .catch(error => {
                console.error('获取进度失败:', error);
            });
    }, 500); // 每500ms更新一次，更快响应

    fetch('/rfm/api/import-data-source', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            task_id: taskId
        })
    })
    .then(response => response.json())
    .then(data => {
        console.log('🎯 导入API响应:', data);
        if (data.success) {
            // 获取数据源ID，开始真实进度轮询
            dataSourceId = data.data_source?.id;
            console.log(`✅ 获取到数据源ID: ${dataSourceId}`);

            // 如果立即完成，处理结果
            if (data.data_source?.status === 'completed') {
                clearInterval(progressInterval);
                const finalRecords = data.data_source.imported_records;

                // 同步完成进度
                updateProgress(100, `导入完成！共导入 ${finalRecords.toLocaleString()} 条记录`);
                updateTaskRowProgress(taskId, 'importing', 100);
                updateTaskRowRecords(taskId, 'completed', finalRecords);
                updateProgressBar(taskId, 100, 'completed', '已完成');

            setTimeout(() => {
                hideProgressModal();

                showResultModal('success', '数据源导入成功', {
                    '任务名称': data.data_source?.source_task_name || '未知',
                    '导入记录数': finalRecords.toLocaleString(),
                    '数据源ID': data.data_source?.id || '未知',
                    '导入时间': new Date().toLocaleString()
                });

                // 立即刷新任务列表以获取最新状态
                loadTasks();
            }, 1000);
            }
        } else {
            hideProgressModal();

            // 立即更新任务行状态为失败
            updateTaskRowProgress(taskId, 'failed');
            updateTaskRowStatus(taskId, 'failed');
            updateTaskRowButtons(taskId, 'failed');
            updateTaskRowRecords(taskId, 'failed', 0); // 导入失败，记录数为0

            showResultModal('error', '导入失败', {
                '错误信息': data.message,
                '建议': '请检查数据文件格式是否正确，或联系管理员'
            });
        }
    })
    .catch(error => {
        clearInterval(progressInterval);
        hideProgressModal();

        // 更新任务行状态为失败
        updateTaskRowProgress(taskId, 'failed');
        updateTaskRowStatus(taskId, 'failed');
        updateTaskRowButtons(taskId, 'failed');
        updateTaskRowRecords(taskId, 'failed', 0); // 网络错误，记录数为0

        console.error('导入数据源失败:', error);
        showResultModal('error', '网络错误', {
            '错误信息': '网络连接失败，请稍后重试',
            '建议': '检查网络连接或刷新页面重试'
        });
    });
}

function deleteDataSource(dataSourceId) {
    // 找到对应的任务ID
    const taskId = findTaskIdByDataSourceId(dataSourceId);
    console.log('删除数据源 - 数据源ID:', dataSourceId, '任务ID:', taskId);

    // 获取当前记录数作为删除的起始数量
    let currentRecords = getCurrentRecordCount(taskId);

    // 立即更新任务行状态为删除中
    if (taskId) {
        console.log('更新任务行状态为删除中:', taskId);
        updateTaskRowStatus(taskId, 'deleting');
        updateTaskRowButtons(taskId, 'deleting');
        updateTaskRowProgress(taskId, 'deleting', 5); // 显示初始进度条
        updateTaskRowRecords(taskId, 'deleting', currentRecords); // 显示当前记录数
    } else {
        console.error('无法找到对应的任务ID，数据源ID:', dataSourceId);
        currentRecords = 1000; // 默认值
    }

    // 显示进度对话框
    showProgressModal('删除数据源', '正在准备删除数据...');

    // 清理可能存在的旧轮询
    if (taskId && importingCounters.has(taskId)) {
        const oldCounter = importingCounters.get(taskId);
        if (oldCounter.interval) {
            clearInterval(oldCounter.interval);
        }
        importingCounters.delete(taskId);
    }

    // 使用真实删除进度API
    const progressInterval = setInterval(() => {
        // 获取真实删除进度
        fetch(`/rfm/api/delete-progress/${dataSourceId}`)
            .then(response => {
                if (!response.ok) {
                    // 如果是404错误，说明删除已完成
                    if (response.status === 404) {
                        return {
                            success: true,
                            status: 'completed',
                            remaining_records: 0,
                            deleted_records: 0,
                            progress_percent: 100
                        };
                    }
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    const progress = data.progress_percent;
                    const remainingRecords = data.remaining_records;
                    const deletedRecords = data.deleted_records;
                    const totalRecords = data.total_records;

                    // 更新弹出框和任务行进度
                    updateProgress(progress, `正在清理数据... 已删除 ${deletedRecords.toLocaleString()} / ${totalRecords.toLocaleString()} 条记录`);
                    if (taskId) {
                        updateTaskRowProgress(taskId, 'deleting', progress);
                        updateTaskRowRecords(taskId, 'deleting', remainingRecords);
                        updateProgressBar(taskId, progress, 'deleting', `删除中 ${Math.round(progress)}%`);
                    }

                    // 如果完成了，停止轮询
                    if (data.status === 'completed' || remainingRecords === 0) {
                        clearInterval(progressInterval);
                        console.log('🎉 删除操作完成，停止轮询');
                    }
                }
            })
            .catch(error => {
                console.error('获取删除进度失败:', error);
            });
    }, 500); // 每500ms更新一次，更快响应

    fetch('/rfm/api/delete-data-source', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            data_source_id: dataSourceId
        })
    })
    .then(response => response.json())
    .then(data => {
        clearInterval(progressInterval);

        if (data.success) {
            // 删除操作已启动，等待真实进度完成
            // 进度轮询会自动处理完成状态

            // 监听删除完成
            const completionCheck = setInterval(() => {
                fetch(`/rfm/api/delete-progress/${dataSourceId}`)
                    .then(response => response.json())
                    .then(progressData => {
                        if (progressData.success && progressData.status === 'completed') {
                            clearInterval(completionCheck);
                            clearInterval(progressInterval);

                            // 显示完成状态
                            updateProgress(100, '删除完成！所有数据已清除');
                            const taskId = findTaskIdByDataSourceId(dataSourceId);
                            if (taskId) {
                                updateTaskRowProgress(taskId, 'deleting', 100);
                                updateTaskRowRecords(taskId, 'deleting', 0);
                                updateProgressBar(taskId, 100, 'completed', '已完成');
                            }

                            setTimeout(() => {
                                hideProgressModal();

                                showResultModal('success', '数据源删除成功', {
                                    '操作结果': '数据源已成功删除',
                                    '清除记录': `${currentRecords.toLocaleString()} 条记录已清除`,
                                    '影响': 'RFM系统中的相关数据已清除',
                                    '说明': '原始数据处理任务不受影响',
                                    '删除时间': new Date().toLocaleString()
                                });

                                // 立即刷新任务列表以获取最新状态
                                loadTasks();
                            }, 1000);
                        }
                    })
                    .catch(error => {
                        console.error('检查删除完成状态失败:', error);
                    });
            }, 2000); // 每2秒检查一次完成状态
        } else {
            hideProgressModal();

            // 更新任务行状态为失败，但保持已导入状态
            const taskId = findTaskIdByDataSourceId(dataSourceId);
            if (taskId) {
                updateTaskRowProgress(taskId, 'failed');
                updateTaskRowStatus(taskId, 'completed'); // 删除失败，恢复为已导入状态
                updateTaskRowButtons(taskId, 'completed', dataSourceId);
                updateTaskRowRecords(taskId, 'completed', currentRecords); // 恢复原始记录数
            }

            showResultModal('error', '删除失败', {
                '错误信息': data.message,
                '建议': '请稍后重试或联系管理员'
            });
        }
    })
    .catch(error => {
        clearInterval(progressInterval);
        hideProgressModal();

        // 更新任务行状态为失败，但保持已导入状态
        const taskId = findTaskIdByDataSourceId(dataSourceId);
        if (taskId) {
            updateTaskRowProgress(taskId, 'failed');
            updateTaskRowStatus(taskId, 'completed'); // 删除失败，恢复为已导入状态
            updateTaskRowButtons(taskId, 'completed', dataSourceId);
            updateTaskRowRecords(taskId, 'completed', currentRecords); // 恢复原始记录数
        }

        console.error('删除数据源失败:', error);
        showResultModal('error', '网络错误', {
            '错误信息': '网络连接失败，请稍后重试',
            '建议': '检查网络连接或刷新页面重试'
        });
    });
}

function refreshTasks() {
    loadTasks();
}

// 进度提示相关函数
function showProgressModal(title, message) {
    document.getElementById('progressModalTitle').innerHTML = `
        <i class="fas fa-cog fa-spin me-2"></i>
        ${title}
    `;
    document.getElementById('progressText').textContent = message;
    document.getElementById('progressPercent').textContent = '0%';
    document.getElementById('progressBar').style.width = '0%';
    document.getElementById('progressDetails').textContent = '请稍候，正在处理您的请求...';

    new bootstrap.Modal(document.getElementById('progressModal')).show();
}

function updateProgress(percent, message) {
    const progressBar = document.getElementById('progressBar');
    const progressPercent = document.getElementById('progressPercent');
    const progressText = document.getElementById('progressText');

    progressBar.style.width = `${percent}%`;
    progressPercent.textContent = `${Math.round(percent)}%`;
    progressText.textContent = message;

    if (percent >= 100) {
        progressBar.classList.remove('progress-bar-striped', 'progress-bar-animated');
        progressBar.classList.add('bg-success');
    }
}

function hideProgressModal() {
    const modal = bootstrap.Modal.getInstance(document.getElementById('progressModal'));
    if (modal) {
        modal.hide();
    }
}

function showResultModal(type, title, details) {
    const modal = document.getElementById('resultModal');
    const titleElement = document.getElementById('resultModalTitle');
    const bodyElement = document.getElementById('resultModalBody');

    // 设置标题和图标
    if (type === 'success') {
        titleElement.innerHTML = `
            <i class="fas fa-check-circle text-success me-2"></i>
            ${title}
        `;
    } else {
        titleElement.innerHTML = `
            <i class="fas fa-exclamation-triangle text-danger me-2"></i>
            ${title}
        `;
    }

    // 设置内容
    let bodyHtml = '';
    if (typeof details === 'object') {
        bodyHtml = '<dl class="row">';
        for (const [key, value] of Object.entries(details)) {
            bodyHtml += `
                <dt class="col-sm-4">${key}:</dt>
                <dd class="col-sm-8">${value}</dd>
            `;
        }
        bodyHtml += '</dl>';
    } else {
        bodyHtml = `<p>${details}</p>`;
    }

    bodyElement.innerHTML = bodyHtml;

    new bootstrap.Modal(modal).show();
}

// 兼容旧的函数名
function showLoading(message) {
    showProgressModal('处理中', message);
}

function hideLoading() {
    hideProgressModal();
}

function showSuccess(message) {
    showResultModal('success', '操作成功', message);
}

function showError(message) {
    showResultModal('error', '操作失败', message);
}

// 显示任务加载错误
function showTaskLoadError(message) {
    // 更新正向盘任务表格
    const forwardBody = document.getElementById('forwardTasksBody');
    if (forwardBody) {
        forwardBody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    ${message}
                    <br>
                    <button class="btn btn-outline-primary btn-sm mt-2" onclick="loadTasks()">
                        <i class="fas fa-redo me-1"></i> 重新加载
                    </button>
                </td>
            </tr>
        `;
    }

    // 更新结果盘任务表格
    const resultBody = document.getElementById('resultTasksBody');
    if (resultBody) {
        resultBody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    ${message}
                    <br>
                    <button class="btn btn-outline-primary btn-sm mt-2" onclick="loadTasks()">
                        <i class="fas fa-redo me-1"></i> 重新加载
                    </button>
                </td>
            </tr>
        `;
    }

    // 更新统计信息
    updateStatistics([]);
}

// 添加自定义样式
const customStyles = `
<style>
.progress-notification {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.modal-header .btn-close {
    opacity: 0.7;
}

.modal-header .btn-close:hover {
    opacity: 1;
}

#progressModal .modal-content {
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

#progressModal .modal-header {
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
}

#progressModal .progress {
    height: 8px;
    border-radius: 4px;
}

#progressModal .progress-bar {
    border-radius: 4px;
}
</style>
`;

// 将样式添加到页面
if (!document.getElementById('custom-progress-styles')) {
    const styleElement = document.createElement('div');
    styleElement.id = 'custom-progress-styles';
    styleElement.innerHTML = customStyles;
    document.head.appendChild(styleElement);
}

// 更新任务行的进度状态
function updateTaskRowProgress(taskId, status, progress = null) {
    console.log(`🔄 更新任务行进度: 任务ID=${taskId}, 状态=${status}, 进度=${progress}%`);
    const row = document.getElementById(`task-row-${taskId}`);
    if (!row) {
        console.error(`❌ 找不到任务行: task-row-${taskId}`);
        return;
    }

    const progressCell = row.children[3]; // 进度列是第4列（索引3）

    switch (status) {
        case 'importing':
            const importProgress = progress || 50;
            progressCell.innerHTML = `
                <div class="progress" style="height: 20px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-primary"
                         role="progressbar" style="width: ${importProgress}%" id="progress-bar-${taskId}">
                        导入中 ${Math.round(importProgress)}%
                    </div>
                </div>
            `;
            break;
        case 'completed':
            progressCell.innerHTML = `
                <div class="progress" style="height: 20px;">
                    <div class="progress-bar bg-success" role="progressbar" style="width: 100%">
                        已完成
                    </div>
                </div>
            `;
            break;
        case 'failed':
            progressCell.innerHTML = `
                <div class="progress" style="height: 20px;">
                    <div class="progress-bar bg-danger" role="progressbar" style="width: 100%">
                        导入失败
                    </div>
                </div>
            `;
            break;
        case 'deleting':
            const deleteProgress = progress || 70;
            progressCell.innerHTML = `
                <div class="progress" style="height: 20px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-warning"
                         role="progressbar" style="width: ${deleteProgress}%" id="progress-bar-${taskId}">
                        删除中 ${Math.round(deleteProgress)}%
                    </div>
                </div>
            `;
            break;
        default:
            progressCell.innerHTML = '<span class="text-muted">-</span>';
    }
}

// 更新任务行的操作按钮
function updateTaskRowButtons(taskId, status, dataSourceId = null) {
    const row = document.getElementById(`task-row-${taskId}`);
    if (!row) return;

    const actionCell = row.children[5]; // 操作列是第6列（索引5）

    switch (status) {
        case 'importing':
            actionCell.innerHTML = `
                <button class="btn btn-outline-warning btn-sm me-1" disabled>
                    <i class="fas fa-spinner fa-spin"></i> 导入中
                </button>
                <button class="btn btn-outline-secondary btn-sm" disabled>
                    <i class="fas fa-trash"></i> 删除数据
                </button>
            `;
            break;
        case 'deleting':
            actionCell.innerHTML = `
                <button class="btn btn-outline-secondary btn-sm me-1" disabled>
                    <i class="fas fa-check"></i> 已应用
                </button>
                <button class="btn btn-outline-warning btn-sm" disabled>
                    <i class="fas fa-spinner fa-spin"></i> 删除中
                </button>
            `;
            break;
        case 'completed':
        case true: // 兼容旧的布尔值调用
            actionCell.innerHTML = `
                <button class="btn btn-outline-primary btn-sm me-1" disabled>
                    <i class="fas fa-check"></i> 已应用
                </button>
                <button class="btn btn-outline-danger btn-sm" onclick="confirmDeleteDataSource(${dataSourceId})">
                    <i class="fas fa-trash"></i> 删除数据
                </button>
            `;
            break;
        case 'failed':
            actionCell.innerHTML = `
                <button class="btn btn-outline-success btn-sm me-1" onclick="confirmImportDataSource(${taskId}, '任务${taskId}')">
                    <i class="fas fa-redo"></i> 重新导入
                </button>
                <button class="btn btn-outline-secondary btn-sm" disabled>
                    <i class="fas fa-trash"></i> 删除数据
                </button>
            `;
            break;
        case 'pending':
        case false: // 兼容旧的布尔值调用
        default:
            actionCell.innerHTML = `
                <button class="btn btn-outline-success btn-sm me-1" onclick="confirmImportDataSource(${taskId}, '任务${taskId}')">
                    <i class="fas fa-download"></i> 应用数据
                </button>
                <button class="btn btn-outline-secondary btn-sm" disabled>
                    <i class="fas fa-trash"></i> 删除数据
                </button>
            `;
            break;
    }
}

// 更新任务行的状态标签
function updateTaskRowStatus(taskId, status) {
    console.log('更新任务行状态 - 任务ID:', taskId, '状态:', status);
    const row = document.getElementById(`task-row-${taskId}`);
    if (!row) {
        console.error('找不到任务行:', `task-row-${taskId}`);
        return;
    }

    const statusCell = row.children[2]; // 状态列是第3列（索引2）

    switch (status) {
        case 'importing':
            statusCell.innerHTML = '<span class="badge bg-warning">导入中</span>';
            break;
        case 'deleting':
            statusCell.innerHTML = '<span class="badge bg-danger">删除中</span>';
            break;
        case 'completed':
        case true: // 兼容旧的布尔值调用
            statusCell.innerHTML = '<span class="badge bg-success">已导入</span>';
            break;
        case 'failed':
            statusCell.innerHTML = '<span class="badge bg-danger">操作失败</span>';
            break;
        case 'pending':
        case false: // 兼容旧的布尔值调用
        default:
            statusCell.innerHTML = '<span class="badge bg-secondary">未导入</span>';
            break;
    }
}

// 根据数据源ID查找任务ID
function findTaskIdByDataSourceId(dataSourceId) {
    // 从当前任务列表中查找
    const rows = document.querySelectorAll('[id^="task-row-"]');
    for (const row of rows) {
        const deleteButton = row.querySelector(`[onclick*="confirmDeleteDataSource(${dataSourceId})"]`);
        if (deleteButton) {
            const taskId = row.id.replace('task-row-', '');
            return parseInt(taskId);
        }
    }

    // 如果上面的方法找不到，尝试从全局任务数据中查找
    if (window.currentTasksData) {
        for (const task of window.currentTasksData) {
            if (task.data_source && task.data_source.id == dataSourceId) {
                return task.id;
            }
        }
    }

    return null;
}

// 获取当前任务的记录数
function getCurrentRecordCount(taskId) {
    if (window.currentTasksData) {
        for (const task of window.currentTasksData) {
            if (task.id == taskId && task.data_source && task.data_source.imported_records) {
                return task.data_source.imported_records;
            }
        }
    }

    // 如果找不到，从页面中解析
    const row = document.getElementById(`task-row-${taskId}`);
    if (row) {
        const recordsCell = row.children[4]; // 记录数列是第5列（索引4）
        const recordsText = recordsCell.textContent.trim();
        const match = recordsText.match(/[\d,]+/);
        if (match) {
            return parseInt(match[0].replace(/,/g, ''));
        }
    }

    return 1000; // 默认值
}

// 注意：模拟计数函数已被移除，现在统一使用真实的数据库记录数

// 获取真实的进度百分比
function getRealProgressPercent(task) {
    if (!task.data_source) {
        return 0;
    }

    const importedRecords = task.data_source.imported_records || 0;
    const totalRecords = task.data_source.total_records || 1;

    if (task.data_source.status === 'importing') {
        // 导入进度：已导入记录数 / 总记录数
        const progress = Math.floor((importedRecords / totalRecords) * 100);
        return Math.min(Math.max(progress, 1), 99); // 1%-99%之间
    } else if (task.data_source.status === 'deleting') {
        // 删除进度：已删除记录数 / 总记录数
        const deletedRecords = totalRecords - importedRecords;
        const progress = Math.floor((deletedRecords / totalRecords) * 100);
        return Math.min(Math.max(progress, 1), 99); // 1%-99%之间
    } else if (task.data_source.status === 'completed') {
        return 100;
    } else if (task.data_source.status === 'failed') {
        return 100; // 失败也显示100%，但颜色不同
    }

    return 0;
}

// 更新进度条显示
function updateProgressBar(taskId, progress, status, text) {
    const progressBar = document.getElementById(`progress-bar-${taskId}`);
    if (!progressBar) return;

    // 更新进度条宽度
    progressBar.style.width = `${progress}%`;

    // 更新进度条文本
    progressBar.textContent = text || `${status} ${Math.round(progress)}%`;

    // 更新进度条颜色
    progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated';
    if (status === 'importing') {
        progressBar.classList.add('bg-primary');
    } else if (status === 'deleting') {
        progressBar.classList.add('bg-warning');
    } else if (status === 'completed') {
        progressBar.classList.remove('progress-bar-striped', 'progress-bar-animated');
        progressBar.classList.add('bg-success');
    } else if (status === 'failed') {
        progressBar.classList.remove('progress-bar-striped', 'progress-bar-animated');
        progressBar.classList.add('bg-danger');
    }
}

// 更新任务行的记录数显示
function updateTaskRowRecords(taskId, status, count) {
    console.log(`📊 更新任务行记录数: 任务ID=${taskId}, 状态=${status}, 记录数=${count}`);
    const row = document.getElementById(`task-row-${taskId}`);
    if (!row) {
        console.error(`❌ 找不到任务行: task-row-${taskId}`);
        return;
    }

    const recordsCell = row.children[4]; // 记录数列是第5列（索引4）

    switch (status) {
        case 'importing':
            recordsCell.innerHTML = `
                <span class="text-primary">
                    <i class="fas fa-arrow-up me-1"></i>
                    <span id="importing-count-${taskId}">${count.toLocaleString()}</span>
                </span>
            `;
            break;
        case 'deleting':
            recordsCell.innerHTML = `
                <span class="text-warning">
                    <i class="fas fa-arrow-down me-1"></i>
                    <span id="deleting-count-${taskId}">${count.toLocaleString()}</span>
                </span>
            `;
            break;
        case 'completed':
            recordsCell.innerHTML = count.toLocaleString();
            break;
        default:
            recordsCell.innerHTML = '-';
            break;
    }
}

// 进度对话框关闭时的处理
function onProgressModalClosed() {
    // 显示提示信息
    showProgressClosedNotification();
}

// 显示进度窗口关闭提示
function showProgressClosedNotification() {
    // 创建提示元素
    const notification = document.createElement('div');
    notification.className = 'alert alert-info alert-dismissible fade show position-fixed progress-notification';
    notification.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        max-width: 400px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;

    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-info-circle me-2"></i>
            <div>
                <strong>进度窗口已关闭</strong><br>
                <small>操作仍在后台继续进行，您可以在任务列表中查看进度</small>
            </div>
            <button type="button" class="btn-close ms-auto" data-bs-dismiss="alert"></button>
        </div>
    `;

    // 添加到页面
    document.body.appendChild(notification);

    // 5秒后自动移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// 启动真实进度轮询
function startRealProgressPolling(taskId, dataSourceId, operationType) {
    const interval = setInterval(() => {
        const apiUrl = operationType === 'importing'
            ? `/rfm/api/import-progress/${dataSourceId}`
            : `/rfm/api/delete-progress/${dataSourceId}`;

        fetch(apiUrl)
            .then(response => {
                if (!response.ok) {
                    // 如果是404错误，说明删除已完成
                    if (response.status === 404 && operationType === 'deleting') {
                        return {
                            success: true,
                            status: 'completed',
                            remaining_records: 0,
                            deleted_records: 0,
                            progress_percent: 100
                        };
                    }
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    const progress = data.progress_percent;

                    if (operationType === 'importing') {
                        const importedCount = data.imported_records;
                        const totalCount = data.total_records;

                        // 更新记录数和进度条
                        updateTaskRowRecords(taskId, 'importing', importedCount);
                        updateProgressBar(taskId, progress, 'importing', `导入中 ${Math.round(progress)}%`);

                        // 如果完成，停止轮询
                        if (data.status === 'completed') {
                            clearInterval(interval);
                            importingCounters.delete(taskId);
                            updateProgressBar(taskId, 100, 'completed', '已完成');
                            updateTaskRowRecords(taskId, 'completed', importedCount);
                        }
                    } else if (operationType === 'deleting') {
                        const remainingCount = data.remaining_records;

                        // 更新记录数和进度条
                        updateTaskRowRecords(taskId, 'deleting', remainingCount);
                        updateProgressBar(taskId, progress, 'deleting', `删除中 ${Math.round(progress)}%`);

                        // 如果完成，停止轮询
                        if (data.status === 'completed' || remainingCount === 0) {
                            clearInterval(interval);
                            importingCounters.delete(taskId);
                            updateProgressBar(taskId, 100, 'completed', '已完成');
                            updateTaskRowRecords(taskId, 'completed', 0);
                            console.log(`🎉 ${operationType}操作完成，停止轮询 - 任务ID: ${taskId}`);
                        }
                    }
                }
            })
            .catch(error => {
                console.error(`获取${operationType}进度失败:`, error);
            });
    }, 1000); // 每1秒轮询一次，更快响应

    // 保存轮询引用
    importingCounters.set(taskId, {
        interval: interval,
        type: operationType,
        dataSourceId: dataSourceId
    });
}

// 更新系统资源显示
function updateSystemResources() {
    fetch('/rfm/api/system-resources')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const resources = data.resources;
                const stats = data.performance_stats;

                // 更新CPU使用率
                const cpuPercent = resources.cpu_percent || 0;
                document.getElementById('cpuProgress').style.width = `${cpuPercent}%`;
                document.getElementById('cpuText').textContent = `${cpuPercent.toFixed(1)}%`;

                // 根据使用率设置颜色
                const cpuBar = document.getElementById('cpuProgress');
                cpuBar.className = 'progress-bar';
                if (cpuPercent > 80) {
                    cpuBar.classList.add('bg-danger');
                } else if (cpuPercent > 60) {
                    cpuBar.classList.add('bg-warning');
                } else {
                    cpuBar.classList.add('bg-info');
                }

                // 更新内存使用率
                const memoryPercent = resources.memory_percent || 0;
                document.getElementById('memoryProgress').style.width = `${memoryPercent}%`;
                document.getElementById('memoryText').textContent = `${memoryPercent.toFixed(1)}%`;

                // 根据使用率设置颜色
                const memoryBar = document.getElementById('memoryProgress');
                memoryBar.className = 'progress-bar';
                if (memoryPercent > 80) {
                    memoryBar.classList.add('bg-danger');
                } else if (memoryPercent > 60) {
                    memoryBar.classList.add('bg-warning');
                } else {
                    memoryBar.classList.add('bg-success');
                }

                // 更新批次大小和延迟（如果有性能统计）
                if (stats && stats.avg_batch_size) {
                    document.getElementById('batchSizeText').textContent = Math.round(stats.avg_batch_size);
                } else {
                    // 显示默认批次大小
                    document.getElementById('batchSizeText').textContent = '50';
                }

                // 显示延迟信息（可以根据当前资源状况计算）
                const delayElement = document.getElementById('delayText');
                if (delayElement) {
                    const avgUsage = (cpuPercent + memoryPercent) / 2;
                    let estimatedDelay;
                    if (avgUsage > 80) {
                        estimatedDelay = '100-500ms';
                    } else if (avgUsage > 60) {
                        estimatedDelay = '50-100ms';
                    } else {
                        estimatedDelay = '10-50ms';
                    }
                    delayElement.textContent = estimatedDelay;
                }

                // 检查psutil可用性（从resources对象检查）
                if (!resources.available) {
                    document.getElementById('cpuText').textContent = '不可用';
                    document.getElementById('memoryText').textContent = '不可用';
                }
            }
        })
        .catch(error => {
            console.error('获取系统资源失败:', error);
        });
}

// 启动系统资源监控
function startSystemResourceMonitoring() {
    // 立即更新一次
    updateSystemResources();

    // 每3秒更新一次
    setInterval(updateSystemResources, 3000);
}
</script>
{% endblock %}
