{% extends "base.html" %}

{% block title %}批量导入渠道 - 老客回访与跟进系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-upload"></i> 批量导入渠道</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('channel.index') }}">渠道管理</a></li>
                <li class="breadcrumb-item active">批量导入</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-file-excel"></i> 上传Excel文件</h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="file" class="form-label">选择Excel文件 <span class="text-danger">*</span></label>
                        <input type="file" class="form-control" id="file" name="file" 
                               accept=".xlsx,.xls" required>
                        <div class="form-text">支持.xlsx和.xls格式的Excel文件</div>
                    </div>
                    
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> 导入说明</h6>
                        <ul class="mb-0">
                            <li>Excel文件必须包含"渠道分类"和"渠道名称"两列</li>
                            <li>如果存在相同的渠道分类+渠道名称，将跳过该条记录</li>
                            <li>渠道分类最多100个字符，渠道名称最多200个字符</li>
                            <li>空行将被自动跳过</li>
                            <li>导入完成后会显示成功、跳过和错误的记录数量</li>
                        </ul>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('channel.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                        <a href="{{ url_for('channel.download_template') }}" class="btn btn-info">
                            <i class="fas fa-download"></i> 下载模板
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-upload"></i> 开始导入
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-table"></i> Excel格式要求</h6>
            </div>
            <div class="card-body">
                <p class="small">Excel文件应包含以下列：</p>
                <table class="table table-sm table-bordered">
                    <thead>
                        <tr>
                            <th>渠道分类</th>
                            <th>渠道名称</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>线上推广</td>
                            <td>百度推广</td>
                        </tr>
                        <tr>
                            <td>线下活动</td>
                            <td>地推活动</td>
                        </tr>
                        <tr>
                            <td>老客推荐</td>
                            <td>老客户介绍</td>
                        </tr>
                    </tbody>
                </table>
                
                <div class="d-grid">
                    <a href="{{ url_for('channel.download_template') }}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-download"></i> 下载标准模板
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-exclamation-triangle"></i> 注意事项</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li><i class="fas fa-check text-success"></i> 导入前请备份现有数据</li>
                    <li><i class="fas fa-check text-success"></i> 确保Excel文件格式正确</li>
                    <li><i class="fas fa-check text-success"></i> 重复的渠道会被自动跳过</li>
                    <li><i class="fas fa-check text-success"></i> 导入过程中请勿关闭页面</li>
                    <li><i class="fas fa-check text-success"></i> 大文件导入可能需要较长时间</li>
                </ul>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-lightbulb"></i> 导入技巧</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li><i class="fas fa-star text-warning"></i> 建议先下载模板文件</li>
                    <li><i class="fas fa-star text-warning"></i> 按模板格式整理数据</li>
                    <li><i class="fas fa-star text-warning"></i> 检查数据完整性和准确性</li>
                    <li><i class="fas fa-star text-warning"></i> 分批导入大量数据</li>
                    <li><i class="fas fa-star text-warning"></i> 导入后检查结果统计</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 文件选择验证
document.getElementById('file').addEventListener('change', function() {
    const file = this.files[0];
    if (file) {
        const fileName = file.name.toLowerCase();
        const validExtensions = ['.xlsx', '.xls'];
        const isValid = validExtensions.some(ext => fileName.endsWith(ext));
        
        if (!isValid) {
            alert('请选择Excel文件（.xlsx或.xls格式）');
            this.value = '';
            return;
        }
        
        // 检查文件大小（限制为10MB）
        const maxSize = 10 * 1024 * 1024; // 10MB
        if (file.size > maxSize) {
            alert('文件大小不能超过10MB');
            this.value = '';
            return;
        }
        
        console.log('文件选择成功:', file.name, '大小:', (file.size / 1024 / 1024).toFixed(2) + 'MB');
    }
});

// 表单提交前确认
document.querySelector('form').addEventListener('submit', function(e) {
    const fileInput = document.getElementById('file');
    if (!fileInput.files[0]) {
        e.preventDefault();
        alert('请选择要上传的Excel文件');
        return;
    }
    
    // 显示加载提示
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 正在导入...';
    submitBtn.disabled = true;
});
</script>
{% endblock %}
