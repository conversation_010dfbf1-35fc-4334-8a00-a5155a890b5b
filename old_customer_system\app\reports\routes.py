# -*- coding: utf-8 -*-
"""
统计报表路由
"""

import os
from datetime import datetime, date
from flask import render_template, redirect, url_for, flash, request, jsonify, send_file
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from app.reports import bp
from app.forms.upload_forms import VisitRecordUploadForm, ConsumptionRecordUploadForm, StatisticsQueryForm
from app.services.upload_service import UploadService
from app.services.statistics_service import StatisticsService
from app.services.smart_upload_service import SmartUploadService
from app.services.permission_service import require_permission, require_any_permission
from app.models import VisitRecord, ConsumptionRecord, UploadHistory
from app import db
from sqlalchemy import text


@bp.route('/dashboard')
@login_required
def dashboard():
    """报表仪表板"""
    # 获取最近7天的统计数据
    from datetime import timedelta
    end_date = date.today()
    start_date = end_date - timedelta(days=6)

    # 获取总体统计
    total_stats = StatisticsService.get_follow_up_statistics()

    # 获取最近7天的每日统计
    daily_stats = StatisticsService.get_daily_statistics(start_date, end_date)

    # 获取数据验证信息
    validation_summary = SmartUploadService.get_data_validation_summary()

    return render_template('reports/dashboard.html',
                         title='统计报表',
                         total_stats=total_stats,
                         daily_stats=daily_stats,
                         validation_summary=validation_summary)


@bp.route('/clear-data')
@login_required
@require_permission('upload_data')
def clear_data_page():
    """数据清除页面"""
    return render_template('reports/clear_data.html', title='数据清除')


@bp.route('/clear-data/confirm', methods=['POST'])
@login_required
@require_permission('upload_data')
def clear_data_confirm():
    """确认清除数据"""
    try:
        clear_type = request.form.get('clear_type')

        if clear_type == 'all':
            # 清除所有数据
            clear_result = clear_all_data()
            flash(f'✅ 已清除所有数据: {clear_result}', 'success')

        elif clear_type == 'consumption':
            # 只清除消费记录
            clear_result = clear_consumption_data()
            flash(f'✅ 已清除消费记录: {clear_result}', 'success')

        elif clear_type == 'visit':
            # 只清除到院记录
            clear_result = clear_visit_data()
            flash(f'✅ 已清除到院记录: {clear_result}', 'success')

        else:
            flash('❌ 无效的清除类型', 'error')

    except Exception as e:
        flash(f'❌ 清除数据失败: {str(e)}', 'error')

    return redirect(url_for('reports.dashboard'))


def clear_all_data():
    """清除所有数据"""
    try:
        # 获取清除前的统计
        consumption_count = db.session.query(ConsumptionRecord).count()
        visit_count = db.session.query(VisitRecord).count()
        upload_count = db.session.query(UploadHistory).count()

        # 清除数据（按依赖关系顺序）
        db.session.execute(text('DELETE FROM upload_history'))
        db.session.execute(text('DELETE FROM consumption_records'))
        db.session.execute(text('DELETE FROM visit_records'))

        # 重置自增ID
        db.session.execute(text('ALTER TABLE consumption_records AUTO_INCREMENT = 1'))
        db.session.execute(text('ALTER TABLE visit_records AUTO_INCREMENT = 1'))
        db.session.execute(text('ALTER TABLE upload_history AUTO_INCREMENT = 1'))

        db.session.commit()

        return f"消费记录{consumption_count}条，到院记录{visit_count}条，上传历史{upload_count}条"

    except Exception as e:
        db.session.rollback()
        raise e


def clear_consumption_data():
    """清除消费记录数据"""
    try:
        # 获取清除前的统计
        consumption_count = db.session.query(ConsumptionRecord).count()

        # 清除消费相关的上传历史
        db.session.execute(text("DELETE FROM upload_history WHERE upload_type = 'consumption'"))

        # 清除消费记录
        db.session.execute(text('DELETE FROM consumption_records'))

        # 重置自增ID
        db.session.execute(text('ALTER TABLE consumption_records AUTO_INCREMENT = 1'))

        db.session.commit()

        return f"消费记录{consumption_count}条"

    except Exception as e:
        db.session.rollback()
        raise e


def clear_visit_data():
    """清除到院记录数据"""
    try:
        # 获取清除前的统计
        visit_count = db.session.query(VisitRecord).count()

        # 清除到院相关的上传历史
        db.session.execute(text("DELETE FROM upload_history WHERE upload_type = 'visit'"))

        # 清除到院记录
        db.session.execute(text('DELETE FROM visit_records'))

        # 重置自增ID
        db.session.execute(text('ALTER TABLE visit_records AUTO_INCREMENT = 1'))

        db.session.commit()

        return f"到院记录{visit_count}条"

    except Exception as e:
        db.session.rollback()
        raise e


@bp.route('/upload/visit-records', methods=['GET', 'POST'])
@login_required
@require_permission('upload_data')
def upload_visit_records():
    """上传到院明细"""
    form = VisitRecordUploadForm()

    if form.validate_on_submit():
        try:
            file = form.file.data
            file_type = form.file_type.data

            # 上传数据
            success_count, errors = UploadService.upload_visit_records(file, file_type, current_user)

            if success_count > 0:
                flash(f'成功上传 {success_count} 条到院记录', 'success')
                if errors:
                    flash(f'部分数据上传失败，共 {len(errors)} 条错误', 'warning')
            else:
                flash('上传失败，请检查文件格式和数据', 'error')

            # 显示错误详情
            for error in errors[:10]:  # 只显示前10个错误
                flash(error, 'error')

            if len(errors) > 10:
                flash(f'还有 {len(errors) - 10} 个错误未显示', 'warning')

        except Exception as e:
            flash(f'上传失败: {str(e)}', 'error')

    return render_template('reports/upload_visit_records.html',
                         form=form, title='上传到院明细')


@bp.route('/upload/consumption-records', methods=['GET', 'POST'])
@login_required
@require_permission('upload_data')
def upload_consumption_records():
    """上传消费明细"""
    form = ConsumptionRecordUploadForm()

    if form.validate_on_submit():
        try:
            file = form.file.data
            file_type = form.file_type.data

            # 上传数据
            success_count, errors = UploadService.upload_consumption_records(file, file_type, current_user)

            if success_count > 0:
                flash(f'成功上传 {success_count} 条消费记录', 'success')
                if errors:
                    flash(f'部分数据上传失败，共 {len(errors)} 条错误', 'warning')
            else:
                flash('上传失败，请检查文件格式和数据', 'error')

            # 显示错误详情
            for error in errors[:10]:  # 只显示前10个错误
                flash(error, 'error')

            if len(errors) > 10:
                flash(f'还有 {len(errors) - 10} 个错误未显示', 'warning')

        except Exception as e:
            flash(f'上传失败: {str(e)}', 'error')

    return render_template('reports/upload_consumption_records.html',
                         form=form, title='上传消费明细')


@bp.route('/statistics', methods=['GET', 'POST'])
@login_required
@require_any_permission('view_all_statistics', 'view_department_statistics', 'view_own_statistics')
def statistics():
    """统计报表"""
    form = StatisticsQueryForm()
    statistics_data = None

    if form.validate_on_submit():
        # 解析查询条件
        start_date = None
        end_date = None

        if form.start_date.data and form.start_date.data != '0':
            start_date = datetime.strptime(form.start_date.data, '%Y-%m-%d').date()

        if form.end_date.data and form.end_date.data != '0':
            end_date = datetime.strptime(form.end_date.data, '%Y-%m-%d').date()

        onsite_consultant_id = form.onsite_consultant_id.data if form.onsite_consultant_id.data > 0 else None
        online_consultant_id = form.online_consultant_id.data if form.online_consultant_id.data > 0 else None
        channel_id = form.channel_id.data if form.channel_id.data > 0 else None

        # 获取统计数据
        statistics_data = StatisticsService.get_follow_up_statistics(
            start_date=start_date,
            end_date=end_date,
            onsite_consultant_id=onsite_consultant_id,
            online_consultant_id=online_consultant_id,
            channel_id=channel_id
        )

    return render_template('reports/statistics.html',
                         form=form,
                         statistics_data=statistics_data,
                         title='统计报表')


@bp.route('/consultant-statistics')
@login_required
@require_any_permission('view_all_statistics', 'view_department_statistics', 'view_own_statistics')
def consultant_statistics():
    """现场咨询师统计报表"""
    # 获取查询参数
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    # 解析日期
    start_date_obj = None
    end_date_obj = None

    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
        except ValueError:
            flash('开始日期格式错误', 'error')

    if end_date:
        try:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
        except ValueError:
            flash('结束日期格式错误', 'error')

    # 获取现场咨询师统计数据
    consultant_data = StatisticsService.get_onsite_consultant_statistics(
        start_date=start_date_obj,
        end_date=end_date_obj
    )

    return render_template('reports/consultant_statistics.html',
                         consultant_data=consultant_data,
                         start_date=start_date,
                         end_date=end_date,
                         title='现场咨询师统计')


@bp.route('/consultant-statistics/export')
@login_required
@require_any_permission('view_all_statistics', 'view_department_statistics', 'view_own_statistics')
def export_consultant_statistics():
    """导出现场咨询师统计报表"""
    # 获取查询参数
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    # 解析日期
    start_date_obj = None
    end_date_obj = None

    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
        except ValueError:
            flash('开始日期格式错误', 'error')
            return redirect(url_for('reports.consultant_statistics'))

    if end_date:
        try:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
        except ValueError:
            flash('结束日期格式错误', 'error')
            return redirect(url_for('reports.consultant_statistics'))

    # 获取现场咨询师统计数据
    consultant_data = StatisticsService.get_onsite_consultant_statistics(
        start_date=start_date_obj,
        end_date=end_date_obj
    )

    # 导出Excel
    try:
        excel_file = StatisticsService.export_onsite_consultant_statistics_to_excel(consultant_data)

        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'现场咨询师统计报表_{timestamp}.xlsx'

        return send_file(excel_file,
                        as_attachment=True,
                        download_name=filename,
                        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

    except Exception as e:
        flash(f'导出失败: {str(e)}', 'error')
        return redirect(url_for('reports.consultant_statistics'))


@bp.route('/statistics/export')
@login_required
@require_any_permission('view_all_statistics', 'view_department_statistics', 'view_own_statistics')
def export_statistics():
    """导出统计报表"""
    # 获取查询参数
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    onsite_consultant_id = request.args.get('onsite_consultant_id', type=int)
    online_consultant_id = request.args.get('online_consultant_id', type=int)
    channel_id = request.args.get('channel_id', type=int)

    # 解析日期
    start_date_obj = None
    end_date_obj = None

    if start_date and start_date != '0':
        start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()

    if end_date and end_date != '0':
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()

    # 获取统计数据
    statistics_data = StatisticsService.get_follow_up_statistics(
        start_date=start_date_obj,
        end_date=end_date_obj,
        onsite_consultant_id=onsite_consultant_id if onsite_consultant_id and onsite_consultant_id > 0 else None,
        online_consultant_id=online_consultant_id if online_consultant_id and online_consultant_id > 0 else None,
        channel_id=channel_id if channel_id and channel_id > 0 else None
    )

    # 导出Excel
    try:
        excel_file = StatisticsService.export_statistics_to_excel(statistics_data)

        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'跟进统计报表_{timestamp}.xlsx'

        return send_file(excel_file,
                        as_attachment=True,
                        download_name=filename,
                        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

    except Exception as e:
        flash(f'导出失败: {str(e)}', 'error')
        return redirect(url_for('reports.statistics'))


@bp.route('/smart-upload/visit-records', methods=['GET', 'POST'])
@login_required
@require_permission('upload_data')
def smart_upload_visit_records():
    """智能上传到院明细"""
    # 获取上传建议
    suggestion = SmartUploadService.get_upload_suggestion(current_user.id, 'visit')

    if request.method == 'POST':
        try:
            # 检查文件
            if 'file' not in request.files:
                flash('请选择文件', 'error')
                return redirect(request.url)

            file = request.files['file']
            if file.filename == '':
                flash('请选择文件', 'error')
                return redirect(request.url)

            upload_mode = request.form.get('upload_mode', 'smart_incremental')

            # 根据模式选择上传方法
            if upload_mode == 'smart_incremental':
                result = SmartUploadService.smart_incremental_upload(
                    file, 'visit', current_user, file.filename
                )
            else:  # batch_upsert
                result = SmartUploadService.batch_upsert_upload(
                    file, 'visit', current_user, file.filename
                )

            if result['success']:
                flash(result['message'], 'success')
                # 显示详细信息
                details = result['details']
                if details.get('errors'):
                    for error in details['errors'][:5]:  # 只显示前5个错误
                        flash(error, 'warning')
                    if len(details['errors']) > 5:
                        flash(f'还有 {len(details["errors"]) - 5} 个错误未显示', 'info')

                # 显示验证信息
                validation_info = details.get('validation_info', {})
                if validation_info:
                    # 显示数据验证信息
                    if validation_info.get('visit_summary'):
                        flash(validation_info['visit_summary'], 'info')
                    if validation_info.get('latest_time_display'):
                        flash(validation_info['latest_time_display'], 'info')
                    if validation_info.get('earliest_time_display'):
                        flash(validation_info['earliest_time_display'], 'info')

                # 刷新建议
                suggestion = SmartUploadService.get_upload_suggestion(current_user.id, 'visit')
            else:
                flash(result['message'], 'error')

        except Exception as e:
            flash(f'上传失败: {str(e)}', 'error')

    return render_template('reports/smart_upload_visit.html',
                         suggestion=suggestion,
                         title='智能上传到院明细')


@bp.route('/smart-upload/consumption-records', methods=['GET', 'POST'])
@login_required
@require_permission('upload_data')
def smart_upload_consumption_records():
    """智能上传消费明细"""
    # 获取上传建议
    suggestion = SmartUploadService.get_upload_suggestion(current_user.id, 'consumption')

    if request.method == 'POST':
        try:
            # 检查文件
            if 'file' not in request.files:
                flash('请选择文件', 'error')
                return redirect(request.url)

            file = request.files['file']
            if file.filename == '':
                flash('请选择文件', 'error')
                return redirect(request.url)

            upload_mode = request.form.get('upload_mode', 'smart_incremental')

            # 根据模式选择上传方法
            if upload_mode == 'smart_incremental':
                result = SmartUploadService.smart_incremental_upload(
                    file, 'consumption', current_user, file.filename
                )
            else:  # batch_upsert
                result = SmartUploadService.batch_upsert_upload(
                    file, 'consumption', current_user, file.filename
                )

            if result['success']:
                flash(result['message'], 'success')
                # 显示详细信息
                details = result['details']
                if details.get('errors'):
                    for error in details['errors'][:5]:  # 只显示前5个错误
                        flash(error, 'warning')
                    if len(details['errors']) > 5:
                        flash(f'还有 {len(details["errors"]) - 5} 个错误未显示', 'info')

                # 显示验证信息
                validation_info = details.get('validation_info', {})
                if validation_info:
                    # 显示数据验证信息
                    if validation_info.get('amount_summary'):
                        flash(validation_info['amount_summary'], 'info')
                    if validation_info.get('latest_time_display'):
                        flash(validation_info['latest_time_display'], 'info')
                    if validation_info.get('earliest_time_display'):
                        flash(validation_info['earliest_time_display'], 'info')

                # 刷新建议
                suggestion = SmartUploadService.get_upload_suggestion(current_user.id, 'consumption')
            else:
                flash(result['message'], 'error')

        except Exception as e:
            flash(f'上传失败: {str(e)}', 'error')

    return render_template('reports/smart_upload_consumption.html',
                         suggestion=suggestion,
                         title='智能上传消费明细')