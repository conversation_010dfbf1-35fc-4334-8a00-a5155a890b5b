#!/usr/bin/env python3
"""
渠道管理功能测试脚本
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.customer import Channel
from app.models.user import User

def test_channel_operations():
    """测试渠道管理基本操作"""
    app = create_app()
    
    with app.app_context():
        print("=== 渠道管理功能测试 ===")
        
        # 1. 测试查询现有渠道
        print("\n1. 查询现有渠道:")
        channels = Channel.query.all()
        print(f"   当前渠道数量: {len(channels)}")
        
        for channel in channels:
            print(f"   - {channel.channel_category}: {channel.channel_name}")
        
        # 2. 测试创建新渠道
        print("\n2. 测试创建新渠道:")
        admin_user = User.query.filter_by(username='admin').first()
        
        if admin_user:
            # 检查是否已存在测试渠道
            test_channel = Channel.query.filter_by(
                channel_category='测试分类',
                channel_name='测试渠道'
            ).first()
            
            if not test_channel:
                new_channel = Channel(
                    channel_category='测试分类',
                    channel_name='测试渠道',
                    created_by=admin_user.id
                )
                db.session.add(new_channel)
                db.session.commit()
                print("   ✅ 测试渠道创建成功")
            else:
                print("   ℹ️  测试渠道已存在")
        else:
            print("   ❌ 未找到admin用户")
        
        # 3. 测试渠道分类统计
        print("\n3. 渠道分类统计:")
        categories = db.session.query(Channel.channel_category).distinct().all()
        for cat in categories:
            count = Channel.query.filter_by(channel_category=cat[0]).count()
            print(f"   - {cat[0]}: {count} 个渠道")
        
        # 4. 测试渠道状态
        print("\n4. 渠道状态统计:")
        active_count = Channel.query.filter_by(is_active=True).count()
        inactive_count = Channel.query.filter_by(is_active=False).count()
        print(f"   - 启用: {active_count} 个")
        print(f"   - 停用: {inactive_count} 个")
        
        print("\n=== 测试完成 ===")

def test_channel_import_format():
    """测试渠道导入数据格式"""
    print("\n=== 测试渠道导入格式 ===")
    
    # 模拟Excel数据
    import pandas as pd
    
    sample_data = {
        '渠道分类': ['线上推广', '线下活动', '老客推荐', '其他渠道'],
        '渠道名称': ['抖音推广', '社区活动', '员工推荐', '自然到店']
    }
    
    df = pd.DataFrame(sample_data)
    print("示例导入数据:")
    print(df.to_string(index=False))
    
    # 验证数据格式
    required_columns = ['渠道分类', '渠道名称']
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if not missing_columns:
        print("✅ 数据格式验证通过")
        
        # 验证数据长度
        for index, row in df.iterrows():
            category = str(row['渠道分类']).strip()
            name = str(row['渠道名称']).strip()
            
            if len(category) > 100:
                print(f"❌ 第{index+2}行渠道分类过长: {len(category)} > 100")
            elif len(name) > 200:
                print(f"❌ 第{index+2}行渠道名称过长: {len(name)} > 200")
            else:
                print(f"✅ 第{index+2}行数据格式正确")
    else:
        print(f"❌ 缺少必要列: {missing_columns}")

if __name__ == '__main__':
    try:
        test_channel_operations()
        test_channel_import_format()
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
