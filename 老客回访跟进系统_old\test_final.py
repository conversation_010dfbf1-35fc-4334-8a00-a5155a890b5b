#!/usr/bin/env python3
"""
最终测试脚本
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.user import User, Role
from app.models.customer import Channel
from config import get_config
import requests
import time

def test_complete_functionality():
    """完整功能测试"""
    print("完整功能测试")
    print("=" * 50)
    
    # 1. 测试数据库连接和数据
    print("1. 测试数据库连接和数据...")
    config = get_config()
    app = create_app(config)
    
    with app.app_context():
        try:
            # 检查渠道数据
            channels = Channel.query.filter_by(is_active=True).count()
            print(f"✅ 活跃渠道数量: {channels}")
            
            # 检查现场顾问
            field_role = Role.query.filter_by(role_code='FIELD_CONSULTANT').first()
            if field_role:
                consultants = User.query.filter_by(role_id=field_role.id, is_active=True).count()
                print(f"✅ 现场顾问数量: {consultants}")
            
            # 检查网络咨询员
            network_role = Role.query.filter_by(role_code='NETWORK_CONSULTANT').first()
            if network_role:
                registrars = User.query.filter_by(role_id=network_role.id, is_active=True).count()
                print(f"✅ 网络咨询员数量: {registrars}")
                
        except Exception as e:
            print(f"❌ 数据库测试失败: {e}")
            return False
    
    # 2. 测试拼音功能
    print("\n2. 测试拼音功能...")
    try:
        from app.utils.pinyin_utils import get_pinyin_first_letter, match_search_term
        
        test_cases = [
            ('百度推广', 'BDTG'),
            ('线上推广', 'XSTG'),
            ('现场咨询', 'XCZX'),
            ('网络部', 'WLB'),
        ]
        
        for text, expected in test_cases:
            result = get_pinyin_first_letter(text)
            print(f"'{text}' -> '{result}' (期望: '{expected}')")
        
        # 测试搜索匹配
        search_cases = [
            ('百度推广', 'bd', True),
            ('百度推广', 'tg', True),
            ('现场咨询', 'xc', True),
            ('网络部', 'wl', True),
        ]
        
        for text, search, expected in search_cases:
            result = match_search_term(text, search)
            status = "✅" if result == expected else "❌"
            print(f"{status} '{text}' 匹配 '{search}': {result}")
            
    except Exception as e:
        print(f"❌ 拼音功能测试失败: {e}")
        return False
    
    print("\n✅ 所有测试通过！")
    return True

def test_web_interface():
    """测试Web界面"""
    print("\n3. 测试Web界面...")
    
    # 检查系统是否在运行
    try:
        response = requests.get('http://localhost:5000', timeout=5)
        if response.status_code == 200:
            print("✅ 系统正在运行")
        else:
            print(f"⚠️ 系统响应异常: {response.status_code}")
    except requests.exceptions.RequestException:
        print("❌ 系统未运行，请先启动系统")
        return False
    
    # 测试API接口
    try:
        session = requests.Session()
        
        # 登录
        login_data = {'username': 'admin', 'password': 'admin123'}
        login_response = session.post('http://localhost:5000/auth/login', data=login_data)
        
        if login_response.status_code == 200 or login_response.status_code == 302:
            print("✅ 登录成功")
            
            # 测试渠道API
            channels_response = session.get('http://localhost:5000/api/channels')
            if channels_response.status_code == 200:
                data = channels_response.json()
                if data.get('success') and data.get('data'):
                    print(f"✅ 渠道API正常，返回 {len(data['data'])} 个渠道")
                else:
                    print("❌ 渠道API返回数据异常")
            else:
                print(f"❌ 渠道API请求失败: {channels_response.status_code}")
            
            # 测试现场顾问API
            consultants_response = session.get('http://localhost:5000/api/consultants')
            if consultants_response.status_code == 200:
                data = consultants_response.json()
                if data.get('success') and data.get('data'):
                    print(f"✅ 现场顾问API正常，返回 {len(data['data'])} 个顾问")
                else:
                    print("❌ 现场顾问API返回数据异常")
            else:
                print(f"❌ 现场顾问API请求失败: {consultants_response.status_code}")
                
        else:
            print(f"❌ 登录失败: {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Web接口测试失败: {e}")
        return False
    
    return True

def print_usage_instructions():
    """打印使用说明"""
    print("\n" + "=" * 60)
    print("🎉 分配现场顾问和激活渠道搜索功能修复完成！")
    print("=" * 60)
    
    print("\n📋 功能特点:")
    print("✅ 支持中文搜索：直接输入中文名称")
    print("✅ 支持拼音首字母搜索：输入拼音首字母快速定位")
    print("✅ 实时搜索：输入时自动搜索，无需点击按钮")
    print("✅ 智能匹配：支持部分匹配和模糊搜索")
    
    print("\n🔧 修复内容:")
    print("1. 修复了jQuery缺失导致的Select2无法初始化问题")
    print("2. 移除了AJAX Select2中不必要的matcher函数")
    print("3. 补充了拼音字典中缺失的汉字")
    print("4. 优化了API接口的错误处理")
    
    print("\n📖 使用方法:")
    print("1. 访问 http://localhost:5000")
    print("2. 使用管理员账号登录（admin/admin123）")
    print("3. 进入客户登记页面或客户管理页面")
    print("4. 在选择框中输入中文或拼音首字母进行搜索")
    
    print("\n🔍 搜索示例:")
    print("• 渠道搜索：")
    print("  - 输入'百度'或'bd'可以找到百度推广")
    print("  - 输入'推广'或'tg'可以找到所有推广渠道")
    print("• 顾问搜索：")
    print("  - 输入顾问姓名或拼音首字母")
    print("  - 输入部门名称或拼音首字母")
    
    print("\n⚠️ 注意事项:")
    print("• 确保系统正在运行")
    print("• 确保已登录系统")
    print("• 如果搜索无结果，请检查数据库中是否有相应数据")
    
    print("\n🆘 故障排除:")
    print("• 如果选择框无法搜索，请检查浏览器控制台是否有JavaScript错误")
    print("• 如果API返回404，请检查路由配置")
    print("• 如果搜索结果不准确，请检查拼音字典配置")

if __name__ == "__main__":
    success = test_complete_functionality()
    
    if success:
        web_success = test_web_interface()
        print_usage_instructions()
        
        if web_success:
            print(f"\n🎊 所有功能测试通过！现在可以使用搜索功能了。")
        else:
            print(f"\n⚠️ 数据库功能正常，但Web界面需要检查。请确保系统正在运行。")
    else:
        print(f"\n❌ 基础功能测试失败，请检查系统配置。")
