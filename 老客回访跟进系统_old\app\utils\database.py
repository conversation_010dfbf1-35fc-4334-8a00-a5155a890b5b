"""
数据库管理工具
"""
import pymysql
from flask import current_app
from sqlalchemy import text
from app import db

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.connection = None
    
    def test_connection(self, host, port, user, password, database=None):
        """测试数据库连接"""
        try:
            connection = pymysql.connect(
                host=host,
                port=port,
                user=user,
                password=password,
                database=database,
                charset='utf8mb4',
                autocommit=True
            )
            connection.close()
            return True, "连接成功"
        except Exception as e:
            return False, f"连接失败: {str(e)}"
    
    def create_database(self, host, port, user, password, database_name):
        """创建数据库"""
        try:
            connection = pymysql.connect(
                host=host,
                port=port,
                user=user,
                password=password,
                charset='utf8mb4',
                autocommit=True
            )
            
            cursor = connection.cursor()
            
            # 检查数据库是否存在
            cursor.execute("SHOW DATABASES LIKE %s", (database_name,))
            if cursor.fetchone():
                connection.close()
                return True, f"数据库 {database_name} 已存在"
            
            # 创建数据库
            cursor.execute(f"CREATE DATABASE `{database_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            connection.close()
            
            return True, f"数据库 {database_name} 创建成功"
            
        except Exception as e:
            return False, f"创建数据库失败: {str(e)}"
    
    def check_table_exists(self, table_name):
        """检查表是否存在"""
        try:
            result = db.session.execute(text("SHOW TABLES LIKE :table_name"), {"table_name": table_name})
            return result.fetchone() is not None
        except Exception as e:
            current_app.logger.error(f"检查表存在性失败: {str(e)}")
            return False
    
    def get_table_info(self, table_name):
        """获取表信息"""
        try:
            # 获取表结构
            result = db.session.execute(text(f"DESCRIBE `{table_name}`"))
            columns = result.fetchall()
            
            # 获取表状态
            result = db.session.execute(text(f"SHOW TABLE STATUS LIKE '{table_name}'"))
            status = result.fetchone()
            
            return {
                'columns': [dict(row._mapping) for row in columns],
                'status': dict(status._mapping) if status else None
            }
        except Exception as e:
            current_app.logger.error(f"获取表信息失败: {str(e)}")
            return None
    
    def backup_table(self, table_name, backup_name=None):
        """备份表"""
        try:
            if backup_name is None:
                from datetime import datetime
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_name = f"{table_name}_backup_{timestamp}"
            
            # 创建备份表
            db.session.execute(text(f"CREATE TABLE `{backup_name}` AS SELECT * FROM `{table_name}`"))
            db.session.commit()
            
            return True, f"表 {table_name} 备份为 {backup_name} 成功"
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"备份表失败: {str(e)}")
            return False, f"备份表失败: {str(e)}"
    
    def get_database_size(self):
        """获取数据库大小"""
        try:
            database_name = current_app.config['MYSQL_DATABASE']
            result = db.session.execute(text("""
                SELECT 
                    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.tables 
                WHERE table_schema = :database_name
            """), {"database_name": database_name})
            
            size = result.scalar()
            return size if size else 0
            
        except Exception as e:
            current_app.logger.error(f"获取数据库大小失败: {str(e)}")
            return 0
    
    def get_table_sizes(self):
        """获取各表大小"""
        try:
            database_name = current_app.config['MYSQL_DATABASE']
            result = db.session.execute(text("""
                SELECT 
                    table_name,
                    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb,
                    table_rows
                FROM information_schema.tables 
                WHERE table_schema = :database_name
                ORDER BY (data_length + index_length) DESC
            """), {"database_name": database_name})
            
            return [dict(row._mapping) for row in result.fetchall()]
            
        except Exception as e:
            current_app.logger.error(f"获取表大小失败: {str(e)}")
            return []
    
    def optimize_tables(self):
        """优化表"""
        try:
            database_name = current_app.config['MYSQL_DATABASE']
            
            # 获取所有表名
            result = db.session.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = :database_name
            """), {"database_name": database_name})
            
            tables = [row[0] for row in result.fetchall()]
            optimized_tables = []
            
            for table in tables:
                try:
                    db.session.execute(text(f"OPTIMIZE TABLE `{table}`"))
                    optimized_tables.append(table)
                except Exception as e:
                    current_app.logger.warning(f"优化表 {table} 失败: {str(e)}")
            
            db.session.commit()
            return True, f"成功优化 {len(optimized_tables)} 个表"
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"优化表失败: {str(e)}")
            return False, f"优化表失败: {str(e)}"
    
    def check_database_health(self):
        """检查数据库健康状态"""
        health_info = {
            'connection': False,
            'tables_exist': False,
            'data_integrity': False,
            'performance': {},
            'issues': []
        }
        
        try:
            # 检查连接
            db.session.execute(text("SELECT 1"))
            health_info['connection'] = True
            
            # 检查核心表是否存在
            core_tables = [
                'user_accounts', 'user_roles', 'permissions', 'role_permissions',
                'channels', 'customer_registrations', 'follow_up_records',
                'consultant_group_mapping', 'customer_consumption', 'system_config'
            ]
            
            missing_tables = []
            for table in core_tables:
                if not self.check_table_exists(table):
                    missing_tables.append(table)
            
            if not missing_tables:
                health_info['tables_exist'] = True
            else:
                health_info['issues'].append(f"缺少表: {', '.join(missing_tables)}")
            
            # 检查数据完整性
            try:
                # 检查是否有管理员用户
                result = db.session.execute(text("""
                    SELECT COUNT(*) FROM user_accounts ua 
                    JOIN user_roles ur ON ua.role_id = ur.id 
                    WHERE ur.role_code = 'ADMIN' AND ua.is_active = 1
                """))
                admin_count = result.scalar()
                
                if admin_count > 0:
                    health_info['data_integrity'] = True
                else:
                    health_info['issues'].append("没有可用的管理员账号")
                
            except Exception as e:
                health_info['issues'].append(f"数据完整性检查失败: {str(e)}")
            
            # 性能信息
            health_info['performance'] = {
                'database_size_mb': self.get_database_size(),
                'table_count': len(self.get_table_sizes())
            }
            
        except Exception as e:
            health_info['issues'].append(f"数据库连接失败: {str(e)}")
        
        return health_info
    
    def execute_sql_script(self, sql_script):
        """执行SQL脚本"""
        try:
            # 分割SQL语句
            statements = [stmt.strip() for stmt in sql_script.split(';') if stmt.strip()]
            
            executed_count = 0
            for statement in statements:
                if statement:
                    db.session.execute(text(statement))
                    executed_count += 1
            
            db.session.commit()
            return True, f"成功执行 {executed_count} 条SQL语句"
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"执行SQL脚本失败: {str(e)}")
            return False, f"执行SQL脚本失败: {str(e)}"
    
    def get_slow_queries(self, limit=10):
        """获取慢查询日志（如果启用）"""
        try:
            # 检查慢查询日志是否启用
            result = db.session.execute(text("SHOW VARIABLES LIKE 'slow_query_log'"))
            slow_log_enabled = result.fetchone()
            
            if not slow_log_enabled or slow_log_enabled[1] != 'ON':
                return []
            
            # 获取慢查询信息（这需要特殊权限）
            result = db.session.execute(text(f"""
                SELECT sql_text, start_time, query_time, rows_examined, rows_sent
                FROM mysql.slow_log 
                ORDER BY start_time DESC 
                LIMIT {limit}
            """))
            
            return [dict(row._mapping) for row in result.fetchall()]
            
        except Exception as e:
            current_app.logger.warning(f"获取慢查询失败: {str(e)}")
            return []
    
    def analyze_table_usage(self):
        """分析表使用情况"""
        try:
            database_name = current_app.config['MYSQL_DATABASE']
            result = db.session.execute(text("""
                SELECT 
                    table_name,
                    table_rows,
                    avg_row_length,
                    data_length,
                    index_length,
                    create_time,
                    update_time
                FROM information_schema.tables 
                WHERE table_schema = :database_name
                ORDER BY table_rows DESC
            """), {"database_name": database_name})
            
            return [dict(row._mapping) for row in result.fetchall()]
            
        except Exception as e:
            current_app.logger.error(f"分析表使用情况失败: {str(e)}")
            return []
