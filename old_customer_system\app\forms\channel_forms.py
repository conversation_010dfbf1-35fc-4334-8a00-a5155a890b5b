# -*- coding: utf-8 -*-
"""
渠道管理相关表单
"""

from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed
from wtforms import StringField, SelectField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Length, Optional, ValidationError
from app.models import Channel

class ChannelForm(FlaskForm):
    """渠道表单（创建/编辑）"""
    name = StringField('渠道名称', validators=[
        DataRequired(message='渠道名称不能为空'),
        Length(min=2, max=200, message='渠道名称长度必须在2-200个字符之间')
    ])
    category = StringField('渠道分类', validators=[
        Optional(),
        Length(max=100, message='渠道分类长度不能超过100个字符')
    ])
    simple_code = StringField('简码', validators=[
        Optional(),
        Length(max=50, message='简码长度不能超过50个字符')
    ])
    is_active = BooleanField('激活状态', default=True)
    submit = SubmitField('保存')
    
    def __init__(self, channel=None, *args, **kwargs):
        super(ChannelForm, self).__init__(*args, **kwargs)
        self.channel = channel
    
    def validate_name(self, name):
        """验证渠道名称唯一性"""
        channel = Channel.query.filter_by(name=name.data).first()
        if channel and (not self.channel or channel.id != self.channel.id):
            raise ValidationError('渠道名称已存在，请选择其他名称。')

class ChannelSearchForm(FlaskForm):
    """渠道搜索表单"""
    query = StringField('搜索', validators=[Optional()])
    category = SelectField('分类筛选', validators=[Optional()])
    is_active = SelectField('状态筛选', choices=[
        ('', '全部状态'),
        ('1', '激活'),
        ('0', '停用')
    ], validators=[Optional()])
    submit = SubmitField('搜索')
    
    def __init__(self, *args, **kwargs):
        super(ChannelSearchForm, self).__init__(*args, **kwargs)
        
        # 动态加载分类选项
        from app.repositories.channel_repository import ChannelRepository
        channel_repo = ChannelRepository()
        categories = channel_repo.get_categories()
        self.category.choices = [('', '全部分类')] + [(cat, cat) for cat in categories]

class ChannelImportForm(FlaskForm):
    """渠道导入表单"""
    file = FileField('Excel文件', validators=[
        DataRequired(message='请选择要导入的Excel文件'),
        FileAllowed(['xlsx', 'xls'], message='只支持Excel文件格式')
    ])
    submit = SubmitField('导入')