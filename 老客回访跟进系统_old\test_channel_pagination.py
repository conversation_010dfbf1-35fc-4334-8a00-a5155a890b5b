#!/usr/bin/env python3
"""
测试渠道分页功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.customer import Channel
from config import get_config
import requests
import time

def test_channel_pagination():
    """测试渠道分页功能"""
    print("测试渠道分页功能...")
    
    # 创建应用上下文
    config = get_config()
    app = create_app(config)
    
    with app.app_context():
        try:
            # 1. 检查渠道总数
            total_channels = Channel.query.count()
            print(f"数据库中总渠道数: {total_channels}")
            
            if total_channels == 0:
                print("数据库中没有渠道数据，创建测试数据...")
                create_test_channels()
                total_channels = Channel.query.count()
                print(f"创建后总渠道数: {total_channels}")
            
            # 2. 计算总页数
            per_page = 20
            total_pages = (total_channels + per_page - 1) // per_page
            print(f"每页 {per_page} 条，总页数: {total_pages}")
            
            # 3. 测试分页查询
            print("\n测试分页查询...")
            for page in range(1, min(total_pages + 1, 6)):  # 测试前5页
                try:
                    channels = Channel.query.order_by(Channel.created_time.desc()).paginate(
                        page=page, per_page=per_page, error_out=False
                    )
                    print(f"第 {page} 页: {len(channels.items)} 条记录")
                    
                    # 验证分页对象属性
                    assert hasattr(channels, 'items'), "分页对象缺少 items 属性"
                    assert hasattr(channels, 'page'), "分页对象缺少 page 属性"
                    assert hasattr(channels, 'pages'), "分页对象缺少 pages 属性"
                    assert hasattr(channels, 'total'), "分页对象缺少 total 属性"
                    assert hasattr(channels, 'has_prev'), "分页对象缺少 has_prev 属性"
                    assert hasattr(channels, 'has_next'), "分页对象缺少 has_next 属性"
                    
                except Exception as e:
                    print(f"第 {page} 页查询失败: {e}")
                    return False
            
            # 4. 测试超出范围的页码
            print("\n测试超出范围的页码...")
            try:
                large_page = total_pages + 10
                channels = Channel.query.order_by(Channel.created_time.desc()).paginate(
                    page=large_page, per_page=per_page, error_out=False
                )
                print(f"第 {large_page} 页 (超出范围): {len(channels.items)} 条记录")
            except Exception as e:
                print(f"超出范围页码测试失败: {e}")
                return False
            
            print("\n✅ 渠道分页功能测试通过")
            return True
            
        except Exception as e:
            print(f"❌ 渠道分页功能测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False

def create_test_channels():
    """创建测试渠道数据"""
    print("创建测试渠道数据...")
    
    categories = ['线上推广', '线下活动', '合作伙伴', '直接访问', '社交媒体']
    channels_data = []
    
    for i in range(100):  # 创建100个测试渠道
        category = categories[i % len(categories)]
        channel_name = f"{category}_渠道_{i+1:03d}"
        
        channel = Channel(
            channel_category=category,
            channel_name=channel_name,
            created_by=1  # 假设管理员ID为1
        )
        channels_data.append(channel)
    
    try:
        db.session.add_all(channels_data)
        db.session.commit()
        print(f"成功创建 {len(channels_data)} 个测试渠道")
    except Exception as e:
        db.session.rollback()
        print(f"创建测试渠道失败: {e}")
        raise

def test_web_pagination():
    """测试Web界面分页"""
    print("\n测试Web界面分页...")
    
    base_url = "http://localhost:5000"
    
    # 首先尝试访问登录页面
    try:
        response = requests.get(f"{base_url}/auth/login", timeout=5)
        if response.status_code != 200:
            print(f"无法访问登录页面: {response.status_code}")
            return False
        
        print("✅ 系统可以正常访问")
        print("注意: 需要手动登录后测试渠道管理页面的分页功能")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到Web服务: {e}")
        return False

if __name__ == "__main__":
    print("渠道分页功能测试")
    print("=" * 50)
    
    # 测试数据库分页
    db_test_result = test_channel_pagination()
    
    # 测试Web分页
    web_test_result = test_web_pagination()
    
    print("\n" + "=" * 50)
    print("测试结果:")
    print(f"数据库分页测试: {'✅ 通过' if db_test_result else '❌ 失败'}")
    print(f"Web访问测试: {'✅ 通过' if web_test_result else '❌ 失败'}")
    
    if db_test_result and web_test_result:
        print("\n🎉 所有测试通过！渠道分页功能应该已经修复。")
        print("请手动登录系统测试渠道管理页面的分页功能。")
    else:
        print("\n⚠️ 部分测试失败，请检查相关问题。")
