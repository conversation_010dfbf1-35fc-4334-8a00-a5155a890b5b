"""
渠道管理视图控制器
"""
import os
import pandas as pd
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from app import db
from app.models.customer import Channel
from app.utils.permissions import require_permission
from app.utils.validators import validate_content_length

bp = Blueprint('channel', __name__)

@bp.route('/')
@require_permission('CHANNEL_VIEW')
def index():
    """渠道列表"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 20
        
        # 验证页码参数
        if page < 1:
            page = 1

        # 限制每页数量，避免查询过大数据集
        if per_page > 100:
            per_page = 100

        # 获取筛选参数
        category = request.args.get('category', '').strip()
        name = request.args.get('name', '').strip()
        status = request.args.get('status')

        # 构建查询
        query = Channel.query

        if category:
            query = query.filter(Channel.channel_category.like(f'%{category}%'))

        if name:
            query = query.filter(Channel.channel_name.like(f'%{name}%'))

        if status == 'active':
            query = query.filter(Channel.is_active == True)
        elif status == 'inactive':
            query = query.filter(Channel.is_active == False)

        # 执行分页查询，添加错误处理和性能优化
        try:
            # 先检查总数，如果页码超出范围则重置为第一页
            total_count = query.count()
            max_page = (total_count + per_page - 1) // per_page if total_count > 0 else 1

            if page > max_page:
                current_app.logger.warning(f"请求页码 {page} 超出最大页数 {max_page}，重置为第一页")
                page = 1

            channels = query.order_by(Channel.created_time.desc()).paginate(
                page=page, per_page=per_page, error_out=False
            )

        except Exception as e:
            current_app.logger.error(f"渠道分页查询失败: {e}")
            # 如果分页失败，尝试返回第一页
            try:
                channels = query.order_by(Channel.created_time.desc()).paginate(
                    page=1, per_page=per_page, error_out=False
                )
            except Exception as e2:
                current_app.logger.error(f"渠道分页查询完全失败: {e2}")
                # 如果连第一页都失败，抛出异常让外层处理
                raise e2
        
        # 获取渠道分类列表（用于筛选）
        try:
            categories = db.session.query(Channel.channel_category).distinct().all()
            categories = [cat[0] for cat in categories if cat[0]]
        except Exception as e:
            current_app.logger.error(f"获取渠道分类失败: {e}")
            categories = []
        
        return render_template('channel/index.html', 
                             channels=channels, 
                             categories=categories)
                             
    except Exception as e:
        current_app.logger.error(f"渠道列表页面错误: {e}")
        flash('加载渠道列表时出现错误，请稍后重试', 'error')
        # 返回空的分页结果 - 使用正确的方式创建空分页对象
        try:
            # 创建一个空查询并进行分页
            empty_query = Channel.query.filter(Channel.id == -1)  # 永远不会匹配的条件
            empty_channels = empty_query.paginate(page=1, per_page=20, error_out=False)
        except Exception:
            # 如果连空查询都失败，创建一个模拟的分页对象
            class EmptyPagination:
                def __init__(self):
                    self.items = []
                    self.page = 1
                    self.pages = 0
                    self.per_page = 20
                    self.total = 0
                    self.has_prev = False
                    self.has_next = False
                    self.prev_num = None
                    self.next_num = None

                def iter_pages(self, left_edge=2, left_current=2, right_current=3, right_edge=2):
                    return []

            empty_channels = EmptyPagination()

        return render_template('channel/index.html',
                             channels=empty_channels,
                             categories=[])

@bp.route('/create', methods=['GET', 'POST'])
@require_permission('CHANNEL_MANAGE')
def create():
    """创建渠道"""
    if request.method == 'POST':
        channel_category = request.form.get('channel_category', '').strip()
        channel_name = request.form.get('channel_name', '').strip()
        
        # 验证输入
        if not channel_category:
            flash('渠道分类不能为空', 'error')
            return render_template('channel/create.html')
        
        if not channel_name:
            flash('渠道名称不能为空', 'error')
            return render_template('channel/create.html')
        
        # 验证长度
        is_valid, message = validate_content_length(channel_category, "渠道分类", max_length=100)
        if not is_valid:
            flash(message, 'error')
            return render_template('channel/create.html')
        
        is_valid, message = validate_content_length(channel_name, "渠道名称", max_length=200)
        if not is_valid:
            flash(message, 'error')
            return render_template('channel/create.html')
        
        # 检查是否已存在相同的渠道
        existing_channel = Channel.query.filter_by(
            channel_category=channel_category,
            channel_name=channel_name
        ).first()
        
        if existing_channel:
            flash('相同的渠道分类和名称已存在', 'error')
            return render_template('channel/create.html')
        
        try:
            # 创建渠道
            channel = Channel(
                channel_category=channel_category,
                channel_name=channel_name,
                created_by=current_user.id
            )
            
            db.session.add(channel)
            db.session.commit()
            
            flash('渠道创建成功', 'success')
            return redirect(url_for('channel.index'))
            
        except Exception as e:
            db.session.rollback()
            flash('渠道创建失败', 'error')
    
    return render_template('channel/create.html')

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@require_permission('CHANNEL_MANAGE')
def edit(id):
    """编辑渠道"""
    channel = Channel.query.get_or_404(id)
    
    if request.method == 'POST':
        channel_category = request.form.get('channel_category', '').strip()
        channel_name = request.form.get('channel_name', '').strip()
        is_active = request.form.get('is_active') == 'on'
        
        # 验证输入
        if not channel_category:
            flash('渠道分类不能为空', 'error')
            return render_template('channel/edit.html', channel=channel)
        
        if not channel_name:
            flash('渠道名称不能为空', 'error')
            return render_template('channel/edit.html', channel=channel)
        
        # 验证长度
        is_valid, message = validate_content_length(channel_category, "渠道分类", max_length=100)
        if not is_valid:
            flash(message, 'error')
            return render_template('channel/edit.html', channel=channel)
        
        is_valid, message = validate_content_length(channel_name, "渠道名称", max_length=200)
        if not is_valid:
            flash(message, 'error')
            return render_template('channel/edit.html', channel=channel)
        
        # 检查是否已存在相同的渠道（排除当前渠道）
        existing_channel = Channel.query.filter(
            Channel.channel_category == channel_category,
            Channel.channel_name == channel_name,
            Channel.id != id
        ).first()
        
        if existing_channel:
            flash('相同的渠道分类和名称已存在', 'error')
            return render_template('channel/edit.html', channel=channel)
        
        try:
            # 更新渠道
            channel.channel_category = channel_category
            channel.channel_name = channel_name
            channel.is_active = is_active
            
            db.session.commit()
            
            flash('渠道更新成功', 'success')
            return redirect(url_for('channel.index'))
            
        except Exception as e:
            db.session.rollback()
            flash('渠道更新失败', 'error')
    
    return render_template('channel/edit.html', channel=channel)

@bp.route('/<int:id>/delete', methods=['POST'])
@require_permission('CHANNEL_MANAGE')
def delete(id):
    """删除渠道"""
    channel = Channel.query.get_or_404(id)
    
    # 检查是否有客户使用此渠道
    from app.models.customer import CustomerRegistration
    customer_count = CustomerRegistration.query.filter_by(activation_channel_id=id).count()
    
    if customer_count > 0:
        flash(f'无法删除，有 {customer_count} 个客户正在使用此渠道', 'error')
        return redirect(url_for('channel.index'))
    
    try:
        db.session.delete(channel)
        db.session.commit()
        flash('渠道删除成功', 'success')
    except Exception as e:
        db.session.rollback()
        flash('渠道删除失败', 'error')
    
    return redirect(url_for('channel.index'))

@bp.route('/import', methods=['GET', 'POST'])
@require_permission('CHANNEL_MANAGE')
def import_channels():
    """批量导入渠道"""
    if request.method == 'POST':
        # 检查是否有文件上传
        if 'file' not in request.files:
            flash('请选择要上传的文件', 'error')
            return render_template('channel/import.html')
        
        file = request.files['file']
        if file.filename == '':
            flash('请选择要上传的文件', 'error')
            return render_template('channel/import.html')
        
        # 检查文件类型
        if not file.filename.lower().endswith(('.xlsx', '.xls')):
            flash('请上传Excel文件（.xlsx或.xls格式）', 'error')
            return render_template('channel/import.html')
        
        try:
            # 读取Excel文件
            df = pd.read_excel(file)
            
            # 验证必要的列
            required_columns = ['渠道分类', '渠道名称']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                flash(f'Excel文件缺少必要的列: {", ".join(missing_columns)}', 'error')
                return render_template('channel/import.html')
            
            # 处理导入数据
            result = process_import_data(df)
            
            flash(f'导入完成！成功导入 {result["success_count"]} 条记录，跳过 {result["skip_count"]} 条重复记录', 'success')
            
            if result["errors"]:
                flash(f'导入过程中发现 {len(result["errors"])} 个错误', 'warning')
            
            return redirect(url_for('channel.index'))
            
        except Exception as e:
            flash(f'文件处理失败: {str(e)}', 'error')
            return render_template('channel/import.html')
    
    return render_template('channel/import.html')

def process_import_data(df):
    """处理导入数据"""
    success_count = 0
    skip_count = 0
    errors = []
    
    for index, row in df.iterrows():
        try:
            channel_category = str(row['渠道分类']).strip()
            channel_name = str(row['渠道名称']).strip()
            
            # 跳过空行
            if not channel_category or not channel_name or channel_category == 'nan' or channel_name == 'nan':
                continue
            
            # 检查长度
            if len(channel_category) > 100:
                errors.append(f'第{index+2}行：渠道分类长度超过100字符')
                continue
            
            if len(channel_name) > 200:
                errors.append(f'第{index+2}行：渠道名称长度超过200字符')
                continue
            
            # 检查是否已存在
            existing_channel = Channel.query.filter_by(
                channel_category=channel_category,
                channel_name=channel_name
            ).first()
            
            if existing_channel:
                skip_count += 1
                continue
            
            # 创建新渠道
            channel = Channel(
                channel_category=channel_category,
                channel_name=channel_name,
                created_by=current_user.id
            )
            
            db.session.add(channel)
            success_count += 1
            
        except Exception as e:
            errors.append(f'第{index+2}行：{str(e)}')
    
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        errors.append(f'数据库保存失败: {str(e)}')
    
    return {
        'success_count': success_count,
        'skip_count': skip_count,
        'errors': errors
    }

@bp.route('/export')
@require_permission('CHANNEL_VIEW')
def export_channels():
    """导出渠道列表"""
    try:
        # 获取所有渠道数据
        channels = Channel.query.order_by(Channel.channel_category, Channel.channel_name).all()
        
        # 准备数据
        data = []
        for channel in channels:
            data.append({
                '渠道分类': channel.channel_category,
                '渠道名称': channel.channel_name,
                '状态': '启用' if channel.is_active else '停用',
                '创建时间': channel.created_time.strftime('%Y-%m-%d %H:%M:%S'),
                '创建人': channel.creator.real_name if channel.creator else ''
            })
        
        # 创建DataFrame
        df = pd.DataFrame(data)
        
        # 生成文件名
        from datetime import datetime
        filename = f'渠道列表_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        
        # 保存到临时文件
        temp_path = os.path.join(current_app.config.get('UPLOAD_FOLDER', '/tmp'), filename)
        df.to_excel(temp_path, index=False)
        
        # 返回下载链接
        from flask import send_file
        return send_file(temp_path, as_attachment=True, download_name=filename)
        
    except Exception as e:
        flash(f'导出失败: {str(e)}', 'error')
        return redirect(url_for('channel.index'))

@bp.route('/template')
@require_permission('CHANNEL_VIEW')
def download_template():
    """下载导入模板"""
    try:
        # 创建模板数据
        template_data = {
            '渠道分类': ['线上推广', '线下活动', '老客推荐'],
            '渠道名称': ['百度推广', '地推活动', '老客户介绍']
        }
        
        df = pd.DataFrame(template_data)
        
        # 生成文件名
        filename = '渠道导入模板.xlsx'
        
        # 保存到临时文件
        temp_path = os.path.join(current_app.config.get('UPLOAD_FOLDER', '/tmp'), filename)
        df.to_excel(temp_path, index=False)
        
        # 返回下载链接
        from flask import send_file
        return send_file(temp_path, as_attachment=True, download_name=filename)
        
    except Exception as e:
        flash(f'模板下载失败: {str(e)}', 'error')
        return redirect(url_for('channel.import_channels'))
