{% extends "base.html" %}

{% block title %}使用帮助 - 老客回访与跟进系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-question-circle"></i> 使用帮助</h2>
    </div>
</div>

<div class="row">
    <div class="col-md-3">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-list"></i> 帮助目录</h6>
            </div>
            <div class="list-group list-group-flush">
                <a href="#getting-started" class="list-group-item list-group-item-action">快速开始</a>
                <a href="#roles" class="list-group-item list-group-item-action">角色权限</a>
                <a href="#customer-management" class="list-group-item list-group-item-action">客户管理</a>
                <a href="#follow-up" class="list-group-item list-group-item-action">跟进管理</a>
                <a href="#reports" class="list-group-item list-group-item-action">统计报表</a>
                <a href="#faq" class="list-group-item list-group-item-action">常见问题</a>
            </div>
        </div>
    </div>
    
    <div class="col-md-9">
        <div class="card">
            <div class="card-body">
                <section id="getting-started">
                    <h4><i class="fas fa-play"></i> 快速开始</h4>
                    <p>欢迎使用老客回访与跟进系统！本系统旨在帮助您高效管理客户信息和跟进记录。</p>
                    
                    <h5>首次登录</h5>
                    <ol>
                        <li>使用管理员提供的用户名和密码登录系统</li>
                        <li>首次登录后，建议立即修改密码</li>
                        <li>熟悉系统界面和功能模块</li>
                    </ol>
                </section>
                
                <hr>
                
                <section id="roles">
                    <h4><i class="fas fa-users"></i> 角色权限</h4>
                    <p>系统采用基于角色的权限控制，不同角色拥有不同的功能权限：</p>
                    
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>角色</th>
                                    <th>主要职责</th>
                                    <th>权限范围</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><span class="badge bg-danger">系统管理员</span></td>
                                    <td>系统维护和用户管理</td>
                                    <td>所有功能权限</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-warning">经营院长</span></td>
                                    <td>查看全局数据和报表</td>
                                    <td>只读权限，查看所有数据</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-info">部门主管</span></td>
                                    <td>管理本部门业务</td>
                                    <td>查看本部门数据</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-success">网络咨询</span></td>
                                    <td>客户登记和初步咨询</td>
                                    <td>客户登记，查看自己的数据</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-primary">现场咨询</span></td>
                                    <td>客户跟进和服务</td>
                                    <td>跟进分配的客户</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </section>
                
                <hr>
                
                <section id="customer-management">
                    <h4><i class="fas fa-user-friends"></i> 客户管理</h4>
                    
                    <h5>客户登记（网络咨询）</h5>
                    <ol>
                        <li>点击"客户登记"按钮</li>
                        <li>填写客户卡号（必填，纯数字）</li>
                        <li>选择分配的现场顾问</li>
                        <li>选择激活渠道</li>
                        <li>填写咨询内容（最多500字）</li>
                        <li>选择客户最近来院时间</li>
                        <li>提交保存</li>
                    </ol>
                    
                    <h5>客户查看</h5>
                    <ul>
                        <li>在客户列表中可以查看所有有权限的客户信息</li>
                        <li>支持按条件筛选和搜索</li>
                        <li>点击客户可查看详细信息和跟进记录</li>
                    </ul>
                </section>
                
                <hr>
                
                <section id="follow-up">
                    <h4><i class="fas fa-comments"></i> 跟进管理</h4>
                    
                    <h5>添加跟进记录（现场咨询）</h5>
                    <ol>
                        <li>在客户列表中找到分配给您的客户</li>
                        <li>点击"添加跟进"按钮</li>
                        <li>填写跟进内容（最多500字）</li>
                        <li>系统自动记录跟进时间</li>
                        <li>提交保存</li>
                    </ol>
                    
                    <h5>查看跟进历史</h5>
                    <ul>
                        <li>在客户详情页面可以查看所有跟进记录</li>
                        <li>按时间倒序显示，最新的在前</li>
                        <li>显示跟进人员和跟进时间</li>
                    </ul>
                </section>
                
                <hr>
                
                <section id="reports">
                    <h4><i class="fas fa-chart-bar"></i> 统计报表</h4>
                    
                    <h5>查看统计数据</h5>
                    <ul>
                        <li>总客户数：系统中的客户总数</li>
                        <li>待跟进客户：还没有跟进记录的客户</li>
                        <li>跟进后到院：跟进后有消费记录的客户</li>
                        <li>总消费金额：客户的总消费金额</li>
                    </ul>
                    
                    <h5>筛选条件</h5>
                    <ul>
                        <li>按时间范围筛选</li>
                        <li>按现场顾问筛选</li>
                        <li>按激活渠道筛选</li>
                    </ul>
                </section>
                
                <hr>
                
                <section id="faq">
                    <h4><i class="fas fa-question"></i> 常见问题</h4>
                    
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq1">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                                    忘记密码怎么办？
                                </button>
                            </h2>
                            <div id="collapse1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    请联系系统管理员重置密码。管理员可以在用户管理中为您重置密码。
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq2">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                                    为什么看不到某些功能？
                                </button>
                            </h2>
                            <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    系统采用基于角色的权限控制，您只能看到和使用您角色权限范围内的功能。如需更多权限，请联系管理员。
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq3">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                                    如何导入Excel数据？
                                </button>
                            </h2>
                            <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    系统支持Excel数据导入功能，包括渠道数据、消费数据等。请使用系统提供的模板格式，确保数据格式正确。
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>
</div>
{% endblock %}
