# -*- coding: utf-8 -*-
"""
数据上传表单
"""

from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileRequired, FileAllowed
from wtforms import SubmitField, SelectField, TextAreaField
from wtforms.validators import DataRequired, Optional


class VisitRecordUploadForm(FlaskForm):
    """到院明细上传表单"""
    file = FileField('到院明细文件', validators=[
        FileRequired(message='请选择要上传的文件'),
        FileAllowed(['xlsx', 'xls', 'csv'], message='只支持Excel文件(.xlsx, .xls)和CSV文件(.csv)')
    ])
    
    file_type = SelectField('文件类型', choices=[
        ('excel', 'Excel文件'),
        ('csv', 'CSV文件')
    ], default='excel', validators=[DataRequired()])
    
    remarks = TextAreaField('备注', validators=[Optional()], 
                          render_kw={'rows': 3, 'placeholder': '可选：上传说明或备注信息'})
    
    submit = SubmitField('上传到院明细')


class ConsumptionRecordUploadForm(FlaskForm):
    """消费明细上传表单"""
    file = FileField('消费明细文件', validators=[
        FileRequired(message='请选择要上传的文件'),
        FileAllowed(['xlsx', 'xls', 'csv'], message='只支持Excel文件(.xlsx, .xls)和CSV文件(.csv)')
    ])
    
    file_type = SelectField('文件类型', choices=[
        ('excel', 'Excel文件'),
        ('csv', 'CSV文件')
    ], default='excel', validators=[DataRequired()])
    
    remarks = TextAreaField('备注', validators=[Optional()], 
                          render_kw={'rows': 3, 'placeholder': '可选：上传说明或备注信息'})
    
    submit = SubmitField('上传消费明细')


class StatisticsQueryForm(FlaskForm):
    """统计查询表单"""
    start_date = SelectField('开始日期', validators=[Optional()])
    end_date = SelectField('结束日期', validators=[Optional()])
    
    onsite_consultant_id = SelectField('现场顾问', coerce=int, validators=[Optional()])
    online_consultant_id = SelectField('网络咨询员', coerce=int, validators=[Optional()])
    channel_id = SelectField('激活渠道', coerce=int, validators=[Optional()])
    
    submit = SubmitField('生成统计报表')
    
    def __init__(self, *args, **kwargs):
        super(StatisticsQueryForm, self).__init__(*args, **kwargs)
        
        # 动态加载选项
        from app.models import User, Channel
        
        # 现场顾问选项
        onsite_consultants = User.query.filter_by(role='onsite_consultant', is_active=True).all()
        self.onsite_consultant_id.choices = [(0, '全部现场顾问')] + [
            (user.id, user.real_name) for user in onsite_consultants
        ]
        
        # 网络咨询员选项
        online_consultants = User.query.filter_by(role='online_consultant', is_active=True).all()
        self.online_consultant_id.choices = [(0, '全部网络咨询员')] + [
            (user.id, user.real_name) for user in online_consultants
        ]
        
        # 渠道选项
        channels = Channel.query.filter_by(is_active=True).all()
        self.channel_id.choices = [(0, '全部渠道')] + [
            (channel.id, channel.name) for channel in channels
        ]
        
        # 日期选项（最近30天）
        from datetime import datetime, timedelta
        today = datetime.now().date()
        date_options = [(0, '不限制')]
        
        for i in range(30):
            date = today - timedelta(days=i)
            date_str = date.strftime('%Y-%m-%d')
            date_options.append((date_str, date_str))
        
        self.start_date.choices = date_options
        self.end_date.choices = date_options
