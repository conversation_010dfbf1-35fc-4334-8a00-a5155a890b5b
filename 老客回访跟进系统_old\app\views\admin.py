"""
管理员功能视图控制器
"""
from datetime import datetime
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from app import db
from app.models.user import User, Role, Department
from app.models.system import SystemConfig, AuditLog
from app.utils.permissions import require_permission, require_admin
from app.utils.validators import validate_username, validate_password, validate_real_name

bp = Blueprint('admin', __name__)

@bp.route('/')
@require_admin
def index():
    """管理员首页"""
    return render_template('admin/index.html')

@bp.route('/users')
@require_permission('USER_MANAGE')
def users():
    """用户管理"""
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    users = User.query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('admin/users.html', users=users)

@bp.route('/users/create', methods=['GET', 'POST'])
@require_permission('USER_MANAGE')
def create_user():
    """创建用户"""
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')
        real_name = request.form.get('real_name', '').strip()
        department_id = request.form.get('department_id', type=int)
        role_id = request.form.get('role_id', type=int)
        
        # 验证输入
        is_valid, message = validate_username(username)
        if not is_valid:
            flash(message, 'error')
            roles = Role.query.filter_by(is_active=True).all()
            departments = Department.get_active_departments()
            return render_template('admin/create_user.html', roles=roles, departments=departments)

        is_valid, message = validate_password(password)
        if not is_valid:
            flash(message, 'error')
            roles = Role.query.filter_by(is_active=True).all()
            departments = Department.get_active_departments()
            return render_template('admin/create_user.html', roles=roles, departments=departments)

        is_valid, message = validate_real_name(real_name)
        if not is_valid:
            flash(message, 'error')
            roles = Role.query.filter_by(is_active=True).all()
            departments = Department.get_active_departments()
            return render_template('admin/create_user.html', roles=roles, departments=departments)

        # 检查用户名是否已存在
        if User.query.filter_by(username=username).first():
            flash('用户名已存在', 'error')
            roles = Role.query.filter_by(is_active=True).all()
            departments = Department.get_active_departments()
            return render_template('admin/create_user.html', roles=roles, departments=departments)

        # 检查角色是否存在
        role = Role.query.get(role_id)
        if not role:
            flash('角色不存在', 'error')
            roles = Role.query.filter_by(is_active=True).all()
            departments = Department.get_active_departments()
            return render_template('admin/create_user.html', roles=roles, departments=departments)

        # 检查部门是否存在
        if not department_id:
            flash('请选择所属部门', 'error')
            roles = Role.query.filter_by(is_active=True).all()
            departments = Department.get_active_departments()
            return render_template('admin/create_user.html', roles=roles, departments=departments)

        department = Department.query.get(department_id)
        if not department:
            flash('所选部门不存在', 'error')
            roles = Role.query.filter_by(is_active=True).all()
            departments = Department.get_active_departments()
            return render_template('admin/create_user.html', roles=roles, departments=departments)
        
        try:
            # 创建用户
            user = User(
                username=username,
                real_name=real_name,
                department_id=department_id,
                role_id=role_id,
                created_by=current_user.id
            )
            user.set_password(password)
            
            db.session.add(user)
            db.session.commit()
            
            # 记录日志
            AuditLog.log_action(
                user_id=current_user.id,
                action='CREATE_USER',
                resource_type='USER',
                resource_id=str(user.id),
                details=f"创建用户: {username}, 部门: {department.department_name}"
            )
            
            flash('用户创建成功', 'success')
            return redirect(url_for('admin.users'))
            
        except Exception as e:
            db.session.rollback()
            flash('用户创建失败', 'error')
    
    roles = Role.query.filter_by(is_active=True).all()
    departments = Department.get_active_departments()
    return render_template('admin/create_user.html', roles=roles, departments=departments)

@bp.route('/system-config')
@require_permission('SYSTEM_CONFIG')
def system_config():
    """系统配置"""
    configs = SystemConfig.get_all_configs()
    return render_template('admin/system_config.html', configs=configs)

@bp.route('/audit-logs')
@require_permission('AUDIT_LOG_VIEW')
def audit_logs():
    """审计日志"""
    page = request.args.get('page', 1, type=int)
    per_page = 50

    logs = AuditLog.query.order_by(AuditLog.created_time.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    return render_template('admin/audit_logs.html', logs=logs)

# 部门管理相关路由
@bp.route('/departments')
@require_permission('USER_MANAGE')
def departments():
    """部门管理"""
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    departments = Department.query.order_by(Department.sort_order, Department.department_name).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('admin/departments.html', departments=departments)

@bp.route('/departments/create', methods=['GET', 'POST'])
@require_permission('USER_MANAGE')
def create_department():
    """创建部门"""
    if request.method == 'POST':
        department_name = request.form.get('department_name', '').strip()
        department_code = request.form.get('department_code', '').strip()
        description = request.form.get('description', '').strip()
        parent_id = request.form.get('parent_id', type=int) or None
        sort_order = request.form.get('sort_order', 0, type=int)
        
        # 验证输入
        if not department_name:
            flash('部门名称不能为空', 'error')
            parent_departments = Department.get_active_departments()
            return render_template('admin/create_department.html', parent_departments=parent_departments)
        
        if not department_code:
            flash('部门代码不能为空', 'error')
            parent_departments = Department.get_active_departments()
            return render_template('admin/create_department.html', parent_departments=parent_departments)
        
        # 检查部门名称是否已存在
        if Department.query.filter_by(department_name=department_name).first():
            flash('部门名称已存在', 'error')
            parent_departments = Department.get_active_departments()
            return render_template('admin/create_department.html', parent_departments=parent_departments)
        
        # 检查部门代码是否已存在
        if Department.query.filter_by(department_code=department_code).first():
            flash('部门代码已存在', 'error')
            parent_departments = Department.get_active_departments()
            return render_template('admin/create_department.html', parent_departments=parent_departments)
        
        # 检查上级部门是否存在
        if parent_id:
            parent_dept = Department.query.get(parent_id)
            if not parent_dept:
                flash('上级部门不存在', 'error')
                parent_departments = Department.get_active_departments()
                return render_template('admin/create_department.html', parent_departments=parent_departments)
        
        try:
            # 创建部门
            department = Department(
                department_name=department_name,
                department_code=department_code,
                description=description,
                parent_id=parent_id,
                sort_order=sort_order,
                created_by=current_user.id
            )
            
            db.session.add(department)
            db.session.commit()
            
            # 记录日志
            AuditLog.log_action(
                user_id=current_user.id,
                action='CREATE_DEPARTMENT',
                resource_type='DEPARTMENT',
                resource_id=str(department.id),
                details=f"创建部门: {department_name}"
            )
            
            flash('部门创建成功', 'success')
            return redirect(url_for('admin.departments'))
            
        except Exception as e:
            db.session.rollback()
            flash('部门创建失败', 'error')
    
    parent_departments = Department.get_active_departments()
    return render_template('admin/create_department.html', parent_departments=parent_departments)

@bp.route('/departments/<int:dept_id>/edit', methods=['GET', 'POST'])
@require_permission('USER_MANAGE')
def edit_department(dept_id):
    """编辑部门"""
    department = Department.query.get_or_404(dept_id)
    
    if request.method == 'POST':
        department_name = request.form.get('department_name', '').strip()
        department_code = request.form.get('department_code', '').strip()
        description = request.form.get('description', '').strip()
        parent_id = request.form.get('parent_id', type=int) or None
        sort_order = request.form.get('sort_order', 0, type=int)
        is_active = request.form.get('is_active') == 'on'
        
        # 验证输入
        if not department_name:
            flash('部门名称不能为空', 'error')
            parent_departments = Department.query.filter(Department.id != dept_id).all()
            return render_template('admin/edit_department.html', department=department, parent_departments=parent_departments)
        
        if not department_code:
            flash('部门代码不能为空', 'error')
            parent_departments = Department.query.filter(Department.id != dept_id).all()
            return render_template('admin/edit_department.html', department=department, parent_departments=parent_departments)
        
        # 检查部门名称是否已存在（排除自己）
        existing_name = Department.query.filter(
            Department.department_name == department_name,
            Department.id != dept_id
        ).first()
        if existing_name:
            flash('部门名称已存在', 'error')
            parent_departments = Department.query.filter(Department.id != dept_id).all()
            return render_template('admin/edit_department.html', department=department, parent_departments=parent_departments)
        
        # 检查部门代码是否已存在（排除自己）
        existing_code = Department.query.filter(
            Department.department_code == department_code,
            Department.id != dept_id
        ).first()
        if existing_code:
            flash('部门代码已存在', 'error')
            parent_departments = Department.query.filter(Department.id != dept_id).all()
            return render_template('admin/edit_department.html', department=department, parent_departments=parent_departments)
        
        # 检查上级部门是否存在且不是自己或自己的子部门
        if parent_id:
            parent_dept = Department.query.get(parent_id)
            if not parent_dept:
                flash('上级部门不存在', 'error')
                parent_departments = Department.query.filter(Department.id != dept_id).all()
                return render_template('admin/edit_department.html', department=department, parent_departments=parent_departments)
            
            # 防止循环引用
            if parent_id == dept_id:
                flash('不能将自己设为上级部门', 'error')
                parent_departments = Department.query.filter(Department.id != dept_id).all()
                return render_template('admin/edit_department.html', department=department, parent_departments=parent_departments)
            
            # 检查是否会形成循环
            current_parent = parent_dept
            while current_parent:
                if current_parent.id == dept_id:
                    flash('不能将子部门设为上级部门', 'error')
                    parent_departments = Department.query.filter(Department.id != dept_id).all()
                    return render_template('admin/edit_department.html', department=department, parent_departments=parent_departments)
                current_parent = current_parent.parent
        
        try:
            # 记录修改前的值
            old_values = {
                'department_name': department.department_name,
                'department_code': department.department_code,
                'description': department.description,
                'parent_id': department.parent_id,
                'sort_order': department.sort_order,
                'is_active': department.is_active
            }
            
            # 更新部门信息
            department.department_name = department_name
            department.department_code = department_code
            department.description = description
            department.parent_id = parent_id
            department.sort_order = sort_order
            department.is_active = is_active
            department.updated_time = datetime.utcnow()
            
            db.session.commit()
            
            # 记录日志
            new_values = {
                'department_name': department_name,
                'department_code': department_code,
                'description': description,
                'parent_id': parent_id,
                'sort_order': sort_order,
                'is_active': is_active
            }
            
            AuditLog.log_action(
                user_id=current_user.id,
                action='UPDATE_DEPARTMENT',
                resource_type='DEPARTMENT',
                resource_id=str(department.id),
                old_values=old_values,
                new_values=new_values,
                details=f"修改部门: {department_name}"
            )
            
            flash('部门修改成功', 'success')
            return redirect(url_for('admin.departments'))
            
        except Exception as e:
            db.session.rollback()
            flash('部门修改失败', 'error')
    
    # 获取可选的上级部门（排除自己和自己的子部门）
    parent_departments = Department.query.filter(Department.id != dept_id).all()
    # 过滤掉子部门
    valid_parents = []
    for dept in parent_departments:
        is_child = False
        current_parent = dept
        while current_parent:
            if current_parent.id == dept_id:
                is_child = True
                break
            current_parent = current_parent.parent
        if not is_child:
            valid_parents.append(dept)
    
    return render_template('admin/edit_department.html', department=department, parent_departments=valid_parents)

@bp.route('/departments/<int:dept_id>/delete', methods=['POST'])
@require_permission('USER_MANAGE')
def delete_department(dept_id):
    """删除部门"""
    department = Department.query.get_or_404(dept_id)
    
    # 检查是否有用户属于该部门
    user_count = User.query.filter_by(department_id=dept_id).count()
    if user_count > 0:
        flash(f'该部门下还有 {user_count} 个用户，无法删除', 'error')
        return redirect(url_for('admin.departments'))
    
    # 检查是否有子部门
    child_count = Department.query.filter_by(parent_id=dept_id).count()
    if child_count > 0:
        flash(f'该部门下还有 {child_count} 个子部门，无法删除', 'error')
        return redirect(url_for('admin.departments'))
    
    try:
        # 记录删除前的信息
        old_values = {
            'department_name': department.department_name,
            'department_code': department.department_code,
            'description': department.description
        }
        
        db.session.delete(department)
        db.session.commit()
        
        # 记录日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='DELETE_DEPARTMENT',
            resource_type='DEPARTMENT',
            resource_id=str(dept_id),
            old_values=old_values,
            details=f"删除部门: {department.department_name}"
        )
        
        flash('部门删除成功', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash('部门删除失败', 'error')
    
    return redirect(url_for('admin.departments'))
