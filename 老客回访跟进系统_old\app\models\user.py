"""
用户相关数据模型
"""
from datetime import datetime
from flask_login import UserMixin
from werkzeug.security import check_password_hash, generate_password_hash
from app import db

class Department(db.Model):
    """部门表"""
    __tablename__ = 'departments'
    
    id = db.Column(db.Integer, primary_key=True)
    department_name = db.Column(db.String(100), unique=True, nullable=False, comment='部门名称')
    department_code = db.Column(db.String(50), unique=True, nullable=False, comment='部门代码')
    description = db.Column(db.Text, comment='部门描述')
    parent_id = db.Column(db.Integer, db.ForeignKey('departments.id'), comment='上级部门ID')
    is_active = db.Column(db.<PERSON>an, default=True, comment='是否启用')
    sort_order = db.Column(db.Integer, default=0, comment='排序顺序')
    created_time = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_time = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    created_by = db.Column(db.Integer, db.ForeignKey('user_accounts.id'), comment='创建人ID')
    
    # 关系
    parent = db.relationship('Department', remote_side=[id], backref='children')
    creator = db.relationship('User', foreign_keys=[created_by], backref='created_departments', post_update=True)
    
    # 索引
    __table_args__ = (
        db.Index('idx_department_name', 'department_name'),
        db.Index('idx_department_code', 'department_code'),
        db.Index('idx_parent_id', 'parent_id'),
    )
    
    @property
    def full_name(self):
        """获取完整部门名称（包含上级部门）"""
        if self.parent:
            return f"{self.parent.full_name} > {self.department_name}"
        return self.department_name
    
    @classmethod
    def get_active_departments(cls):
        """获取所有启用的部门"""
        return cls.query.filter_by(is_active=True).order_by(cls.sort_order, cls.department_name).all()
    
    @classmethod
    def get_root_departments(cls):
        """获取根部门（无上级部门）"""
        return cls.query.filter_by(parent_id=None, is_active=True).order_by(cls.sort_order, cls.department_name).all()
    
    def get_all_children(self):
        """获取所有子部门（递归）"""
        children = []
        for child in self.children:
            if child.is_active:
                children.append(child)
                children.extend(child.get_all_children())
        return children
    
    def __repr__(self):
        return f'<Department {self.department_name}>'

class User(UserMixin, db.Model):
    """用户账号表"""
    __tablename__ = 'user_accounts'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False, comment='用户名')
    password_hash = db.Column(db.String(255), nullable=False, comment='密码哈希')
    real_name = db.Column(db.String(100), nullable=False, comment='真实姓名')
    department_id = db.Column(db.Integer, db.ForeignKey('departments.id'), nullable=False, comment='所属部门ID')
    role_id = db.Column(db.Integer, db.ForeignKey('user_roles.id'), nullable=False, comment='角色ID')
    is_active = db.Column(db.Boolean, default=True, comment='是否启用')
    created_time = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_time = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    last_login_time = db.Column(db.DateTime, comment='最后登录时间')
    created_by = db.Column(db.Integer, db.ForeignKey('user_accounts.id'), comment='创建人ID')
    
    # 关系
    role = db.relationship('Role', backref='users', lazy=True)
    department = db.relationship('Department', foreign_keys=[department_id], backref='users', lazy=True)
    creator = db.relationship('User', remote_side=[id], backref='created_users')
    
    # 索引
    __table_args__ = (
        db.Index('idx_username', 'username'),
        db.Index('idx_role_id', 'role_id'),
        db.Index('idx_department_id', 'department_id'),
    )
    
    def set_password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)
    
    def has_permission(self, permission_code):
        """检查用户是否有指定权限"""
        try:
            if not self.role:
                return False
            
            # 管理员拥有所有权限
            if self.role.role_code == 'ADMIN':
                return True
            
            return db.session.query(
                db.exists().where(
                    db.and_(
                        RolePermission.role_id == self.role_id,
                        Permission.id == RolePermission.permission_id,
                        Permission.permission_code == permission_code
                    )
                )
            ).scalar()
            
        except Exception as e:
            # 记录错误但不抛出异常，默认拒绝访问
            import logging
            logging.error(f"权限检查失败 - 用户ID: {self.id}, 权限: {permission_code}, 错误: {e}")
            return False
    
    def get_permissions(self):
        """获取用户所有权限"""
        if not self.role:
            return []
        
        return db.session.query(Permission).join(
            RolePermission, Permission.id == RolePermission.permission_id
        ).filter(RolePermission.role_id == self.role_id).all()
    
    def is_admin(self):
        """判断是否为管理员"""
        return self.role and self.role.role_code == 'ADMIN'
    
    def can_view_user_data(self, target_user):
        """判断是否可以查看目标用户的数据"""
        if self.is_admin():
            return True
        
        if self.role.role_code == 'GENERAL_MANAGER':
            return True
        
        if self.role.role_code == 'DEPARTMENT_MANAGER':
            return self.department_id == target_user.department_id
        
        return self.id == target_user.id
    
    def __repr__(self):
        return f'<User {self.username}>'

class Role(db.Model):
    """角色表"""
    __tablename__ = 'user_roles'
    
    id = db.Column(db.Integer, primary_key=True)
    role_name = db.Column(db.String(50), unique=True, nullable=False, comment='角色名称')
    role_code = db.Column(db.String(50), unique=True, nullable=False, comment='角色代码')
    description = db.Column(db.Text, comment='角色描述')
    is_active = db.Column(db.Boolean, default=True, comment='是否启用')
    created_time = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    
    # 关系
    permissions = db.relationship('Permission', secondary='role_permissions', backref='roles', lazy='dynamic')
    
    # 索引
    __table_args__ = (
        db.Index('idx_role_code', 'role_code'),
    )
    
    def __repr__(self):
        return f'<Role {self.role_name}>'

class Permission(db.Model):
    """权限表"""
    __tablename__ = 'permissions'
    
    id = db.Column(db.Integer, primary_key=True)
    permission_name = db.Column(db.String(100), nullable=False, comment='权限名称')
    permission_code = db.Column(db.String(100), unique=True, nullable=False, comment='权限代码')
    module_name = db.Column(db.String(50), nullable=False, comment='所属模块')
    description = db.Column(db.Text, comment='权限描述')
    created_time = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    
    # 索引
    __table_args__ = (
        db.Index('idx_permission_code', 'permission_code'),
        db.Index('idx_module_name', 'module_name'),
    )
    
    def __repr__(self):
        return f'<Permission {self.permission_name}>'

class RolePermission(db.Model):
    """角色权限关联表"""
    __tablename__ = 'role_permissions'
    
    id = db.Column(db.Integer, primary_key=True)
    role_id = db.Column(db.Integer, db.ForeignKey('user_roles.id'), nullable=False, comment='角色ID')
    permission_id = db.Column(db.Integer, db.ForeignKey('permissions.id'), nullable=False, comment='权限ID')
    created_time = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    
    # 唯一约束
    __table_args__ = (
        db.UniqueConstraint('role_id', 'permission_id', name='uk_role_permission'),
    )
    
    def __repr__(self):
        return f'<RolePermission role_id={self.role_id} permission_id={self.permission_id}>'
