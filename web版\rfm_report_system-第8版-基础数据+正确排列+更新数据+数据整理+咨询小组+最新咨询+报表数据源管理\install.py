#!/usr/bin/env python3
"""
RFM报表处理系统安装脚本
自动化安装和配置系统
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 50)
    print(f"  {title}")
    print("=" * 50)

def run_command(command, description):
    """运行命令并处理错误"""
    print(f"\n{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description}完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description}失败: {e}")
        if e.stdout:
            print(f"输出: {e.stdout}")
        if e.stderr:
            print(f"错误: {e.stderr}")
        return False

def check_python():
    """检查Python版本"""
    print("检查Python版本...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✓ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"✗ Python版本过低: {version.major}.{version.minor}.{version.micro}")
        print("需要Python 3.8或更高版本")
        return False

def create_virtual_environment():
    """创建虚拟环境"""
    if os.path.exists('venv'):
        print("虚拟环境已存在，跳过创建")
        return True
    
    return run_command(f"{sys.executable} -m venv venv", "创建虚拟环境")

def install_dependencies():
    """安装依赖包"""
    # 确定pip路径
    if os.name == 'nt':  # Windows
        pip_path = os.path.join('venv', 'Scripts', 'pip.exe')
        python_path = os.path.join('venv', 'Scripts', 'python.exe')
    else:  # Linux/Mac
        pip_path = os.path.join('venv', 'bin', 'pip')
        python_path = os.path.join('venv', 'bin', 'python')

    if not os.path.exists(pip_path):
        print("✗ 找不到虚拟环境中的pip")
        return False

    # 升级pip（使用python -m pip方式，更可靠）
    upgrade_cmd = f"{python_path} -m pip install --upgrade pip"
    print(f"\n升级pip...")
    try:
        result = subprocess.run(upgrade_cmd, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ 升级pip完成")
    except subprocess.CalledProcessError as e:
        # pip升级失败不是致命错误，继续安装依赖
        print(f"⚠ pip升级失败，但继续安装依赖: {e}")

    # 安装依赖
    return run_command(f"{pip_path} install -r requirements.txt", "安装依赖包")

def setup_environment_config():
    """设置环境配置"""
    if os.path.exists('.env'):
        print("环境配置文件已存在，跳过创建")
        return True
    
    if not os.path.exists('.env.example'):
        print("✗ 找不到 .env.example 文件")
        return False
    
    try:
        shutil.copy('.env.example', '.env')
        print("✓ 创建环境配置文件")
        
        print("\n请编辑 .env 文件配置以下信息:")
        print("- SECRET_KEY: 应用密钥（建议生成随机字符串）")
        print("- SYSTEM_DB_HOST: MySQL服务器地址")
        print("- SYSTEM_DB_PORT: MySQL端口（默认3306）")
        print("- SYSTEM_DB_USER: MySQL用户名")
        print("- SYSTEM_DB_PASSWORD: MySQL密码")
        
        return True
    except Exception as e:
        print(f"✗ 创建环境配置文件失败: {e}")
        return False

def create_directories():
    """创建必要的目录"""
    directories = [
        'logs',
        'uploads',
        'uploads/results'
    ]
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✓ 创建目录: {directory}")
        except Exception as e:
            print(f"✗ 创建目录失败 {directory}: {e}")
            return False
    
    return True

def set_file_permissions():
    """设置文件权限（仅Linux/Mac）"""
    if os.name == 'nt':  # Windows
        return True
    
    scripts = ['start.sh', 'run.py', 'init_db.py', 'manage.py', 'check_system.py']
    
    for script in scripts:
        if os.path.exists(script):
            try:
                os.chmod(script, 0o755)
                print(f"✓ 设置执行权限: {script}")
            except Exception as e:
                print(f"⚠ 设置权限失败 {script}: {e}")
    
    return True

def generate_secret_key():
    """生成随机密钥"""
    import secrets
    import string
    
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    secret_key = ''.join(secrets.choice(alphabet) for _ in range(32))
    
    try:
        # 读取.env文件
        with open('.env', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换SECRET_KEY
        content = content.replace(
            'SECRET_KEY=your-very-secret-key-here',
            f'SECRET_KEY={secret_key}'
        )
        
        # 写回文件
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✓ 生成并设置随机密钥")
        return True
    except Exception as e:
        print(f"⚠ 设置密钥失败: {e}")
        return False

def main():
    """主安装流程"""
    print_header("RFM报表处理系统安装程序")

    # 确保在正确的目录中运行
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"工作目录: {os.getcwd()}")

    print("本程序将自动安装和配置RFM报表处理系统")
    print("请确保已安装MySQL服务器并且可以正常连接")

    # 确认继续
    try:
        response = input("\n是否继续安装？(y/N): ").strip().lower()
        if response != 'y':
            print("安装已取消")
            return False
    except KeyboardInterrupt:
        print("\n安装已取消")
        return False
    
    # 安装步骤
    steps = [
        ("检查Python环境", check_python),
        ("创建虚拟环境", create_virtual_environment),
        ("安装依赖包", install_dependencies),
        ("设置环境配置", setup_environment_config),
        ("生成安全密钥", generate_secret_key),
        ("创建目录结构", create_directories),
        ("设置文件权限", set_file_permissions)
    ]
    
    failed_steps = []
    
    for step_name, step_func in steps:
        print_header(step_name)
        try:
            if not step_func():
                failed_steps.append(step_name)
        except Exception as e:
            print(f"✗ {step_name}失败: {e}")
            failed_steps.append(step_name)
    
    # 安装结果
    print_header("安装结果")
    
    if not failed_steps:
        print("✓ 安装成功完成！")
        print("\n后续步骤:")
        print("1. 编辑 .env 文件，配置MySQL数据库连接信息")
        print("2. 运行 python check_system.py 检查系统环境")
        print("3. 运行 python init_db.py 初始化数据库")
        print("4. 运行 python run.py 启动系统")
        print("\n或者直接运行启动脚本:")
        if os.name == 'nt':
            print("  start.bat")
        else:
            print("  ./start.sh")
        return True
    else:
        print(f"✗ 安装过程中有 {len(failed_steps)} 个步骤失败:")
        for step in failed_steps:
            print(f"  - {step}")
        print("\n请根据错误信息修复问题后重新运行安装程序")
        return False

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n安装已取消")
        sys.exit(1)
    except Exception as e:
        print(f"\n安装过程中发生错误: {e}")
        sys.exit(1)
