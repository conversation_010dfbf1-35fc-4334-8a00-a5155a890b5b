{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">客户管理</h1>
    <div class="btn-group">
        {% if current_user.has_permission('create_customer') %}
        <a href="{{ url_for('registration.register') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>登记客户
        </a>
        {% endif %}
        <button type="button" class="btn btn-success" onclick="exportCustomers()">
            <i class="fas fa-download me-2"></i>导出Excel
        </button>
        {% if current_user.has_permission('delete_customer') %}
        <button type="button" class="btn btn-danger" onclick="batchDeleteCustomers()" id="batchDeleteBtn" style="display: none;">
            <i class="fas fa-trash me-2"></i>批量删除
        </button>
        {% endif %}
    </div>
</div>

<!-- 搜索表单 -->
<div class="card mb-3">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-2">
                {{ form.query.label(class="form-label") }}
                {{ form.query(class="form-control", placeholder="卡号或咨询内容") }}
            </div>
            <div class="col-md-2">
                {{ form.onsite_consultant_id.label(class="form-label") }}
                {{ form.onsite_consultant_id(class="form-select select2-consultant-filter") }}
            </div>
            <div class="col-md-2">
                {{ form.online_consultant_id.label(class="form-label") }}
                {{ form.online_consultant_id(class="form-select select2-online-consultant") }}
            </div>
            <div class="col-md-2">
                {{ form.channel_category.label(class="form-label") }}
                {{ form.channel_category(class="form-select") }}
            </div>
            <div class="col-md-2">
                {{ form.channel_id.label(class="form-label") }}
                {{ form.channel_id(class="form-select select2-channel-filter") }}
            </div>
            <div class="col-md-2">
                {{ form.has_follow_up.label(class="form-label") }}
                {{ form.has_follow_up(class="form-select") }}
            </div>
            <div class="col-md-3">
                {{ form.start_date.label(class="form-label") }}
                {{ form.start_date(class="form-control", placeholder="开始日期") }}
            </div>
            <div class="col-md-3">
                {{ form.end_date.label(class="form-label") }}
                {{ form.end_date(class="form-control", placeholder="结束日期") }}
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-search me-1"></i>搜索
                </button>
                <a href="{{ url_for('registration.customers') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-undo me-1"></i>重置
                </a>
            </div>
        </form>
    </div>
</div>

<!-- 客户列表 -->
<div class="card">
    <div class="card-body">
        {% if pagination.items %}
            <div class="table-responsive">
                <form id="batchDeleteForm" method="POST" action="{{ url_for('registration.batch_delete_customers') }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                {% if current_user.has_permission('delete_customer') %}
                                <th width="50">
                                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                </th>
                                {% endif %}
                                <th>卡号</th>
                            <th>所属现场</th>
                            <th>所属网资</th>
                            <th>渠道分类</th>
                            <th>激活渠道</th>
                            <th>最近来院时间</th>
                            <th>跟进状态</th>
                            <th>跟进到院状态</th>
                            <th>跟进消费金额</th>
                            <th>信息登记时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for customer in pagination.items %}
                        <tr>
                            {% if current_user.has_permission('delete_customer') %}
                            <td>
                                <input type="checkbox" name="customer_ids" value="{{ customer.id }}" class="customer-checkbox">
                            </td>
                            {% endif %}
                            <td>
                                <strong>{{ customer.card_number }}</strong>
                            </td>
                            <td>{{ customer.onsite_consultant.real_name }}</td>
                            <td>{{ customer.online_consultant.real_name }}</td>
                            <td>
                                {% if customer.channel.category %}
                                    <span class="badge bg-secondary">{{ customer.channel.category }}</span>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-info">{{ customer.channel.name }}</span>
                            </td>
                            <td>
                                {% if customer.last_visit_date %}
                                    {{ customer.last_visit_date.strftime('%Y-%m-%d') }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>
                                {% if customer.follow_up_note and customer.follow_up_note.strip() %}
                                    <span class="badge bg-success">已跟进</span>
                                    {% if customer.follow_up_time %}
                                        <br><small class="text-muted">{{ customer.follow_up_time.strftime('%m-%d %H:%M') }}</small>
                                    {% endif %}
                                {% else %}
                                    <span class="badge bg-warning">待跟进</span>
                                {% endif %}
                            </td>
                            <td>
                                {% set visit_status = customer.get_follow_up_visit_status() %}
                                {% if visit_status == "已到院" %}
                                    <span class="badge bg-success">{{ visit_status }}</span>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% set consumption_amount = customer.get_follow_up_consumption_amount() %}
                                {% if consumption_amount %}
                                    <span class="text-success fw-bold">¥{{ "%.2f"|format(consumption_amount) }}</span>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>{{ customer.registration_date.strftime('%Y/%m/%d') }}</td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{{ url_for('registration.customer_detail', customer_id=customer.id) }}"
                                       class="btn btn-outline-primary" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if current_user.role == 'admin' or customer.created_by == current_user.id %}
                                    <a href="{{ url_for('registration.edit_customer', customer_id=customer.id) }}"
                                       class="btn btn-outline-secondary" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% endif %}
                                    {% if current_user.has_permission('delete_customer') %}
                                    <button type="button" class="btn btn-outline-danger" title="删除"
                                            onclick="deleteCustomer({{ customer.id }}, {{ customer.card_number|tojson }})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                </form>
            </div>
            
            <!-- 分页 -->
            {% if pagination.pages > 1 %}
            <nav aria-label="客户列表分页">
                <ul class="pagination justify-content-center">
                    {% if pagination.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('registration.customers', page=pagination.prev_num, **request.args) }}">上一页</a>
                        </li>
                    {% endif %}
                    
                    {% for page_num in range(1, pagination.pages + 1) %}
                        {% if page_num == pagination.page %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% else %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('registration.customers', page=page_num, **request.args) }}">{{ page_num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if pagination.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('registration.customers', page=pagination.next_num, **request.args) }}">下一页</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
            
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <p class="text-muted">暂无客户数据</p>
                {% if current_user.has_permission('create_customer') %}
                    <a href="{{ url_for('registration.register') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>登记第一个客户
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<!-- 删除功能JS -->
<script src="{{ url_for('static', filename='js/delete_functions.js') }}"></script>

<script>
// 客户管理页面JavaScript初始化
console.log('客户列表页面JavaScript初始化...');

$(document).ready(function() {
    // 初始化现场咨询师筛选器
    $('.select2-consultant-filter').select2({
        theme: 'bootstrap-5',
        placeholder: '选择现场咨询师',
        allowClear: true,
        width: '100%',
        language: {
            noResults: function() {
                return "未找到匹配的现场咨询师";
            },
            searching: function() {
                return "搜索中...";
            }
        }
    });
    
    // 初始化渠道筛选器
    $('.select2-channel-filter').select2({
        theme: 'bootstrap-5',
        placeholder: '选择渠道',
        allowClear: true,
        width: '100%',
        language: {
            noResults: function() {
                return "未找到匹配的渠道";
            },
            searching: function() {
                return "搜索中...";
            }
        }
    });
});

// 删除功能JavaScript已通过外部文件加载
console.log('客户列表页面JavaScript初始化...');
</script>

<style>
/* Select2 自定义样式 */
.select2-container--bootstrap-5 .select2-selection {
    min-height: 38px;
}

.select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
    line-height: 36px;
    padding-left: 12px;
}

.select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
    height: 36px;
    right: 12px;
}

.select2-dropdown {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
}

.select2-results__option--highlighted {
    background-color: #0d6efd;
    color: white;
}

.select2-search--dropdown .select2-search__field {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 8px 12px;
}
</style>
{% endblock %}