{% extends "base.html" %}

{% block title %}系统首页 - 老客回访与跟进系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-home"></i> 欢迎使用老客回访与跟进系统</h2>
        <p class="text-muted">您好，{{ current_user.real_name }}！当前角色：{{ current_user.role.role_name }}</p>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> 系统功能</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% if current_user.has_permission('CUSTOMER_REGISTER') %}
                    <div class="col-md-6 mb-3">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <i class="fas fa-user-plus fa-3x text-primary mb-3"></i>
                                <h5>客户登记</h5>
                                <p class="text-muted">登记新的客户信息</p>
                                <a href="{{ url_for('customer.register') }}" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> 开始登记
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if current_user.has_permission('CUSTOMER_VIEW') %}
                    <div class="col-md-6 mb-3">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-3x text-success mb-3"></i>
                                <h5>客户管理</h5>
                                <p class="text-muted">查看和管理客户信息</p>
                                <a href="{{ url_for('customer.index') }}" class="btn btn-success">
                                    <i class="fas fa-list"></i> 客户列表
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if current_user.has_permission('FOLLOW_UP_ADD') %}
                    <div class="col-md-6 mb-3">
                        <div class="card border-warning">
                            <div class="card-body text-center">
                                <i class="fas fa-comments fa-3x text-warning mb-3"></i>
                                <h5>客户跟进</h5>
                                <p class="text-muted">添加客户跟进记录</p>
                                <a href="{{ url_for('customer.index') }}" class="btn btn-warning">
                                    <i class="fas fa-edit"></i> 开始跟进
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if current_user.has_permission('CHANNEL_VIEW') %}
                    <div class="col-md-6 mb-3">
                        <div class="card border-warning">
                            <div class="card-body text-center">
                                <i class="fas fa-tags fa-3x text-warning mb-3"></i>
                                <h5>渠道管理</h5>
                                <p class="text-muted">管理客户来源渠道</p>
                                <a href="{{ url_for('channel.index') }}" class="btn btn-warning">
                                    <i class="fas fa-tags"></i> 渠道管理
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    {% if current_user.has_permission('REPORT_VIEW') %}
                    <div class="col-md-6 mb-3">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-bar fa-3x text-info mb-3"></i>
                                <h5>统计报表</h5>
                                <p class="text-muted">查看数据统计和报表</p>
                                <a href="{{ url_for('report.statistics') }}" class="btn btn-info">
                                    <i class="fas fa-chart-line"></i> 查看报表
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-user"></i> 个人信息</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><i class="fas fa-user"></i> 姓名</td>
                        <td>{{ current_user.real_name }}</td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-id-badge"></i> 用户名</td>
                        <td>{{ current_user.username }}</td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-building"></i> 部门</td>
                        <td>{{ current_user.department }}</td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-user-tag"></i> 角色</td>
                        <td>{{ current_user.role.role_name }}</td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-clock"></i> 最后登录</td>
                        <td>{{ current_user.last_login_time.strftime('%m-%d %H:%M') if current_user.last_login_time else '首次登录' }}</td>
                    </tr>
                </table>
                
                <div class="d-grid gap-2">
                    <a href="{{ url_for('main.profile') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-edit"></i> 编辑资料
                    </a>
                    <a href="{{ url_for('auth.change_password') }}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-key"></i> 修改密码
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-question-circle"></i> 帮助信息</h5>
            </div>
            <div class="card-body">
                <p class="text-muted small">
                    根据您的角色权限，您可以使用以上功能模块。如有疑问，请联系系统管理员。
                </p>
                
                <div class="d-grid">
                    <a href="{{ url_for('main.help') }}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-book"></i> 使用帮助
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bullhorn"></i> 系统公告</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>欢迎使用老客回访与跟进系统！</strong>
                    <br>
                    系统已成功启动，您可以开始使用各项功能。如果是首次使用，建议先查看使用帮助。
                </div>
                
                {% if current_user.username == 'admin' %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>安全提醒：</strong>
                    检测到您正在使用默认管理员账号，请及时修改密码以确保系统安全。
                    <a href="{{ url_for('auth.change_password') }}" class="alert-link">立即修改</a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
