"""
主页面视图控制器
"""
from flask import Blueprint, render_template, redirect, url_for
from flask_login import login_required, current_user
from app.utils.permissions import require_login

bp = Blueprint('main', __name__)

@bp.route('/')
def index():
    """首页"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    else:
        return redirect(url_for('auth.login'))

@bp.route('/dashboard')
@require_login
def dashboard():
    """仪表板"""
    # 根据用户角色显示不同的仪表板
    role_code = current_user.role.role_code
    
    if role_code == 'ADMIN':
        return render_template('dashboard/admin_dashboard.html')
    elif role_code == 'GENERAL_MANAGER':
        return render_template('dashboard/manager_dashboard.html')
    elif role_code == 'DEPARTMENT_MANAGER':
        return render_template('dashboard/department_dashboard.html')
    elif role_code == 'NETWORK_CONSULTANT':
        return render_template('dashboard/network_dashboard.html')
    elif role_code == 'FIELD_CONSULTANT':
        return render_template('dashboard/field_dashboard.html')
    else:
        return render_template('dashboard/default_dashboard.html')

@bp.route('/profile')
@require_login
def profile():
    """个人资料"""
    return render_template('profile/profile.html')

@bp.route('/help')
@require_login
def help():
    """帮助页面"""
    return render_template('help/help.html')
