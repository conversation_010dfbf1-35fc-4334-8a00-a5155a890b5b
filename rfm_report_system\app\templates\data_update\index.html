{% extends "base.html" %}

{% block title %}数据更新{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-database me-2 text-primary"></i>数据更新</h2>
                    <p class="text-muted">通过Excel文件安全地维护数据库表数据</p>
                </div>
                <div>
                    <button class="btn btn-info" onclick="showHelp()">
                        <i class="fas fa-question-circle me-2"></i>使用帮助
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 当前数据库信息 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-server me-2"></i>当前数据库</h5>
                        <span class="badge {% if connection_status == 'connected' %}bg-success{% else %}bg-danger{% endif %}">
                            {% if connection_status == 'connected' %}
                            <i class="fas fa-check-circle me-1"></i>已连接
                            {% else %}
                            <i class="fas fa-times-circle me-1"></i>连接失败
                            {% endif %}
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h6>{{ db_config.name }}</h6>
                            <p class="text-muted mb-0">
                                <i class="fas fa-database me-1"></i>{{ db_config.db_type }} |
                                <i class="fas fa-server me-1"></i>{{ db_config.host }}:{{ db_config.port }}
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('data_update.data_preview') }}" class="btn btn-outline-info btn-sm me-2">
                                <i class="fas fa-chart-bar me-1"></i>数据预览
                            </a>
                            <a href="{{ url_for('data_update.data_cleanup') }}" class="btn btn-outline-warning btn-sm me-2">
                                <i class="fas fa-broom me-1"></i>数据整理
                            </a>
                            <a href="{{ url_for('config_mgmt.list_configs') }}" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-cog me-1"></i>配置管理
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作模式选择 -->
    {% if connection_status == 'connected' %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-cog me-2"></i>选择操作模式</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card operation-card h-100" onclick="selectOperation('append')">
                                <div class="card-body text-center">
                                    <i class="fas fa-plus-circle fa-3x text-success mb-3"></i>
                                    <h6>追加数据</h6>
                                    <p class="small text-muted">将Excel数据追加到现有表的末尾</p>
                                    <div class="alert alert-warning alert-sm p-2 mb-2">
                                        <small><i class="fas fa-info-circle me-1"></i>不支持季度表</small>
                                    </div>
                                    <button class="btn btn-outline-success btn-sm w-100">
                                        <i class="fas fa-arrow-right me-1"></i>选择此模式
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card operation-card h-100" onclick="selectOperation('update')">
                                <div class="card-body text-center">
                                    <i class="fas fa-sync-alt fa-3x text-warning mb-3"></i>
                                    <h6>更新数据 <i class="fas fa-exclamation-triangle text-danger" title="高风险操作"></i></h6>
                                    <p class="small text-muted">清空现有表后导入新数据</p>
                                    <button class="btn btn-outline-warning btn-sm w-100">
                                        <i class="fas fa-arrow-right me-1"></i>选择此模式
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card operation-card h-100" onclick="selectOperation('create_table')">
                                <div class="card-body text-center">
                                    <i class="fas fa-table fa-3x text-info mb-3"></i>
                                    <h6>创建新表</h6>
                                    <p class="small text-muted">根据Excel文件结构创建新表</p>
                                    <button class="btn btn-outline-info btn-sm w-100">
                                        <i class="fas fa-arrow-right me-1"></i>选择此模式
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 表选择区域（仅追加和更新模式显示） -->
    <div class="row mb-4" id="tableSelectionArea" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-table me-2"></i>选择目标表</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>请先选择操作模式</h6>
                        <p class="mb-0">选择操作模式后，系统将显示相应的数据表列表。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% elif connection_status != 'connected' %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-danger">
                <h6><i class="fas fa-exclamation-triangle me-2"></i>数据库连接失败</h6>
                <p class="mb-0">无法连接到数据库，请检查 <a href="{{ url_for('config_mgmt.list_configs') }}">数据库配置</a>。</p>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 功能说明 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle me-2"></i>功能说明</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <i class="fas fa-plus-circle fa-3x text-success mb-2"></i>
                                <h6>追加数据</h6>
                                <p class="small text-muted">将Excel中的数据追加到现有表的末尾，不影响原有数据</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <i class="fas fa-sync-alt fa-3x text-warning mb-2"></i>
                                <h6>更新数据</h6>
                                <p class="small text-muted">清空表中所有数据，然后导入Excel中的新数据</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <i class="fas fa-table fa-3x text-info mb-2"></i>
                                <h6>创建新表</h6>
                                <p class="small text-muted">根据Excel文件结构创建新表并导入数据</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 帮助模态框 -->
<div class="modal fade" id="helpModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">数据更新使用帮助</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>操作流程</h6>
                <ol>
                    <li>选择要操作的数据库</li>
                    <li>选择目标数据表</li>
                    <li>选择操作类型（追加/更新/创建新表）</li>
                    <li>上传Excel文件</li>
                    <li>预览操作结果</li>
                    <li>确认执行操作</li>
                </ol>

                <h6 class="mt-4">注意事项</h6>
                <ul>
                    <li>Excel文件大小不能超过50MB</li>
                    <li>只支持.xlsx和.xls格式</li>
                    <li>Excel第一行必须是列名</li>
                    <li>列名必须与数据库表完全匹配（追加和更新模式）</li>
                    <li>更新操作会清空原有数据，请谨慎使用</li>
                    <li>所有操作都支持预览和撤销</li>
                </ul>

                <h6 class="mt-4">安全保障</h6>
                <ul>
                    <li>所有操作都在事务中执行，确保数据一致性</li>
                    <li>高风险操作需要二次确认</li>
                    <li>支持操作撤销功能</li>
                    <li>详细的操作日志记录</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let selectedOperation = null;

// 选择操作模式
function selectOperation(operation) {
    selectedOperation = operation;

    // 更新UI状态
    $('.operation-card').removeClass('border-primary border-success border-warning border-info');
    $(`.operation-card`).each(function() {
        $(this).removeClass('border-primary');
    });

    // 高亮选中的操作
    const selectedCard = $(`.operation-card`).filter(function() {
        return $(this).attr('onclick').includes(operation);
    });

    if (operation === 'append') {
        selectedCard.addClass('border-success');
    } else if (operation === 'update') {
        selectedCard.addClass('border-warning');
    } else if (operation === 'create_table') {
        selectedCard.addClass('border-info');
    }

    // 根据操作类型显示不同的界面
    if (operation === 'create_table') {
        // 创建新表模式：直接跳转到任务创建页面
        window.location.href = `{{ url_for("data_update.create_task") }}?operation=${operation}`;
    } else {
        // 追加或更新模式：显示表选择区域并过滤表
        filterAndShowTables(operation);
        $('#tableSelectionArea').show();

        // 滚动到表选择区域
        $('html, body').animate({
            scrollTop: $('#tableSelectionArea').offset().top - 100
        }, 500);
    }
}

// 选择表
function selectTable(tableName) {
    if (!selectedOperation) {
        Swal.fire('错误', '请先选择操作模式', 'error');
        return;
    }

    // 跳转到任务创建页面
    window.location.href = `{{ url_for("data_update.create_task") }}?operation=${selectedOperation}&table_name=${encodeURIComponent(tableName)}`;
}

// 显示帮助
function showHelp() {
    $('#helpModal').modal('show');
}

// 操作卡片悬停效果
$(document).on('mouseenter', '.operation-card', function() {
    $(this).addClass('shadow');
});

$(document).on('mouseleave', '.operation-card', function() {
    $(this).removeClass('shadow');
});

// 根据操作类型过滤和显示表
function filterAndShowTables(operation) {
    const allTables = {{ tables | tojson }};
    let filteredTables = [];

    if (operation === 'append') {
        // 追加模式：过滤掉季度表
        filteredTables = allTables.filter(function(table) {
            return !isQuarterlyTable(table.name);
        });

        // 更新表选择区域的标题
        $('#tableSelectionArea .card-header h5').html('<i class="fas fa-table me-2"></i>选择目标表 <small class="text-muted">(已过滤季度表)</small>');
    } else if (operation === 'update') {
        // 更新模式：显示所有表
        filteredTables = allTables;
        $('#tableSelectionArea .card-header h5').html('<i class="fas fa-table me-2"></i>选择目标表');
    }

    // 重新渲染表列表
    renderTableList(filteredTables, operation);
}

// 判断是否为季度表
function isQuarterlyTable(tableName) {
    const quarterlyKeywords = ['季度', 'quarterly', 'quarter', 'q1', 'q2', 'q3', 'q4'];
    const tableNameLower = tableName.toLowerCase();

    // 检查关键词
    for (let keyword of quarterlyKeywords) {
        if (tableNameLower.includes(keyword)) {
            return true;
        }
    }

    // 检查季度模式，如 2024Q1, 2024_Q2 等
    const quarterlyPattern = /(20\d{2}[_\-]?q[1-4]|q[1-4][_\-]?20\d{2})/i;
    return quarterlyPattern.test(tableName);
}

// 渲染表列表
function renderTableList(tables, operation) {
    const tableListContainer = $('#tableSelectionArea .card-body');

    if (tables.length === 0) {
        let message = '没有找到可用的数据表';
        if (operation === 'append') {
            message = '没有找到可用于追加的数据表（季度表已被过滤）';
        }

        tableListContainer.html(`
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle me-2"></i>${message}</h6>
                <p class="mb-0">您可以选择"创建新表"模式来创建第一个表。</p>
            </div>
        `);
        return;
    }

    let html = '<div class="row">';

    tables.forEach(function(table) {
        let tableTypeIcon = '';
        let tableTypeText = '';

        if (table.is_quarterly || isQuarterlyTable(table.name)) {
            tableTypeIcon = '<i class="fas fa-calendar-alt text-warning ms-1" title="季度表"></i>';
            tableTypeText = '<small class="text-warning">季度表</small>';
        }

        html += `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card table-card h-100" onclick="selectTable('${table.name}')">
                    <div class="card-body">
                        <h6 class="card-title">
                            ${table.name}
                            ${tableTypeIcon}
                        </h6>
                        <p class="card-text small text-muted">
                            <i class="fas fa-list me-1"></i>${table.record_count} 行数据<br>
                            <i class="fas fa-columns me-1"></i>${table.column_count} 列
                            ${tableTypeText ? '<br>' + tableTypeText : ''}
                        </p>
                        <button class="btn btn-outline-primary btn-sm w-100">
                            <i class="fas fa-arrow-right me-1"></i>选择此表
                        </button>
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';
    tableListContainer.html(html);
}

// 表卡片悬停效果
$(document).on('mouseenter', '.table-card', function() {
    $(this).addClass('border-primary shadow');
});

$(document).on('mouseleave', '.table-card', function() {
    $(this).removeClass('border-primary shadow');
});
</script>

<style>
.operation-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid #dee2e6;
}

.operation-card:hover {
    transform: translateY(-2px);
}

.table-card {
    cursor: pointer;
    transition: all 0.3s ease;
}

.table-card:hover {
    transform: translateY(-2px);
}
</style>
{% endblock %}
