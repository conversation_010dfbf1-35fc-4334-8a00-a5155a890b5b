#!/usr/bin/env python3
"""
部门管理数据库迁移脚本
将用户表中的部门字符串字段迁移为部门ID关联
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.user import User, Department
from sqlalchemy import text

def create_departments_table():
    """创建部门表"""
    print("创建部门表...")
    
    # 创建部门表的SQL
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS departments (
        id INT PRIMARY KEY AUTO_INCREMENT,
        department_name VARCHAR(100) UNIQUE NOT NULL COMMENT '部门名称',
        department_code VARCHAR(50) UNIQUE NOT NULL COMMENT '部门代码',
        description TEXT COMMENT '部门描述',
        parent_id INT COMMENT '上级部门ID',
        is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
        sort_order INT DEFAULT 0 COMMENT '排序顺序',
        created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        created_by INT COMMENT '创建人ID',
        
        INDEX idx_department_name (department_name),
        INDEX idx_department_code (department_code),
        INDEX idx_parent_id (parent_id),
        FOREIGN KEY (parent_id) REFERENCES departments(id),
        FOREIGN KEY (created_by) REFERENCES user_accounts(id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部门表'
    """
    
    db.session.execute(text(create_table_sql))
    db.session.commit()
    print("部门表创建完成")

def migrate_existing_departments():
    """迁移现有部门数据"""
    print("分析现有用户部门数据...")
    
    # 获取所有现有的部门名称（去重）
    existing_departments = db.session.execute(
        text("SELECT DISTINCT department FROM user_accounts WHERE department IS NOT NULL AND department != ''")
    ).fetchall()
    
    if not existing_departments:
        print("未发现现有部门数据，创建默认部门...")
        # 创建默认部门
        default_departments = [
            {'name': '管理部', 'code': 'ADMIN', 'description': '系统管理部门'},
            {'name': '网络咨询部', 'code': 'NETWORK', 'description': '网络咨询部门'},
            {'name': '现场咨询部', 'code': 'FIELD', 'description': '现场咨询部门'},
            {'name': '技术部', 'code': 'TECH', 'description': '技术支持部门'},
        ]
        
        for dept_info in default_departments:
            department = Department(
                department_name=dept_info['name'],
                department_code=dept_info['code'],
                description=dept_info['description'],
                created_by=1  # 假设管理员ID为1
            )
            db.session.add(department)
        
        db.session.commit()
        print(f"创建了 {len(default_departments)} 个默认部门")
        return
    
    print(f"发现 {len(existing_departments)} 个现有部门，开始迁移...")
    
    # 为每个现有部门创建部门记录
    department_mapping = {}
    for i, (dept_name,) in enumerate(existing_departments, 1):
        if dept_name and dept_name.strip():
            dept_name = dept_name.strip()
            # 生成部门代码（使用拼音首字母或简化方式）
            dept_code = generate_department_code(dept_name, i)
            
            department = Department(
                department_name=dept_name,
                department_code=dept_code,
                description=f'从原有数据迁移的部门: {dept_name}',
                created_by=1  # 假设管理员ID为1
            )
            
            db.session.add(department)
            db.session.flush()  # 获取ID
            
            department_mapping[dept_name] = department.id
            print(f"  创建部门: {dept_name} (代码: {dept_code}, ID: {department.id})")
    
    db.session.commit()
    print(f"部门迁移完成，共创建 {len(department_mapping)} 个部门")
    return department_mapping

def generate_department_code(dept_name, index):
    """生成部门代码"""
    # 简单的代码生成逻辑
    code_map = {
        '管理': 'ADMIN',
        '技术': 'TECH',
        '销售': 'SALES',
        '市场': 'MARKET',
        '财务': 'FINANCE',
        '人事': 'HR',
        '网络咨询': 'NETWORK',
        '现场咨询': 'FIELD',
        '客服': 'SERVICE',
        '运营': 'OPERATION'
    }
    
    for key, code in code_map.items():
        if key in dept_name:
            return code
    
    # 如果没有匹配，使用DEPT_序号
    return f'DEPT_{index:02d}'

def add_department_id_column():
    """添加department_id列到用户表"""
    print("添加department_id列到用户表...")
    
    # 检查列是否已存在
    column_exists = db.session.execute(text("""
        SELECT COUNT(*) as count FROM information_schema.columns 
        WHERE table_schema = DATABASE() 
        AND table_name = 'user_accounts' 
        AND column_name = 'department_id'
    """)).fetchone()
    
    if column_exists[0] == 0:
        # 添加department_id列
        db.session.execute(text("""
            ALTER TABLE user_accounts 
            ADD COLUMN department_id INT COMMENT '所属部门ID' AFTER real_name
        """))
        
        # 添加外键约束
        db.session.execute(text("""
            ALTER TABLE user_accounts 
            ADD CONSTRAINT fk_user_department 
            FOREIGN KEY (department_id) REFERENCES departments(id)
        """))
        
        # 添加索引
        db.session.execute(text("""
            ALTER TABLE user_accounts 
            ADD INDEX idx_department_id (department_id)
        """))
        
        db.session.commit()
        print("department_id列添加完成")
    else:
        print("department_id列已存在，跳过添加")

def update_user_department_ids():
    """更新用户的department_id"""
    print("更新用户的department_id...")
    
    # 获取所有部门映射
    departments = Department.query.all()
    dept_mapping = {dept.department_name: dept.id for dept in departments}
    
    # 获取所有用户
    users = db.session.execute(text("""
        SELECT id, username, department FROM user_accounts 
        WHERE department IS NOT NULL AND department != ''
    """)).fetchall()
    
    updated_count = 0
    for user_id, username, dept_name in users:
        if dept_name and dept_name.strip() in dept_mapping:
            dept_id = dept_mapping[dept_name.strip()]
            db.session.execute(text("""
                UPDATE user_accounts 
                SET department_id = :dept_id 
                WHERE id = :user_id
            """), {'dept_id': dept_id, 'user_id': user_id})
            updated_count += 1
            print(f"  更新用户 {username}: {dept_name} -> 部门ID {dept_id}")
    
    db.session.commit()
    print(f"用户department_id更新完成，共更新 {updated_count} 个用户")

def handle_users_without_department():
    """处理没有部门的用户"""
    print("处理没有部门的用户...")
    
    # 获取默认部门（如果没有则创建）
    default_dept = Department.query.filter_by(department_code='ADMIN').first()
    if not default_dept:
        default_dept = Department.query.first()
    
    if not default_dept:
        print("错误：没有可用的部门！")
        return
    
    # 更新没有部门的用户
    result = db.session.execute(text("""
        UPDATE user_accounts 
        SET department_id = :dept_id 
        WHERE department_id IS NULL
    """), {'dept_id': default_dept.id})
    
    db.session.commit()
    
    if result.rowcount > 0:
        print(f"将 {result.rowcount} 个无部门用户分配到 {default_dept.department_name}")
    else:
        print("所有用户都已有部门分配")

def make_department_id_required():
    """将department_id设为必填字段"""
    print("将department_id设为必填字段...")
    
    db.session.execute(text("""
        ALTER TABLE user_accounts 
        MODIFY COLUMN department_id INT NOT NULL COMMENT '所属部门ID'
    """))
    
    db.session.commit()
    print("department_id已设为必填字段")

def backup_old_department_column():
    """备份旧的department列"""
    print("备份旧的department列...")
    
    # 重命名旧列为department_old
    try:
        db.session.execute(text("""
            ALTER TABLE user_accounts 
            CHANGE COLUMN department department_old VARCHAR(100) COMMENT '旧部门字段(已废弃)'
        """))
        db.session.commit()
        print("旧department列已重命名为department_old")
    except Exception as e:
        print(f"备份旧列时出错: {e}")
        print("可能旧列已经被处理过，继续执行...")

def main():
    """主迁移函数"""
    print("=" * 50)
    print("开始部门管理数据库迁移")
    print("=" * 50)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 创建部门表
            create_departments_table()
            
            # 2. 迁移现有部门数据
            migrate_existing_departments()
            
            # 3. 添加department_id列
            add_department_id_column()
            
            # 4. 更新用户的department_id
            update_user_department_ids()
            
            # 5. 处理没有部门的用户
            handle_users_without_department()
            
            # 6. 将department_id设为必填
            make_department_id_required()
            
            # 7. 备份旧的department列
            backup_old_department_column()
            
            print("=" * 50)
            print("部门管理数据库迁移完成！")
            print("=" * 50)
            
            # 显示迁移结果
            dept_count = Department.query.count()
            user_count = User.query.filter(User.department_id.isnot(None)).count()
            total_users = User.query.count()
            
            print(f"迁移结果:")
            print(f"  - 部门总数: {dept_count}")
            print(f"  - 已分配部门的用户: {user_count}/{total_users}")
            print(f"  - 迁移状态: 成功")
            
        except Exception as e:
            print(f"迁移过程中出现错误: {e}")
            print("正在回滚...")
            db.session.rollback()
            raise

if __name__ == '__main__':
    main()