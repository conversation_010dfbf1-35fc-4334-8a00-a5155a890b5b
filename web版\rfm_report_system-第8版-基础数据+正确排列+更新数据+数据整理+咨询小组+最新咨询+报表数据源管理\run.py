#!/usr/bin/env python3
"""
RFM报表处理系统启动脚本
"""

import os
import sys
from app import create_app

def main():
    """主函数"""
    # 设置环境变量
    os.environ.setdefault('FLASK_ENV', 'development')
    
    # 创建应用
    app = create_app()
    
    # 创建数据库表（如果不存在）
    with app.app_context():
        try:
            from app import db
            db.create_all()
            print("数据库表检查完成")
        except Exception as e:
            print(f"数据库表检查失败: {e}")
            print("请先运行 python init_db.py 初始化数据库")
    
    # 启动应用
    print("启动RFM报表处理系统...")
    print("访问地址: http://localhost:5000")
    print("按 Ctrl+C 停止服务")
    
    try:
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n系统已停止")
        sys.exit(0)

if __name__ == '__main__':
    main()
