"""
RFM数据源管理器
负责数据导入、删除和状态管理
"""

import pandas as pd
import os
import logging
from flask import current_app
from app.models import db, DataProcessingTask
from app.rfm_reports.models import RFMDataSource, RFMAnalysisData
from datetime import datetime
from app.utils.resource_monitor import resource_monitor


class RFMDataManager:
    """RFM数据源管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def get_available_tasks(self):
        """获取可用的数据处理任务"""
        try:
            # 获取所有已完成的数据处理任务
            tasks = DataProcessingTask.query.filter_by(status='completed').all()
            
            available_tasks = []
            for task in tasks:
                # 检查是否已经导入
                existing_source = RFMDataSource.query.filter_by(source_task_id=task.id).first()
                
                task_info = {
                    'id': task.id,
                    'name': task.task_name,
                    'type': task.task_type,
                    'created_at': task.created_at.isoformat() if task.created_at else None,
                    'file_path': task.output_file_path,
                    'is_imported': existing_source is not None and existing_source.status == 'completed',
                    'data_source': existing_source.get_summary() if existing_source else None
                }
                
                available_tasks.append(task_info)
            
            return available_tasks
            
        except Exception as e:
            self.logger.error(f"获取可用任务失败: {str(e)}")
            return []
    
    def import_data_source(self, task_id, user_id):
        """导入数据源"""
        try:
            # 检查任务是否存在
            task = DataProcessingTask.query.get(task_id)
            if not task:
                return {'success': False, 'message': '任务不存在'}
            
            if task.status != 'completed':
                return {'success': False, 'message': '任务未完成，无法导入'}
            
            # 检查是否已经导入
            existing_source = RFMDataSource.query.filter_by(source_task_id=task_id).first()
            if existing_source:
                return {'success': False, 'message': '该任务数据已经导入'}
            
            # 创建数据源记录
            data_source = RFMDataSource(
                source_task_id=task_id,
                source_task_name=task.task_name,
                report_type=task.task_type,
                status='importing',
                imported_by=user_id
            )
            db.session.add(data_source)
            db.session.commit()
            
            # 开始导入数据
            result = self._import_task_data(data_source, task)
            
            if result['success']:
                data_source.status = 'completed'
                data_source.imported_at = datetime.utcnow()
                data_source.total_records = result['total_records']
                data_source.imported_records = result['imported_records']
                data_source.quarters = result['quarters']
                data_source.data_columns = result['columns']

                # 添加数据源ID到返回结果
                result['data_source_id'] = data_source.id
            else:
                data_source.status = 'failed'
                data_source.error_message = result['message']

            db.session.commit()

            return result
            
        except Exception as e:
            db.session.rollback()
            self.logger.error(f"导入数据源失败: {str(e)}")
            return {'success': False, 'message': f'导入失败: {str(e)}'}
    
    def _import_task_data(self, data_source, task):
        """导入任务数据到分析表"""
        try:
            # 加载Excel文件
            file_path = task.output_file_path
            if not os.path.isabs(file_path):
                file_path = os.path.join(os.getcwd(), file_path)
            
            if not os.path.exists(file_path):
                return {'success': False, 'message': f'文件不存在: {file_path}'}
            
            df = pd.read_excel(file_path)
            self.logger.info(f"加载Excel文件: {len(df)} 行, {len(df.columns)} 列")
            
            # 分析数据结构
            columns = list(df.columns)
            quarters = self._extract_quarters(columns)
            
            # 导入数据
            imported_count = 0
            total_count = len(df)

            # 设置总记录数和初始状态
            data_source.total_records = total_count
            data_source.imported_records = 0
            db.session.commit()

            # 添加初始延迟，让前端有时间开始轮询
            import time
            time.sleep(0.5)

            # 获取初始批次大小和延迟
            current_batch_size = resource_monitor.calculate_optimal_batch_size('import')
            current_delay = resource_monitor.calculate_optimal_delay(current_batch_size, 'import')

            self.logger.info(f"开始导入 - 初始批次大小: {current_batch_size}, 延迟: {current_delay:.3f}s")

            for _, row in df.iterrows():
                # 提取会员卡号
                member_id = str(row.get('会员卡号', '')).strip()
                if not member_id or member_id == 'nan':
                    continue

                # 创建分析数据记录
                analysis_data = RFMAnalysisData(
                    data_source_id=data_source.id,
                    member_id=member_id
                )

                # 处理所有数据（包括季度字段和扩展字段）
                extended_data = {}

                # 填充固定季度字段（如果存在对应的模型字段）
                for quarter in quarters:
                    quarter_lower = quarter.lower()

                    # 综合等级
                    comp_level = str(row.get(f'{quarter}_综合等级', '')).strip()
                    if comp_level and comp_level != 'nan':
                        # 检查模型是否有对应字段
                        field_name = f'comprehensive_level_{quarter_lower}'
                        if hasattr(analysis_data, field_name):
                            setattr(analysis_data, field_name, comp_level)
                        else:
                            # 如果模型没有对应字段，放入扩展数据
                            extended_data[f'{quarter}_综合等级'] = comp_level

                    # 细分等级
                    detail_level = str(row.get(f'{quarter}_细分等级', '')).strip()
                    if detail_level and detail_level != 'nan':
                        field_name = f'detailed_level_{quarter_lower}'
                        if hasattr(analysis_data, field_name):
                            setattr(analysis_data, field_name, detail_level)
                        else:
                            extended_data[f'{quarter}_细分等级'] = detail_level

                    # 会员卡级
                    card_level = str(row.get(f'{quarter}_会员卡级', '')).strip()
                    if card_level and card_level != 'nan':
                        field_name = f'member_card_level_{quarter_lower}'
                        if hasattr(analysis_data, field_name):
                            setattr(analysis_data, field_name, card_level)
                        else:
                            extended_data[f'{quarter}_会员卡级'] = card_level

                    # 科室标签
                    dept_tag = str(row.get(f'{quarter}_科室标签', '')).strip()
                    if dept_tag and dept_tag != 'nan':
                        field_name = f'department_tag_{quarter_lower}'
                        if hasattr(analysis_data, field_name):
                            setattr(analysis_data, field_name, dept_tag)
                        else:
                            extended_data[f'{quarter}_科室标签'] = dept_tag

                    # 现场
                    scene = str(row.get(f'{quarter}_现场', '')).strip()
                    if scene and scene != 'nan':
                        extended_data[f'{quarter}_现场'] = scene

                # 处理其他扩展数据
                for col in columns:
                    if col not in ['会员卡号'] and not col.endswith(('_综合等级', '_细分等级', '_会员卡级', '_科室标签', '_现场')):
                        value = row.get(col)
                        if pd.notna(value):
                            extended_data[col] = str(value)

                if extended_data:
                    analysis_data.extended_data = extended_data

                db.session.add(analysis_data)
                imported_count += 1

                # 动态批量提交并更新进度
                if imported_count % current_batch_size == 0:
                    db.session.commit()

                    # 更新数据源的当前导入记录数
                    data_source.imported_records = imported_count
                    db.session.commit()

                    # 重新计算最优批次大小和延迟
                    current_batch_size = resource_monitor.calculate_optimal_batch_size('import')
                    current_delay = resource_monitor.calculate_optimal_delay(current_batch_size, 'import')

                    self.logger.info(f"导入进度: {imported_count}/{total_count}, "
                                   f"批次大小: {current_batch_size}, 延迟: {current_delay:.3f}s")

                    # 使用动态延迟
                    time.sleep(current_delay)

            # 最终提交并设置完成状态
            data_source.imported_records = imported_count
            data_source.status = 'completed'
            db.session.commit()

            self.logger.info(f"数据导入完成: {imported_count}/{total_count}")

            return {
                'success': True,
                'data_source_id': data_source.id,
                'total_records': total_count,
                'imported_records': imported_count,
                'quarters': quarters,
                'columns': columns
            }
            
        except Exception as e:
            db.session.rollback()
            self.logger.error(f"导入任务数据失败: {str(e)}")
            return {'success': False, 'message': f'数据导入失败: {str(e)}'}
    
    def _extract_quarters(self, columns):
        """提取季度信息"""
        quarters = set()
        for col in columns:
            if '_综合等级' in col:
                quarter = col.replace('_综合等级', '')
                quarters.add(quarter)
        return sorted(list(quarters))
    
    def delete_data_source(self, data_source_id, user_id):
        """删除数据源"""
        try:
            data_source = RFMDataSource.query.get(data_source_id)
            if not data_source:
                return {'success': False, 'message': '数据源不存在'}

            # 检查权限（仅管理员或导入者可删除）
            from app.models import User
            user = User.query.get(user_id)
            if not user.is_admin and data_source.imported_by != user_id:
                return {'success': False, 'message': '无权限删除此数据源'}

            # 设置删除中状态
            data_source.status = 'deleting'
            initial_count = data_source.imported_records or 0
            data_source.total_records = initial_count  # 设置总记录数为初始记录数
            data_source.imported_records = initial_count  # 保存初始记录数
            db.session.commit()

            # 分批删除分析数据，显示真实进度
            analysis_data_query = RFMAnalysisData.query.filter_by(data_source_id=data_source_id)
            total_records = analysis_data_query.count()
            deleted_count = 0

            # 获取初始批次大小和延迟
            current_batch_size = resource_monitor.calculate_optimal_batch_size('delete')
            current_delay = resource_monitor.calculate_optimal_delay(current_batch_size, 'delete')

            self.logger.info(f"开始删除 - 总记录数: {total_records}, "
                           f"初始批次大小: {current_batch_size}, 延迟: {current_delay:.3f}s")

            # 动态批量删除
            while True:
                batch = analysis_data_query.limit(current_batch_size).all()
                if not batch:
                    break

                for record in batch:
                    db.session.delete(record)
                    deleted_count += 1

                # 更新剩余记录数
                remaining_count = total_records - deleted_count
                data_source.imported_records = remaining_count
                db.session.commit()

                # 重新计算最优批次大小和延迟
                current_batch_size = resource_monitor.calculate_optimal_batch_size('delete')
                current_delay = resource_monitor.calculate_optimal_delay(current_batch_size, 'delete')

                self.logger.info(f"删除进度: {deleted_count}/{total_records}, 剩余: {remaining_count}, "
                               f"批次大小: {current_batch_size}, 延迟: {current_delay:.3f}s")

                # 使用动态延迟
                import time
                time.sleep(current_delay)

            # 删除缓存
            from app.rfm_reports.models import RFMReportCache
            RFMReportCache.query.filter_by(data_source_id=data_source_id).delete()

            # 删除数据源记录
            db.session.delete(data_source)
            db.session.commit()

            self.logger.info(f"数据源删除成功: {data_source.source_task_name}")

            return {'success': True, 'message': '数据源删除成功'}

        except Exception as e:
            db.session.rollback()
            # 如果删除失败，恢复状态
            try:
                data_source = RFMDataSource.query.get(data_source_id)
                if data_source:
                    data_source.status = 'completed'
                    db.session.commit()
            except:
                pass

            self.logger.error(f"删除数据源失败: {str(e)}")
            return {'success': False, 'message': f'删除失败: {str(e)}'}
    
    def get_data_sources_by_type(self, report_type):
        """根据类型获取数据源"""
        try:
            from app.models import User

            # 使用join查询避免N+1问题，提高查询效率
            data_sources = RFMDataSource.query.options(
                db.joinedload(RFMDataSource.importer)
            ).filter_by(
                report_type=report_type,
                status='completed'
            ).order_by(RFMDataSource.created_at.desc()).all()

            # 直接构建摘要，避免重复查询
            result = []
            for ds in data_sources:
                summary = {
                    'id': ds.id,
                    'source_task_id': ds.source_task_id,
                    'source_task_name': ds.source_task_name,
                    'report_type': ds.report_type,
                    'total_records': ds.total_records,
                    'imported_records': ds.imported_records,
                    'status': ds.status,
                    'error_message': ds.error_message,
                    'quarters': ds.quarters or [],
                    'is_available': ds.is_available,
                    'is_locked': ds.is_locked,
                    'imported_by': ds.importer.username if ds.importer else None,
                    'created_at': ds.created_at.isoformat() if ds.created_at else None,
                    'imported_at': ds.imported_at.isoformat() if ds.imported_at else None
                }
                result.append(summary)

            return result

        except Exception as e:
            self.logger.error(f"获取数据源失败: {str(e)}")
            return []
    
    def check_task_lock_status(self, task_id):
        """检查任务锁定状态"""
        try:
            data_source = RFMDataSource.query.filter_by(source_task_id=task_id).first()
            if data_source and data_source.is_locked:
                return {
                    'is_locked': True,
                    'locked_by': 'RFM数据源',
                    'data_source_id': data_source.id,
                    'message': f'该任务数据已被导入到RFM数据源中，请先在RFM数据源管理中删除数据源后再删除此任务'
                }
            
            return {'is_locked': False}
            
        except Exception as e:
            self.logger.error(f"检查任务锁定状态失败: {str(e)}")
            return {'is_locked': False}


# 全局实例
rfm_data_manager = RFMDataManager()
