from flask import render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
import pymysql
from app.config_mgmt import bp
from app.config_mgmt.forms import DatabaseConfigForm, EditDatabaseConfigForm
from app.models import DatabaseConfig, db

@bp.route('/database_configs')
@login_required
def list_configs():
    """数据库配置列表"""
    configs = DatabaseConfig.query.order_by(DatabaseConfig.created_at.desc()).all()
    return render_template('config_mgmt/list_configs.html', 
                         title='数据库配置管理', 
                         configs=configs)

@bp.route('/database_configs/add', methods=['GET', 'POST'])
@login_required
def add_config():
    """添加数据库配置"""
    form = DatabaseConfigForm()

    if request.method == 'POST':
        if form.test_connection.data:
            # 测试连接
            try:
                connection = pymysql.connect(
                    host=form.host.data,
                    port=form.port.data,
                    user=form.username.data,
                    password=form.password.data,
                    charset='utf8mb4',
                    connect_timeout=10
                )

                # 获取数据库列表
                with connection.cursor() as cursor:
                    cursor.execute("SHOW DATABASES")
                    databases = [row[0] for row in cursor.fetchall()
                               if row[0] not in ['information_schema', 'performance_schema', 'mysql', 'sys']]

                connection.close()

                # 更新数据库选择列表
                form.database_name.choices = [(db_name, db_name) for db_name in databases]
                flash('连接测试成功！请选择数据库', 'success')

            except Exception as e:
                flash(f'连接测试失败: {str(e)}', 'error')

        else:
            # 处理保存配置的情况
            # 首先需要重新获取数据库列表以验证表单
            if form.database_name.data:
                try:
                    connection = pymysql.connect(
                        host=form.host.data,
                        port=form.port.data,
                        user=form.username.data,
                        password=form.password.data,
                        charset='utf8mb4',
                        connect_timeout=10
                    )

                    # 获取数据库列表
                    with connection.cursor() as cursor:
                        cursor.execute("SHOW DATABASES")
                        databases = [row[0] for row in cursor.fetchall()
                                   if row[0] not in ['information_schema', 'performance_schema', 'mysql', 'sys']]

                    connection.close()

                    # 重新设置选择列表
                    form.database_name.choices = [(db_name, db_name) for db_name in databases]

                    # 验证表单
                    if form.validate():
                        # 保存配置
                        config = DatabaseConfig(
                            name=form.name.data,
                            host=form.host.data,
                            port=form.port.data,
                            username=form.username.data,
                            database_name=form.database_name.data,
                            created_by=current_user.id
                        )
                        config.set_password(form.password.data)

                        db.session.add(config)
                        db.session.commit()

                        flash('数据库配置添加成功！', 'success')
                        return redirect(url_for('config_mgmt.list_configs'))
                    else:
                        # 表单验证失败，显示错误
                        for field, errors in form.errors.items():
                            for error in errors:
                                flash(f'{field}: {error}', 'error')

                except Exception as e:
                    flash(f'保存配置时连接测试失败: {str(e)}', 'error')
            else:
                flash('请先测试连接并选择数据库', 'error')
    
    return render_template('config_mgmt/add_config.html', 
                         title='添加数据库配置', 
                         form=form)

@bp.route('/database_configs/edit/<int:config_id>', methods=['GET', 'POST'])
@login_required
def edit_config(config_id):
    """编辑数据库配置"""
    config = DatabaseConfig.query.get_or_404(config_id)
    form = EditDatabaseConfigForm(original_name=config.name)
    
    if form.validate_on_submit():
        config.name = form.name.data
        config.host = form.host.data
        config.port = form.port.data
        config.username = form.username.data
        config.database_name = form.database_name.data
        
        # 如果提供了新密码，则更新密码
        if form.password.data:
            config.set_password(form.password.data)
        
        db.session.commit()
        flash('数据库配置更新成功！', 'success')
        return redirect(url_for('config_mgmt.list_configs'))
    
    elif request.method == 'GET':
        # 填充表单数据
        form.id.data = config.id
        form.name.data = config.name
        form.host.data = config.host
        form.port.data = config.port
        form.username.data = config.username
        form.database_name.data = config.database_name
    
    return render_template('config_mgmt/edit_config.html',
                         title='编辑数据库配置',
                         form=form,
                         config=config)

@bp.route('/database_configs/delete/<int:config_id>', methods=['POST'])
@login_required
def delete_config(config_id):
    """删除数据库配置"""
    config = DatabaseConfig.query.get_or_404(config_id)

    if config.is_active:
        flash('无法删除当前激活的数据库配置', 'error')
        return redirect(url_for('config_mgmt.list_configs'))

    db.session.delete(config)
    db.session.commit()

    flash('数据库配置删除成功！', 'success')
    return redirect(url_for('config_mgmt.list_configs'))

@bp.route('/database_configs/activate/<int:config_id>', methods=['POST'])
@login_required
def activate_config(config_id):
    """激活数据库配置"""
    config = DatabaseConfig.query.get_or_404(config_id)

    try:
        # 测试连接
        connection = pymysql.connect(
            host=config.host,
            port=config.port,
            user=config.username,
            password=config.get_password(),
            database=config.database_name,
            charset='utf8mb4',
            connect_timeout=10
        )
        connection.close()

        # 激活配置
        config.activate()
        flash(f'数据库配置 "{config.name}" 已激活！', 'success')

    except Exception as e:
        flash(f'激活失败，无法连接到数据库: {str(e)}', 'error')

    return redirect(url_for('config_mgmt.list_configs'))

@bp.route('/api/test_connection', methods=['POST'])
@login_required
def test_connection():
    """API: 测试数据库连接"""
    data = request.get_json()

    try:
        connection = pymysql.connect(
            host=data['host'],
            port=int(data['port']),
            user=data['username'],
            password=data['password'],
            charset='utf8mb4',
            connect_timeout=10
        )

        # 获取数据库列表
        with connection.cursor() as cursor:
            cursor.execute("SHOW DATABASES")
            databases = [row[0] for row in cursor.fetchall()
                       if row[0] not in ['information_schema', 'performance_schema', 'mysql', 'sys']]

        connection.close()

        return jsonify({
            'success': True,
            'message': '连接测试成功',
            'databases': databases
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'连接测试失败: {str(e)}'
        }), 400

@bp.route('/api/save_config', methods=['POST'])
@login_required
def save_config():
    """API: 保存数据库配置"""
    data = request.get_json()

    try:
        # 验证必填字段
        required_fields = ['name', 'host', 'port', 'username', 'password', 'database_name']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'message': f'字段 {field} 不能为空'
                }), 400

        # 检查配置名称是否已存在
        existing_config = DatabaseConfig.query.filter_by(name=data['name']).first()
        if existing_config:
            return jsonify({
                'success': False,
                'message': '配置名称已存在，请选择其他名称'
            }), 400

        # 再次测试连接以确保配置有效
        connection = pymysql.connect(
            host=data['host'],
            port=int(data['port']),
            user=data['username'],
            password=data['password'],
            database=data['database_name'],
            charset='utf8mb4',
            connect_timeout=10
        )
        connection.close()

        # 创建配置
        config = DatabaseConfig(
            name=data['name'],
            host=data['host'],
            port=int(data['port']),
            username=data['username'],
            database_name=data['database_name'],
            created_by=current_user.id
        )
        config.set_password(data['password'])

        db.session.add(config)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '数据库配置保存成功',
            'config_id': config.id
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'保存配置失败: {str(e)}'
        }), 400
