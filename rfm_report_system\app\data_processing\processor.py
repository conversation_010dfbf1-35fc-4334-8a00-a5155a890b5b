import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Optional
import pymysql
from sqlalchemy import create_engine
import re

# 确保xlsxwriter可用
try:
    import xlsxwriter
    XLSXWRITER_AVAILABLE = True
    print(f"✅ xlsxwriter 版本: {xlsxwriter.__version__}")
except ImportError as e:
    XLSXWRITER_AVAILABLE = False
    print(f"❌ xlsxwriter 导入失败: {e}")
    print("请在虚拟环境中运行: pip install xlsxwriter")

class RFMDataProcessor:
    """RFM数据处理核心引擎"""
    
    # 科室映射关系
    DEPARTMENT_MAPPING = {
        '皮肤美容项目': '皮肤',
        '形体美容项目': '皮肤',
        '口腔美容项目': '口腔',
        '注射美容项目': '注射',
        '检验项目': '整形',
        '整形美容项目': '整形',
        '麻醉项目': '整形',
        '毛发种植项目': '毛发'
    }
    
    def __init__(self, db_config, logger=None):
        """
        初始化数据处理器
        
        Args:
            db_config: 数据库配置对象
            logger: 日志记录器
        """
        self.db_config = db_config
        self.logger = logger or logging.getLogger(__name__)
        self.engine = None
        self.cache_df = None

        # 分类映射表（从数据库加载）
        self.level1_mappings = {}
        self.level2_mappings = {}
        self.level3_mappings = {}
        self.field_mappings = {}
        self.field_group_mappings = {}
        self.mapping_options = {
            'use_level1_mapping': False,
            'use_level2_mapping': False,
            'use_level3_mapping': False,
            'use_field_mapping': False,
            'use_field_group': False
        }
        
    def connect_database(self):
        """连接数据库"""
        try:
            connection_string = self.db_config.get_connection_string()
            self.engine = create_engine(connection_string)
            self.logger.info("数据库连接成功")
            return True
        except Exception as e:
            self.logger.error(f"数据库连接失败: {str(e)}")
            return False
    
    def get_quarter_date_range(self, quarter: str) -> Tuple[str, str]:
        """
        获取季度的日期范围
        
        Args:
            quarter: 季度字符串，如 '24Q1', '25Q2'
            
        Returns:
            (start_date, end_date): 季度开始和结束日期
        """
        # 解析季度字符串
        match = re.match(r'(\d{2})Q(\d)', quarter)
        if not match:
            raise ValueError(f"无效的季度格式: {quarter}")
        
        year = int('20' + match.group(1))
        q = int(match.group(2))
        
        # 计算季度日期范围
        if q == 1:
            start_date = f"{year}-01-01"
            end_date = f"{year}-03-31"
        elif q == 2:
            start_date = f"{year}-04-01"
            end_date = f"{year}-06-30"
        elif q == 3:
            start_date = f"{year}-07-01"
            end_date = f"{year}-09-30"
        elif q == 4:
            start_date = f"{year}-10-01"
            end_date = f"{year}-12-31"
        else:
            raise ValueError(f"无效的季度: {q}")
        
        return start_date, end_date
    
    def get_table_list(self) -> List[str]:
        """获取数据库中的表列表"""
        try:
            # 使用pymysql直接连接，避免SQLAlchemy版本兼容问题
            import pymysql
            connection = pymysql.connect(
                host=self.db_config.host,
                port=self.db_config.port,
                user=self.db_config.username,
                password=self.db_config.get_password(),
                database=self.db_config.database_name,
                charset='utf8mb4'
            )

            with connection.cursor() as cursor:
                cursor.execute("SHOW TABLES")
                tables = [row[0] for row in cursor.fetchall()]

            connection.close()
            return tables
        except Exception as e:
            self.logger.error(f"获取表列表失败: {str(e)}")
            return []
    
    def get_quarter_tables(self) -> List[str]:
        """获取季度客户表列表"""
        tables = self.get_table_list()
        quarter_tables = []

        # 支持您的表名格式：24Q1, 24q1, 23q4 等（大小写不敏感）
        patterns = [
            r'^\d{2}[qQ][1-4]$',            # 24Q1, 24q1, 23q4 等 (您的格式)
            r'^\d{4}[qQ][1-4]$',            # 2024Q1, 2024q1 等
            r'\d{2}[qQ]\d_customer_data',   # 24Q3_customer_data (标准格式)
            r'\d{4}[qQ]\d_customer_data',   # 2024Q3_customer_data
            r'\d{2}[qQ]\d客户数据',          # 24Q3客户数据
            r'\d{4}[qQ]\d客户数据',          # 2024Q3客户数据
        ]

        for table in tables:
            # 检查是否匹配任何一种格式
            for pattern in patterns:
                if re.match(pattern, table, re.IGNORECASE):
                    quarter_tables.append(table)
                    break
            else:
                # 也检查包含关键词的表
                table_lower = table.lower()
                if (any(keyword in table_lower for keyword in ['customer', '客户']) and
                    any(keyword in table_lower for keyword in ['q1', 'q2', 'q3', 'q4', '季度', '季'])):
                    quarter_tables.append(table)

        return sorted(list(set(quarter_tables)))  # 去重并排序
    
    def create_base_cache_table(self, task_type: str, base_quarter_table: str,
                               analysis_quarters: List[str], quarter_tables: List[str] = None) -> pd.DataFrame:
        """
        创建基础缓存表

        Args:
            task_type: 处理类型 ('forward' 或 'result')
            base_quarter_table: 基础会员来源表（正向盘使用）
            analysis_quarters: 要分析的季度列表（如 ['24Q4', '25Q1', '25Q2', '25Q3', '25Q4']）
            quarter_tables: 季度表列表（结果盘使用）

        Returns:
            基础缓存表DataFrame
        """
        self.logger.info(f"开始创建基础缓存表，处理类型: {task_type}")
        self.logger.info(f"分析季度: {analysis_quarters}")

        # 第一步：确定基础会员卡号
        if task_type == 'forward':
            # 正向盘：从基础会员来源表提取会员卡号
            base_members = self._get_members_from_table(base_quarter_table)
            self.logger.info(f"正向盘：从基础表 {base_quarter_table} 获取到 {len(base_members)} 个会员")
        else:
            # 结果盘：从用户选择的所有季度表中提取会员卡号并去重
            base_members = set()
            for table in quarter_tables or []:
                members = self._get_members_from_table(table)
                base_members.update(members)
                self.logger.info(f"结果盘：从表 {table} 获取到 {len(members)} 个会员")
            self.logger.info(f"结果盘：总共获取到 {len(base_members)} 个唯一会员")

        # 创建基础缓存表
        cache_df = pd.DataFrame({'会员卡号': list(base_members)})

        # 第二步：合并各分析季度的数据
        for quarter in analysis_quarters:
            quarter_table = self._get_table_by_quarter(quarter)
            if not quarter_table:
                self.logger.warning(f"未找到季度 {quarter} 对应的表")
                continue

            try:
                # 读取季度表数据
                query = f"SELECT * FROM `{quarter_table}`"
                df = pd.read_sql(query, self.engine)

                # 重命名列（添加季度前缀，格式：24Q3_现场）
                columns_to_rename = {}
                for col in df.columns:
                    if col != '会员卡号':
                        columns_to_rename[col] = f"{quarter}_{col}"

                df = df.rename(columns=columns_to_rename)

                # 合并到缓存表
                cache_df = cache_df.merge(df, on='会员卡号', how='left')

                # 只对RFM基础值列填充"不在盘内"，业绩列保持原值
                new_columns = [col for col in df.columns if col != '会员卡号']
                for col in new_columns:
                    if self._is_rfm_base_column(col):
                        cache_df[col] = cache_df[col].fillna('不在盘内')
                        # 同时处理空字符串
                        cache_df[col] = cache_df[col].replace('', '不在盘内')
                    # 业绩相关列保持原值（NaN或0）

                # 应用现场映射
                for col in new_columns:
                    if '现场' in col and '现场小组' not in col and self.mapping_options.get('use_field_mapping', False):
                        # 只对现场列应用现场映射，不生成季度现场小组列
                        cache_df[col] = cache_df[col].apply(lambda x: self.apply_mapping(x, 'field_mapping') if pd.notna(x) and x != '不在盘内' else x)
                        self.logger.info(f"对列 {col} 应用现场映射")

                # 添加现场小组和最新现场列（使用现场映射）
                self.logger.info(f"检查现场映射选项: use_field_mapping = {self.mapping_options.get('use_field_mapping', False)}")
                if self.mapping_options.get('use_field_mapping', False):
                    self.logger.info("开始添加现场小组和最新现场列...")
                    self._add_field_consultation_columns(cache_df, new_columns)
                    self.logger.info("已添加现场小组和最新现场列")
                else:
                    self.logger.info("现场映射未启用，跳过添加现场小组和最新现场列")

                # 记录哪些列被填充了
                filled_columns = [col for col in new_columns if self._is_rfm_base_column(col)]
                other_columns = [col for col in new_columns if not self._is_rfm_base_column(col)]

                self.logger.info(f"合并季度 {quarter} (表 {quarter_table})，当前缓存表列数: {len(cache_df.columns)}")
                print(f"=== 季度 {quarter} 列填充情况 ===")
                if filled_columns:
                    self.logger.info(f"  已填充'不在盘内'的列: {filled_columns}")
                    print(f"已填充'不在盘内'的列: {filled_columns}")
                if other_columns:
                    self.logger.info(f"  保持原值的列: {other_columns}")
                    print(f"保持原值的列: {other_columns}")

            except Exception as e:
                self.logger.error(f"合并季度 {quarter} 失败: {str(e)}")
                continue

        # 最终检查：只对RFM基础值列填充"不在盘内"
        rfm_columns_filled = 0
        for col in cache_df.columns:
            if col != '会员卡号' and self._is_rfm_base_column(col):
                cache_df[col] = cache_df[col].fillna('不在盘内')
                cache_df[col] = cache_df[col].replace('', '不在盘内')
                # 处理可能的None值
                cache_df[col] = cache_df[col].replace([None], '不在盘内')
                rfm_columns_filled += 1

        self.cache_df = cache_df
        self.logger.info(f"基础缓存表创建完成，共 {len(cache_df)} 行，{len(cache_df.columns)} 列，其中 {rfm_columns_filled} 个RFM基础值列已填充为'不在盘内'")

        # 显示当前列的顺序（用于调试）
        self.logger.info(f"当前列顺序（前20列）: {list(cache_df.columns)[:20]}")

        return cache_df

    def _get_members_from_table(self, table_name: str) -> set:
        """从指定表中获取会员卡号"""
        try:
            query = f"SELECT DISTINCT 会员卡号 FROM `{table_name}` WHERE 会员卡号 IS NOT NULL"
            df = pd.read_sql(query, self.engine)
            return set(df['会员卡号'].dropna().astype(str))
        except Exception as e:
            self.logger.error(f"从表 {table_name} 获取会员卡号失败: {str(e)}")
            return set()

    def _get_table_by_quarter(self, quarter: str) -> str:
        """根据季度获取对应的表名"""
        # 将季度格式转换为表名格式（如 24Q3 -> 24q3）
        table_name = quarter.lower()

        # 检查表是否存在
        all_tables = self.get_quarter_tables()
        for table in all_tables:
            if table.lower() == table_name:
                return table

        self.logger.warning(f"未找到季度 {quarter} 对应的表")
        return None

    def _is_rfm_base_column(self, column_name: str) -> bool:
        """判断是否为RFM基础值列（需要填充"不在盘内"）"""
        # 精确的需要填充的列名列表
        fill_columns = [
            '现场', '年龄', 'R1', 'R1区间', 'R2', 'R2区间',
            'F1', 'F1区间', 'F2', 'F2区间', 'M1', 'M1区间',
            'M2', 'M2区间', '年限', 'R1分值', 'R2分值', 'F1分值',
            'F2分值', 'M1分值', 'M2分值', '加权总分', '综合等级',
            '细分等级', '模型', '会员卡级', '消费年限', '会员等级',
            '科室标签', 'RFM'
        ]

        # 检查列名是否完全匹配或包含这些关键词
        for fill_col in fill_columns:
            if fill_col in column_name:
                return True

        # 也检查带季度前缀的列名，如 "24Q1_现场", "25Q2_综合等级" 等
        for fill_col in fill_columns:
            # 检查是否是季度前缀的列名格式：季度_列名
            if column_name.endswith(f'_{fill_col}') or column_name.endswith(fill_col):
                return True

        return False

    def _get_column_sort_order(self) -> list:
        """定义列的排序顺序"""
        # 基础字段排序规则
        base_field_order = [
            '会员卡号',
            '现场', '综合等级', '细分等级', '科室标签', '会员等级',
            'R1', 'R2', 'F1', 'F2', 'M1', 'M2',
            'R1区间', 'R2区间', 'F1区间', 'F2区间', 'M1区间', 'M2区间',
            'R1分值', 'R2分值', 'F1分值', 'F2分值', 'M1分值', 'M2分值', '加权总分',  # 修正分值顺序
            '年龄', '模型', '消费年限', 'RFM',
            '执行业绩', '皮肤_执行业绩', '注射_执行业绩',
            # 按类型分组的TOP字段
            '皮肤_TOP品类1', '皮肤_TOP品类2', '皮肤_TOP品类3', '皮肤_TOP品类4', '皮肤_TOP品类5',
            '注射_TOP品类1', '注射_TOP品类2', '注射_TOP品类3', '注射_TOP品类4', '注射_TOP品类5',
            '皮肤_TOP品项1', '皮肤_TOP品项2', '皮肤_TOP品项3', '皮肤_TOP品项4', '皮肤_TOP品项5',
            '皮肤_TOP品项6', '皮肤_TOP品项7', '皮肤_TOP品项8', '皮肤_TOP品项9', '皮肤_TOP品项10',
            '皮肤_TOP品项11', '皮肤_TOP品项12', '皮肤_TOP品项13', '皮肤_TOP品项14', '皮肤_TOP品项15',
            '注射_TOP品项1', '注射_TOP品项2', '注射_TOP品项3', '注射_TOP品项4', '注射_TOP品项5',
            '注射_TOP品项6', '注射_TOP品项7', '注射_TOP品项8', '注射_TOP品项9', '注射_TOP品项10',
            '注射_TOP品项11', '注射_TOP品项12', '注射_TOP品项13', '注射_TOP品项14', '注射_TOP品项15'
        ]

        return base_field_order

    def _sort_columns_by_field_type(self, df: pd.DataFrame) -> pd.DataFrame:
        """按字段类型对列进行排序 - 使用直接匹配方法"""
        try:
            current_columns = list(df.columns)
            sorted_columns = []
            used_columns = set()

            self.logger.info(f"开始列排序，原始列数: {len(current_columns)}")

            # 1. 会员卡号
            if '会员卡号' in current_columns:
                sorted_columns.append('会员卡号')
                used_columns.add('会员卡号')

            # 2. 现场相关列（紧跟会员卡号）
            field_consultation_fields = ['现场小组', '最新现场']
            for field in field_consultation_fields:
                if field in current_columns and field not in used_columns:
                    sorted_columns.append(field)
                    used_columns.add(field)
                    self.logger.info(f"添加现场字段: {field}")

            # 3. 按字段类型和季度顺序手动排序
            field_patterns = [
                '现场', '综合等级', '细分等级', '科室标签', '会员卡级', '会员等级', '年限',
                'R1', 'R2', 'F1', 'F2', 'M1', 'M2',
                'R1区间', 'R2区间', 'F1区间', 'F2区间', 'M1区间', 'M2区间',
                'R1分值', 'R2分值', 'F1分值', 'F2分值', 'M1分值', 'M2分值', '加权总分',
                '年龄', '模型', '消费年限', 'RFM'
            ]

            # 处理基础字段（按季度排序）
            for pattern in field_patterns:
                matching_cols = []
                for col in current_columns:
                    if col not in used_columns and self._matches_pattern(col, pattern):
                        matching_cols.append(col)

                # 按季度排序
                matching_cols.sort(key=self._extract_quarter_for_sorting)
                sorted_columns.extend(matching_cols)
                used_columns.update(matching_cols)

                if matching_cols:
                    self.logger.info(f"字段 '{pattern}' 匹配: {matching_cols}")

            # 3. 业绩数据 - 按类型分组
            performance_types = ['执行业绩', '皮肤_执行业绩', '注射_执行业绩']
            for perf_type in performance_types:
                matching_cols = []
                for col in current_columns:
                    if col not in used_columns and self._matches_performance_pattern(col, perf_type):
                        matching_cols.append(col)

                # 按季度排序
                matching_cols.sort(key=self._extract_quarter_for_sorting)
                sorted_columns.extend(matching_cols)
                used_columns.update(matching_cols)

                if matching_cols:
                    self.logger.info(f"业绩类型 '{perf_type}' 匹配: {matching_cols}")

            # 4. TOP字段 - 按类型和编号分组
            self._add_top_fields(current_columns, sorted_columns, used_columns)

            # 5. 添加剩余列
            remaining_columns = [col for col in current_columns if col not in used_columns]
            if remaining_columns:
                remaining_columns.sort()
                sorted_columns.extend(remaining_columns)
                self.logger.info(f"剩余列: {remaining_columns}")

            # 重新排列DataFrame
            df_sorted = df[sorted_columns]

            self.logger.info(f"列排序完成，最终列数: {len(sorted_columns)}")
            self.logger.info(f"前20列: {sorted_columns[:20]}")
            return df_sorted

        except Exception as e:
            self.logger.error(f"列排序失败: {str(e)}")
            return df

    def _matches_pattern(self, column_name: str, pattern: str) -> bool:
        """检查列名是否匹配指定模式"""
        # 精确匹配：季度_字段名 格式
        if column_name.endswith(f'_{pattern}'):
            return True

        # 直接匹配字段名
        if column_name == pattern:
            return True

        # 特殊字段的精确匹配规则
        if pattern == '现场':
            # 只匹配 XXQ_现场，不匹配 XXQ_现场小组
            return re.match(r'\d{2}Q\d_现场$', column_name) is not None

        # 现场小组列不再按季度生成，只有单独的"现场小组"列
        if pattern == '现场小组':
            # 只匹配单独的"现场小组"列，不匹配季度现场小组列
            return column_name == '现场小组'

        if pattern == '科室标签':
            # 匹配 XXQ_科室标签
            return re.match(r'\d{2}Q\d_科室标签$', column_name) is not None

        if pattern == '会员卡级':
            # 匹配 XXQ_会员卡级
            return re.match(r'\d{2}Q\d_会员卡级$', column_name) is not None

        if pattern == '年限':
            # 匹配 XXQ_年限
            return re.match(r'\d{2}Q\d_年限$', column_name) is not None

        return False

    def _matches_performance_pattern(self, column_name: str, perf_type: str) -> bool:
        """检查列名是否匹配业绩模式"""
        if perf_type == '执行业绩':
            # 匹配 24Q4_执行业绩 但不匹配 24Q4_皮肤_执行业绩
            return column_name.endswith('_执行业绩') and '皮肤' not in column_name and '注射' not in column_name
        elif perf_type == '皮肤_执行业绩':
            return '皮肤_执行业绩' in column_name
        elif perf_type == '注射_执行业绩':
            return '注射_执行业绩' in column_name

        return False

    def _add_top_fields(self, current_columns: list, sorted_columns: list, used_columns: set):
        """添加TOP字段，按类型和编号分组"""
        self.logger.info("开始处理TOP字段排序")

        # 动态处理所有科室的TOP品类
        for dept in self.departments:
            for i in range(1, self.category_top_count + 1):
                matching_cols = [col for col in current_columns
                               if col not in used_columns and self._is_exact_top_match(col, f'{dept}_TOP品类{i}')]
                if matching_cols:
                    sorted_columns.extend(matching_cols)
                    used_columns.update(matching_cols)
                    self.logger.info(f"{dept}_TOP品类{i} 匹配: {matching_cols}")

        # 动态处理所有科室的TOP品项
        for dept in self.departments:
            for i in range(1, self.item_top_count + 1):
                matching_cols = [col for col in current_columns
                               if col not in used_columns and self._is_exact_top_match(col, f'{dept}_TOP品项{i}')]
                if matching_cols:
                    sorted_columns.extend(matching_cols)
                    used_columns.update(matching_cols)
                    self.logger.info(f"{dept}_TOP品项{i} 匹配: {matching_cols}")

        self.logger.info("TOP字段排序处理完成")

    def _is_exact_top_match(self, column_name: str, pattern: str) -> bool:
        """精确匹配TOP字段"""
        # 例如：皮肤_TOP品项1_五代热玛吉 匹配 皮肤_TOP品项1
        # 但是：皮肤_TOP品项10_黑金超光子 不匹配 皮肤_TOP品项1

        if not column_name.startswith(pattern):
            return False

        # 检查模式后面是否紧跟下划线或结束
        remaining = column_name[len(pattern):]
        return remaining.startswith('_') or remaining == ''

    def _column_matches_field(self, column_name: str, base_field: str) -> bool:
        """判断列名是否匹配基础字段"""
        # 精确匹配：季度_字段名 格式
        if column_name.endswith(f'_{base_field}'):
            return True

        # 直接匹配字段名
        if column_name == base_field:
            return True

        # 特殊处理业绩相关字段
        if base_field == '执行业绩':
            # 匹配 24Q4_执行业绩 格式
            if column_name.endswith('_执行业绩') and '皮肤' not in column_name and '注射' not in column_name:
                return True
        elif base_field == '皮肤_执行业绩':
            # 匹配 24Q4_皮肤_执行业绩 格式
            if '皮肤_执行业绩' in column_name:
                return True
        elif base_field == '注射_执行业绩':
            # 匹配 24Q4_注射_执行业绩 格式
            if '注射_执行业绩' in column_name:
                return True

        # 对于现场字段的特殊匹配
        if base_field == '现场':
            # 现场字段只匹配季度现场列，不匹配现场小组列
            if '现场' in column_name and '现场小组' not in column_name:
                return True
            return False

        if base_field == '现场小组':
            # 现场小组字段只匹配单独的"现场小组"列
            return column_name == '现场小组'

        # TOP品类和TOP品项的精确匹配
        if base_field.startswith('皮肤_TOP品类') or base_field.startswith('注射_TOP品类'):
            # 精确匹配，如 皮肤_TOP品类1_紧致塑形 匹配 皮肤_TOP品类1
            if base_field in column_name:
                return True
        elif base_field.startswith('皮肤_TOP品项') or base_field.startswith('注射_TOP品项'):
            # 精确匹配，如 皮肤_TOP品项1_五代热玛吉 匹配 皮肤_TOP品项1
            if base_field in column_name:
                return True

        return False

    def _extract_quarter_for_sorting(self, column_name: str) -> tuple:
        """从列名中提取季度信息用于排序"""
        import re

        # 匹配季度模式，如 24Q4, 25Q1 等
        quarter_pattern = r'(\d{2})Q(\d)'
        match = re.search(quarter_pattern, column_name)

        if match:
            year = int(match.group(1))
            quarter = int(match.group(2))
            # 返回年份和季度的元组用于排序
            return (year, quarter)
        else:
            # 如果没有季度信息，放在最后
            return (99, 9)

    def load_mappings(self, mapping_options):
        """加载分类映射表"""
        self.mapping_options = mapping_options

        try:
            # 导入模型（避免循环导入）
            from app.models import MappingTemplate, CategoryMapping

            # 获取当前激活的模板
            active_template = MappingTemplate.get_active_template()

            if not active_template:
                self.logger.info("没有激活的参照表，使用原始分类")
                return

            self.logger.info(f"加载参照表: {active_template.template_name}")

            # 根据选项加载对应的映射
            if mapping_options.get('use_level1_mapping', False):
                self.level1_mappings = CategoryMapping.get_mappings_by_type('level1', active_template.template_name)
                self.logger.info(f"加载一级分类映射: {len(self.level1_mappings)} 个")

            if mapping_options.get('use_level2_mapping', False):
                self.level2_mappings = CategoryMapping.get_mappings_by_type('level2', active_template.template_name)
                self.logger.info(f"加载二级分类映射: {len(self.level2_mappings)} 个")

            if mapping_options.get('use_level3_mapping', False):
                self.level3_mappings = CategoryMapping.get_mappings_by_type('level3', active_template.template_name)
                self.logger.info(f"加载三级分类映射: {len(self.level3_mappings)} 个")

            # 加载现场映射
            if mapping_options.get('use_field_mapping', False):
                self.field_mappings = CategoryMapping.get_mappings_by_type('field_mapping', active_template.template_name)
                self.logger.info(f"加载现场映射: {len(self.field_mappings)} 个")

            # 加载现场小组映射
            if mapping_options.get('use_field_group', False):
                self.field_group_mappings = CategoryMapping.get_mappings_by_type('field_group', active_template.template_name)
                self.logger.info(f"加载现场小组映射: {len(self.field_group_mappings)} 个")

        except Exception as e:
            self.logger.error(f"加载映射表失败: {str(e)}")

    def apply_mapping(self, value, mapping_type):
        """应用分类映射"""
        if mapping_type == 'level1' and self.mapping_options.get('use_level1_mapping', False):
            mapped_value = self.level1_mappings.get(value, value)
            if mapped_value == value and value not in self.level1_mappings:
                self.logger.debug(f"一级分类 '{value}' 没有映射，保持原值")
            return mapped_value
        elif mapping_type == 'level2' and self.mapping_options.get('use_level2_mapping', False):
            mapped_value = self.level2_mappings.get(value, value)
            if mapped_value == value and value not in self.level2_mappings:
                self.logger.debug(f"二级分类 '{value}' 没有映射，保持原值")
            return mapped_value
        elif mapping_type == 'level3' and self.mapping_options.get('use_level3_mapping', False):
            mapped_value = self.level3_mappings.get(value, value)
            if mapped_value == value and value not in self.level3_mappings:
                self.logger.debug(f"三级分类 '{value}' 没有映射，保持原值")
            return mapped_value
        elif mapping_type == 'field_mapping' and self.mapping_options.get('use_field_mapping', False):
            mapped_value = self.field_mappings.get(value, value)
            if mapped_value == value and value not in self.field_mappings:
                self.logger.debug(f"现场 '{value}' 没有映射，保持原值")
            return mapped_value
        elif mapping_type == 'field_group' and self.mapping_options.get('use_field_group', False):
            mapped_value = self.field_group_mappings.get(value, value)
            if mapped_value == value and value not in self.field_group_mappings:
                self.logger.debug(f"现场小组 '{value}' 没有映射，保持原值")
            return mapped_value
        else:
            return value

    def _add_field_consultation_columns(self, cache_df, new_columns):
        """添加现场小组和最新现场列"""
        try:
            # 找到会员卡号列的位置
            card_col_index = None
            for i, col in enumerate(cache_df.columns):
                if '会员卡号' in col or '卡号' in col:
                    card_col_index = i
                    break

            if card_col_index is None:
                self.logger.warning("未找到会员卡号列，无法添加现场相关列")
                return

            # 获取所有现场相关列，按时间排序
            consultation_columns = self._get_consultation_columns_sorted(new_columns)

            if not consultation_columns:
                self.logger.warning("未找到现场相关列，无法添加现场相关列")
                return

            self.logger.info(f"找到 {len(consultation_columns)} 个现场列: {consultation_columns}")

            # 为每个会员卡号计算最新现场
            latest_field_values = []
            field_group_values = []

            for index, row in cache_df.iterrows():
                latest_field = self._get_latest_consultation(row, consultation_columns)

                # 应用现场映射
                if latest_field and pd.notna(latest_field) and latest_field != '不在盘内' and latest_field != '':
                    # 先应用现场映射（如果启用）
                    if self.mapping_options.get('use_field_mapping', False):
                        mapped_field = self.apply_mapping(latest_field, 'field_mapping')
                    else:
                        mapped_field = latest_field

                    # 再应用现场小组映射（如果启用）
                    if self.mapping_options.get('use_field_group', False):
                        field_group = self.apply_mapping(mapped_field, 'field_group')
                    else:
                        field_group = ''
                else:
                    # 对于"其他咨询"、空值或"不在盘内"，不进行映射
                    mapped_field = latest_field if latest_field else '其他咨询'
                    field_group = ''

                latest_field_values.append(mapped_field)
                field_group_values.append(field_group)

            # 在会员卡号列后插入新列
            insert_position = card_col_index + 1

            # 检查列是否已存在，避免重复插入
            if '现场小组' not in cache_df.columns:
                cache_df.insert(insert_position, '现场小组', field_group_values)
                self.logger.info("已添加现场小组列")
                insert_position += 1
            else:
                self.logger.info("现场小组列已存在，跳过插入")
                # 更新现有列的值
                cache_df['现场小组'] = field_group_values
                insert_position += 1

            # 插入最新现场列
            if '最新现场' not in cache_df.columns:
                cache_df.insert(insert_position, '最新现场', latest_field_values)
                self.logger.info("已添加最新现场列")
            else:
                self.logger.info("最新现场列已存在，跳过插入")
                # 更新现有列的值
                cache_df['最新现场'] = latest_field_values

        except Exception as e:
            self.logger.error(f"添加现场列失败: {str(e)}")

    def _get_consultation_columns_sorted(self, columns):
        """获取现场相关列并按时间排序（从新到旧）"""
        consultation_columns = []

        self.logger.info(f"搜索现场列，总列数: {len(columns)}")
        self.logger.info(f"所有列名: {columns[:10]}...")  # 只显示前10个列名

        for col in columns:
            if '现场' in col and '现场小组' not in col:  # 排除现场小组列
                consultation_columns.append(col)
                self.logger.info(f"找到现场列: {col}")

        self.logger.info(f"找到的现场列总数: {len(consultation_columns)}")

        # 按季度排序（从新到旧）
        def extract_quarter_year(col_name):
            """从列名中提取年份和季度，用于排序"""
            import re
            # 匹配 23Q4, 24Q1 等格式
            match = re.search(r'(\d{2})Q(\d)', col_name)
            if match:
                year = int('20' + match.group(1))  # 23 -> 2023
                quarter = int(match.group(2))
                return (year, quarter)

            # 匹配 2023Q4, 2024Q1 等格式
            match = re.search(r'(\d{4})Q(\d)', col_name)
            if match:
                year = int(match.group(1))
                quarter = int(match.group(2))
                return (year, quarter)

            # 如果无法解析，返回一个很小的值，排在最后
            return (0, 0)

        # 按年份和季度降序排序（最新的在前）
        consultation_columns.sort(key=extract_quarter_year, reverse=True)

        return consultation_columns

    def _get_latest_consultation(self, row, consultation_columns):
        """获取某行的最新现场值，跳过'不在盘内'"""
        for col in consultation_columns:
            value = row.get(col)
            # 跳过空值和"不在盘内"，寻找有效的现场值
            if pd.notna(value) and value != '' and value != '不在盘内':
                return value

        # 如果所有现场列都为空或都是"不在盘内"，返回"其他咨询"
        return '其他咨询'
    
    def calculate_quarterly_performance(self, quarters: List[str]) -> None:
        """
        计算季度执行业绩
        
        Args:
            quarters: 季度列表，如 ['24Q4', '25Q1', '25Q2']
        """
        self.logger.info(f"开始计算季度执行业绩，季度: {quarters}")
        
        for quarter in quarters:
            try:
                start_date, end_date = self.get_quarter_date_range(quarter)
                
                # 查询该季度的执行业绩
                query = f"""
                SELECT 会员卡号, SUM(执行业绩（真实金额）) as total_revenue
                FROM 客户执行明细表
                WHERE 执行日期 BETWEEN '{start_date}' AND '{end_date}'
                  AND 会员卡号 IS NOT NULL
                  AND 执行业绩（真实金额） IS NOT NULL
                GROUP BY 会员卡号
                """
                
                df = pd.read_sql(query, self.engine)
                df['会员卡号'] = df['会员卡号'].astype(str)
                
                # 合并到缓存表
                column_name = f"{quarter}_执行业绩"
                performance_df = df.rename(columns={'total_revenue': column_name})
                
                self.cache_df = self.cache_df.merge(
                    performance_df[['会员卡号', column_name]], 
                    on='会员卡号', 
                    how='left'
                )
                
                # 填充空值为0，使用现代方法避免警告
                self.cache_df[column_name] = pd.to_numeric(self.cache_df[column_name], errors='coerce').fillna(0)
                
                self.logger.info(f"完成季度 {quarter} 业绩计算，平均业绩: {self.cache_df[column_name].mean():.2f}")
                
            except Exception as e:
                self.logger.error(f"计算季度 {quarter} 业绩失败: {str(e)}")
                continue
    
    def calculate_department_performance(self, quarters: List[str], departments: List[str] = None) -> None:
        """
        计算科室季度执行业绩
        
        Args:
            quarters: 季度列表
            departments: 科室列表，默认为 ['皮肤', '注射']
        """
        if departments is None:
            departments = ['皮肤', '注射']
            
        self.logger.info(f"开始计算科室季度业绩，季度: {quarters}，科室: {departments}")
        
        for quarter in quarters:
            for department in departments:
                try:
                    start_date, end_date = self.get_quarter_date_range(quarter)
                    
                    # 构建科室条件
                    dept_conditions = []
                    for original_dept, mapped_dept in self.DEPARTMENT_MAPPING.items():
                        if mapped_dept == department:
                            dept_conditions.append(f"一级分类 = '{original_dept}'")
                    
                    if not dept_conditions:
                        continue
                    
                    dept_condition = " OR ".join(dept_conditions)
                    
                    # 查询该季度该科室的执行业绩
                    query = f"""
                    SELECT 会员卡号, SUM(执行业绩（真实金额）) as total_revenue
                    FROM 客户执行明细表
                    WHERE 执行日期 BETWEEN '{start_date}' AND '{end_date}'
                      AND ({dept_condition})
                      AND 会员卡号 IS NOT NULL
                      AND 执行业绩（真实金额） IS NOT NULL
                    GROUP BY 会员卡号
                    """
                    
                    df = pd.read_sql(query, self.engine)
                    df['会员卡号'] = df['会员卡号'].astype(str)
                    
                    # 合并到缓存表
                    column_name = f"{quarter}_{department}_执行业绩"
                    performance_df = df.rename(columns={'total_revenue': column_name})
                    
                    self.cache_df = self.cache_df.merge(
                        performance_df[['会员卡号', column_name]], 
                        on='会员卡号', 
                        how='left'
                    )
                    
                    # 填充空值为0，使用现代方法避免警告
                    self.cache_df[column_name] = pd.to_numeric(self.cache_df[column_name], errors='coerce').fillna(0)
                    
                    self.logger.info(f"完成 {quarter} {department} 科室业绩计算")
                    
                except Exception as e:
                    self.logger.error(f"计算 {quarter} {department} 科室业绩失败: {str(e)}")
                    continue

    def calculate_top_rankings(self, quarters: List[str], departments: List[str] = None, top_ranking_dates: Dict = None) -> None:
        """
        计算TOP排名并生成对应列

        Args:
            quarters: 季度列表
            departments: 科室列表，默认为 ['皮肤', '注射']
            top_ranking_dates: TOP排名时间范围配置
        """
        if departments is None:
            departments = ['皮肤', '注射']

        self.logger.info(f"开始计算TOP排名，季度范围: {quarters}")

        # 确定TOP排名统计时间范围（确定哪些品类/品项进入TOP排名）
        if top_ranking_dates and top_ranking_dates.get('top_ranking_start_date') and top_ranking_dates.get('top_ranking_end_date'):
            ranking_start_date = top_ranking_dates['top_ranking_start_date']
            ranking_end_date = top_ranking_dates['top_ranking_end_date']
            self.logger.info(f"使用用户指定的TOP排名统计时间范围: {ranking_start_date} 到 {ranking_end_date}")
        else:
            # 使用季度范围作为默认时间范围
            ranking_start_date = self.get_quarter_date_range(quarters[0])[0]
            ranking_end_date = self.get_quarter_date_range(quarters[-1])[1]
            self.logger.info(f"使用默认TOP排名统计时间范围: {ranking_start_date} 到 {ranking_end_date} (基于季度: {quarters[0]} 到 {quarters[-1]})")

        # 确定TOP列值计算时间范围（计算每个会员的消费金额）
        if top_ranking_dates and top_ranking_dates.get('top_value_start_date') and top_ranking_dates.get('top_value_end_date'):
            value_start_date = top_ranking_dates['top_value_start_date']
            value_end_date = top_ranking_dates['top_value_end_date']
            self.logger.info(f"使用用户指定的TOP列值计算时间范围: {value_start_date} 到 {value_end_date}")
        else:
            # 使用季度范围作为默认时间范围
            value_start_date = self.get_quarter_date_range(quarters[0])[0]
            value_end_date = self.get_quarter_date_range(quarters[-1])[1]
            self.logger.info(f"使用默认TOP列值计算时间范围: {value_start_date} 到 {value_end_date} (基于季度: {quarters[0]} 到 {quarters[-1]})")

        # 保存时间范围信息到摘要
        self.top_rankings_summary['ranking_period'] = {
            'start_date': ranking_start_date,
            'end_date': ranking_end_date,
            'description': f"{ranking_start_date} 到 {ranking_end_date}"
        }
        self.top_rankings_summary['value_period'] = {
            'start_date': value_start_date,
            'end_date': value_end_date,
            'description': f"{value_start_date} 到 {value_end_date}"
        }

        for department in departments:
            try:
                # 构建科室条件（根据是否使用一级分类映射）
                dept_conditions = []

                if self.mapping_options.get('use_level1_mapping', False):
                    # 使用映射表：找到所有映射到该科室的原始一级分类
                    original_depts = [k for k, v in self.level1_mappings.items() if v == department]
                    if not original_depts:
                        # 如果映射表中没有，使用默认映射
                        for original_dept, mapped_dept in self.DEPARTMENT_MAPPING.items():
                            if mapped_dept == department:
                                original_depts.append(original_dept)
                else:
                    # 不使用映射表：使用默认科室映射
                    original_depts = []
                    for original_dept, mapped_dept in self.DEPARTMENT_MAPPING.items():
                        if mapped_dept == department:
                            original_depts.append(original_dept)

                if not original_depts:
                    self.logger.warning(f"科室 {department} 没有找到对应的一级分类")
                    continue

                dept_conditions = [f"一级分类 = '{dept}'" for dept in original_depts]
                dept_condition = " OR ".join(dept_conditions)

                self.logger.info(f"科室 {department} 对应的一级分类: {original_depts}")

                # 计算品类TOP 5（应用映射）- 使用TOP排名统计时间范围
                category_query = f"""
                SELECT 二级分类, SUM(执行业绩（真实金额）) as total_revenue
                FROM 客户执行明细表
                WHERE 执行日期 BETWEEN '{ranking_start_date}' AND '{ranking_end_date}'
                  AND ({dept_condition})
                  AND 二级分类 IS NOT NULL
                  AND 执行业绩（真实金额） IS NOT NULL
                GROUP BY 二级分类
                ORDER BY total_revenue DESC
                """

                category_df = pd.read_sql(category_query, self.engine)

                print(f"=== {department} 科室原始品类数据（前10）===")
                self.logger.info(f"{department} 科室原始品类数据（前10）:")
                if not category_df.empty:
                    for i, row in category_df.head(10).iterrows():
                        log_msg = f"  {i+1}. {row['二级分类']}: {row['total_revenue']:,.2f}"
                        print(log_msg)
                        self.logger.info(log_msg)

                # 应用二级分类映射
                if not category_df.empty:
                    category_df['mapped_category'] = category_df['二级分类'].apply(
                        lambda x: self.apply_mapping(x, 'level2')
                    )

                    print(f"=== {department} 科室映射后数据 ===")
                    self.logger.info(f"{department} 科室映射后数据:")
                    for i, row in category_df.iterrows():
                        log_msg = f"  {row['二级分类']} → {row['mapped_category']}: {row['total_revenue']:,.2f}"
                        print(log_msg)
                        self.logger.info(log_msg)

                    # 按映射后的分类重新聚合
                    category_df = category_df.groupby('mapped_category')['total_revenue'].sum().reset_index()
                    category_df = category_df.sort_values('total_revenue', ascending=False).head(self.category_top_count)
                    category_df.columns = ['二级分类', 'total_revenue']
                    # 重置索引确保从0开始
                    category_df = category_df.reset_index(drop=True)

                    print(f"=== {department} 科室最终TOP{self.category_top_count}排序 ===")
                    self.logger.info(f"{department} 科室最终TOP{self.category_top_count}排序:")
                    for i, row in category_df.iterrows():
                        log_msg = f"  TOP{i+1}. {row['二级分类']}: {row['total_revenue']:,.2f}"
                        print(log_msg)
                        self.logger.info(log_msg)

                self.logger.info(f"{department} 科室品类TOP{self.category_top_count}: {list(category_df['二级分类']) if not category_df.empty else '无数据'}")

                # 保存品类排名信息到摘要
                if not category_df.empty:
                    dept_key = f"{department}_品类"
                    self.top_rankings_summary['categories'][dept_key] = []
                    for i, row in category_df.iterrows():
                        self.top_rankings_summary['categories'][dept_key].append({
                            'rank': i + 1,
                            'category': row['二级分类'],
                            'revenue': float(row['total_revenue']),
                            'column_name': f"{department}_TOP品类{i+1}_{self._clean_column_name(row['二级分类'])}"
                        })

                # 计算品项TOP 15（应用映射）- 使用TOP排名统计时间范围
                item_query = f"""
                SELECT 三级分类, SUM(执行业绩（真实金额）) as total_revenue
                FROM 客户执行明细表
                WHERE 执行日期 BETWEEN '{ranking_start_date}' AND '{ranking_end_date}'
                  AND ({dept_condition})
                  AND 三级分类 IS NOT NULL
                  AND 执行业绩（真实金额） IS NOT NULL
                GROUP BY 三级分类
                ORDER BY total_revenue DESC
                """

                item_df = pd.read_sql(item_query, self.engine)

                self.logger.info(f"{department} 科室原始品项数据（前10）:")
                if not item_df.empty:
                    for i, row in item_df.head(10).iterrows():
                        self.logger.info(f"  {i+1}. {row['三级分类']}: {row['total_revenue']:,.2f}")

                # 应用三级分类映射
                if not item_df.empty:
                    item_df['mapped_item'] = item_df['三级分类'].apply(
                        lambda x: self.apply_mapping(x, 'level3')
                    )
                    # 按映射后的分类重新聚合
                    item_df = item_df.groupby('mapped_item')['total_revenue'].sum().reset_index()
                    item_df = item_df.sort_values('total_revenue', ascending=False).head(self.item_top_count)
                    item_df.columns = ['三级分类', 'total_revenue']
                    # 重置索引确保从0开始
                    item_df = item_df.reset_index(drop=True)

                    print(f"=== {department} 科室最终品项TOP{self.item_top_count}排序（前5个）===")
                    self.logger.info(f"{department} 科室最终品项TOP{self.item_top_count}排序:")
                    for i, row in item_df.head(5).iterrows():  # 只显示前5个
                        log_msg = f"  TOP{i+1}. {row['三级分类']}: {row['total_revenue']:,.2f}"
                        print(log_msg)
                        self.logger.info(log_msg)

                self.logger.info(f"{department} 科室品项TOP{self.item_top_count}: {list(item_df['三级分类']) if not item_df.empty else '无数据'}")

                # 保存品项排名信息到摘要
                if not item_df.empty:
                    dept_key = f"{department}_品项"
                    self.top_rankings_summary['products'][dept_key] = []
                    for i, row in item_df.iterrows():
                        self.top_rankings_summary['products'][dept_key].append({
                            'rank': i + 1,
                            'item': row['三级分类'],
                            'revenue': float(row['total_revenue']),
                            'column_name': f"{department}_TOP品项{i+1}_{self._clean_column_name(row['三级分类'])}"
                        })

                # 为每个TOP品类生成列（使用配置的TOP数量）
                if not category_df.empty:
                    for idx, row in category_df.iterrows():
                        if idx >= self.category_top_count:  # 使用配置的品类TOP数量
                            break
                        category_name = self._clean_column_name(row['二级分类'])
                        column_name = f"{department}_TOP品类{idx+1}_{category_name}"

                        print(f">>> 生成列: {column_name} (索引:{idx}, 分类:{row['二级分类']}, 业绩:{row['total_revenue']:,.2f})")
                        self.logger.info(f"生成 {department} TOP品类{idx+1}: {row['二级分类']} (业绩: {row['total_revenue']:,.2f})")

                        # 计算每个会员在该品类的业绩（考虑映射）- 使用TOP列值计算时间范围
                        if self.mapping_options.get('use_level2_mapping', False):
                            # 如果使用映射，需要找到所有映射到该分类的原始值
                            original_categories = [k for k, v in self.level2_mappings.items() if v == row['二级分类']]
                            if not original_categories:
                                original_categories = [row['二级分类']]

                            category_condition = " OR ".join([f"二级分类 = '{cat}'" for cat in original_categories])
                            member_query = f"""
                            SELECT 会员卡号, SUM(执行业绩（真实金额）) as revenue
                            FROM 客户执行明细表
                            WHERE 执行日期 BETWEEN '{value_start_date}' AND '{value_end_date}'
                              AND ({dept_condition})
                              AND ({category_condition})
                              AND 会员卡号 IS NOT NULL
                            GROUP BY 会员卡号
                            """
                        else:
                            member_query = f"""
                            SELECT 会员卡号, SUM(执行业绩（真实金额）) as revenue
                            FROM 客户执行明细表
                            WHERE 执行日期 BETWEEN '{value_start_date}' AND '{value_end_date}'
                              AND ({dept_condition})
                              AND 二级分类 = '{row['二级分类']}'
                              AND 会员卡号 IS NOT NULL
                            GROUP BY 会员卡号
                            """

                        member_df = pd.read_sql(member_query, self.engine)
                        member_df['会员卡号'] = member_df['会员卡号'].astype(str)
                        member_df = member_df.rename(columns={'revenue': column_name})

                        # 合并到缓存表
                        self.cache_df = self.cache_df.merge(
                            member_df[['会员卡号', column_name]],
                            on='会员卡号',
                            how='left'
                        )
                        self.cache_df[column_name] = pd.to_numeric(self.cache_df[column_name], errors='coerce').fillna(0)

                # 为每个TOP品项生成列（使用配置的TOP数量）
                if not item_df.empty:
                    for idx, row in item_df.iterrows():
                        if idx >= self.item_top_count:  # 使用配置的品项TOP数量
                            break
                        item_name = self._clean_column_name(row['三级分类'])
                        column_name = f"{department}_TOP品项{idx+1}_{item_name}"

                        print(f">>> 生成列: {column_name} (索引:{idx}, 分类:{row['三级分类']}, 业绩:{row['total_revenue']:,.2f})")
                        self.logger.info(f"生成 {department} TOP品项{idx+1}: {row['三级分类']} (业绩: {row['total_revenue']:,.2f})")

                        # 计算每个会员在该品项的业绩（考虑映射）- 使用TOP列值计算时间范围
                        if self.mapping_options.get('use_level3_mapping', False):
                            # 如果使用映射，需要找到所有映射到该分类的原始值
                            original_items = [k for k, v in self.level3_mappings.items() if v == row['三级分类']]
                            if not original_items:
                                original_items = [row['三级分类']]

                            item_condition = " OR ".join([f"三级分类 = '{item}'" for item in original_items])
                            member_query = f"""
                            SELECT 会员卡号, SUM(执行业绩（真实金额）) as revenue
                            FROM 客户执行明细表
                            WHERE 执行日期 BETWEEN '{value_start_date}' AND '{value_end_date}'
                              AND ({dept_condition})
                              AND ({item_condition})
                              AND 会员卡号 IS NOT NULL
                            GROUP BY 会员卡号
                            """
                        else:
                            member_query = f"""
                            SELECT 会员卡号, SUM(执行业绩（真实金额）) as revenue
                            FROM 客户执行明细表
                            WHERE 执行日期 BETWEEN '{value_start_date}' AND '{value_end_date}'
                              AND ({dept_condition})
                              AND 三级分类 = '{row['三级分类']}'
                              AND 会员卡号 IS NOT NULL
                            GROUP BY 会员卡号
                            """

                        member_df = pd.read_sql(member_query, self.engine)
                        member_df['会员卡号'] = member_df['会员卡号'].astype(str)
                        member_df = member_df.rename(columns={'revenue': column_name})

                        # 合并到缓存表
                        self.cache_df = self.cache_df.merge(
                            member_df[['会员卡号', column_name]],
                            on='会员卡号',
                            how='left'
                        )
                        self.cache_df[column_name] = pd.to_numeric(self.cache_df[column_name], errors='coerce').fillna(0)

                self.logger.info(f"完成 {department} 科室TOP排名计算")

            except Exception as e:
                self.logger.error(f"计算 {department} 科室TOP排名失败: {str(e)}")
                continue

    def _clean_column_name(self, name: str) -> str:
        """清理列名中的特殊字符"""
        if not name:
            return "未知"
        # 移除或替换特殊字符
        cleaned = re.sub(r'[/\-\s\(\)（）]', '_', str(name))
        cleaned = re.sub(r'_+', '_', cleaned)  # 合并多个下划线
        cleaned = cleaned.strip('_')  # 移除首尾下划线
        return cleaned[:50]  # 限制长度

    def get_result_summary(self) -> Dict:
        """获取处理结果摘要"""
        if self.cache_df is None:
            return {}

        summary = {
            'total_members': len(self.cache_df),
            'total_columns': len(self.cache_df.columns),
            'processing_time': datetime.now().isoformat(),
            'data_shape': self.cache_df.shape,
            'mapping_info': {
                'use_level1_mapping': self.mapping_options.get('use_level1_mapping', False),
                'use_level2_mapping': self.mapping_options.get('use_level2_mapping', False),
                'use_level3_mapping': self.mapping_options.get('use_level3_mapping', False),
                'use_field_mapping': self.mapping_options.get('use_field_mapping', False),
                'use_field_group': self.mapping_options.get('use_field_group', False),
                'level1_mappings_count': len(self.level1_mappings),
                'level2_mappings_count': len(self.level2_mappings),
                'level3_mappings_count': len(self.level3_mappings),
                'field_mappings_count': len(getattr(self, 'field_mappings', {})),
                'field_group_mappings_count': len(getattr(self, 'field_group_mappings', {}))
            }
        }

        # 计算关键业绩列的统计信息
        performance_columns = [col for col in self.cache_df.columns if '执行业绩' in col]
        if performance_columns:
            summary['performance_stats'] = {}
            for col in performance_columns:
                summary['performance_stats'][col] = {
                    'total': float(self.cache_df[col].sum()),
                    'mean': float(self.cache_df[col].mean()),
                    'non_zero_count': int((self.cache_df[col] > 0).sum())
                }

        # 添加TOP排名信息
        if hasattr(self, 'top_rankings_summary') and self.top_rankings_summary:
            summary['top_rankings'] = self.top_rankings_summary

        return summary

    def export_to_excel(self, output_path: str) -> bool:
        """
        导出结果到Excel文件

        Args:
            output_path: 输出文件路径

        Returns:
            是否导出成功
        """
        try:
            if self.cache_df is None:
                raise ValueError("缓存表为空，无法导出")

            # 对列进行排序
            sorted_df = self._sort_columns_by_field_type(self.cache_df)
            self.logger.info("应用列排序规则")

            # 检查xlsxwriter是否可用，选择合适的引擎
            if XLSXWRITER_AVAILABLE:
                engine = 'xlsxwriter'
                self.logger.info("使用xlsxwriter引擎导出Excel")
            else:
                engine = 'openpyxl'
                self.logger.warning("xlsxwriter不可用，使用openpyxl引擎导出Excel")

            with pd.ExcelWriter(output_path, engine=engine) as writer:
                sorted_df.to_excel(writer, sheet_name='RFM分析结果', index=False)

                # 根据引擎类型设置格式
                if engine == 'xlsxwriter':
                    # xlsxwriter格式设置
                    workbook = writer.book
                    worksheet = writer.sheets['RFM分析结果']

                    # 设置列宽
                    for i, col in enumerate(sorted_df.columns):
                        max_len = max(
                            len(str(col)),
                            sorted_df[col].astype(str).str.len().max() if len(sorted_df) > 0 else 0
                        )
                        worksheet.set_column(i, i, min(max_len + 2, 50))

                    # 设置标题行格式
                    header_format = workbook.add_format({
                        'bold': True,
                        'text_wrap': True,
                        'valign': 'top',
                        'fg_color': '#D7E4BC',
                        'border': 1
                    })

                    for col_num, value in enumerate(sorted_df.columns.values):
                        worksheet.write(0, col_num, value, header_format)
                else:
                    # openpyxl格式设置（简化版）
                    self.logger.info("使用openpyxl引擎，应用基础格式")

            self.logger.info(f"数据成功导出到: {output_path}")
            return True

        except Exception as e:
            self.logger.error(f"导出Excel失败: {str(e)}")
            return False

    def process_data(self, task_type: str, quarter_tables: List[str],
                    quarters: List[str], departments: List[str] = None,
                    mapping_options: Dict = None, top_ranking_dates: Dict = None,
                    category_top_count: int = 5, item_top_count: int = 15, task=None) -> Dict:
        """
        执行完整的数据处理流程

        Args:
            task_type: 处理类型 ('forward' 或 'result')
            quarter_tables: 季度表列表（正向盘时为基础会员来源表）
            quarters: 分析季度列表（如 ['24Q1', '24Q2', '24Q3']）
            departments: 科室列表
            mapping_options: 映射选项
            task: 任务对象（用于更新进度）

        Returns:
            处理结果摘要
        """
        try:
            # 连接数据库
            if not self.connect_database():
                raise Exception("数据库连接失败")

            # 加载映射表
            if mapping_options:
                self.load_mappings(mapping_options)

            # 保存TOP排名时间范围配置
            self.top_ranking_dates = top_ranking_dates or {}

            # 保存TOP数量配置
            self.category_top_count = category_top_count
            self.item_top_count = item_top_count
            self.departments = departments or ['皮肤', '注射']  # 保存departments属性
            self.logger.info(f"品类TOP数: {self.category_top_count}, 品项TOP数: {self.item_top_count}")
            self.logger.info(f"处理科室: {self.departments}")

            # 初始化TOP排名结果存储
            self.top_rankings_summary = {
                'ranking_period': {},
                'value_period': {},
                'categories': {},
                'products': {}  # 改名避免与Python内置items()冲突
            }

            # 第一步：创建基础缓存表
            if task:
                task.update_progress(20, step_name="创建基础缓存表",
                                   message=f"正在创建基础缓存表，分析季度: {', '.join(quarters)}")

            if task_type == 'forward':
                # 正向盘：使用指定的基础会员来源表，分析所有季度
                base_quarter_table = quarter_tables[0] if quarter_tables else None
                if not base_quarter_table:
                    raise Exception("正向盘模式下必须指定基础会员来源表")
                self.create_base_cache_table(task_type, base_quarter_table, quarters)
            else:
                # 结果盘：使用所有季度表的会员并集
                self.create_base_cache_table(task_type, None, quarters, quarter_tables)

            # 第二步：计算季度执行业绩
            if task:
                task.update_progress(40, step_name="计算季度业绩", message="正在计算各季度执行业绩...")
            self.calculate_quarterly_performance(quarters)

            # 第三步：计算科室季度执行业绩
            if task:
                task.update_progress(60, step_name="计算科室业绩", message="正在计算各科室季度业绩...")
            self.calculate_department_performance(quarters, departments)

            # 第四步：计算TOP排名
            if task:
                task.update_progress(75, step_name="计算TOP排名", message="正在计算品类和品项TOP排名...")
            self.calculate_top_rankings(quarters, departments, self.top_ranking_dates)

            # 返回处理结果摘要
            return self.get_result_summary()

        except Exception as e:
            self.logger.error(f"数据处理失败: {str(e)}")
            raise
