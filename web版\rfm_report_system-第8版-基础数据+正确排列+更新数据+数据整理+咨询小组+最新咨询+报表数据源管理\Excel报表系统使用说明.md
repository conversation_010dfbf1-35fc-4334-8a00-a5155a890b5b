# Excel报表系统使用说明

## 🎯 系统概述

Excel报表系统是一个基于Excel公式逻辑的智能报表生成系统，可以将您现有的Excel报表逻辑移植到Web系统中，实现自动化报表生成。

## ✨ 核心特性

### 1. Excel公式支持
- **逻辑函数**：IF、AND、OR
- **数学函数**：SUM、SUMIF、SUMIFS、AVERAGE、COUNT、COUNTIF、MAX、MIN
- **文本函数**：CONCATENATE、LEFT、RIGHT、MID、LEN
- **日期函数**：TODAY、NOW、YEAR、MONTH、DAY
- **查找函数**：VLOOKUP、INDEX、MATCH

### 2. 预定义报表模板
- **会员RFM分析报表**：自动计算R、F、M值和综合等级
- **科室业绩分析报表**：科室业绩统计和分析
- **TOP品类分析报表**：品类排名和TOP标识

### 3. 自定义报表
- 灵活配置数据源
- 自定义计算列和公式
- 实时数据处理

## 🚀 使用流程

### 方式一：使用预定义模板

1. **访问Excel报表页面**
   - 点击导航栏中的"Excel报表"

2. **选择报表模板**
   - 查看可用的预定义模板
   - 点击"预览"查看报表结构
   - 点击"生成"创建完整报表

3. **下载报表**
   - 系统生成Excel文件
   - 自动下载到本地

### 方式二：创建自定义报表

1. **点击"自定义报表"**

2. **配置数据源**
   ```json
   {
     "name": "会员数据",
     "query": "SELECT 会员卡号, SUM(执行业绩) as 总金额 FROM 客户执行明细表 GROUP BY 会员卡号"
   }
   ```

3. **配置计算列**
   ```json
   {
     "name": "客户等级",
     "formula": "=IF([总金额]>50000, \"VIP\", IF([总金额]>20000, \"高级\", \"普通\"))",
     "source_data": "会员数据"
   }
   ```

4. **生成报表**

## 📊 Excel公式示例

### 条件判断
```excel
=IF([金额]>1000, "高消费", "普通消费")
=IF(AND([金额]>1000, [次数]>5), "优质客户", "普通客户")
=IF(OR([金额]>5000, [次数]>10), "重要客户", "普通客户")
```

### 数据统计
```excel
=SUM([金额列])
=SUMIF([条件列], "条件值", [求和列])
=COUNTIF([条件列], "条件值")
=AVERAGE([数值列])
```

### 文本处理
```excel
=CONCATENATE([文本1], "-", [文本2])
=LEFT([文本], 3)
=RIGHT([文本], 3)
=MID([文本], 2, 3)
```

### 日期处理
```excel
=TODAY()
=(TODAY()-[日期列])
=YEAR([日期列])
=MONTH([日期列])
```

## 🔧 技术实现

### 数据流程
1. **数据提取**：从数据库提取原始数据
2. **公式计算**：应用Excel公式逻辑
3. **结果生成**：生成计算结果
4. **Excel导出**：导出为Excel文件

### 公式引擎
- 基于Python实现的Excel公式解析器
- 支持嵌套公式和复杂逻辑
- 安全的公式执行环境

### 模板系统
- 配置化的报表模板
- 可扩展的列定义
- 灵活的数据源配置

## 📋 报表模板配置格式

```json
{
  "description": "报表描述",
  "source_tables": ["数据表1", "数据表2"],
  "columns": [
    {
      "name": "列名",
      "formula": "=Excel公式",
      "source_data": "数据源名称",
      "description": "列描述"
    }
  ]
}
```

## 🎯 应用场景

### 1. 会员分析
- RFM分析
- 客户分层
- 消费行为分析

### 2. 业绩分析
- 科室业绩统计
- 品类排名分析
- 趋势分析

### 3. 运营报表
- 日常运营指标
- KPI监控
- 数据看板

## 🔍 注意事项

1. **公式语法**：使用标准Excel公式语法
2. **列引用**：使用 `[列名]` 格式引用列
3. **数据类型**：注意数值和文本的处理
4. **性能考虑**：大数据量时注意查询优化

## 🆘 常见问题

### Q: 如何引用其他表的数据？
A: 在数据源配置中定义多个表，然后在公式中使用VLOOKUP等函数

### Q: 支持哪些Excel函数？
A: 支持常用的逻辑、数学、文本、日期和查找函数，详见功能列表

### Q: 如何处理复杂的嵌套公式？
A: 系统支持嵌套公式，按照Excel语法编写即可

### Q: 数据更新频率如何？
A: 每次生成报表时都会从数据库获取最新数据

## 📞 技术支持

如有问题或需要帮助，请联系系统管理员。
