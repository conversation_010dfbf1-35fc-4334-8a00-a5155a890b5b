# -*- coding: utf-8 -*-
"""
用户服务
"""

from typing import Optional, Dict, List, Tuple
from datetime import datetime
from flask_login import login_user, logout_user
from app import db
from app.models import User
from app.repositories.user_repository import UserRepository

class UserService:
    """用户业务逻辑服务"""
    
    def __init__(self):
        self.user_repo = UserRepository()
    
    def authenticate_user(self, username: str, password: str) -> Tu<PERSON>[bool, Optional[User], str]:
        """用户认证"""
        try:
            # 查找用户
            user = self.user_repo.find_by_username(username)
            if not user:
                return False, None, "用户名不存在"
            
            # 检查用户是否激活
            if not user.is_active:
                return False, None, "账号已被停用，请联系管理员"
            
            # 验证密码
            if not user.check_password(password):
                return False, None, "密码错误"
            
            return True, user, "登录成功"
            
        except Exception as e:
            return False, None, f"登录失败: {str(e)}"
    
    def login_user_session(self, user: User, remember: bool = False) -> bool:
        """用户登录会话管理"""
        try:
            # 更新最后登录时间
            self.user_repo.update_last_login(user.id)
            
            # 创建登录会话
            login_user(user, remember=remember)
            
            return True
            
        except Exception as e:
            return False
    
    def logout_user_session(self) -> bool:
        """用户登出会话管理"""
        try:
            logout_user()
            return True
        except Exception as e:
            return False
    
    def create_user(self, user_data: Dict) -> User:
        """创建用户"""
        try:
            # 检查用户名是否已存在
            if self.user_repo.find_by_username(user_data['username']):
                raise ValueError("用户名已存在")
            
            # 创建用户对象
            user = User(
                username=user_data['username'],
                real_name=user_data['real_name'],
                role=user_data['role'],
                department_id=user_data.get('department_id'),
                simple_code=user_data.get('simple_code'),
                is_active=user_data.get('is_active', True)
            )
            
            # 设置密码
            user.set_password(user_data['password'])
            
            # 保存到数据库
            db.session.add(user)
            db.session.commit()
            
            return user
            
        except Exception as e:
            db.session.rollback()
            raise e
    
    def update_user(self, user_id: int, user_data: Dict) -> bool:
        """更新用户信息"""
        try:
            user = self.user_repo.get_by_id(user_id)
            if not user:
                return False
            
            # 更新用户信息
            for key, value in user_data.items():
                if key != 'password' and hasattr(user, key):
                    setattr(user, key, value)
            
            # 如果包含密码更新
            if 'password' in user_data and user_data['password']:
                user.set_password(user_data['password'])
            
            db.session.commit()
            return True
            
        except Exception as e:
            db.session.rollback()
            raise e
    
    def change_password(self, user_id: int, old_password: str, new_password: str) -> Tuple[bool, str]:
        """修改密码"""
        try:
            user = self.user_repo.get_by_id(user_id)
            if not user:
                return False, "用户不存在"
            
            # 验证旧密码
            if not user.check_password(old_password):
                return False, "原密码错误"
            
            # 设置新密码
            user.set_password(new_password)
            db.session.commit()
            
            return True, "密码修改成功"
            
        except Exception as e:
            db.session.rollback()
            return False, f"密码修改失败: {str(e)}"
    
    def reset_password(self, user_id: int, new_password: str) -> bool:
        """重置密码（管理员操作）"""
        return self.user_repo.change_password(user_id, new_password)
    
    def activate_user(self, user_id: int) -> bool:
        """激活用户"""
        return self.user_repo.activate_user(user_id)
    
    def deactivate_user(self, user_id: int) -> bool:
        """停用用户"""
        return self.user_repo.deactivate_user(user_id)
    
    def get_user_by_id(self, user_id: int) -> Optional[User]:
        """根据ID获取用户"""
        return self.user_repo.get_by_id(user_id)
    
    def get_user_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        return self.user_repo.find_by_username(username)
    
    def get_users_by_role(self, role: str) -> List[User]:
        """根据角色获取用户列表"""
        return self.user_repo.find_by_role(role)
    
    def get_users_by_department(self, department_id: int) -> List[User]:
        """根据部门获取用户列表"""
        return self.user_repo.find_by_department(department_id)
    
    def search_users(self, query: str) -> List[User]:
        """搜索用户"""
        return self.user_repo.search_by_simple_code(query)
    
    def get_online_consultants(self) -> List[User]:
        """获取网络咨询人员列表"""
        return self.user_repo.get_online_consultants()
    
    def get_onsite_consultants(self) -> List[User]:
        """获取现场咨询人员列表"""
        return self.user_repo.get_onsite_consultants()
    
    def get_user_statistics(self) -> Dict:
        """获取用户统计信息"""
        return self.user_repo.get_user_statistics()
    
    def generate_simple_code(self, real_name: str) -> str:
        """生成简码（基于真实姓名的拼音首字母）"""
        try:
            # 这里可以集成拼音库来生成简码
            # 暂时使用简单的实现
            import re
            # 移除非中文字符
            chinese_chars = re.findall(r'[\u4e00-\u9fff]', real_name)
            if chinese_chars:
                # 简单的拼音映射（实际项目中应该使用专业的拼音库）
                return ''.join(chinese_chars).upper()[:4]
            else:
                return real_name.upper()[:4]
        except:
            return real_name.upper()[:4]