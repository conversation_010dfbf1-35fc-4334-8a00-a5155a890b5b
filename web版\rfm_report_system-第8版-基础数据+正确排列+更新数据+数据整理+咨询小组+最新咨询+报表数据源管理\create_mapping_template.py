#!/usr/bin/env python3
"""
创建标准的参照表模板文件
"""

import pandas as pd
import os

def create_mapping_template():
    """创建参照表模板"""
    
    print("正在创建参照表模板...")
    
    # 创建Excel写入器
    output_path = "参照表模板.xlsx"
    
    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
        
        # 1. 一级分类映射
        level1_data = {
            '原始值': ['美容项目', '医疗项目', '产品销售', '其他'],
            '映射值': ['美容', '医疗', '产品', '其他']
        }
        df_level1 = pd.DataFrame(level1_data)
        df_level1.to_excel(writer, sheet_name='一级分类映射', index=False)
        
        # 2. 二级分类映射
        level2_data = {
            '原始值': [
                '皮肤美容项目', '形体美容项目', '注射美容项目', '口腔美容项目', '毛发移植项目',
                '皮肤医疗项目', '形体医疗项目', '注射医疗项目', '口腔医疗项目', '毛发医疗项目',
                '护肤产品', '美容产品', '医疗产品', '其他产品'
            ],
            '映射值': [
                '皮肤美容', '形体美容', '注射美容', '口腔美容', '毛发美容',
                '皮肤医疗', '形体医疗', '注射医疗', '口腔医疗', '毛发医疗',
                '护肤品', '美容品', '医疗品', '其他'
            ]
        }
        df_level2 = pd.DataFrame(level2_data)
        df_level2.to_excel(writer, sheet_name='二级分类映射', index=False)
        
        # 3. 三级分类映射
        level3_data = {
            '原始值': [
                '五代热玛吉', '超皮秒', '黄金微针', '水光针', '肉毒素',
                '玻尿酸', '线雕', '激光脱毛', '牙齿美白', '种植牙',
                '面部填充', '瘦脸针', '除皱针', '美白针', '溶脂针'
            ],
            '映射值': [
                '热玛吉', '皮秒激光', '微针', '水光', '肉毒',
                '透明质酸', '线雕提升', '激光脱毛', '牙齿美白', '种植牙',
                '面部填充', '瘦脸', '除皱', '美白', '溶脂'
            ]
        }
        df_level3 = pd.DataFrame(level3_data)
        df_level3.to_excel(writer, sheet_name='三级分类映射', index=False)
        
        # 4. 现场映射
        field_data = {
            '原始值': [
                '张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十',
                '郑一', '王二', '冯三', '陈四', '褚五', '卫六', '蒋七', '沈八'
            ],
            '映射值': [
                '张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十',
                '郑一', '王二', '冯三', '陈四', '褚五', '卫六', '蒋七', '沈八'
            ]
        }
        df_field = pd.DataFrame(field_data)
        df_field.to_excel(writer, sheet_name='现场映射', index=False)
        
        # 5. 现场小组映射
        field_group_data = {
            '原始值': [
                '张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十',
                '郑一', '王二', '冯三', '陈四', '褚五', '卫六', '蒋七', '沈八'
            ],
            '映射值': [
                '张三小组', '李四小组', '王五小组', '赵六小组', '钱七小组', '孙八小组', '周九小组', '吴十小组',
                '郑一小组', '王二小组', '冯三小组', '陈四小组', '褚五小组', '卫六小组', '蒋七小组', '沈八小组'
            ]
        }
        df_field_group = pd.DataFrame(field_group_data)
        df_field_group.to_excel(writer, sheet_name='现场小组映射', index=False)
    
    print(f"✅ 参照表模板已创建: {output_path}")
    print("\n📋 模板包含以下工作表:")
    print("1. 一级分类映射 - 项目大类映射")
    print("2. 二级分类映射 - 项目细分映射") 
    print("3. 三级分类映射 - 具体项目映射")
    print("4. 现场映射 - 现场人员映射")
    print("5. 现场小组映射 - 现场小组映射")
    print("\n📝 每个工作表都包含两列:")
    print("- 原始值: 原始数据中的值")
    print("- 映射值: 要映射到的目标值")
    
    return output_path

def validate_template(file_path):
    """验证模板格式"""
    print(f"\n🔍 验证模板格式: {file_path}")
    
    try:
        excel_file = pd.ExcelFile(file_path)
        print(f"📋 工作表: {excel_file.sheet_names}")
        
        required_sheets = ['一级分类映射', '二级分类映射', '三级分类映射', '现场映射', '现场小组映射']
        
        for sheet in required_sheets:
            if sheet in excel_file.sheet_names:
                df = pd.read_excel(file_path, sheet_name=sheet)
                print(f"✅ {sheet}: {list(df.columns)} ({len(df)}行)")
            else:
                print(f"❌ 缺少工作表: {sheet}")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {str(e)}")
        return False

if __name__ == "__main__":
    # 创建模板
    template_path = create_mapping_template()
    
    # 验证模板
    validate_template(template_path)
    
    print(f"\n🎉 模板创建完成！请使用 {template_path} 作为参照表模板。")
