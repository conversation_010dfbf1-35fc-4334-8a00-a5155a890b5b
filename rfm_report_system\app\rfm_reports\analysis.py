#!/usr/bin/env python3
"""
RFM报表分析模块
"""

import pandas as pd
import re
from typing import Dict, List, Any, Tuple
from app.rfm_reports.models import RFMAnalysisData


class BaseVolumeTrendAnalysis:
    """基盘体量趋势分析器"""
    
    def __init__(self, data_source):
        """
        初始化分析器
        
        Args:
            data_source: RFMDataSource对象
        """
        self.data_source = data_source
        self.quarters = self._detect_quarters()
        
    def _detect_quarters(self) -> List[str]:
        """
        从数据源中检测可用的季度

        Returns:
            排序后的季度列表，如['24Q4', '25Q1', '25Q2']
        """
        # 直接使用数据源中存储的季度信息
        if self.data_source.quarters:
            return sorted(self.data_source.quarters, key=lambda x: (x[:2], x[3:]))

        # 如果数据源没有季度信息，尝试从数据中检测
        sample_data = RFMAnalysisData.query.filter_by(
            data_source_id=self.data_source.id
        ).first()

        if not sample_data:
            return []

        quarters = set()

        # 检查固定的季度字段
        quarter_fields = [
            ('23Q4', 'comprehensive_level_23q4'),
            ('24Q1', 'comprehensive_level_24q1'),
            ('24Q2', 'comprehensive_level_24q2'),
            ('24Q3', 'comprehensive_level_24q3')
        ]

        for quarter, field_name in quarter_fields:
            # 检查该字段是否有数据
            if hasattr(sample_data, field_name) and getattr(sample_data, field_name):
                quarters.add(quarter)

        # 检查extended_data中是否有其他季度数据
        if sample_data.extended_data:
            quarter_pattern = r'^(\d{2}Q[1-4])_'

            for key in sample_data.extended_data.keys():
                match = re.match(quarter_pattern, key)
                if match:
                    quarter = match.group(1)
                    quarters.add(quarter)

        # 排序并返回
        return sorted(list(quarters), key=lambda x: (x[:2], x[3:]))
    
    def generate_reports(self) -> Dict[str, Any]:
        """
        生成基盘体量趋势的3个报表

        Returns:
            包含3个报表数据的字典
        """
        try:
            # 获取所有分析数据
            analysis_data = RFMAnalysisData.query.filter_by(
                data_source_id=self.data_source.id
            ).all()

            if not analysis_data:
                return self._empty_reports()

            # 转换为DataFrame
            df = self._to_dataframe(analysis_data)

            # 检查数据源是否包含必要的字段
            validation_result = self._validate_data_structure(df)
            if not validation_result['valid']:
                return {
                    'analysis_type': 'base-volume-trend',
                    'data_source_id': self.data_source.id,
                    'quarters': self.quarters,
                    'total_records': len(analysis_data),
                    'error': validation_result['message'],
                    'missing_fields': validation_result['missing_fields'],
                    'available_fields': validation_result['available_fields'],
                    'reports': {}
                }

            # 生成3个报表
            report1_data = self._generate_rfm_structure_report(df)
            report2_data = self._generate_member_structure_report(df)
            report3_data = self._generate_consultant_attribution_report(df)

            return {
                'analysis_type': 'base-volume-trend',
                'data_source_id': self.data_source.id,
                'quarters': self.quarters,
                'total_records': len(analysis_data),
                'reports': {
                    'rfm_structure': report1_data,
                    'member_structure': report2_data,
                    'consultant_attribution': report3_data
                }
            }

        except Exception as e:
            return {
                'analysis_type': 'base-volume-trend',
                'error': str(e),
                'quarters': self.quarters,
                'total_records': 0,
                'reports': {}
            }
    
    def _to_dataframe(self, analysis_data: List[RFMAnalysisData]) -> pd.DataFrame:
        """
        将分析数据转换为DataFrame

        Args:
            analysis_data: RFMAnalysisData对象列表

        Returns:
            pandas DataFrame
        """
        records = []
        for item in analysis_data:
            record = {'会员卡号': item.member_id}

            # 添加固定字段数据
            # 23Q4数据
            if item.comprehensive_level_23q4:
                record['23Q4_综合等级'] = item.comprehensive_level_23q4
                record['23Q4_细分等级'] = item.detailed_level_23q4
                record['23Q4_会员卡级'] = item.member_card_level_23q4
                record['23Q4_科室标签'] = item.department_tag_23q4

            # 24Q1数据
            if item.comprehensive_level_24q1:
                record['24Q1_综合等级'] = item.comprehensive_level_24q1
                record['24Q1_细分等级'] = item.detailed_level_24q1
                record['24Q1_会员卡级'] = item.member_card_level_24q1
                record['24Q1_科室标签'] = item.department_tag_24q1

            # 24Q2数据
            if item.comprehensive_level_24q2:
                record['24Q2_综合等级'] = item.comprehensive_level_24q2
                record['24Q2_细分等级'] = item.detailed_level_24q2
                record['24Q2_会员卡级'] = item.member_card_level_24q2
                record['24Q2_科室标签'] = item.department_tag_24q2

            # 24Q3数据
            if item.comprehensive_level_24q3:
                record['24Q3_综合等级'] = item.comprehensive_level_24q3
                record['24Q3_细分等级'] = item.detailed_level_24q3
                record['24Q3_会员卡级'] = item.member_card_level_24q3
                record['24Q3_科室标签'] = item.department_tag_24q3

            # 添加扩展数据
            if item.extended_data:
                record.update(item.extended_data)

            records.append(record)

        return pd.DataFrame(records)
    
    def _generate_rfm_structure_report(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        生成报表一：存量老客RFM基盘体量 - 锁盘老客结构变化

        Args:
            df: 数据DataFrame

        Returns:
            报表数据字典
        """
        report_data = []

        for quarter in self.quarters:
            # 获取该季度的综合等级和细分等级字段
            comprehensive_field = f'{quarter}_综合等级'
            subdivision_field = f'{quarter}_细分等级'
            card_level_field = f'{quarter}_会员卡级'

            if comprehensive_field not in df.columns or subdivision_field not in df.columns:
                continue

            # 统一的基础筛选条件：必须有会员卡号，且该季度有RFM数据
            quarter_df = df[
                (df['会员卡号'].notna()) &
                (df['会员卡号'] != '') &
                (df[comprehensive_field].notna()) &
                (df[comprehensive_field] != '') &
                (df[comprehensive_field] != '不在盘内') &
                (df[subdivision_field].notna()) &
                (df[subdivision_field] != '') &
                (df[subdivision_field] != '不在盘内')
            ].copy()
            
            # 按综合等级和细分等级分组统计
            grouped = quarter_df.groupby([comprehensive_field, subdivision_field])['会员卡号'].nunique().reset_index()
            grouped.columns = ['综合等级', '细分等级', '锁盘老客人数']
            grouped['季度'] = quarter
            
            report_data.append(grouped)
        
        if not report_data:
            return {'data': [], 'summary': {}}
        
        # 合并所有季度数据
        combined_df = pd.concat(report_data, ignore_index=True)
        
        # 计算环比增长率
        combined_df = self._calculate_growth_rates(combined_df, ['综合等级', '细分等级'])
        
        # 计算小计和合计
        combined_df = self._calculate_subtotals_and_totals(combined_df, '综合等级')
        
        return {
            'data': combined_df.to_dict('records'),
            'summary': self._generate_summary(combined_df),
            'quarters': self.quarters
        }
    
    def _generate_member_structure_report(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        生成报表二：会员老客RFM基盘体量 - 锁盘会员结构变化
        
        Args:
            df: 数据DataFrame
            
        Returns:
            报表数据字典
        """
        report_data = []
        
        for quarter in self.quarters:
            # 获取该季度的字段
            comprehensive_field = f'{quarter}_综合等级'
            subdivision_field = f'{quarter}_细分等级'
            card_level_field = f'{quarter}_会员卡级'

            if card_level_field not in df.columns:
                continue

            # 统一的基础筛选条件：必须有会员卡号，且该季度有完整的RFM数据
            quarter_df = df[
                (df['会员卡号'].notna()) &
                (df['会员卡号'] != '') &
                (df[comprehensive_field].notna()) &
                (df[comprehensive_field] != '') &
                (df[comprehensive_field] != '不在盘内') &
                (df[subdivision_field].notna()) &
                (df[subdivision_field] != '') &
                (df[subdivision_field] != '不在盘内') &
                (df[card_level_field].notna()) &
                (df[card_level_field] != '') &
                (df[card_level_field] != '不在盘内')
            ].copy()
            
            # 映射会员池
            quarter_df['会员池'] = quarter_df[card_level_field].apply(self._map_member_pool)
            
            # 过滤掉无效的会员池
            quarter_df = quarter_df[quarter_df['会员池'].notna()]
            
            # 按会员池和会员卡级分组统计
            grouped = quarter_df.groupby(['会员池', card_level_field])['会员卡号'].nunique().reset_index()
            grouped.columns = ['会员池', '会员卡级', '锁盘老客人数']
            grouped['季度'] = quarter
            
            report_data.append(grouped)
        
        if not report_data:
            return {'data': [], 'summary': {}}
        
        # 合并所有季度数据
        combined_df = pd.concat(report_data, ignore_index=True)
        
        # 计算环比增长率
        combined_df = self._calculate_growth_rates(combined_df, ['会员池', '会员卡级'])
        
        # 计算小计和合计
        combined_df = self._calculate_subtotals_and_totals(combined_df, '会员池')
        
        return {
            'data': combined_df.to_dict('records'),
            'summary': self._generate_summary(combined_df),
            'quarters': self.quarters
        }
    
    def _generate_consultant_attribution_report(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        生成报表三：咨询归属RFM基盘体量
        
        Args:
            df: 数据DataFrame
            
        Returns:
            报表数据字典
        """
        # 获取筛选器选项
        filter_options = self._get_filter_options(df)
        
        # 生成默认报表数据（全选状态）
        report_data = self._generate_consultant_data(df, filter_options['subdivision_levels'], filter_options['member_card_levels'])
        
        return {
            'data': report_data['data'],
            'summary': report_data['summary'],
            'quarters': self.quarters,
            'filter_options': filter_options
        }
    
    def _map_member_pool(self, card_level: str) -> str:
        """
        映射会员卡级到会员池
        
        Args:
            card_level: 会员卡级
            
        Returns:
            会员池名称
        """
        if card_level == '大众卡':
            return '非会员'
        elif card_level in ['水晶', '银卡']:
            return '基础会员'
        elif card_level in ['金卡', '铂金卡', '钻石卡', '黑钻卡', '黑钻PLUS']:
            return 'VIP会员'
        else:
            return None
    
    def _get_filter_options(self, df: pd.DataFrame) -> Dict[str, List[str]]:
        """
        获取筛选器选项
        
        Args:
            df: 数据DataFrame
            
        Returns:
            筛选器选项字典
        """
        subdivision_levels = set()
        member_card_levels = set()
        
        for quarter in self.quarters:
            # 细分等级选项
            subdivision_field = f'{quarter}_细分等级'
            if subdivision_field in df.columns:
                values = df[subdivision_field].dropna().unique()
                valid_values = [v for v in values if v and v != '不在盘内']
                subdivision_levels.update(valid_values)
            
            # 会员卡级选项
            card_level_field = f'{quarter}_会员卡级'
            if card_level_field in df.columns:
                values = df[card_level_field].dropna().unique()
                valid_values = [v for v in values if v and v != '不在盘内']
                member_card_levels.update(valid_values)
        
        return {
            'subdivision_levels': sorted(list(subdivision_levels)),
            'member_card_levels': sorted(list(member_card_levels))
        }
    
    def _generate_consultant_data(self, df: pd.DataFrame, subdivision_filter: List[str], card_level_filter: List[str]) -> Dict[str, Any]:
        """
        生成咨询归属报表数据

        Args:
            df: 数据DataFrame
            subdivision_filter: 细分等级筛选条件
            card_level_filter: 会员卡级筛选条件

        Returns:
            报表数据字典
        """
        report_data = []

        for quarter in self.quarters:
            # 获取该季度的相关字段
            comprehensive_field = f'{quarter}_综合等级'
            subdivision_field = f'{quarter}_细分等级'
            card_level_field = f'{quarter}_会员卡级'

            if not all(field in df.columns for field in [comprehensive_field, subdivision_field, card_level_field]):
                continue

            # 检查是否有最新现场字段
            if '最新现场' not in df.columns:
                continue

            # 统一的基础筛选条件 + 业务筛选条件
            quarter_df = df[
                # 基础数据完整性筛选
                (df['会员卡号'].notna()) &
                (df['会员卡号'] != '') &
                (df[comprehensive_field].notna()) &
                (df[comprehensive_field] != '') &
                (df[comprehensive_field] != '不在盘内') &
                (df[subdivision_field].notna()) &
                (df[subdivision_field] != '') &
                (df[subdivision_field] != '不在盘内') &
                (df[card_level_field].notna()) &
                (df[card_level_field] != '') &
                (df[card_level_field] != '不在盘内') &
                # 现场信息筛选
                (df['最新现场'].notna()) &
                (df['最新现场'] != '') &
                (df['最新现场'] != '不在盘内') &
                (df['现场小组'].notna()) &
                (df['现场小组'] != '') &
                (df['现场小组'] != '不在盘内') &
                # 业务筛选条件
                (df[subdivision_field].isin(subdivision_filter)) &
                (df[card_level_field].isin(card_level_filter))
            ].copy()

            # 按现场小组和最新现场分组统计
            grouped = quarter_df.groupby(['现场小组', '最新现场'])['会员卡号'].nunique().reset_index()
            grouped.columns = ['现场小组', '最新现场', '锁盘老客人数']
            grouped['季度'] = quarter

            report_data.append(grouped)

        if not report_data:
            return {'data': [], 'summary': {}}

        # 合并所有季度数据
        combined_df = pd.concat(report_data, ignore_index=True)

        # 计算环比增长率
        combined_df = self._calculate_growth_rates(combined_df, ['现场小组', '最新现场'])

        # 计算小计和合计
        combined_df = self._calculate_subtotals_and_totals(combined_df, '现场小组')

        return {
            'data': combined_df.to_dict('records'),
            'summary': self._generate_summary(combined_df)
        }
    
    def _calculate_growth_rates(self, df: pd.DataFrame, group_columns: List[str]) -> pd.DataFrame:
        """
        计算环比增长率
        
        Args:
            df: 数据DataFrame
            group_columns: 分组列
            
        Returns:
            包含环比增长率的DataFrame
        """
        # 透视表，季度作为列
        pivot_df = df.pivot_table(
            index=group_columns,
            columns='季度',
            values='锁盘老客人数',
            fill_value=0
        )
        
        # 计算环比增长率
        growth_rates = {}
        for i, quarter in enumerate(self.quarters):
            if i == 0:
                growth_rates[f'{quarter}_环比增长'] = None  # 第一个季度没有环比
            else:
                prev_quarter = self.quarters[i-1]
                if quarter in pivot_df.columns and prev_quarter in pivot_df.columns:
                    growth = ((pivot_df[quarter] - pivot_df[prev_quarter]) / pivot_df[prev_quarter].replace(0, 1) * 100).round(1)
                    growth_rates[f'{quarter}_环比增长'] = growth
        
        # 重新整理数据格式
        result_data = []
        for group_values, row in pivot_df.iterrows():
            for quarter in self.quarters:
                if quarter in pivot_df.columns:
                    record = {}
                    for i, col in enumerate(group_columns):
                        record[col] = group_values[i] if isinstance(group_values, tuple) else group_values
                    record['季度'] = quarter
                    record['锁盘老客人数'] = row[quarter]
                    
                    # 添加环比增长率
                    growth_key = f'{quarter}_环比增长'
                    if growth_key in growth_rates and growth_rates[growth_key] is not None:
                        if isinstance(group_values, tuple):
                            growth_value = growth_rates[growth_key].loc[group_values]
                        else:
                            growth_value = growth_rates[growth_key].loc[group_values]
                        record['环比增长'] = f"{growth_value:.1f}%" if pd.notna(growth_value) else "0%"
                    else:
                        record['环比增长'] = "-"
                    
                    result_data.append(record)
        
        return pd.DataFrame(result_data)
    
    def _calculate_subtotals_and_totals(self, df: pd.DataFrame, group_column: str) -> pd.DataFrame:
        """
        计算小计和合计

        Args:
            df: 数据DataFrame
            group_column: 分组列名

        Returns:
            包含小计和合计的DataFrame
        """
        if df.empty:
            return df

        result_rows = []

        # 按分组列分组，为每个分组添加原始数据和小计
        groups = df.groupby(group_column)

        for group_name, group_data in groups:
            # 添加该分组的原始数据行
            for _, row in group_data.iterrows():
                result_rows.append(row.to_dict())

            # 为该分组添加小计行（每个季度一行）
            for quarter in self.quarters:
                quarter_data = group_data[group_data['季度'] == quarter]
                if not quarter_data.empty:
                    subtotal_count = quarter_data['锁盘老客人数'].sum()

                    # 计算环比增长
                    prev_quarter_idx = list(self.quarters).index(quarter) - 1 if quarter in self.quarters else -1
                    if prev_quarter_idx >= 0:
                        prev_quarter = self.quarters[prev_quarter_idx]
                        prev_quarter_data = group_data[group_data['季度'] == prev_quarter]
                        if not prev_quarter_data.empty:
                            prev_subtotal_count = prev_quarter_data['锁盘老客人数'].sum()
                            if prev_subtotal_count > 0:
                                growth_rate = (subtotal_count - prev_subtotal_count) / prev_subtotal_count * 100
                                growth_str = f"{growth_rate:.1f}%"
                            else:
                                growth_str = "0%"
                        else:
                            growth_str = "-"
                    else:
                        growth_str = "-"

                    # 创建小计行
                    subtotal_row = {}
                    subtotal_row[group_column] = f"{group_name}小计"

                    # 对于其他维度列，设置为空
                    for col in df.columns:
                        if col != group_column and col not in ['季度', '锁盘老客人数', '环比增长']:
                            subtotal_row[col] = ""

                    subtotal_row['季度'] = quarter
                    subtotal_row['锁盘老客人数'] = subtotal_count
                    subtotal_row['环比增长'] = growth_str

                    result_rows.append(subtotal_row)

        # 添加合计行（每个季度一行）
        for quarter in self.quarters:
            quarter_data = df[df['季度'] == quarter]
            if not quarter_data.empty:
                total_count = quarter_data['锁盘老客人数'].sum()

                # 计算环比增长
                prev_quarter_idx = list(self.quarters).index(quarter) - 1 if quarter in self.quarters else -1
                if prev_quarter_idx >= 0:
                    prev_quarter = self.quarters[prev_quarter_idx]
                    prev_quarter_data = df[df['季度'] == prev_quarter]
                    if not prev_quarter_data.empty:
                        prev_total_count = prev_quarter_data['锁盘老客人数'].sum()
                        if prev_total_count > 0:
                            growth_rate = (total_count - prev_total_count) / prev_total_count * 100
                            growth_str = f"{growth_rate:.1f}%"
                        else:
                            growth_str = "0%"
                    else:
                        growth_str = "-"
                else:
                    growth_str = "-"

                # 创建合计行
                total_row = {}
                first_col = df.columns[0]
                total_row[first_col] = "合计"

                # 对于其他维度列，设置为空
                for col in df.columns:
                    if col not in [first_col, '季度', '锁盘老客人数', '环比增长']:
                        total_row[col] = ""

                total_row['季度'] = quarter
                total_row['锁盘老客人数'] = total_count
                total_row['环比增长'] = growth_str

                result_rows.append(total_row)

        return pd.DataFrame(result_rows)




    
    def _generate_summary(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        生成报表摘要信息
        
        Args:
            df: 数据DataFrame
            
        Returns:
            摘要信息字典
        """
        if df.empty:
            return {
                'total_customers': 0,
                'growth_rate': '0%',
                'quarters_count': len(self.quarters)
            }
        
        # 计算最新季度的总客户数
        latest_quarter = self.quarters[-1] if self.quarters else None
        if latest_quarter:
            latest_data = df[df['季度'] == latest_quarter]
            total_customers = latest_data['锁盘老客人数'].sum()
        else:
            total_customers = 0
        
        return {
            'total_customers': total_customers,
            'growth_rate': '计算中',
            'quarters_count': len(self.quarters)
        }
    
    def _validate_data_structure(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        验证数据源是否包含基盘体量趋势分析所需的字段

        Args:
            df: 数据DataFrame

        Returns:
            验证结果字典
        """
        required_fields = []
        missing_fields = []
        available_fields = list(df.columns)

        # 检查每个季度是否有必要的字段
        for quarter in self.quarters:
            required_quarter_fields = [
                f'{quarter}_综合等级',
                f'{quarter}_细分等级',
                f'{quarter}_会员卡级',
                f'{quarter}_现场'
            ]
            required_fields.extend(required_quarter_fields)

            for field in required_quarter_fields:
                if field not in available_fields:
                    missing_fields.append(field)

        # 检查基础字段
        basic_fields = ['会员卡号', '现场小组']
        for field in basic_fields:
            if field not in available_fields:
                missing_fields.append(field)

        is_valid = len(missing_fields) == 0

        if not is_valid:
            message = f"数据源缺少基盘体量趋势分析所需的字段。当前数据源类型为'{self.data_source.report_type}'，" \
                     f"需要包含季度格式的RFM字段（如24Q4_综合等级、25Q1_细分等级等）。" \
                     f"请在数据处理页面重新生成包含正确字段结构的正向盘数据源。"
        else:
            message = "数据结构验证通过"

        return {
            'valid': is_valid,
            'message': message,
            'missing_fields': missing_fields,
            'available_fields': available_fields,
            'required_fields': required_fields
        }

    def _empty_reports(self) -> Dict[str, Any]:
        """
        返回空报表结构

        Returns:
            空报表数据字典
        """
        return {
            'analysis_type': 'base-volume-trend',
            'data_source_id': self.data_source.id,
            'quarters': [],
            'total_records': 0,
            'reports': {
                'rfm_structure': {'data': [], 'summary': {}},
                'member_structure': {'data': [], 'summary': {}},
                'consultant_attribution': {'data': [], 'summary': {}, 'filter_options': {'subdivision_levels': [], 'member_card_levels': []}}
            }
        }
