# RFM报表处理系统启动脚本
# PowerShell版本

Write-Host "================================================" -ForegroundColor Green
Write-Host "  RFM报表处理系统启动器" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green

# 获取脚本所在目录
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $ScriptDir

Write-Host "当前目录: $PWD" -ForegroundColor Yellow
Write-Host ""

# 详细检查文件是否存在
Write-Host "正在检查必要文件..." -ForegroundColor Cyan

$VenvDir = Join-Path $ScriptDir "venv"
$VenvScriptsDir = Join-Path $ScriptDir "venv\Scripts"
$VenvPython = Join-Path $ScriptDir "venv\Scripts\python.exe"
$StartScript = Join-Path $ScriptDir "start_server.py"

if (-not (Test-Path $VenvDir)) {
    Write-Host "[错误] venv 目录不存在！" -ForegroundColor Red
    Write-Host "当前目录: $PWD" -ForegroundColor Yellow
    Write-Host "目录内容:" -ForegroundColor Yellow
    Get-ChildItem | Format-Table Name, Length, LastWriteTime
    Read-Host "按回车键退出"
    exit 1
}

if (-not (Test-Path $VenvScriptsDir)) {
    Write-Host "[错误] venv\Scripts 目录不存在！" -ForegroundColor Red
    Write-Host "venv 目录内容:" -ForegroundColor Yellow
    Get-ChildItem $VenvDir | Format-Table Name, Length, LastWriteTime
    Read-Host "按回车键退出"
    exit 1
}

if (-not (Test-Path $VenvPython)) {
    Write-Host "[错误] python.exe 不存在！" -ForegroundColor Red
    Write-Host "venv\Scripts 目录内容:" -ForegroundColor Yellow
    Get-ChildItem $VenvScriptsDir | Format-Table Name, Length, LastWriteTime
    Read-Host "按回车键退出"
    exit 1
}

if (-not (Test-Path $StartScript)) {
    Write-Host "[错误] start_server.py 不存在！" -ForegroundColor Red
    Write-Host "当前目录内容:" -ForegroundColor Yellow
    Get-ChildItem | Format-Table Name, Length, LastWriteTime
    Read-Host "按回车键退出"
    exit 1
}

Write-Host "[成功] 所有必要文件都存在" -ForegroundColor Green
Write-Host "虚拟环境: $VenvPython" -ForegroundColor Cyan
Write-Host "启动脚本: $StartScript" -ForegroundColor Cyan
Write-Host ""

Write-Host "正在启动系统..." -ForegroundColor Yellow
Write-Host ""

try {
    # 使用虚拟环境启动系统
    & $VenvPython $StartScript

    if ($LASTEXITCODE -ne 0) {
        Write-Host ""
        Write-Host "[错误] 启动失败，退出代码: $LASTEXITCODE" -ForegroundColor Red
    }
}
catch {
    Write-Host "启动失败: $_" -ForegroundColor Red
    Write-Host "错误详情: $($_.Exception.Message)" -ForegroundColor Red
}
finally {
    Write-Host ""
    Write-Host "系统已停止运行" -ForegroundColor Yellow
    Read-Host "按回车键退出"
}
