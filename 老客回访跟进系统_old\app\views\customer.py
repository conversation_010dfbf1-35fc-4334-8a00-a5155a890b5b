"""
客户管理视图控制器
"""
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from app import db
from app.models.customer import CustomerRegistration, FollowUpRecord, Channel
from app.models.user import User
from app.utils.permissions import require_permission, filter_query_by_permission
from app.utils.validators import validate_card_number, validate_content_length

bp = Blueprint('customer', __name__)

@bp.route('/')
@require_permission('CUSTOMER_VIEW')
def index():
    """客户列表"""
    page = request.args.get('page', 1, type=int)
    per_page = 20

    # 获取筛选参数
    card_number = request.args.get('card_number', '').strip()
    consultant_id = request.args.get('consultant_id', type=int)
    registrar_id = request.args.get('registrar_id', type=int)
    channel_id = request.args.get('channel_id', type=int)
    date_range = request.args.get('date_range')

    # 根据权限过滤查询
    query = CustomerRegistration.query
    query = filter_query_by_permission(query, CustomerRegistration, current_user)

    # 应用筛选条件
    if card_number:
        query = query.filter(CustomerRegistration.card_number.like(f'%{card_number}%'))

    if consultant_id:
        query = query.filter(CustomerRegistration.assigned_consultant_id == consultant_id)

    if registrar_id:
        query = query.filter(CustomerRegistration.registrar_id == registrar_id)

    if channel_id:
        query = query.filter(CustomerRegistration.activation_channel_id == channel_id)

    if date_range:
        from datetime import datetime
        try:
            filter_date = datetime.strptime(date_range, '%Y-%m-%d').date()
            query = query.filter(db.func.date(CustomerRegistration.registration_time) == filter_date)
        except ValueError:
            pass

    customers = query.order_by(CustomerRegistration.registration_time.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    # 获取筛选选项数据
    consultants = get_field_consultants()
    registrars = get_network_consultants()
    channels = Channel.query.filter_by(is_active=True).all()

    return render_template('customer/index.html',
                         customers=customers,
                         consultants=consultants,
                         registrars=registrars,
                         channels=channels)

@bp.route('/register', methods=['GET', 'POST'])
@require_permission('CUSTOMER_REGISTER')
def register():
    """客户登记"""
    if request.method == 'POST':
        card_number = request.form.get('card_number', '').strip()
        assigned_consultant_id = request.form.get('assigned_consultant_id', type=int)
        activation_channel_id = request.form.get('activation_channel_id', type=int)
        consultation_content = request.form.get('consultation_content', '').strip()
        last_visit_date = request.form.get('last_visit_date')
        
        # 验证输入
        is_valid, message = validate_card_number(card_number)
        if not is_valid:
            flash(message, 'error')
            consultants = get_field_consultants()
            channels = Channel.query.filter_by(is_active=True).all()
            return render_template('customer/register.html',
                                 consultants=consultants,
                                 channels=channels)

        is_valid, message = validate_content_length(consultation_content, "咨询内容")
        if not is_valid:
            flash(message, 'error')
            consultants = get_field_consultants()
            channels = Channel.query.filter_by(is_active=True).all()
            return render_template('customer/register.html',
                                 consultants=consultants,
                                 channels=channels)
        
        try:
            from datetime import datetime
            visit_date = datetime.strptime(last_visit_date, '%Y-%m-%d').date()
            
            # 创建客户登记
            registration = CustomerRegistration(
                card_number=card_number,
                assigned_consultant_id=assigned_consultant_id,
                activation_channel_id=activation_channel_id,
                consultation_content=consultation_content,
                last_visit_date=visit_date,
                registrar_id=current_user.id
            )
            
            db.session.add(registration)
            db.session.commit()
            
            flash('客户登记成功', 'success')
            return redirect(url_for('customer.index'))
            
        except Exception as e:
            db.session.rollback()
            flash('客户登记失败', 'error')
    
    consultants = get_field_consultants()
    channels = Channel.query.filter_by(is_active=True).all()
    return render_template('customer/register.html', 
                         consultants=consultants, channels=channels)

@bp.route('/<int:id>/follow-up', methods=['GET', 'POST'])
@require_permission('FOLLOW_UP_ADD')
def add_follow_up(id):
    """添加跟进记录"""
    registration = CustomerRegistration.query.get_or_404(id)
    
    # 检查权限：只能跟进分配给自己的客户
    if registration.assigned_consultant_id != current_user.id and not current_user.is_admin():
        flash('您无权跟进此客户', 'error')
        return redirect(url_for('customer.index'))
    
    if request.method == 'POST':
        follow_up_content = request.form.get('follow_up_content', '').strip()
        
        # 验证输入
        is_valid, message = validate_content_length(follow_up_content, "跟进内容")
        if not is_valid:
            flash(message, 'error')
            return render_template('customer/follow_up.html', registration=registration)
        
        try:
            # 创建跟进记录
            follow_up = FollowUpRecord(
                customer_registration_id=registration.id,
                follow_up_content=follow_up_content,
                consultant_id=current_user.id
            )
            
            db.session.add(follow_up)
            db.session.commit()
            
            flash('跟进记录添加成功', 'success')
            return redirect(url_for('customer.view', id=registration.id))
            
        except Exception as e:
            db.session.rollback()
            flash('跟进记录添加失败', 'error')
    
    return render_template('customer/follow_up.html', registration=registration)

@bp.route('/<int:id>')
@require_permission('CUSTOMER_VIEW')
def view(id):
    """查看客户详情"""
    registration = CustomerRegistration.query.get_or_404(id)
    
    # 检查数据权限
    from app.utils.permissions import check_data_permission
    if not check_data_permission(registration, current_user):
        flash('您无权查看此客户信息', 'error')
        return redirect(url_for('customer.index'))
    
    # 获取跟进记录
    follow_ups = FollowUpRecord.query.filter_by(
        customer_registration_id=registration.id,
        is_active=True
    ).order_by(FollowUpRecord.follow_up_time.desc()).all()
    
    return render_template('customer/view.html', 
                         registration=registration, follow_ups=follow_ups)

def get_field_consultants():
    """获取现场咨询员列表"""
    from app.models.user import Role
    field_role = Role.query.filter_by(role_code='FIELD_CONSULTANT').first()
    if field_role:
        return User.query.filter_by(role_id=field_role.id, is_active=True).all()
    return []

def get_network_consultants():
    """获取网络咨询员列表"""
    from app.models.user import Role
    network_role = Role.query.filter_by(role_code='NETWORK_CONSULTANT').first()
    if network_role:
        return User.query.filter_by(role_id=network_role.id, is_active=True).all()
    return []

@bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
@require_permission('CUSTOMER_EDIT')
def edit(id):
    """编辑客户信息"""
    try:
        registration = CustomerRegistration.query.get_or_404(id)

        # 检查数据权限
        from app.utils.permissions import check_data_permission
        if not check_data_permission(registration, current_user):
            flash('您无权编辑此客户信息', 'error')
            return redirect(url_for('customer.index'))

        if request.method == 'POST':
            # 获取表单数据
            card_number = request.form.get('card_number', '').strip()
            consultation_content = request.form.get('consultation_content', '').strip()
            assigned_consultant_id = request.form.get('assigned_consultant_id', type=int)
            activation_channel_id = request.form.get('activation_channel_id', type=int)
            last_visit_date = request.form.get('last_visit_date')

            # 验证必填字段
            if not card_number:
                flash('客户卡号不能为空', 'error')
                return redirect(url_for('customer.edit', id=id))

            if not assigned_consultant_id:
                flash('必须分配现场顾问', 'error')
                return redirect(url_for('customer.edit', id=id))

            if not activation_channel_id:
                flash('必须选择激活渠道', 'error')
                return redirect(url_for('customer.edit', id=id))

            if not last_visit_date:
                flash('最近来院日期不能为空', 'error')
                return redirect(url_for('customer.edit', id=id))

            # 验证卡号格式
            if not card_number.isdigit() or len(card_number) > 10:
                flash('卡号只能包含数字，且不超过10位', 'error')
                return redirect(url_for('customer.edit', id=id))

            # 检查卡号是否已存在（排除当前客户）
            existing_registration = CustomerRegistration.query.filter(
                CustomerRegistration.card_number == card_number,
                CustomerRegistration.id != id
            ).first()
            if existing_registration:
                flash('该卡号已存在', 'error')
                return redirect(url_for('customer.edit', id=id))

            # 验证咨询内容长度
            if consultation_content and len(consultation_content) > 500:
                flash('咨询内容不能超过500字', 'error')
                return redirect(url_for('customer.edit', id=id))

            # 验证现场顾问是否存在
            from app.models.user import User, Role
            field_role = Role.query.filter_by(role_code='FIELD_CONSULTANT').first()
            if field_role:
                consultant = User.query.filter_by(
                    id=assigned_consultant_id,
                    role_id=field_role.id,
                    is_active=True
                ).first()
                if not consultant:
                    flash('选择的现场顾问不存在或已停用', 'error')
                    return redirect(url_for('customer.edit', id=id))

            # 验证激活渠道是否存在
            channel = Channel.query.filter_by(
                id=activation_channel_id,
                is_active=True
            ).first()
            if not channel:
                flash('选择的激活渠道不存在或已停用', 'error')
                return redirect(url_for('customer.edit', id=id))

            # 验证日期格式
            try:
                from datetime import datetime
                visit_date = datetime.strptime(last_visit_date, '%Y-%m-%d').date()
            except ValueError:
                flash('日期格式不正确', 'error')
                return redirect(url_for('customer.edit', id=id))

            # 更新客户信息
            registration.card_number = card_number
            registration.consultation_content = consultation_content
            registration.assigned_consultant_id = assigned_consultant_id
            registration.activation_channel_id = activation_channel_id
            registration.last_visit_date = visit_date

            try:
                db.session.commit()

                # 记录操作日志
                current_app.logger.info(f"用户 {current_user.username} 编辑了客户 {registration.card_number} (ID: {registration.id})")

                flash('客户信息更新成功', 'success')
                return redirect(url_for('customer.view', id=id))

            except Exception as e:
                db.session.rollback()
                current_app.logger.error(f"更新客户信息失败: {e}")
                flash('更新客户信息失败，请重试', 'error')
                return redirect(url_for('customer.edit', id=id))

        # GET请求，显示编辑表单
        consultants = get_field_consultants()
        channels = Channel.query.filter_by(is_active=True).all()
        return render_template('customer/edit.html',
                             registration=registration,
                             consultants=consultants,
                             channels=channels)

    except Exception as e:
        current_app.logger.error(f"编辑客户信息错误: {e}")
        flash('编辑客户信息时出现错误', 'error')
        return redirect(url_for('customer.index'))
