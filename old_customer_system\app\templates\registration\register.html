{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">客户登记</h1>
    <a href="{{ url_for('registration.customers') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>返回列表
    </a>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.card_number.label(class="form-label") }}
                                {{ form.card_number(class="form-control") }}
                                {% if form.card_number.errors %}
                                    <div class="text-danger">
                                        {% for error in form.card_number.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.onsite_consultant_id.label(class="form-label") }}
                                {{ form.onsite_consultant_id(class="form-select select2-consultant", placeholder="搜索现场咨询师...") }}
                                {% if form.onsite_consultant_id.errors %}
                                    <div class="text-danger">
                                        {% for error in form.onsite_consultant_id.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.channel_id.label(class="form-label") }}
                                {{ form.channel_id(class="form-select select2-channel", placeholder="搜索激活渠道...") }}
                                {% if form.channel_id.errors %}
                                    <div class="text-danger">
                                        {% for error in form.channel_id.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.registration_date.label(class="form-label") }}
                                {{ form.registration_date(class="form-control") }}
                                {% if form.registration_date.errors %}
                                    <div class="text-danger">
                                        {% for error in form.registration_date.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.last_visit_date.label(class="form-label") }}
                                {{ form.last_visit_date(class="form-control") }}
                                {% if form.last_visit_date.errors %}
                                    <div class="text-danger">
                                        {% for error in form.last_visit_date.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.inquiry_content.label(class="form-label") }}
                        {{ form.inquiry_content(class="form-control", rows="4", placeholder="请输入客户咨询的内容...") }}
                        <small class="form-text text-muted">最多500个字符</small>
                        {% if form.inquiry_content.errors %}
                            <div class="text-danger">
                                {% for error in form.inquiry_content.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('registration.customers') }}" class="btn btn-secondary me-md-2">取消</a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>登记说明
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <strong>卡号:</strong> 必须为纯数字，系统会自动验证长度和唯一性
                    </li>
                    <li class="mb-2">
                        <strong>所属现场:</strong> 选择负责跟进的现场咨询师
                    </li>
                    <li class="mb-2">
                        <strong>激活渠道:</strong> 选择客户的来源渠道
                    </li>
                    <li class="mb-2">
                        <strong>咨询内容:</strong> 记录客户的咨询需求和问题
                    </li>
                    <li class="mb-2">
                        <strong>来院时间:</strong> 客户最近一次到院的日期（可选）
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>登记信息
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <strong>所属网资:</strong><br>
                        {{ current_user.real_name }} ({{ current_user.username }})
                    </li>
                    <li class="mb-2">
                        <strong>登记时间:</strong><br>
                        系统自动记录
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
$(document).ready(function() {
    // 初始化现场咨询师选择器
    $('.select2-consultant').select2({
        theme: 'bootstrap-5',
        placeholder: '搜索现场咨询师...',
        allowClear: true,
        width: '100%',
        language: {
            noResults: function() {
                return "未找到匹配的现场咨询师";
            },
            searching: function() {
                return "搜索中...";
            },
            inputTooShort: function() {
                return "请输入更多字符进行搜索";
            }
        }
    });
    
    // 初始化渠道选择器
    $('.select2-channel').select2({
        theme: 'bootstrap-5',
        placeholder: '搜索激活渠道...',
        allowClear: true,
        width: '100%',
        language: {
            noResults: function() {
                return "未找到匹配的渠道";
            },
            searching: function() {
                return "搜索中...";
            },
            inputTooShort: function() {
                return "请输入更多字符进行搜索";
            }
        }
    });
    
    // 自定义样式调整
    $('.select2-container').css('z-index', '9999');
});
</script>

<style>
/* Select2 自定义样式 */
.select2-container--bootstrap-5 .select2-selection {
    min-height: 38px;
}

.select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
    line-height: 36px;
    padding-left: 12px;
}

.select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
    height: 36px;
    right: 12px;
}

.select2-dropdown {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
}

.select2-results__option--highlighted {
    background-color: #0d6efd;
    color: white;
}

/* 搜索框样式 */
.select2-search--dropdown .select2-search__field {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 8px 12px;
}
</style>
{% endblock %}