@echo off
chcp 65001 >nul
echo ========================================
echo    RFM报表处理系统启动脚本
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

:: 检查虚拟环境
if not exist "venv" (
    echo 创建虚拟环境...
    python -m venv venv
    if errorlevel 1 (
        echo 错误: 创建虚拟环境失败
        pause
        exit /b 1
    )
)

:: 激活虚拟环境
echo 激活虚拟环境...
call venv\Scripts\activate.bat

:: 安装依赖
echo 检查并安装依赖...
pip install -r requirements.txt

:: 检查环境配置文件
if not exist ".env" (
    echo 创建环境配置文件...
    copy .env.example .env
    echo.
    echo 请编辑 .env 文件配置数据库连接信息
    echo 然后重新运行此脚本
    pause
    exit /b 0
)

:: 检查是否需要初始化数据库
python -c "from app import create_app; from app.models import User; app = create_app(); app.app_context().push(); exit(0 if User.query.count() > 0 else 1)" 2>nul
if errorlevel 1 (
    echo.
    echo 检测到首次运行，开始初始化数据库...
    python init_db.py
    if errorlevel 1 (
        echo 数据库初始化失败
        pause
        exit /b 1
    )
)

:: 启动系统
echo.
echo 启动RFM报表处理系统...
echo 访问地址: http://localhost:5000
echo 按 Ctrl+C 停止服务
echo.
python run.py

pause
