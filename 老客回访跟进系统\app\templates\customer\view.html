{% extends "base.html" %}

{% block title %}客户详情 - 老客回访与跟进系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-user"></i> 客户详情</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('customer.index') }}">客户管理</a></li>
                <li class="breadcrumb-item active">客户详情</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> 基本信息</h5>
            </div>
            <div class="card-body">
                <table class="table">
                    <tr>
                        <th width="150">客户卡号</th>
                        <td><strong class="text-primary">{{ registration.card_number }}</strong></td>
                    </tr>
                    <tr>
                        <th>登记人员</th>
                        <td>{{ registration.registrar.real_name }} ({{ registration.registrar.department.department_name }})</td>
                    </tr>
                    <tr>
                        <th>现场顾问</th>
                        <td>{{ registration.assigned_consultant.real_name }} ({{ registration.assigned_consultant.department.department_name }})</td>
                    </tr>
                    <tr>
                        <th>激活渠道</th>
                        <td>
                            <span class="badge bg-info">{{ registration.activation_channel.channel_category }}</span>
                            {{ registration.activation_channel.channel_name }}
                        </td>
                    </tr>
                    <tr>
                        <th>咨询内容</th>
                        <td>{{ registration.consultation_content }}</td>
                    </tr>
                    <tr>
                        <th>最近来院</th>
                        <td>{{ registration.last_visit_date.strftime('%Y-%m-%d') }}</td>
                    </tr>
                    <tr>
                        <th>登记时间</th>
                        <td>{{ registration.registration_time.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                    </tr>
                </table>
                
                <div class="mt-3">
                    {% if current_user.has_permission('FOLLOW_UP_ADD') and registration.assigned_consultant_id == current_user.id %}
                    <a href="{{ url_for('customer.add_follow_up', id=registration.id) }}" class="btn btn-success">
                        <i class="fas fa-plus"></i> 添加跟进
                    </a>
                    {% endif %}
                    {% if current_user.has_permission('CUSTOMER_EDIT') %}
                    <a href="{{ url_for('customer.edit', id=registration.id) }}" class="btn btn-warning">
                        <i class="fas fa-edit"></i> 编辑信息
                    </a>
                    {% endif %}
                    <a href="{{ url_for('customer.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> 返回列表
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-history"></i> 跟进记录</h6>
            </div>
            <div class="card-body">
                {% if follow_ups %}
                    {% for follow_up in follow_ups %}
                    <div class="border-bottom pb-2 mb-2">
                        <div class="d-flex justify-content-between">
                            <strong>{{ follow_up.follow_up_time.strftime('%Y-%m-%d %H:%M') }}</strong>
                            <span class="badge bg-primary">{{ follow_up.follow_up_type }}</span>
                        </div>
                        <div class="text-muted small">
                            跟进人：{{ follow_up.follow_up_user.real_name }}
                        </div>
                        <div class="mt-1">
                            {{ follow_up.follow_up_content }}
                        </div>
                        {% if follow_up.next_follow_up_time %}
                        <div class="text-info small mt-1">
                            <i class="fas fa-clock"></i> 下次跟进：{{ follow_up.next_follow_up_time.strftime('%Y-%m-%d %H:%M') }}
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center text-muted">
                        <i class="fas fa-inbox fa-2x mb-2"></i><br>
                        暂无跟进记录
                        {% if current_user.has_permission('FOLLOW_UP_ADD') and registration.assigned_consultant_id == current_user.id %}
                        <br><a href="{{ url_for('customer.add_follow_up', id=registration.id) }}" class="btn btn-success btn-sm mt-2">
                            <i class="fas fa-plus"></i> 添加跟进
                        </a>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-chart-bar"></i> 统计信息</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary">{{ registration.get_follow_up_count() }}</h4>
                            <small class="text-muted">跟进次数</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">
                            {% set days_since_registration = (now() - registration.registration_time).days %}
                            {{ days_since_registration }}
                        </h4>
                        <small class="text-muted">登记天数</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
