"""
认证相关视图控制器
"""
from flask import Blueprint, render_template, request, redirect, url_for, flash, session
from flask_login import login_user, logout_user, current_user, login_required
from werkzeug.security import check_password_hash, generate_password_hash
from datetime import datetime
from app import db
from app.models.user import User
from app.models.system import AuditLog
from app.utils.validators import validate_password

bp = Blueprint('auth', __name__)

@bp.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')
        remember_me = bool(request.form.get('remember_me'))
        
        # 验证输入
        if not username:
            flash('请输入用户名', 'error')
            return render_template('auth/login.html')
        
        if not password:
            flash('请输入密码', 'error')
            return render_template('auth/login.html')
        
        # 查找用户
        user = User.query.filter_by(username=username).first()
        
        if user and user.is_active and user.check_password(password):
            # 登录成功
            login_user(user, remember=remember_me)
            
            # 更新最后登录时间
            user.last_login_time = datetime.utcnow()
            db.session.commit()
            
            # 记录登录日志
            AuditLog.log_action(
                user_id=user.id,
                action='LOGIN',
                resource_type='USER',
                resource_id=str(user.id),
                details=f"用户 {username} 登录成功"
            )
            
            # 重定向到原来要访问的页面或仪表板
            next_page = request.args.get('next')
            if next_page:
                return redirect(next_page)
            else:
                return redirect(url_for('main.dashboard'))
        else:
            # 登录失败
            flash('用户名或密码错误', 'error')
            
            # 记录失败的登录尝试
            if user:
                AuditLog.log_action(
                    user_id=user.id,
                    action='LOGIN_FAILED',
                    resource_type='USER',
                    resource_id=str(user.id),
                    details=f"用户 {username} 登录失败：密码错误"
                )
            else:
                AuditLog.log_action(
                    user_id=None,
                    action='LOGIN_FAILED',
                    resource_type='USER',
                    details=f"登录失败：用户 {username} 不存在"
                )
    
    return render_template('auth/login.html')

@bp.route('/logout')
@login_required
def logout():
    """用户登出"""
    # 记录登出日志
    AuditLog.log_action(
        user_id=current_user.id,
        action='LOGOUT',
        resource_type='USER',
        resource_id=str(current_user.id),
        details=f"用户 {current_user.username} 登出"
    )
    
    logout_user()
    flash('您已成功登出', 'info')
    return redirect(url_for('auth.login'))

@bp.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    """修改密码"""
    if request.method == 'POST':
        current_password = request.form.get('current_password', '')
        new_password = request.form.get('new_password', '')
        confirm_password = request.form.get('confirm_password', '')
        
        # 验证当前密码
        if not current_user.check_password(current_password):
            flash('当前密码不正确', 'error')
            return render_template('auth/change_password.html')
        
        # 验证新密码
        is_valid, message = validate_password(new_password)
        if not is_valid:
            flash(message, 'error')
            return render_template('auth/change_password.html')
        
        # 验证确认密码
        if new_password != confirm_password:
            flash('两次输入的新密码不一致', 'error')
            return render_template('auth/change_password.html')
        
        # 检查新密码是否与当前密码相同
        if current_user.check_password(new_password):
            flash('新密码不能与当前密码相同', 'error')
            return render_template('auth/change_password.html')
        
        try:
            # 更新密码
            current_user.set_password(new_password)
            db.session.commit()
            
            # 记录密码修改日志
            AuditLog.log_action(
                user_id=current_user.id,
                action='CHANGE_PASSWORD',
                resource_type='USER',
                resource_id=str(current_user.id),
                details=f"用户 {current_user.username} 修改密码"
            )
            
            flash('密码修改成功', 'success')
            return redirect(url_for('main.profile'))
            
        except Exception as e:
            db.session.rollback()
            flash('密码修改失败，请重试', 'error')
            return render_template('auth/change_password.html')
    
    return render_template('auth/change_password.html')

@bp.route('/session-info')
@login_required
def session_info():
    """会话信息（用于AJAX检查）"""
    return {
        'authenticated': True,
        'user_id': current_user.id,
        'username': current_user.username,
        'role': current_user.role.role_name,
        'last_activity': datetime.utcnow().isoformat()
    }

@bp.before_app_request
def load_logged_in_user():
    """在每个请求前加载登录用户信息"""
    if current_user.is_authenticated:
        # 检查用户是否仍然有效
        if not current_user.is_active:
            logout_user()
            flash('您的账号已被停用，请联系管理员', 'warning')
            return redirect(url_for('auth.login'))

@bp.context_processor
def inject_auth_vars():
    """注入认证相关的模板变量"""
    return {
        'current_user': current_user
    }
