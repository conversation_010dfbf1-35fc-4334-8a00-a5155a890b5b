{% extends "base.html" %}

{% block content %}
<div class="row mt-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2><i class="fas fa-tasks me-2"></i>{{ task.task_name }}</h2>
            <div>
                {% if task.status == 'completed' and task.output_file_path %}
                    <a href="{{ url_for('data_processing.download_result', task_id=task.id) }}" 
                       class="btn btn-success">
                        <i class="fas fa-download me-2"></i>下载结果
                    </a>
                {% endif %}
                <a href="{{ url_for('data_processing.task_history') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>返回列表
                </a>
            </div>
        </div>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">首页</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('data_processing.task_history') }}">任务历史</a></li>
                <li class="breadcrumb-item active">{{ task.task_name }}</li>
            </ol>
        </nav>
    </div>
</div>

<!-- 状态提示 -->
{% if task.status in ['pending', 'running'] %}
<div class="row mt-3">
    <div class="col-12">
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            <i class="fas fa-info-circle me-2"></i>
            <strong>自动更新：</strong>页面正在自动监控任务状态，无需手动刷新。
            {% if task.status == 'pending' %}
                任务正在准备中，请稍候...
            {% else %}
                任务正在执行中，进度会实时更新。
            {% endif %}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    </div>
</div>
{% endif %}

<!-- 任务状态卡片 -->
<div class="row mt-3">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle me-2"></i>任务信息</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>任务名称:</strong></td>
                                <td>{{ task.task_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>处理类型:</strong></td>
                                <td>
                                    <span class="badge bg-{{ 'primary' if task.task_type == 'forward' else 'info' }}">
                                        {{ '正向盘' if task.task_type == 'forward' else '结果盘' }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>当前状态:</strong></td>
                                <td>
                                    <span id="taskStatus" class="badge bg-{{ 'success' if task.status == 'completed' else 'danger' if task.status == 'failed' else 'primary' if task.status == 'running' else 'secondary' }}">
                                        {% if task.status == 'completed' %}已完成
                                        {% elif task.status == 'failed' %}失败
                                        {% elif task.status == 'running' %}运行中
                                        {% else %}等待中{% endif %}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>创建时间:</strong></td>
                                <td>{{ task.created_at | local_datetime }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>开始时间:</strong></td>
                                <td>
                                    {% if task.started_at %}
                                        {{ task.started_at | local_datetime }}
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>完成时间:</strong></td>
                                <td>
                                    {% if task.completed_at %}
                                        {{ task.completed_at | local_datetime }}
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>执行时长:</strong></td>
                                <td>
                                    <span id="taskDuration">
                                        {{ task.duration_formatted }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>创建者:</strong></td>
                                <td>{{ task.creator.username }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <!-- 进度条 -->
                {% if task.status in ['running', 'pending'] %}
                <div class="mt-3">
                    <label class="form-label">处理进度:</label>
                    <div class="progress mb-2">
                        <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated"
                             role="progressbar" style="width: {{ task.progress }}%">
                            <span id="progressText">{{ task.progress }}%</span>
                        </div>
                    </div>
                    <div id="currentStep" class="text-muted small">
                        {% if task.status == 'pending' %}
                            等待开始处理...
                        {% else %}
                            正在处理中...
                        {% endif %}
                    </div>
                </div>
                {% elif task.status == 'completed' %}
                <div class="mt-3">
                    <label class="form-label">处理进度:</label>
                    <div class="progress mb-2">
                        <div class="progress-bar bg-success" role="progressbar" style="width: 100%">
                            100% - 已完成
                        </div>
                    </div>
                </div>
                {% elif task.status == 'failed' %}
                <div class="mt-3">
                    <label class="form-label">处理进度:</label>
                    <div class="progress mb-2">
                        <div class="progress-bar bg-danger" role="progressbar" style="width: {{ task.progress }}%">
                            {{ task.progress }}% - 处理失败
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <!-- 错误信息 -->
                {% if task.status == 'failed' and task.error_message %}
                <div class="mt-3">
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>错误信息:</h6>
                        <pre class="mb-0">{{ task.error_message }}</pre>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- 任务参数 -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-cog me-2"></i>任务参数</h6>
            </div>
            <div class="card-body">
                {% set params = task.get_parameters() %}
                
                {% if params.get('description') %}
                <div class="mb-3">
                    <strong>描述:</strong>
                    <p class="text-muted">{{ params.get('description') }}</p>
                </div>
                {% endif %}
                
                <div class="mb-3">
                    <strong>季度表:</strong>
                    <ul class="list-unstyled">
                        {% for table in params.get('quarter_tables', []) %}
                            <li><span class="badge bg-light text-dark">{{ table }}</span></li>
                        {% endfor %}
                    </ul>
                </div>
                
                <div class="mb-3">
                    <strong>分析季度:</strong>
                    <div>
                        {% for quarter in params.get('quarters', []) %}
                            <span class="badge bg-primary me-1">{{ quarter }}</span>
                        {% endfor %}
                    </div>
                </div>
                
                <div class="mb-3">
                    <strong>科室:</strong>
                    <div>
                        {% for dept in params.get('departments', []) %}
                            <span class="badge bg-info me-1">{{ dept }}</span>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 执行日志 -->
{% set progress_logs = task.get_progress_logs() %}
{% if progress_logs or task.status in ['running', 'pending'] %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-list-alt me-2"></i>执行日志</h5>
            </div>
            <div class="card-body">
                <div id="progressLogs">
                    {% if progress_logs %}
                        {% for log in progress_logs %}
                        <div class="d-flex align-items-start mb-3 progress-log-item">
                            <div class="flex-shrink-0">
                                {% if log.status == 'completed' %}
                                    <i class="fas fa-check-circle text-success"></i>
                                {% else %}
                                    <i class="fas fa-clock text-primary"></i>
                                {% endif %}
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">{{ log.step_name }}</h6>
                                        <p class="mb-1 text-muted">{{ log.message }}</p>
                                    </div>
                                    <div class="text-end">
                                        <small class="text-muted">{{ log.progress }}%</small>
                                        <br>
                                        <small class="text-muted">
                                            {% set log_time = log.timestamp | datetime_from_iso %}
                                            {{ log_time | local_datetime('%H:%M:%S') }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center text-muted py-3">
                            <i class="fas fa-hourglass-start fa-2x mb-2"></i>
                            <p>等待任务开始...</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- 结果摘要 -->
{% if task.status == 'completed' %}
    {% set summary = task.get_result_summary() %}
    {% if summary %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar me-2"></i>处理结果摘要</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-primary">{{ summary.get('total_members', 0) }}</h4>
                                <small class="text-muted">处理会员数</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-info">{{ summary.get('total_columns', 0) }}</h4>
                                <small class="text-muted">生成列数</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-success">{{ summary.get('data_shape', [0, 0])[0] }}</h4>
                                <small class="text-muted">数据行数</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-warning">{{ summary.get('data_shape', [0, 0])[1] }}</h4>
                                <small class="text-muted">数据列数</small>
                            </div>
                        </div>
                    </div>
                    
                    {% if summary.get('performance_stats') %}
                    <hr>
                    <h6>业绩统计:</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>业绩类型</th>
                                    <th>总金额</th>
                                    <th>平均金额</th>
                                    <th>有消费会员数</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for col, stats in summary.get('performance_stats').items() %}
                                <tr>
                                    <td>{{ col }}</td>
                                    <td>¥{{ "{:,.2f}".format(stats.get('total', 0)) }}</td>
                                    <td>¥{{ "{:,.2f}".format(stats.get('mean', 0)) }}</td>
                                    <td>{{ stats.get('non_zero_count', 0) }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% endif %}

                    {% if summary.get('mapping_info') %}
                    <hr>
                    <h6><i class="fas fa-exchange-alt me-2"></i>分类映射信息:</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <h6 class="card-title">一级分类映射</h6>
                                    {% if summary.mapping_info.use_level1_mapping %}
                                        <span class="badge bg-success">已启用</span>
                                        <p class="mt-2 mb-0">{{ summary.mapping_info.level1_mappings_count }} 个映射</p>
                                    {% else %}
                                        <span class="badge bg-secondary">未启用</span>
                                        <p class="mt-2 mb-0">使用原始分类</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <h6 class="card-title">二级分类映射</h6>
                                    {% if summary.mapping_info.use_level2_mapping %}
                                        <span class="badge bg-success">已启用</span>
                                        <p class="mt-2 mb-0">{{ summary.mapping_info.level2_mappings_count }} 个映射</p>
                                    {% else %}
                                        <span class="badge bg-secondary">未启用</span>
                                        <p class="mt-2 mb-0">使用原始分类</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-info">
                                <div class="card-body text-center">
                                    <h6 class="card-title">三级分类映射</h6>
                                    {% if summary.mapping_info.use_level3_mapping %}
                                        <span class="badge bg-success">已启用</span>
                                        <p class="mt-2 mb-0">{{ summary.mapping_info.level3_mappings_count }} 个映射</p>
                                    {% else %}
                                        <span class="badge bg-secondary">未启用</span>
                                        <p class="mt-2 mb-0">使用原始分类</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 现场映射信息 -->
                    {% if summary.mapping_info.use_field_mapping or summary.mapping_info.use_field_group %}
                    <div class="row mt-3">
                        {% if summary.mapping_info.use_field_mapping %}
                        <div class="col-md-6">
                            <div class="card border-warning">
                                <div class="card-body text-center">
                                    <h6 class="card-title">现场映射</h6>
                                    <span class="badge bg-success">已启用</span>
                                    <p class="mt-2 mb-0">{{ summary.mapping_info.field_mappings_count }} 个映射</p>
                                    <small class="text-muted">添加最新现场列</small>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        {% if summary.mapping_info.use_field_group %}
                        <div class="col-md-6">
                            <div class="card border-danger">
                                <div class="card-body text-center">
                                    <h6 class="card-title">现场小组映射</h6>
                                    <span class="badge bg-success">已启用</span>
                                    <p class="mt-2 mb-0">{{ summary.mapping_info.field_group_mappings_count }} 个映射</p>
                                    <small class="text-muted">添加现场小组列</small>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}

                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- TOP排名信息 -->
    {% if summary.get('top_rankings') %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-trophy me-2"></i>TOP排名详情</h5>
                </div>
                <div class="card-body">
                    <!-- 时间维度信息 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>排名时间维度</h6>
                                </div>
                                <div class="card-body">
                                    <p class="mb-0">{{ summary.top_rankings.ranking_period.description }}</p>
                                    <small class="text-muted">用于确定哪些品类/品项进入TOP排名</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0"><i class="fas fa-calculator me-2"></i>列值时间维度</h6>
                                </div>
                                <div class="card-body">
                                    <p class="mb-0">{{ summary.top_rankings.value_period.description }}</p>
                                    <small class="text-muted">用于计算每个会员的消费金额</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- TOP品类排名 -->
                    {% if summary.top_rankings.categories %}
                    <div class="mb-4">
                        <h6><i class="fas fa-chart-bar me-2"></i>品类排名（排名时间维度：{{ summary.top_rankings.ranking_period.description }}）</h6>
                        {% for dept_key, categories in summary.top_rankings.categories.items() %}
                        <div class="mb-3">
                            <h6 class="text-primary">{{ dept_key }}：</h6>
                            <div class="table-responsive">
                                <table class="table table-sm table-striped">
                                    <thead>
                                        <tr>
                                            <th>排名</th>
                                            <th>列名</th>
                                            <th>品类名称</th>
                                            <th>业绩金额</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in categories %}
                                        <tr>
                                            <td><span class="badge bg-primary">TOP{{ item.rank }}</span></td>
                                            <td><code>{{ item.column_name }}</code></td>
                                            <td>{{ item.category }}</td>
                                            <td class="text-end">{{ "{:,.2f}".format(item.revenue) }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <!-- TOP品项排名 -->
                    {% if summary.top_rankings.products %}
                    <div class="mb-4">
                        <h6><i class="fas fa-list me-2"></i>品项排名（排名时间维度：{{ summary.top_rankings.ranking_period.description }}）</h6>
                        {% for dept_key, dept_items in summary.top_rankings.products.items() %}
                        <div class="mb-3">
                            <h6 class="text-success">{{ dept_key }}：</h6>
                            <div class="table-responsive">
                                <table class="table table-sm table-striped">
                                    <thead>
                                        <tr>
                                            <th>排名</th>
                                            <th>列名</th>
                                            <th>品项名称</th>
                                            <th>业绩金额</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in dept_items[:5] %}
                                        <tr>
                                            <td><span class="badge bg-success">TOP{{ item.rank }}</span></td>
                                            <td><code>{{ item.column_name }}</code></td>
                                            <td>{{ item.item }}</td>
                                            <td class="text-end">{{ "{:,.2f}".format(item.revenue) }}</td>
                                        </tr>
                                        {% endfor %}
                                        {% if dept_items|length > 5 %}
                                        <tr>
                                            <td colspan="4" class="text-center text-muted">
                                                <small>... 还有 {{ dept_items|length - 5 }} 个品项</small>
                                            </td>
                                        </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    {% endif %}
{% endif %}
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // 如果任务正在运行或等待中，定期更新状态
    {% if task.status in ['running', 'pending'] %}
    function updateTaskStatus() {
        $.get('{{ url_for("data_processing.task_status", task_id=task.id) }}')
            .done(function(data) {
                // 更新状态标签
                var statusBadge = $('#taskStatus');
                var statusClass = 'bg-secondary';
                var statusText = '等待中';
                
                if (data.status === 'completed') {
                    statusClass = 'bg-success';
                    statusText = '已完成';
                } else if (data.status === 'failed') {
                    statusClass = 'bg-danger';
                    statusText = '失败';
                } else if (data.status === 'running') {
                    statusClass = 'bg-primary';
                    statusText = '运行中';
                }
                
                statusBadge.removeClass().addClass('badge ' + statusClass).text(statusText);
                
                // 更新进度条
                if (data.status === 'running') {
                    $('#progressBar').css('width', data.progress + '%');
                    $('#progressText').text(data.progress + '%');

                    // 更新当前步骤
                    if (data.result_summary && data.result_summary.progress_logs) {
                        var logs = data.result_summary.progress_logs;
                        if (logs.length > 0) {
                            var lastLog = logs[logs.length - 1];
                            $('#currentStep').text(lastLog.message);

                            // 更新进度日志
                            updateProgressLogs(logs);
                        }
                    }
                } else {
                    // 任务完成或失败，停止定时器并刷新页面显示最新信息
                    clearInterval(durationInterval);
                    clearInterval(statusInterval);
                    location.reload();
                }
                
                // 更新执行时长
                if (data.duration) {
                    $('#taskDuration').text(data.duration);
                }
            })
            .fail(function() {
                console.log('获取任务状态失败');
            });
    }
    
    // 更新进度日志的函数
    function updateProgressLogs(logs) {
        var logsContainer = $('#progressLogs');
        var html = '';

        if (logs && logs.length > 0) {
            logs.forEach(function(log) {
                var icon = log.status === 'completed' ?
                    '<i class="fas fa-check-circle text-success"></i>' :
                    '<i class="fas fa-clock text-primary"></i>';

                var logTime = new Date(log.timestamp);
                var timeStr = logTime.toLocaleTimeString();

                html += `
                    <div class="d-flex align-items-start mb-3 progress-log-item">
                        <div class="flex-shrink-0">
                            ${icon}
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">${log.step_name}</h6>
                                    <p class="mb-1 text-muted">${log.message}</p>
                                </div>
                                <div class="text-end">
                                    <small class="text-muted">${log.progress}%</small>
                                    <br>
                                    <small class="text-muted">${timeStr}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
        } else {
            html = `
                <div class="text-center text-muted py-3">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <h6>正在准备数据处理任务...</h6>
                    <p class="mb-0">系统正在初始化，请稍候</p>
                    <small class="text-muted">页面将自动更新显示处理进度</small>
                </div>
            `;
        }

        logsContainer.html(html);
    }

    // 格式化时长显示
    function formatDuration(seconds) {
        var hours = Math.floor(seconds / 3600);
        var minutes = Math.floor((seconds % 3600) / 60);
        var secs = seconds % 60;

        if (hours > 0) {
            return hours + '小时' + minutes + '分钟' + secs + '秒';
        } else if (minutes > 0) {
            return minutes + '分钟' + secs + '秒';
        } else {
            return secs + '秒';
        }
    }

    // 实时更新执行时长
    function updateDuration() {
        {% if task.status in ['running', 'pending'] and task.started_at %}
        var startTime = new Date('{{ task.started_at.isoformat() }}Z');
        var now = new Date();
        var diffSeconds = Math.floor((now - startTime) / 1000);
        $('#taskDuration').text(formatDuration(diffSeconds));
        {% endif %}
    }

    // 每秒更新时长，每2秒更新状态（更及时的状态更新）
    var durationInterval = setInterval(updateDuration, 1000);
    var statusInterval = setInterval(updateTaskStatus, 2000);

    // 页面加载后立即检查一次状态
    updateTaskStatus();

    // 页面卸载时清除定时器
    $(window).on('beforeunload', function() {
        clearInterval(durationInterval);
        clearInterval(statusInterval);
    });
    {% endif %}
});
</script>
{% endblock %}
