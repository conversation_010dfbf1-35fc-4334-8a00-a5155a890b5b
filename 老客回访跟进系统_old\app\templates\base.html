<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}老客回访与跟进系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .navbar-brand {
            font-weight: bold;
        }
        .main-content {
            margin-top: 20px;
        }
        .footer {
            margin-top: 50px;
            padding: 20px 0;
            background-color: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }
        .alert {
            margin-bottom: 20px;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('main.index') }}">
                <i class="fas fa-users"></i> 老客回访与跟进系统
            </a>
            
            {% if current_user.is_authenticated %}
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.dashboard') }}">
                            <i class="fas fa-tachometer-alt"></i> 仪表板
                        </a>
                    </li>
                    
                    {% if current_user.has_permission('CUSTOMER_VIEW') %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('customer.index') }}">
                            <i class="fas fa-users"></i> 客户管理
                        </a>
                    </li>
                    {% endif %}

                    {% if current_user.has_permission('CHANNEL_VIEW') %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('channel.index') }}">
                            <i class="fas fa-tags"></i> 渠道管理
                        </a>
                    </li>
                    {% endif %}
                    
                    {% if current_user.has_permission('REPORT_VIEW') %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('report.index') }}">
                            <i class="fas fa-chart-bar"></i> 统计报表
                        </a>
                    </li>
                    {% endif %}
                    
                    {% if current_user.is_admin() %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin.index') }}">
                            <i class="fas fa-cog"></i> 系统管理
                        </a>
                    </li>
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> {{ current_user.real_name }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('main.profile') }}">
                                <i class="fas fa-user-edit"></i> 个人资料
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.change_password') }}">
                                <i class="fas fa-key"></i> 修改密码
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt"></i> 退出登录
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
            {% endif %}
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container main-content">
        <!-- 消息提示 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- 页面内容 -->
        {% block content %}{% endblock %}
    </div>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="text-muted">&copy; 2024 老客回访与跟进系统. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="text-muted">
                        <small>当前用户: 
                            {% if current_user.is_authenticated %}
                                {{ current_user.real_name }} ({{ current_user.role.role_name }})
                            {% else %}
                                未登录
                            {% endif %}
                        </small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>
