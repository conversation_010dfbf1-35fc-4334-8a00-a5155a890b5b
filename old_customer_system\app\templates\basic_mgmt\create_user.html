{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">创建用户</h1>
    <a href="{{ url_for('basic_mgmt.users') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>返回列表
    </a>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.username.label(class="form-label") }}
                                {{ form.username(class="form-control") }}
                                {% if form.username.errors %}
                                    <div class="text-danger">
                                        {% for error in form.username.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.real_name.label(class="form-label") }}
                                {{ form.real_name(class="form-control") }}
                                {% if form.real_name.errors %}
                                    <div class="text-danger">
                                        {% for error in form.real_name.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.role.label(class="form-label") }}
                                {{ form.role(class="form-select") }}
                                {% if form.role.errors %}
                                    <div class="text-danger">
                                        {% for error in form.role.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.department_id.label(class="form-label") }}
                                {{ form.department_id(class="form-select") }}
                                {% if form.department_id.errors %}
                                    <div class="text-danger">
                                        {% for error in form.department_id.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.simple_code.label(class="form-label") }}
                                {{ form.simple_code(class="form-control", placeholder="留空自动生成") }}
                                <small class="form-text text-muted">用于快速搜索的简码，留空将根据姓名自动生成</small>
                                {% if form.simple_code.errors %}
                                    <div class="text-danger">
                                        {% for error in form.simple_code.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.password.label(class="form-label") }}
                                {{ form.password(class="form-control") }}
                                {% if form.password.errors %}
                                    <div class="text-danger">
                                        {% for error in form.password.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_active(class="form-check-input") }}
                            {{ form.is_active.label(class="form-check-label") }}
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('basic_mgmt.users') }}" class="btn btn-secondary me-md-2">取消</a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>角色说明
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <span class="badge bg-danger me-2">管理员</span>
                        拥有系统所有权限
                    </li>
                    <li class="mb-2">
                        <span class="badge bg-warning me-2">经营院长</span>
                        查看所有数据和报表
                    </li>
                    <li class="mb-2">
                        <span class="badge bg-info me-2">部门主管</span>
                        管理本部门数据
                    </li>
                    <li class="mb-2">
                        <span class="badge bg-primary me-2">网络咨询</span>
                        登记客户信息
                    </li>
                    <li class="mb-2">
                        <span class="badge bg-success me-2">现场咨询</span>
                        跟进客户信息
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}