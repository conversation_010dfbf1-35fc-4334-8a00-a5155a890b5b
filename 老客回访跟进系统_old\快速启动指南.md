# 老客回访与跟进系统 - 快速启动指南

## 🚀 5分钟快速启动

### 第一步：环境准备

#### 1.1 安装Python环境
确保您的系统已安装Python 3.8或更高版本：
```bash
python --version
```

#### 1.2 安装MySQL数据库
- 下载并安装MySQL 8.0+
- 创建数据库用户（建议不使用root用户）
- 记录数据库连接信息

### 第二步：项目配置

#### 2.1 安装项目依赖

**方法一：自动安装（推荐）**
```bash
# 进入项目目录
cd 老客回访跟进系统

# Windows用户：
install.bat

# 或者使用Python脚本：
python install.py
```

**方法二：手动安装**
```bash
# 进入项目目录
cd 老客回访跟进系统

# 创建虚拟环境（推荐）
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# 升级pip
python -m pip install --upgrade pip

# 安装核心依赖包
pip install -r requirements.txt

# 如果安装失败，尝试使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 可选：安装额外功能包
pip install -r requirements-optional.txt
```

#### 2.2 配置数据库连接
编辑 `config.py` 文件，修改数据库连接信息：
```python
# 数据库配置
MYSQL_HOST = 'localhost'        # 数据库主机
MYSQL_PORT = 3306              # 数据库端口
MYSQL_USER = 'your_username'   # 数据库用户名
MYSQL_PASSWORD = 'your_password' # 数据库密码
MYSQL_DATABASE = 'Old_Customer_System' # 数据库名
```

或者设置环境变量：
```bash
# Windows
set MYSQL_HOST=localhost
set MYSQL_USER=your_username
set MYSQL_PASSWORD=your_password

# Linux/Mac
export MYSQL_HOST=localhost
export MYSQL_USER=your_username
export MYSQL_PASSWORD=your_password
```

### 第三步：初始化数据库

#### 3.1 运行初始化脚本
```bash
python init_db.py
```

初始化脚本会自动：
- 创建数据库 `Old_Customer_System`
- 创建所有数据表
- 初始化角色和权限
- 创建系统配置
- 创建默认管理员账号
- 添加示例数据

#### 3.2 确认初始化成功
看到以下信息表示初始化成功：
```
=== 老客回访与跟进系统数据库初始化 ===
创建数据库表...
初始化角色和权限...
分配权限给角色...
初始化系统配置...
创建默认管理员账号: admin / admin123

数据库初始化完成！
默认管理员账号: admin
默认密码: admin123
请在首次登录后立即修改密码！
```

### 第四步：启动系统

#### 4.1 启动开发服务器
```bash
# 简化启动（推荐）
python run.py

# 或完整启动
python app.py
```

#### 4.2 访问系统
- 打开浏览器访问：http://localhost:5000
- 使用默认管理员账号登录：
  - 用户名：`admin`
  - 密码：`admin123`

### 第五步：首次登录配置

#### 5.1 修改管理员密码
1. 登录系统后，点击右上角用户名
2. 选择"修改密码"
3. 输入当前密码和新密码
4. 保存修改

#### 5.2 创建其他用户账号
1. 进入"系统管理" -> "用户管理"
2. 点击"新增用户"
3. 填写用户信息并分配角色
4. 保存用户

## 🔧 常见问题解决

### 问题1：数据库连接失败
**错误信息**：`数据库连接失败: (2003, "Can't connect to MySQL server")`

**解决方案**：
1. 检查MySQL服务是否启动
2. 确认数据库连接信息是否正确
3. 检查防火墙设置
4. 确认MySQL用户权限

### 问题2：依赖包安装失败
**错误信息**：`pip install` 报错或找不到某些包版本

**解决方案**：
1. 使用自动安装脚本：`python install.py` 或 `install.bat`
2. 升级pip：`python -m pip install --upgrade pip`
3. 使用国内镜像：`pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/`
4. 检查Python版本是否符合要求（需要Python 3.8+）
5. 如果某些可选包安装失败，可以跳过，不影响核心功能

### 问题3：端口被占用
**错误信息**：`Address already in use`

**解决方案**：
1. 更改端口：修改启动脚本中的端口号
2. 或终止占用端口的进程

### 问题4：权限不足
**错误信息**：`权限不足` 或 `403 Forbidden`

**解决方案**：
1. 确认用户角色是否正确
2. 检查权限分配是否正常
3. 重新初始化数据库

## 📋 系统默认配置

### 默认账号信息
- **管理员账号**：admin / admin123
- **角色**：系统管理员
- **权限**：所有功能权限

### 默认系统配置
- **卡号最大长度**：10位
- **咨询内容最大长度**：500字
- **跟进内容最大长度**：500字
- **会话超时时间**：30分钟
- **密码最小长度**：8位
- **每页显示条数**：20条
- **最大文件上传大小**：16MB

### 预置角色权限
1. **系统管理员**：所有权限
2. **经营院长**：查看权限（渠道、客户、跟进、报表）
3. **部门主管**：本部门数据查看权限
4. **网络咨询**：客户登记和管理权限
5. **现场咨询**：客户跟进权限

### 示例渠道数据
- 线上推广 - 百度推广
- 线上推广 - 微信朋友圈
- 线下活动 - 商场活动
- 老客推荐 - 老客转介绍

## 🎯 下一步操作建议

### 1. 系统配置
- [ ] 修改默认管理员密码
- [ ] 创建部门用户账号
- [ ] 配置渠道信息
- [ ] 设置现场小组映射

### 2. 数据准备
- [ ] 导入现有渠道数据
- [ ] 导入现场咨询员信息
- [ ] 配置小组映射关系
- [ ] 准备客户数据模板

### 3. 用户培训
- [ ] 管理员功能培训
- [ ] 网络咨询操作培训
- [ ] 现场咨询操作培训
- [ ] 报表查看培训

### 4. 系统测试
- [ ] 功能测试
- [ ] 权限测试
- [ ] 数据导入测试
- [ ] 报表生成测试

## 📞 技术支持

如果在启动过程中遇到问题，请：

1. 查看控制台错误信息
2. 检查日志文件
3. 参考项目文档
4. 联系技术支持团队

## 🔗 相关文档

- [系统设计方案.md](./系统设计方案.md) - 详细的技术设计文档
- [README.md](./README.md) - 项目完整说明
- [项目完成总结.md](./项目完成总结.md) - 项目完成情况总结

---

**祝您使用愉快！** 🎉
