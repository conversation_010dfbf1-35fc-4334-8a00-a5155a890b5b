#!/usr/bin/env python3
"""
简单的表检查脚本
"""

import re
import pymysql
from app import create_app
from app.models import DatabaseConfig

def check_tables():
    """检查数据库表"""
    app = create_app()
    
    with app.app_context():
        # 获取激活的数据库配置
        active_config = DatabaseConfig.get_active_config()
        
        if not active_config:
            print("❌ 没有激活的数据库配置")
            return
        
        print(f"✅ 数据库配置: {active_config.name}")
        print(f"   连接: {active_config.host}:{active_config.port}/{active_config.database_name}")
        
        try:
            # 连接数据库
            connection = pymysql.connect(
                host=active_config.host,
                port=active_config.port,
                user=active_config.username,
                password=active_config.get_password(),
                database=active_config.database_name,
                charset='utf8mb4'
            )
            
            print("✅ 数据库连接成功")
            
            # 获取所有表
            with connection.cursor() as cursor:
                cursor.execute("SHOW TABLES")
                tables = [row[0] for row in cursor.fetchall()]
            
            print(f"\n📋 数据库中共有 {len(tables)} 个表:")
            
            # 查找季度表
            quarter_tables = []
            detail_table_found = False
            
            for table in tables:
                print(f"   - {table}")
                
                # 检查是否是季度表 (24Q1, 24Q2 格式)
                if re.match(r'^\d{2}Q[1-4]$', table):
                    quarter_tables.append(table)
                
                # 检查是否是客户执行明细表
                if table == '客户执行明细表':
                    detail_table_found = True
            
            print(f"\n🎯 分析结果:")
            print(f"   季度表 (24Q1格式): {len(quarter_tables)} 个")
            for table in quarter_tables:
                print(f"     ✅ {table}")
            
            if detail_table_found:
                print(f"   ✅ 找到客户执行明细表")
            else:
                print(f"   ❌ 未找到客户执行明细表")
            
            # 检查季度表结构
            if quarter_tables:
                print(f"\n🔍 检查季度表结构 (以 {quarter_tables[0]} 为例):")
                with connection.cursor() as cursor:
                    cursor.execute(f"DESCRIBE `{quarter_tables[0]}`")
                    columns = cursor.fetchall()
                
                has_member_id = False
                for column in columns:
                    column_name = column[0]
                    column_type = column[1]
                    print(f"     - {column_name} ({column_type})")
                    
                    if column_name == '会员卡号':
                        has_member_id = True
                
                if has_member_id:
                    print(f"     ✅ 包含'会员卡号'字段")
                else:
                    print(f"     ❌ 缺少'会员卡号'字段")
            
            # 检查客户执行明细表结构
            if detail_table_found:
                print(f"\n🔍 检查客户执行明细表结构:")
                with connection.cursor() as cursor:
                    cursor.execute("DESCRIBE `客户执行明细表`")
                    columns = cursor.fetchall()
                
                required_fields = ['执行日期', '一级分类', '二级分类', '三级分类', '执行业绩（真实金额）', '会员卡号']
                found_fields = []
                
                for column in columns:
                    column_name = column[0]
                    column_type = column[1]
                    print(f"     - {column_name} ({column_type})")
                    
                    if column_name in required_fields:
                        found_fields.append(column_name)
                
                print(f"\n     必需字段检查:")
                for field in required_fields:
                    if field in found_fields:
                        print(f"       ✅ {field}")
                    else:
                        print(f"       ❌ {field}")
            
            connection.close()
            
            # 总结
            print(f"\n📊 总结:")
            if quarter_tables and detail_table_found:
                print("   ✅ 数据库结构符合要求，可以进行数据处理")
            else:
                print("   ❌ 数据库结构不完整")
                if not quarter_tables:
                    print("      - 缺少季度表 (格式: 24Q1, 24Q2, 24Q3, 24Q4)")
                if not detail_table_found:
                    print("      - 缺少客户执行明细表")
            
        except Exception as e:
            print(f"❌ 数据库操作失败: {e}")

if __name__ == '__main__':
    try:
        check_tables()
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
