{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">编辑客户信息</h1>
    <div class="btn-group">
        <a href="{{ url_for('registration.customer_detail', customer_id=customer.id) }}" class="btn btn-outline-secondary">
            <i class="fas fa-eye me-2"></i>查看详情
        </a>
        <a href="{{ url_for('registration.customers') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>返回列表
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>客户信息编辑
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.card_number.label(class="form-label") }}
                                {{ form.card_number(class="form-control") }}
                                {% if form.card_number.errors %}
                                    <div class="text-danger">
                                        {% for error in form.card_number.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">客户的唯一标识，修改后请确保准确性</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.onsite_consultant_id.label(class="form-label") }}
                                {{ form.onsite_consultant_id(class="form-select select2-consultant", placeholder="搜索现场咨询师...") }}
                                {% if form.onsite_consultant_id.errors %}
                                    <div class="text-danger">
                                        {% for error in form.onsite_consultant_id.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">负责跟进此客户的现场咨询师</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.channel_id.label(class="form-label") }}
                                {{ form.channel_id(class="form-select select2-channel", placeholder="搜索激活渠道...") }}
                                {% if form.channel_id.errors %}
                                    <div class="text-danger">
                                        {% for error in form.channel_id.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">客户的来源渠道</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.last_visit_date.label(class="form-label") }}
                                {{ form.last_visit_date(class="form-control") }}
                                {% if form.last_visit_date.errors %}
                                    <div class="text-danger">
                                        {% for error in form.last_visit_date.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">客户最近一次到院的日期</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.inquiry_content.label(class="form-label") }}
                        {{ form.inquiry_content(class="form-control", rows="4", placeholder="请输入客户咨询的内容...") }}
                        <small class="form-text text-muted">最多500个字符</small>
                        {% if form.inquiry_content.errors %}
                            <div class="text-danger">
                                {% for error in form.inquiry_content.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('registration.customer_detail', customer_id=customer.id) }}" class="btn btn-outline-secondary me-md-2">取消</a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>编辑说明
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <small>
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        编辑客户信息时请谨慎操作，确保信息的准确性。
                    </small>
                </div>
                
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <strong>卡号:</strong> 必须为纯数字，系统会验证唯一性
                    </li>
                    <li class="mb-2">
                        <strong>所属现场:</strong> 可以重新分配负责的现场咨询师
                    </li>
                    <li class="mb-2">
                        <strong>激活渠道:</strong> 可以修正客户的来源渠道
                    </li>
                    <li class="mb-2">
                        <strong>咨询内容:</strong> 可以补充或修正咨询记录
                    </li>
                    <li class="mb-2">
                        <strong>来院时间:</strong> 可以更新最近的到院日期
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>原始信息
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <strong>原卡号:</strong><br>
                        <code>{{ customer.card_number }}</code>
                    </li>
                    <li class="mb-2">
                        <strong>登记人:</strong><br>
                        {{ customer.creator.real_name }} ({{ customer.creator.username }})
                    </li>
                    <li class="mb-2">
                        <strong>登记时间:</strong><br>
                        {{ customer.created_at.strftime('%Y-%m-%d %H:%M') }}
                    </li>
                    {% if customer.updated_at != customer.created_at %}
                    <li class="mb-2">
                        <strong>最后修改:</strong><br>
                        {{ customer.updated_at.strftime('%Y-%m-%d %H:%M') }}
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
        
        {% if customer.follow_up_note %}
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-comment me-2"></i>跟进情况
                </h6>
            </div>
            <div class="card-body">
                <p class="mb-2">{{ customer.follow_up_note }}</p>
                {% if customer.follow_up_time %}
                <small class="text-muted">
                    跟进时间: {{ customer.follow_up_time.strftime('%Y-%m-%d %H:%M') }}
                    {% if customer.follow_up_consultant %}
                    <br>跟进人: {{ customer.follow_up_consultant.real_name }}
                    {% endif %}
                </small>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
$(document).ready(function() {
    // 初始化现场咨询师选择器
    $('.select2-consultant').select2({
        theme: 'bootstrap-5',
        placeholder: '搜索现场咨询师...',
        allowClear: true,
        width: '100%',
        language: {
            noResults: function() {
                return "未找到匹配的现场咨询师";
            },
            searching: function() {
                return "搜索中...";
            },
            inputTooShort: function() {
                return "请输入更多字符进行搜索";
            }
        }
    });
    
    // 初始化渠道选择器
    $('.select2-channel').select2({
        theme: 'bootstrap-5',
        placeholder: '搜索激活渠道...',
        allowClear: true,
        width: '100%',
        language: {
            noResults: function() {
                return "未找到匹配的渠道";
            },
            searching: function() {
                return "搜索中...";
            },
            inputTooShort: function() {
                return "请输入更多字符进行搜索";
            }
        }
    });
    
    // 自定义样式调整
    $('.select2-container').css('z-index', '9999');
    
    // 表单提交确认
    $('form').on('submit', function(e) {
        if (!confirm('确定要保存对客户信息的修改吗？')) {
            e.preventDefault();
            return false;
        }
    });
});
</script>

<style>
/* Select2 自定义样式 */
.select2-container--bootstrap-5 .select2-selection {
    min-height: 38px;
}

.select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
    line-height: 36px;
    padding-left: 12px;
}

.select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
    height: 36px;
    right: 12px;
}

.select2-dropdown {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
}

.select2-results__option--highlighted {
    background-color: #0d6efd;
    color: white;
}

/* 搜索框样式 */
.select2-search--dropdown .select2-search__field {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 8px 12px;
}

/* 原始信息样式 */
code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 0.9em;
}
</style>
{% endblock %}