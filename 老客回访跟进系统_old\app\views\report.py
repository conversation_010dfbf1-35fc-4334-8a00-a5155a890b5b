"""
报表统计视图控制器
"""
from flask import Blueprint, render_template, request, jsonify
from flask_login import login_required, current_user
from app import db
from app.models.customer import CustomerRegistration, FollowUpRecord, CustomerConsumption
from app.models.user import User
from app.utils.permissions import require_permission
from datetime import datetime, date, timedelta

bp = Blueprint('report', __name__)

@bp.route('/')
@require_permission('REPORT_VIEW')
def index():
    """报表首页"""
    return render_template('report/index.html')

@bp.route('/statistics')
@require_permission('REPORT_VIEW')
def statistics():
    """统计报表"""
    # 获取筛选参数
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    consultant_id = request.args.get('consultant_id', type=int)
    channel_id = request.args.get('channel_id', type=int)
    
    # 构建基础查询
    query = CustomerRegistration.query
    
    # 应用权限过滤
    from app.utils.permissions import filter_query_by_permission
    query = filter_query_by_permission(query, CustomerRegistration, current_user)
    
    # 应用筛选条件
    if start_date:
        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            query = query.filter(CustomerRegistration.registration_time >= start_date)
        except ValueError:
            pass
    
    if end_date:
        try:
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(CustomerRegistration.registration_time <= end_date)
        except ValueError:
            pass
    
    if consultant_id:
        query = query.filter(CustomerRegistration.assigned_consultant_id == consultant_id)
    
    if channel_id:
        query = query.filter(CustomerRegistration.activation_channel_id == channel_id)
    
    # 获取统计数据
    registrations = query.all()
    
    # 计算统计指标
    total_customers = len(registrations)
    
    # 待跟进客户数（没有跟进记录的）
    pending_follow_up = 0
    visited_after_follow_up = 0
    total_consumption = 0
    
    for reg in registrations:
        follow_ups = FollowUpRecord.query.filter_by(
            customer_registration_id=reg.id,
            is_active=True
        ).count()
        
        if follow_ups == 0:
            pending_follow_up += 1
        else:
            # 检查跟进后是否有到院记录
            consumption = CustomerConsumption.query.filter_by(
                card_number=reg.card_number
            ).first()
            if consumption and consumption.visit_date:
                visited_after_follow_up += 1
                total_consumption += float(consumption.consumption_amount or 0)
    
    # 创建统计数据对象
    class StatisticsData:
        def __init__(self, total_customers, pending_follow_up, visited_after_follow_up, total_consumption):
            self.total_customers = total_customers
            self.pending_follow_up = pending_follow_up
            self.visited_after_follow_up = visited_after_follow_up
            self.total_consumption = total_consumption

    statistics_data = StatisticsData(
        total_customers=total_customers,
        pending_follow_up=pending_follow_up,
        visited_after_follow_up=visited_after_follow_up,
        total_consumption=total_consumption
    )
    
    # 获取筛选选项
    from app.models.customer import Channel
    consultants = get_accessible_consultants()
    channels = Channel.query.filter_by(is_active=True).all()
    
    return render_template('report/statistics.html',
                         statistics=statistics_data,
                         consultants=consultants,
                         channels=channels)

@bp.route('/api/statistics')
@require_permission('REPORT_VIEW')
def api_statistics():
    """统计数据API"""
    # 获取筛选参数
    filters = {
        'start_date': request.args.get('start_date'),
        'end_date': request.args.get('end_date'),
        'consultant_id': request.args.get('consultant_id', type=int),
        'channel_id': request.args.get('channel_id', type=int)
    }
    
    # 移除空值
    filters = {k: v for k, v in filters.items() if v is not None}
    
    # 生成统计数据
    statistics = generate_statistics(filters)
    
    return jsonify({
        'success': True,
        'data': statistics
    })

def generate_statistics(filters):
    """生成统计数据"""
    # 构建查询
    query = CustomerRegistration.query
    
    # 应用权限过滤
    from app.utils.permissions import filter_query_by_permission
    query = filter_query_by_permission(query, CustomerRegistration, current_user)
    
    # 应用筛选条件
    if filters.get('start_date'):
        try:
            start_date = datetime.strptime(filters['start_date'], '%Y-%m-%d').date()
            query = query.filter(CustomerRegistration.registration_time >= start_date)
        except ValueError:
            pass
    
    if filters.get('end_date'):
        try:
            end_date = datetime.strptime(filters['end_date'], '%Y-%m-%d').date()
            query = query.filter(CustomerRegistration.registration_time <= end_date)
        except ValueError:
            pass
    
    if filters.get('consultant_id'):
        query = query.filter(CustomerRegistration.assigned_consultant_id == filters['consultant_id'])
    
    if filters.get('channel_id'):
        query = query.filter(CustomerRegistration.activation_channel_id == filters['channel_id'])
    
    # 执行查询
    registrations = query.all()
    
    # 计算统计指标
    total_customers = len(registrations)
    pending_follow_up = 0
    visited_after_follow_up = 0
    total_consumption = 0.0
    
    for reg in registrations:
        # 检查是否有跟进记录
        follow_up_count = FollowUpRecord.query.filter_by(
            customer_registration_id=reg.id,
            is_active=True
        ).count()
        
        if follow_up_count == 0:
            pending_follow_up += 1
        else:
            # 检查跟进后是否有消费记录
            consumption_records = CustomerConsumption.query.filter_by(
                card_number=reg.card_number
            ).all()
            
            if consumption_records:
                visited_after_follow_up += 1
                for record in consumption_records:
                    total_consumption += float(record.consumption_amount or 0)
    
    return {
        'total_customers': total_customers,
        'pending_follow_up': pending_follow_up,
        'visited_after_follow_up': visited_after_follow_up,
        'total_consumption': round(total_consumption, 2),
        'follow_up_rate': round((total_customers - pending_follow_up) / total_customers * 100, 2) if total_customers > 0 else 0,
        'conversion_rate': round(visited_after_follow_up / total_customers * 100, 2) if total_customers > 0 else 0
    }

def get_accessible_consultants():
    """获取当前用户可访问的咨询员列表"""
    from app.utils.permissions import get_accessible_users
    accessible_users = get_accessible_users(current_user)
    
    # 筛选出现场咨询员
    from app.models.user import Role
    field_role = Role.query.filter_by(role_code='FIELD_CONSULTANT').first()
    if field_role:
        return [user for user in accessible_users if user.role_id == field_role.id]
    
    return []
