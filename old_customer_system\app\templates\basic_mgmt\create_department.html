{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">创建部门</h1>
    <a href="{{ url_for('basic_mgmt.departments') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>返回列表
    </a>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.name.label(class="form-label") }}
                        {{ form.name(class="form-control") }}
                        {% if form.name.errors %}
                            <div class="text-danger">
                                {% for error in form.name.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control", rows="3", placeholder="部门职能描述（可选）") }}
                        {% if form.description.errors %}
                            <div class="text-danger">
                                {% for error in form.description.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_active(class="form-check-input") }}
                            {{ form.is_active.label(class="form-check-label") }}
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('basic_mgmt.departments') }}" class="btn btn-secondary me-md-2">取消</a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>部门说明
                </h6>
            </div>
            <div class="card-body">
                <p class="text-muted mb-2">部门用于组织和管理用户，每个用户可以归属于一个部门。</p>
                <ul class="list-unstyled mb-0">
                    <li class="mb-1">• 部门名称必须唯一</li>
                    <li class="mb-1">• 停用的部门不会显示在用户选择列表中</li>
                    <li class="mb-1">• 部门主管只能管理本部门的数据</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}