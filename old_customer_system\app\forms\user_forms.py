# -*- coding: utf-8 -*-
"""
用户管理相关表单
"""

from flask_wtf import FlaskForm
from wtforms import StringField, SelectField, PasswordField, BooleanField, SubmitField, HiddenField
from wtforms.validators import DataRequired, Length, Optional, ValidationError
from app.models import User, Department

class UserForm(FlaskForm):
    """用户表单（创建/编辑）"""
    username = StringField('用户名', validators=[
        DataRequired(message='用户名不能为空'),
        Length(min=3, max=50, message='用户名长度必须在3-50个字符之间')
    ])
    real_name = StringField('真实姓名', validators=[
        DataRequired(message='真实姓名不能为空'),
        Length(min=2, max=100, message='真实姓名长度必须在2-100个字符之间')
    ])
    role = SelectField('角色', choices=[
        ('admin', '管理员'),
        ('director', '经营院长'),
        ('manager', '部门主管'),
        ('online_consultant', '网络咨询'),
        ('onsite_consultant', '现场咨询')
    ], validators=[DataRequired(message='请选择角色')])
    department_id = SelectField('所属部门', coerce=int, validators=[Optional()])
    simple_code = StringField('简码', validators=[
        Optional(),
        Length(max=20, message='简码长度不能超过20个字符')
    ])
    password = PasswordField('密码', validators=[
        Length(min=6, max=128, message='密码长度必须在6-128个字符之间')
    ])
    is_active = BooleanField('激活状态', default=True)
    submit = SubmitField('保存')
    
    def __init__(self, user=None, *args, **kwargs):
        super(UserForm, self).__init__(*args, **kwargs)
        self.user = user
        
        # 加载部门选项
        departments = Department.query.filter_by(is_active=True).all()
        self.department_id.choices = [(0, '请选择部门')] + [(d.id, d.name) for d in departments]
        
        # 如果是编辑模式，密码不是必需的
        if user:
            self.password.validators = [Optional(), Length(min=6, max=128, message='密码长度必须在6-128个字符之间')]
        else:
            self.password.validators = [DataRequired(message='密码不能为空'), Length(min=6, max=128, message='密码长度必须在6-128个字符之间')]
    
    def validate_username(self, username):
        """验证用户名唯一性"""
        user = User.query.filter_by(username=username.data).first()
        if user and (not self.user or user.id != self.user.id):
            raise ValidationError('用户名已存在，请选择其他用户名。')
    
    def validate_department_id(self, department_id):
        """验证部门选择"""
        if department_id.data == 0:
            self.department_id.data = None

class UserSearchForm(FlaskForm):
    """用户搜索表单"""
    query = StringField('搜索', validators=[Optional()])
    role = SelectField('角色筛选', choices=[
        ('', '全部角色'),
        ('admin', '管理员'),
        ('director', '经营院长'),
        ('manager', '部门主管'),
        ('online_consultant', '网络咨询'),
        ('onsite_consultant', '现场咨询')
    ], validators=[Optional()])
    department_id = SelectField('部门筛选', coerce=int, validators=[Optional()])
    is_active = SelectField('状态筛选', choices=[
        ('', '全部状态'),
        ('1', '激活'),
        ('0', '停用')
    ], validators=[Optional()])
    submit = SubmitField('搜索')
    
    def __init__(self, *args, **kwargs):
        super(UserSearchForm, self).__init__(*args, **kwargs)
        
        # 加载部门选项
        departments = Department.query.filter_by(is_active=True).all()
        self.department_id.choices = [(0, '全部部门')] + [(d.id, d.name) for d in departments]

class ResetPasswordForm(FlaskForm):
    """重置密码表单"""
    user_id = HiddenField('用户ID', validators=[DataRequired()])
    new_password = PasswordField('新密码', validators=[
        DataRequired(message='新密码不能为空'),
        Length(min=6, max=128, message='密码长度必须在6-128个字符之间')
    ])
    submit = SubmitField('重置密码')