#!/usr/bin/env python3
"""
老客回访跟进系统综合诊断脚本
全面检查系统状态，识别500错误的根本原因
"""

import sys
import os
from datetime import datetime
import traceback

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def safe_import():
    """安全导入模块，避免导入错误"""
    try:
        from app import create_app, db
        from sqlalchemy import text, inspect
        return create_app, db, text, inspect, None
    except Exception as e:
        return None, None, None, None, str(e)

def diagnose_system():
    """系统综合诊断"""
    print("=" * 60)
    print("老客回访跟进系统 - 综合诊断报告")
    print("=" * 60)
    print(f"诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 安全导入
    create_app, db, text, inspect, import_error = safe_import()
    if import_error:
        print("❌ 模块导入失败:")
        print(f"   错误: {import_error}")
        print("   建议: 检查Python环境和依赖包安装")
        return False
    
    app = create_app()
    issues_found = []
    
    with app.app_context():
        try:
            # 1. 数据库连接测试
            print("1. 数据库连接测试")
            print("-" * 30)
            try:
                db.session.execute(text("SELECT 1"))
                print("✓ 数据库连接正常")
            except Exception as e:
                print(f"❌ 数据库连接失败: {e}")
                issues_found.append("数据库连接失败")
                return False
            
            # 2. 表结构检查
            print("\n2. 数据库表结构检查")
            print("-" * 30)
            
            required_tables = [
                'user_accounts', 'user_roles', 'permissions', 'role_permissions',
                'departments', 'channels', 'customer_registrations', 'follow_up_records'
            ]
            
            inspector = inspect(db.engine)
            existing_tables = inspector.get_table_names()
            
            for table in required_tables:
                if table in existing_tables:
                    print(f"✓ 表 {table} 存在")
                else:
                    print(f"❌ 表 {table} 不存在")
                    issues_found.append(f"缺少表: {table}")
            
            # 3. 用户表结构详细检查
            print("\n3. 用户表结构详细检查")
            print("-" * 30)
            
            if 'user_accounts' in existing_tables:
                user_columns = inspector.get_columns('user_accounts')
                column_names = [col['name'] for col in user_columns]
                
                required_user_columns = ['id', 'username', 'department_id', 'role_id']
                for col in required_user_columns:
                    if col in column_names:
                        print(f"✓ 用户表字段 {col} 存在")
                    else:
                        print(f"❌ 用户表字段 {col} 不存在")
                        issues_found.append(f"用户表缺少字段: {col}")
                
                # 检查是否还有旧的department字段
                if 'department' in column_names:
                    print("⚠️ 用户表仍有旧的department字段，可能需要完成迁移")
                    issues_found.append("用户表存在旧的department字段")
            
            # 4. 外键关系检查
            print("\n4. 外键关系检查")
            print("-" * 30)
            
            if 'user_accounts' in existing_tables:
                foreign_keys = inspector.get_foreign_keys('user_accounts')
                fk_columns = [fk['constrained_columns'][0] for fk in foreign_keys]
                
                required_fks = ['department_id', 'role_id']
                for fk in required_fks:
                    if fk in fk_columns:
                        print(f"✓ 外键 {fk} 存在")
                    else:
                        print(f"❌ 外键 {fk} 不存在")
                        issues_found.append(f"缺少外键: {fk}")
            
            # 5. 数据完整性检查
            print("\n5. 数据完整性检查")
            print("-" * 30)
            
            # 检查用户数据
            try:
                user_count = db.session.execute(text("SELECT COUNT(*) FROM user_accounts")).scalar()
                print(f"✓ 用户总数: {user_count}")
                
                if 'department_id' in [col['name'] for col in inspector.get_columns('user_accounts')]:
                    users_without_dept = db.session.execute(text("""
                        SELECT COUNT(*) FROM user_accounts WHERE department_id IS NULL
                    """)).scalar()
                    
                    if users_without_dept > 0:
                        print(f"❌ {users_without_dept} 个用户没有分配部门")
                        issues_found.append(f"{users_without_dept}个用户缺少部门分配")
                    else:
                        print("✓ 所有用户都已分配部门")
                
            except Exception as e:
                print(f"❌ 用户数据检查失败: {e}")
                issues_found.append("用户数据检查失败")
            
            # 检查部门数据
            if 'departments' in existing_tables:
                try:
                    dept_count = db.session.execute(text("SELECT COUNT(*) FROM departments")).scalar()
                    print(f"✓ 部门总数: {dept_count}")
                    
                    if dept_count == 0:
                        print("❌ 没有部门数据")
                        issues_found.append("缺少部门数据")
                        
                except Exception as e:
                    print(f"❌ 部门数据检查失败: {e}")
                    issues_found.append("部门数据检查失败")
            
            # 6. 权限系统检查
            print("\n6. 权限系统检查")
            print("-" * 30)
            
            try:
                # 检查权限数据
                if 'permissions' in existing_tables:
                    perm_count = db.session.execute(text("SELECT COUNT(*) FROM permissions")).scalar()
                    print(f"✓ 权限总数: {perm_count}")
                    
                    # 检查关键权限
                    critical_permissions = ['REPORT_VIEW', 'CHANNEL_VIEW', 'USER_MANAGE']
                    for perm in critical_permissions:
                        exists = db.session.execute(text("""
                            SELECT COUNT(*) FROM permissions WHERE permission_code = :perm
                        """), {'perm': perm}).scalar()
                        
                        if exists > 0:
                            print(f"✓ 关键权限 {perm} 存在")
                        else:
                            print(f"❌ 关键权限 {perm} 不存在")
                            issues_found.append(f"缺少关键权限: {perm}")
                
                # 检查角色权限关联
                if 'role_permissions' in existing_tables:
                    role_perm_count = db.session.execute(text("SELECT COUNT(*) FROM role_permissions")).scalar()
                    print(f"✓ 角色权限关联总数: {role_perm_count}")
                    
                    if role_perm_count == 0:
                        print("❌ 没有角色权限关联数据")
                        issues_found.append("缺少角色权限关联")
                        
            except Exception as e:
                print(f"❌ 权限系统检查失败: {e}")
                issues_found.append("权限系统检查失败")
            
            # 7. 渠道数据检查
            print("\n7. 渠道数据检查")
            print("-" * 30)
            
            if 'channels' in existing_tables:
                try:
                    channel_count = db.session.execute(text("SELECT COUNT(*) FROM channels")).scalar()
                    print(f"✓ 渠道总数: {channel_count}")
                    
                    if channel_count > 500:  # 大量数据可能导致性能问题
                        print(f"⚠️ 渠道数据量较大 ({channel_count})，可能影响分页性能")
                        issues_found.append("渠道数据量过大可能影响性能")
                        
                except Exception as e:
                    print(f"❌ 渠道数据检查失败: {e}")
                    issues_found.append("渠道数据检查失败")
            
            # 8. 模型导入测试
            print("\n8. 应用模型导入测试")
            print("-" * 30)
            
            try:
                from app.models.user import User, Role, Permission, Department
                from app.models.customer import Channel
                print("✓ 用户模型导入成功")
                print("✓ 客户模型导入成功")
                
                # 测试模型关系
                try:
                    test_user = User.query.first()
                    if test_user:
                        dept_name = test_user.department.department_name if test_user.department else "无部门"
                        role_name = test_user.role.role_name if test_user.role else "无角色"
                        print(f"✓ 模型关系测试成功 (用户: {test_user.username}, 部门: {dept_name}, 角色: {role_name})")
                    else:
                        print("⚠️ 没有用户数据进行关系测试")
                        
                except Exception as e:
                    print(f"❌ 模型关系测试失败: {e}")
                    issues_found.append(f"模型关系错误: {str(e)}")
                    
            except Exception as e:
                print(f"❌ 模型导入失败: {e}")
                issues_found.append(f"模型导入错误: {str(e)}")
            
            # 9. 权限检查功能测试
            print("\n9. 权限检查功能测试")
            print("-" * 30)
            
            try:
                from app.models.user import User
                admin_user = db.session.execute(text("""
                    SELECT u.id FROM user_accounts u 
                    JOIN user_roles r ON u.role_id = r.id 
                    WHERE r.role_code = 'ADMIN' 
                    LIMIT 1
                """)).fetchone()
                
                if admin_user:
                    user = User.query.get(admin_user[0])
                    if user:
                        # 测试权限检查方法
                        has_report_view = user.has_permission('REPORT_VIEW')
                        print(f"✓ 权限检查功能测试: REPORT_VIEW = {has_report_view}")
                        
                        if not has_report_view:
                            print("❌ 管理员缺少REPORT_VIEW权限")
                            issues_found.append("管理员缺少REPORT_VIEW权限")
                    else:
                        print("❌ 无法获取管理员用户对象")
                        issues_found.append("无法获取管理员用户对象")
                else:
                    print("❌ 没有找到管理员用户")
                    issues_found.append("没有管理员用户")
                    
            except Exception as e:
                print(f"❌ 权限检查功能测试失败: {e}")
                issues_found.append(f"权限检查功能错误: {str(e)}")
            
        except Exception as e:
            print(f"❌ 诊断过程中出现严重错误: {e}")
            print("错误详情:")
            traceback.print_exc()
            issues_found.append(f"诊断过程错误: {str(e)}")
    
    # 生成诊断总结
    print("\n" + "=" * 60)
    print("诊断总结")
    print("=" * 60)
    
    if not issues_found:
        print("🎉 恭喜！没有发现严重问题，系统状态良好。")
        print("\n建议操作:")
        print("1. 重新启动系统: python start.py")
        print("2. 测试渠道管理页面功能")
        return True
    else:
        print(f"⚠️ 发现 {len(issues_found)} 个问题需要修复:")
        for i, issue in enumerate(issues_found, 1):
            print(f"   {i}. {issue}")
        
        print("\n🔧 建议修复步骤:")
        
        if any("权限" in issue for issue in issues_found):
            print("1. 运行权限修复: python fix_permissions.py")
        
        if any("部门" in issue or "department" in issue for issue in issues_found):
            print("2. 运行部门迁移: python migrate_departments.py")
        
        if any("模型关系" in issue for issue in issues_found):
            print("3. 检查并修复模型定义")
        
        if any("表" in issue and "不存在" in issue for issue in issues_found):
            print("4. 运行数据库初始化: python init_db.py")
        
        print("\n修复完成后，请重新运行此诊断脚本验证。")
        return False

if __name__ == '__main__':
    success = diagnose_system()
    print(f"\n诊断完成，状态: {'正常' if success else '需要修复'}")
    sys.exit(0 if success else 1)