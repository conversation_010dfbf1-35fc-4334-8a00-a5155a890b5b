# -*- coding: utf-8 -*-
"""
客户登记路由
"""

from flask import render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from app.registration import bp
from app.forms.customer_forms import CustomerForm, CustomerSearchForm, FollowUpForm
from app.services.customer_service import CustomerService
from app.services.permission_service import require_permission, require_any_permission, require_customer_access
from app.models import Customer, db
from datetime import datetime
@bp.route('/customers')
@login_required
@require_any_permission('view_all_customers', 'view_department_customers', 'view_own_customers', 'view_assigned_customers')
def customers():
    """客户列表"""
    search_form = CustomerSearchForm()
    customer_service = CustomerService()
    
    # 获取搜索参数
    query = request.args.get('query', '')
    onsite_consultant_id = request.args.get('onsite_consultant_id', type=int)
    online_consultant_id = request.args.get('online_consultant_id', type=int)
    channel_id = request.args.get('channel_id', type=int)
    channel_category = request.args.get('channel_category', '')
    has_follow_up = request.args.get('has_follow_up', '')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    page = request.args.get('page', 1, type=int)
    
    # 构建搜索条件
    criteria = {}
    if onsite_consultant_id:
        criteria['onsite_consultant_id'] = onsite_consultant_id
    if online_consultant_id:
        criteria['online_consultant_id'] = online_consultant_id
    if channel_id:
        criteria['channel_id'] = channel_id
    if channel_category:
        criteria['channel_category'] = channel_category
    if has_follow_up:
        criteria['has_follow_up'] = has_follow_up
    if start_date:
        criteria['start_date'] = datetime.strptime(start_date, '%Y-%m-%d').date()
    if end_date:
        criteria['end_date'] = datetime.strptime(end_date, '%Y-%m-%d').date()
    
    # 获取客户列表
    if query:
        # 搜索模式：先应用筛选条件，再在结果中搜索
        if criteria:
            # 先根据条件筛选
            customers_list = customer_service.get_customers_by_criteria(current_user, criteria)
            # 再在筛选结果中搜索
            customers_list = [c for c in customers_list if query.lower() in c.card_number.lower() or
                            (c.inquiry_content and query.lower() in c.inquiry_content.lower())]
        else:
            # 只有搜索条件，没有其他筛选条件
            customers_list = customer_service.search_customers(current_user, query)
        
        # 简单分页
        total = len(customers_list)
        per_page = 20
        start = (page - 1) * per_page
        end = start + per_page
        customers_list = customers_list[start:end]
        
        # 创建分页对象
        class SimplePagination:
            def __init__(self, items, total, page, per_page):
                self.items = items
                self.total = total
                self.page = page
                self.per_page = per_page
                self.pages = (total + per_page - 1) // per_page
                self.has_prev = page > 1
                self.has_next = page < self.pages
                self.prev_num = page - 1 if self.has_prev else None
                self.next_num = page + 1 if self.has_next else None
        
        pagination = SimplePagination(customers_list, total, page, per_page)
    else:
        # 分页模式
        if criteria:
            customers_list = customer_service.get_customers_by_criteria(current_user, criteria)
            # 简单分页
            total = len(customers_list)
            per_page = 20
            start = (page - 1) * per_page
            end = start + per_page
            customers_list = customers_list[start:end]
            
            class SimplePagination:
                def __init__(self, items, total, page, per_page):
                    self.items = items
                    self.total = total
                    self.page = page
                    self.per_page = per_page
                    self.pages = (total + per_page - 1) // per_page
                    self.has_prev = page > 1
                    self.has_next = page < self.pages
                    self.prev_num = page - 1 if self.has_prev else None
                    self.next_num = page + 1 if self.has_next else None
            
            pagination = SimplePagination(customers_list, total, page, per_page)
        else:
            per_page = 20
            pagination = customer_service.get_customers_with_permission(current_user, page, per_page)
    
    # 设置表单默认值
    if request.method == 'GET':
        search_form.query.data = query
        search_form.onsite_consultant_id.data = onsite_consultant_id or 0
        search_form.online_consultant_id.data = online_consultant_id or 0
        search_form.channel_id.data = channel_id or 0
        search_form.channel_category.data = channel_category
        search_form.has_follow_up.data = has_follow_up
        # 处理日期字段，确保是date对象
        if start_date:
            try:
                search_form.start_date.data = datetime.strptime(start_date, '%Y-%m-%d').date()
            except ValueError:
                search_form.start_date.data = None
        else:
            search_form.start_date.data = None

        if end_date:
            try:
                search_form.end_date.data = datetime.strptime(end_date, '%Y-%m-%d').date()
            except ValueError:
                search_form.end_date.data = None
        else:
            search_form.end_date.data = None
    
    return render_template('registration/customers.html', 
                         pagination=pagination,
                         form=search_form,  # 修改此处变量名
                         title='客户管理')

@bp.route('/register', methods=['GET', 'POST'])
@login_required
@require_permission('create_customer')
def register():
    """客户登记"""
    form = CustomerForm()
    
    if form.validate_on_submit():
        try:
            customer_service = CustomerService()
            
            customer_data = {
                'card_number': form.card_number.data,
                'onsite_consultant_id': form.onsite_consultant_id.data,
                'channel_id': form.channel_id.data,
                'inquiry_content': form.inquiry_content.data,
                'last_visit_date': form.last_visit_date.data
            }
            
            customer = customer_service.create_customer(customer_data, current_user)
            flash(f'客户 "{customer.card_number}" 登记成功！', 'success')
            return redirect(url_for('registration.customers'))
            
        except Exception as e:
            flash(f'客户登记失败: {str(e)}', 'error')
    
    return render_template('registration/register.html', form=form, title='客户登记')

@bp.route('/customers/<int:customer_id>')
@login_required
@require_customer_access()
def customer_detail(customer_id):
    """客户详情"""
    customer_service = CustomerService()
    customer = customer_service.get_customer_by_id(customer_id)
    
    if not customer:
        flash('客户不存在', 'error')
        return redirect(url_for('registration.customers'))
    
    return render_template('registration/customer_detail.html', 
                         customer=customer, title=f'客户详情 - {customer.card_number}')


@bp.route('/customers/<int:customer_id>/follow-up', methods=['POST'])
@login_required
@require_customer_access()
def update_follow_up(customer_id):
    """更新跟进信息"""
    form = FollowUpForm()
    
    if form.validate_on_submit():
        try:
            customer_service = CustomerService()
            success = customer_service.update_follow_up(
                customer_id, form.follow_up_note.data, current_user
            )
            
            if success:
                return jsonify({'success': True, 'message': '跟进信息更新成功'})
            else:
                return jsonify({'success': False, 'message': '跟进信息更新失败'})
                
        except Exception as e:
            return jsonify({'success': False, 'message': str(e)})
    
    return jsonify({'success': False, 'message': '表单验证失败'})

@bp.route('/customers/export')
@login_required
@require_any_permission('view_all_customers', 'view_department_customers', 'view_own_customers', 'view_assigned_customers')
def export_customers():
    """导出客户数据到Excel"""
    import pandas as pd
    from flask import send_file
    import io
    import tempfile
    from datetime import datetime
    
    try:
        customer_service = CustomerService()
        
        # 获取搜索参数
        query = request.args.get('query', '')
        onsite_consultant_id = request.args.get('onsite_consultant_id', type=int)
        channel_id = request.args.get('channel_id', type=int)
        has_follow_up = request.args.get('has_follow_up', '')
        
        # 构建搜索条件
        criteria = {}
        if onsite_consultant_id:
            criteria['onsite_consultant_id'] = onsite_consultant_id
        if channel_id:
            criteria['channel_id'] = channel_id
        if has_follow_up:
            criteria['has_follow_up'] = has_follow_up
        
        # 获取客户数据
        if query:
            customers = customer_service.search_customers(current_user, query)
            if criteria:
                # 应用额外的筛选条件
                filtered_customers = customer_service.get_customers_by_criteria(current_user, criteria)
                customers = [c for c in customers if c in filtered_customers]
        else:
            if criteria:
                customers = customer_service.get_customers_by_criteria(current_user, criteria)
            else:
                # 获取用户有权限查看的所有客户
                customers = customer_service.get_customers_with_permission(current_user, page=1, per_page=10000).items
        
        # 准备导出数据
        export_data = []
        for customer in customers:
            # 确定跟进状态
            if customer.follow_up_note and customer.follow_up_note.strip():
                follow_status = '已跟进'
            else:
                follow_status = '待跟进'
            
            # 获取跟进到院状态
            visit_status = customer.get_follow_up_visit_status()

            # 获取跟进消费金额
            consumption_amount = customer.get_follow_up_consumption_amount()
            consumption_amount_str = f"¥{consumption_amount:.2f}" if consumption_amount else ""

            export_data.append({
                '卡号': customer.card_number,
                '所属现场': customer.onsite_consultant.real_name,
                '所属网资': customer.online_consultant.real_name,
                '渠道分类': customer.channel.category or '',
                '激活渠道': customer.channel.name,
                '最近来院时间': customer.last_visit_date.strftime('%Y-%m-%d') if customer.last_visit_date else '',
                '跟进状态': follow_status,
                '跟进到院状态': visit_status,
                '跟进消费金额': consumption_amount_str,
                '信息登记时间': customer.created_at.strftime('%Y-%m-%d %H:%M')
            })
        
        # 创建DataFrame
        df = pd.DataFrame(export_data)
        
        if df.empty:
            flash('没有符合条件的客户数据可导出', 'warning')
            return redirect(url_for('registration.customers'))
        
        # 使用临时文件创建Excel
        with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as tmp_file:
            # 创建Excel文件
            with pd.ExcelWriter(tmp_file.name, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='客户数据', index=False)
                
                # 获取工作表并设置列宽
                worksheet = writer.sheets['客户数据']
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'客户数据_{timestamp}.xlsx'
            
            return send_file(tmp_file.name, 
                           as_attachment=True, 
                           download_name=filename,
                           mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    
    except Exception as e:
        flash(f'导出失败: {str(e)}', 'error')
        return redirect(url_for('registration.customers'))

@bp.route('/customers/<int:customer_id>/edit', methods=['GET', 'POST'])
@login_required
@require_any_permission('edit_all_customers', 'edit_department_customers', 'edit_own_customers')
def edit_customer(customer_id):
    """编辑客户信息"""
    from app.forms.customer_forms import CustomerForm
    customer_service = CustomerService()

    # 获取客户信息
    customer = customer_service.get_customer_by_id(current_user, customer_id)
    if not customer:
        flash('客户不存在或您无权访问', 'error')
        return redirect(url_for('registration.customers'))

    # 创建表单并预填充数据
    form = CustomerForm(customer=customer)

    if form.validate_on_submit():
        try:
            # 更新客户信息
            customer_data = {
                'card_number': form.card_number.data,
                'onsite_consultant_id': form.onsite_consultant_id.data,
                'channel_id': form.channel_id.data,
                'inquiry_content': form.inquiry_content.data,
                'last_visit_date': form.last_visit_date.data
            }

            success = customer_service.update_customer(
                customer_id=customer_id,
                customer_data=customer_data,
                operator=current_user
            )

            if success:
                flash('客户信息更新成功', 'success')
                return redirect(url_for('registration.customer_detail', customer_id=customer_id))
            else:
                flash('更新客户信息失败，请重试', 'error')

        except Exception as e:
            flash(f'更新客户信息时出现错误: {str(e)}', 'error')

    # 预填充表单数据
    if request.method == 'GET':
        form.card_number.data = customer.card_number
        form.onsite_consultant_id.data = customer.onsite_consultant_id
        form.channel_id.data = customer.channel_id
        form.inquiry_content.data = customer.inquiry_content
        # 处理日期字段，确保是date对象而不是字符串
        if customer.last_visit_date:
            if isinstance(customer.last_visit_date, str):
                try:
                    form.last_visit_date.data = datetime.strptime(customer.last_visit_date, '%Y-%m-%d').date()
                except ValueError:
                    form.last_visit_date.data = None
            else:
                form.last_visit_date.data = customer.last_visit_date
        else:
            form.last_visit_date.data = None

    return render_template('registration/edit_customer.html',
                         form=form,
                         customer=customer)


@bp.route('/delete_customer/<int:customer_id>', methods=['POST'])
@login_required
@require_permission('delete_customer')
def delete_customer(customer_id):
    """删除单个客户"""
    customer = Customer.query.get_or_404(customer_id)

    try:
        # 记录删除信息
        card_number = customer.card_number

        # 删除客户
        db.session.delete(customer)
        db.session.commit()

        flash(f'客户 {card_number} 已成功删除', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'删除客户失败：{str(e)}', 'error')

    return redirect(url_for('registration.customers'))


@bp.route('/batch_delete_customers', methods=['POST'])
@login_required
@require_permission('delete_customer')
def batch_delete_customers():
    """批量删除客户"""
    customer_ids = request.form.getlist('customer_ids')

    if not customer_ids:
        flash('请选择要删除的客户', 'warning')
        return redirect(url_for('registration.customers'))

    try:
        # 转换为整数
        customer_ids = [int(id) for id in customer_ids]

        # 查找要删除的客户
        customers = Customer.query.filter(Customer.id.in_(customer_ids)).all()

        if not customers:
            flash('未找到要删除的客户', 'warning')
            return redirect(url_for('registration.customers'))

        # 记录删除信息
        deleted_cards = [customer.card_number for customer in customers]

        # 批量删除
        for customer in customers:
            db.session.delete(customer)

        db.session.commit()

        flash(f'成功删除 {len(customers)} 个客户：{", ".join(deleted_cards)}', 'success')

    except ValueError:
        flash('客户ID格式错误', 'error')
    except Exception as e:
        db.session.rollback()
        flash(f'批量删除失败：{str(e)}', 'error')

    return redirect(url_for('registration.customers'))