#!/usr/bin/env python3
"""
直接测试API接口
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.user import User, Role
from app.models.customer import Channel
from config import get_config
from flask import url_for

def test_api_direct():
    """直接测试API接口"""
    print("直接测试API接口...")
    
    config = get_config()
    app = create_app(config)
    
    with app.app_context():
        # 模拟登录用户
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            print("❌ 未找到管理员用户")
            return False
        
        # 测试渠道API逻辑
        print("\n1. 测试渠道API逻辑...")
        try:
            channels = Channel.query.filter_by(is_active=True).all()
            print(f"✅ 找到 {len(channels)} 个活跃渠道")
            
            if channels:
                from app.utils.pinyin_utils import get_pinyin_first_letter
                first_channel = channels[0]
                channel_data = {
                    'id': first_channel.id,
                    'text': f"{first_channel.channel_category} - {first_channel.channel_name}",
                    'category': first_channel.channel_category,
                    'name': first_channel.channel_name,
                    'pinyin': get_pinyin_first_letter(f"{first_channel.channel_category}{first_channel.channel_name}"),
                }
                print(f"第一个渠道数据: {channel_data}")
        except Exception as e:
            print(f"❌ 渠道API逻辑测试失败: {e}")
        
        # 测试现场顾问API逻辑
        print("\n2. 测试现场顾问API逻辑...")
        try:
            field_role = Role.query.filter_by(role_code='FIELD_CONSULTANT').first()
            if field_role:
                consultants = User.query.filter_by(role_id=field_role.id, is_active=True).all()
                print(f"✅ 找到 {len(consultants)} 个现场顾问")
                
                if consultants:
                    from app.utils.pinyin_utils import get_pinyin_first_letter
                    first_consultant = consultants[0]
                    department_name = first_consultant.department.department_name if first_consultant.department else '未分配部门'
                    consultant_data = {
                        'id': first_consultant.id,
                        'text': f"{first_consultant.real_name} - {department_name}",
                        'name': first_consultant.real_name,
                        'department': department_name,
                        'pinyin': get_pinyin_first_letter(f"{first_consultant.real_name}{department_name}"),
                    }
                    print(f"第一个顾问数据: {consultant_data}")
            else:
                print("❌ 未找到现场顾问角色")
        except Exception as e:
            print(f"❌ 现场顾问API逻辑测试失败: {e}")
        
        # 测试网络咨询员API逻辑
        print("\n3. 测试网络咨询员API逻辑...")
        try:
            network_role = Role.query.filter_by(role_code='NETWORK_CONSULTANT').first()
            if network_role:
                registrars = User.query.filter_by(role_id=network_role.id, is_active=True).all()
                print(f"✅ 找到 {len(registrars)} 个网络咨询员")
                
                if registrars:
                    from app.utils.pinyin_utils import get_pinyin_first_letter
                    first_registrar = registrars[0]
                    department_name = first_registrar.department.department_name if first_registrar.department else '未分配部门'
                    registrar_data = {
                        'id': first_registrar.id,
                        'text': f"{first_registrar.real_name} - {department_name}",
                        'name': first_registrar.real_name,
                        'department': department_name,
                        'pinyin': get_pinyin_first_letter(f"{first_registrar.real_name}{department_name}"),
                    }
                    print(f"第一个咨询员数据: {registrar_data}")
            else:
                print("❌ 未找到网络咨询员角色")
        except Exception as e:
            print(f"❌ 网络咨询员API逻辑测试失败: {e}")
        
        # 测试搜索功能
        print("\n4. 测试搜索功能...")
        try:
            from app.utils.pinyin_utils import match_search_term
            
            test_cases = [
                ('百度推广', '百度'),
                ('百度推广', 'bd'),
                ('线上推广', 'xs'),
                ('线上推广', 'tg'),
            ]
            
            for text, search in test_cases:
                result = match_search_term(text, search)
                print(f"'{text}' 匹配 '{search}': {result}")
                
        except Exception as e:
            print(f"❌ 搜索功能测试失败: {e}")
        
        return True

def test_with_client():
    """使用测试客户端测试API"""
    print("\n使用测试客户端测试API...")
    
    config = get_config()
    app = create_app(config)
    
    with app.test_client() as client:
        # 登录
        login_response = client.post('/auth/login', data={
            'username': 'admin',
            'password': 'admin123'
        })
        print(f"登录响应状态: {login_response.status_code}")
        
        # 测试渠道API
        channels_response = client.get('/api/channels')
        print(f"渠道API响应状态: {channels_response.status_code}")
        if channels_response.status_code == 200:
            data = channels_response.get_json()
            print(f"渠道数量: {len(data.get('data', []))}")
            if data.get('data'):
                print(f"第一个渠道: {data['data'][0]}")
        else:
            print(f"渠道API错误: {channels_response.get_data(as_text=True)}")
        
        # 测试现场顾问API
        consultants_response = client.get('/api/consultants')
        print(f"现场顾问API响应状态: {consultants_response.status_code}")
        if consultants_response.status_code == 200:
            data = consultants_response.get_json()
            print(f"现场顾问数量: {len(data.get('data', []))}")
            if data.get('data'):
                print(f"第一个顾问: {data['data'][0]}")
        else:
            print(f"现场顾问API错误: {consultants_response.get_data(as_text=True)}")

if __name__ == "__main__":
    print("API接口直接测试")
    print("=" * 50)
    
    test_api_direct()
    test_with_client()
    
    print("\n" + "=" * 50)
    print("测试完成")
