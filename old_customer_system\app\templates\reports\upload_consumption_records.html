{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-upload"></i>
                        上传消费明细
                    </h3>
                    <div class="card-tools">
                        <a href="{{ url_for('reports.dashboard') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回报表
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- 使用说明 -->
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> 使用说明</h5>
                        <ul class="mb-0">
                            <li><strong>必需列：</strong>会员卡号、消费时间、消费金额</li>
                            <li><strong>可选列：</strong>消费具体时间、消费项目、项目分类、数量、单价、科室、医生、支付方式、备注</li>
                            <li><strong>日期格式：</strong>支持 YYYY-MM-DD、YYYY/MM/DD、YYYY年MM月DD日 等格式</li>
                            <li><strong>金额格式：</strong>支持数字格式，可包含逗号分隔符和货币符号（￥、¥）</li>
                            <li><strong>文件格式：</strong>支持 Excel (.xlsx, .xls) 和 CSV (.csv) 文件</li>
                            <li><strong>编码要求：</strong>CSV文件请使用UTF-8或GBK编码</li>
                        </ul>
                    </div>
                    
                    <!-- 示例表格 -->
                    <div class="alert alert-secondary">
                        <h6><i class="fas fa-table"></i> 数据格式示例</h6>
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <thead>
                                    <tr>
                                        <th>会员卡号</th>
                                        <th>消费时间</th>
                                        <th>消费金额</th>
                                        <th>消费项目</th>
                                        <th>项目分类</th>
                                        <th>数量</th>
                                        <th>单价</th>
                                        <th>科室</th>
                                        <th>医生</th>
                                        <th>支付方式</th>
                                        <th>备注</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>11111</td>
                                        <td>2025-07-28</td>
                                        <td>1500.00</td>
                                        <td>体检套餐A</td>
                                        <td>体检</td>
                                        <td>1</td>
                                        <td>1500.00</td>
                                        <td>体检科</td>
                                        <td>王医生</td>
                                        <td>微信支付</td>
                                        <td>年度体检</td>
                                    </tr>
                                    <tr>
                                        <td>22222</td>
                                        <td>2025/07/28</td>
                                        <td>￥800</td>
                                        <td>血常规检查</td>
                                        <td>检验</td>
                                        <td>1</td>
                                        <td>800</td>
                                        <td>检验科</td>
                                        <td>李医生</td>
                                        <td>现金</td>
                                        <td></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- 上传表单 -->
                    <form method="POST" enctype="multipart/form-data">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.file.label(class="form-label") }}
                                    {{ form.file(class="form-control") }}
                                    {% if form.file.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.file.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.file_type.label(class="form-label") }}
                                    {{ form.file_type(class="form-select") }}
                                    {% if form.file_type.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.file_type.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            {{ form.remarks.label(class="form-label") }}
                            {{ form.remarks(class="form-control") }}
                            {% if form.remarks.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.remarks.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            {{ form.submit(class="btn btn-primary") }}
                            <a href="{{ url_for('reports.dashboard') }}" class="btn btn-secondary">取消</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // 文件选择变化时自动检测文件类型
    $('#file').change(function() {
        var fileName = $(this).val();
        if (fileName) {
            var extension = fileName.split('.').pop().toLowerCase();
            if (extension === 'csv') {
                $('#file_type').val('csv');
            } else if (extension === 'xlsx' || extension === 'xls') {
                $('#file_type').val('excel');
            }
        }
    });
    
    // 表单提交时显示加载状态
    $('form').submit(function() {
        var submitBtn = $(this).find('input[type="submit"]');
        submitBtn.prop('disabled', true);
        submitBtn.val('上传中...');
        
        // 显示进度提示
        var progressHtml = '<div class="alert alert-info mt-3" id="upload-progress">' +
                          '<i class="fas fa-spinner fa-spin"></i> 正在上传和处理数据，请稍候...' +
                          '</div>';
        $(this).after(progressHtml);
    });
});
</script>
{% endblock %}
