@echo off
chcp 65001 >nul
echo ===================================
echo 老客回访与跟进系统 - 启动脚本
echo ===================================
echo.

:: 切换到脚本所在目录
cd /d "%~dp0"
echo 当前目录: %CD%

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

:: 检查关键文件
if not exist "init_db.py" (
    echo ❌ 找不到 init_db.py 文件
    echo 请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

if not exist "app\__init__.py" (
    echo ❌ 找不到 app 目录或 __init__.py 文件
    echo 请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

echo ✅ 文件检查通过

:: 尝试启动系统
echo.
echo 正在启动系统...
python quick_start.py

if errorlevel 1 (
    echo.
    echo ❌ 启动失败，尝试其他方式...
    echo.
    echo 方法1: 初始化数据库
    python init_db.py
    
    echo.
    echo 方法2: 启动系统
    python run.py
)

echo.
pause
