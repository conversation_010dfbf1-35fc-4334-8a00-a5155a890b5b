# 统计报表功能使用指南

## 🎉 功能概述

统计报表功能已完成开发，实现了以下核心需求：

1. **Excel数据上传**：支持上传客户消费明细和到院明细Excel文件到MySQL数据库
2. **智能数据匹配**：使用客户列表中登记过的卡号匹配上传的明细数据
3. **跟进效果统计**：
   - 来院时间在登记时间之后的，为跟进到院成功，统计数量+1
   - 消费时间在登记时间之后的，为跟进消费成功，统计数量+1
   - 汇总计算登记时间之后的消费金额

## 🚀 快速开始

### 1. 启动系统
```bash
cd old_customer_system
python run.py
```
系统将在 http://127.0.0.1:5000 启动

### 2. 登录系统
- 访问 http://127.0.0.1:5000
- 使用您的用户名和密码登录
- 所有角色用户都有上传和查看统计的权限

### 3. 访问统计报表
- 登录后访问：http://127.0.0.1:5000/reports/dashboard
- 或点击导航菜单中的"统计报表"

## 📊 功能详解

### 数据上传功能

#### 上传到院明细
- **访问路径**：统计报表仪表板 → "上传到院明细"
- **支持格式**：Excel (.xlsx, .xls) 和 CSV 文件
- **必需字段**：
  - 会员卡号
  - 到院时间 (支持多种日期格式)
  - 到院具体时间 (可选)
  - 到院类型、科室、医生等 (可选)

#### 上传消费明细
- **访问路径**：统计报表仪表板 → "上传消费明细"  
- **支持格式**：Excel (.xlsx, .xls) 和 CSV 文件
- **必需字段**：
  - 会员卡号
  - 消费时间 (支持多种日期格式)
  - 消费金额
  - 消费项目、数量、单价等 (可选)

### 统计报表功能

#### 统计报表仪表板
- **访问路径**：http://127.0.0.1:5000/reports/dashboard
- **功能**：
  - 总体数据概览（总客户数、成功数、成功率、消费金额）
  - 快捷功能入口
  - 最近7天趋势图

#### 详细统计报表
- **访问路径**：统计报表仪表板 → "跟进统计报表"
- **功能**：
  - 多维度筛选（日期范围、顾问、渠道）
  - 详细统计指标展示
  - 客户详细数据表格
  - Excel导出功能

## 📁 测试数据

系统已为您准备了测试数据：

### 生成测试文件
```bash
python create_test_data.py
```
将生成：
- `test_visit_records.xlsx` - 到院明细测试数据
- `test_consumption_records.xlsx` - 消费明细测试数据

### 测试流程
1. 运行 `python create_test_data.py` 生成测试文件
2. 访问上传页面，选择对应的测试文件上传
3. 上传完成后查看统计报表

## 📈 统计指标说明

### 核心指标
- **总登记客户数**：符合筛选条件的客户总数
- **跟进到院成功数**：有登记时间后到院记录的客户数
- **跟进消费成功数**：有登记时间后消费记录的客户数
- **总消费金额**：登记时间后的消费金额汇总
- **到院成功率**：跟进到院成功数 / 总登记客户数 × 100%
- **消费成功率**：跟进消费成功数 / 总登记客户数 × 100%

### 匹配逻辑
```
客户登记：卡号11111，登记时间2025-07-27
到院记录：会员卡号11111，到院时间2025-07-28 → 跟进到院成功 +1
消费记录：会员卡号11111，消费时间2025-07-28，消费金额1500 → 跟进消费成功 +1，累计消费金额 +1500
```

## 🔧 技术特性

### 数据解析
- **智能日期解析**：支持 YYYY-MM-DD、YYYY/MM/DD、YYYY年MM月DD日 等多种格式
- **Excel序列号支持**：自动识别Excel日期序列号
- **编码自适应**：支持 UTF-8、GBK、GB2312 等编码

### 数据验证
- **必填字段检查**：确保关键字段不为空
- **数据类型验证**：金额、日期格式验证
- **重复数据处理**：避免重复上传相同数据

### 权限控制
- **角色权限**：不同角色有不同的统计查看权限
- **数据安全**：上传和查看都有权限验证

## 🛠️ 故障排除

### 常见问题

#### 1. 403权限错误
- **原因**：用户没有上传权限
- **解决**：已修复，所有角色用户都有上传权限

#### 2. 上传失败
- **检查**：文件格式是否正确（Excel或CSV）
- **检查**：必需字段是否完整
- **检查**：日期格式是否正确

#### 3. 统计数据为空
- **检查**：是否已上传到院和消费明细数据
- **检查**：客户卡号是否匹配
- **检查**：时间筛选条件是否正确

### 调试工具

#### 测试权限
```bash
python fix_upload_permissions.py
```

#### 测试统计功能
```bash
python test_statistics.py
```

#### 测试页面访问
```bash
python test_upload_access.py
```

## 📞 技术支持

如遇到问题，请检查：
1. 应用程序是否正常运行
2. 数据库连接是否正常
3. 用户权限是否正确
4. 文件格式是否符合要求

系统日志会显示详细的错误信息，有助于问题诊断。
