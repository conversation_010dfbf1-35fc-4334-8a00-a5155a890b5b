{% extends "base.html" %}

{% block content %}
<div class="row mt-4">
    <div class="col-12">
        <h2><i class="fas fa-edit me-2"></i>编辑数据库配置</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">首页</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('config_mgmt.list_configs') }}">数据库配置</a></li>
                <li class="breadcrumb-item active">编辑配置</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row mt-3">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.name.label(class="form-label") }}
                                {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                                {% if form.name.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.host.label(class="form-label") }}
                                {{ form.host(class="form-control" + (" is-invalid" if form.host.errors else "")) }}
                                {% if form.host.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.host.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.port.label(class="form-label") }}
                                {{ form.port(class="form-control" + (" is-invalid" if form.port.errors else "")) }}
                                {% if form.port.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.port.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.username.label(class="form-label") }}
                                {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else "")) }}
                                {% if form.username.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.username.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.password.label(class="form-label") }}
                        {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else "")) }}
                        {% if form.password.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.password.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">留空表示不修改密码</div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.database_name.label(class="form-label") }}
                        {{ form.database_name(class="form-control" + (" is-invalid" if form.database_name.errors else "")) }}
                        {% if form.database_name.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.database_name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('config_mgmt.list_configs') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>返回
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-info-circle me-2"></i>当前配置信息</h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>配置名称:</strong></td>
                        <td>{{ config.name }}</td>
                    </tr>
                    <tr>
                        <td><strong>创建时间:</strong></td>
                        <td>{{ config.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                    </tr>
                    <tr>
                        <td><strong>更新时间:</strong></td>
                        <td>{{ config.updated_at.strftime('%Y-%m-%d %H:%M') }}</td>
                    </tr>
                    <tr>
                        <td><strong>状态:</strong></td>
                        <td>
                            {% if config.is_active %}
                                <span class="badge bg-success">激活</span>
                            {% else %}
                                <span class="badge bg-secondary">未激活</span>
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-exclamation-triangle me-2"></i>注意事项</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <small>
                        <ul class="mb-0">
                            <li>修改配置后需要重新激活才能生效</li>
                            <li>如果此配置正在被使用，修改可能影响正在运行的任务</li>
                            <li>密码留空表示不修改当前密码</li>
                        </ul>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
