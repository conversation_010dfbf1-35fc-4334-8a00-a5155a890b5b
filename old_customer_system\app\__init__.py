# -*- coding: utf-8 -*-
"""
老客登记信息反馈系统
Flask应用工厂模式实现
"""

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from flask_wtf.csrf import CSRFProtect
from config import config

# 初始化扩展
db = SQLAlchemy()
login_manager = LoginManager()
csrf = CSRFProtect()

def create_app(config_name='default'):
    """应用工厂函数"""
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 初始化扩展
    db.init_app(app)
    login_manager.init_app(app)
    csrf.init_app(app)
    
    # 配置登录管理器
    login_manager.login_view = 'auth.login'
    login_manager.login_message = '请先登录以访问此页面。'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        from app.models import User
        return User.query.get(int(user_id))
    
    # 注册蓝图
    from app.init import bp as init_bp
    app.register_blueprint(init_bp, url_prefix='/init')
    
    from app.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')
    
    from app.main import bp as main_bp
    app.register_blueprint(main_bp)
    
    from app.basic_mgmt import bp as basic_mgmt_bp
    app.register_blueprint(basic_mgmt_bp, url_prefix='/basic')
    
    from app.registration import bp as registration_bp
    app.register_blueprint(registration_bp, url_prefix='/registration')
    
    from app.reports import bp as reports_bp
    app.register_blueprint(reports_bp, url_prefix='/reports')
    
    # 错误处理
    from app.errors import bp as errors_bp
    app.register_blueprint(errors_bp)
    
    return app

from app import models