#!/usr/bin/env python3
"""
测试表单验证
"""

from app import create_app
from app.models import DatabaseConfig
from app.data_processing.forms import DataProcessingForm
from app.data_processing.processor import RFMDataProcessor

def test_form_validation():
    """测试表单验证"""
    app = create_app()
    
    with app.app_context():
        # 获取激活的数据库配置
        active_config = DatabaseConfig.get_active_config()
        
        if not active_config:
            print("❌ 没有激活的数据库配置")
            return
        
        # 创建表单
        form = DataProcessingForm()
        
        # 获取季度表
        processor = RFMDataProcessor(active_config)
        if processor.connect_database():
            quarter_tables = processor.get_quarter_tables()
            print(f"📋 季度表: {quarter_tables}")
            
            # 设置表单选项
            form.quarter_tables.choices = [(table, table) for table in quarter_tables]
            base_choices = [('', '请选择基础会员来源')] + [(table, table) for table in quarter_tables]
            form.base_quarter_table.choices = base_choices
            
            # 生成季度选项
            import re
            from datetime import datetime
            
            quarters = set()
            for table in quarter_tables:
                match = re.match(r'^(\d{2})[qQ]([1-4])$', table)
                if match:
                    year, quarter = match.groups()
                    quarters.add(f"{year}Q{quarter}")
            
            current_year = datetime.now().year % 100
            for year in range(current_year - 2, current_year + 2):
                for q in range(1, 5):
                    quarters.add(f"{year:02d}Q{q}")
            
            form.quarters.choices = [(q, q) for q in sorted(quarters)]
            
            print(f"📊 季度选项: {[q[0] for q in form.quarters.choices]}")
            
            # 模拟用户输入
            print(f"\n🧪 测试表单验证:")
            
            # 模拟正向盘提交
            form.task_name.data = "测试任务"
            form.task_type.data = "forward"
            form.base_quarter_table.data = "25q4"
            form.quarters.data = ["24Q4", "25Q1", "25Q2", "25Q3", "25Q4"]
            form.departments.data = ["皮肤", "注射"]
            
            print(f"   任务名称: {form.task_name.data}")
            print(f"   任务类型: {form.task_type.data}")
            print(f"   基础季度表: {form.base_quarter_table.data}")
            print(f"   分析季度: {form.quarters.data}")
            print(f"   科室: {form.departments.data}")
            
            # 验证表单
            is_valid = form.validate()
            print(f"\n📝 表单验证结果: {'✅ 通过' if is_valid else '❌ 失败'}")
            
            if not is_valid:
                print(f"   错误信息:")
                for field, errors in form.errors.items():
                    for error in errors:
                        print(f"     {field}: {error}")
        
        else:
            print("❌ 无法连接到数据库")

if __name__ == '__main__':
    try:
        test_form_validation()
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
