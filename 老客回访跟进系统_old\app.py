"""
老客回访与跟进系统 - 应用入口文件
"""
import os
import sys
from flask import Flask, render_template, redirect, url_for, flash, request
from config import get_config

def create_application():
    """创建Flask应用实例"""
    config_class = get_config()
    from app import create_app
    app = create_app(config_class)

    return app

def check_database_connection(app):
    """检查数据库连接"""
    try:
        with app.app_context():
            from app import db
            db.engine.connect()
        return True
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return False

def initialize_database(app):
    """初始化数据库"""
    try:
        with app.app_context():
            from app import db
            # 创建所有表
            db.create_all()

            # 初始化基础数据
            init_basic_data()

        print("数据库初始化成功")
        return True
    except Exception as e:
        print(f"数据库初始化失败: {e}")
        return False

def init_basic_data():
    """初始化基础数据"""
    from app import db
    from app.models import Role, Permission, SystemConfig

    # 检查是否已经初始化
    if Role.query.first():
        return
    
    # 创建角色
    roles_data = [
        {'role_name': '系统管理员', 'role_code': 'ADMIN', 'description': '拥有系统所有功能的操作权限'},
        {'role_name': '经营院长', 'role_code': 'GENERAL_MANAGER', 'description': '可查看所有登记信息和统计报表，但不能进行登记和修改操作'},
        {'role_name': '部门主管', 'role_code': 'DEPARTMENT_MANAGER', 'description': '可查看其所属部门下所有员工登记的信息和相关统计报表'},
        {'role_name': '网络咨询', 'role_code': 'NETWORK_CONSULTANT', 'description': '负责登记客户，只能查看和修改自己名下的登记信息'},
        {'role_name': '现场咨询', 'role_code': 'FIELD_CONSULTANT', 'description': '负责跟进客户，只能查看分配给自己的客户信息，并填写跟进情况'}
    ]
    
    for role_data in roles_data:
        role = Role(**role_data)
        db.session.add(role)
    
    # 创建权限
    permissions_data = [
        # 系统管理权限
        {'permission_name': '用户管理', 'permission_code': 'USER_MANAGE', 'module_name': 'SYSTEM', 'description': '创建、编辑、删除用户账号'},
        {'permission_name': '角色管理', 'permission_code': 'ROLE_MANAGE', 'module_name': 'SYSTEM', 'description': '管理系统角色和权限'},
        {'permission_name': '系统配置', 'permission_code': 'SYSTEM_CONFIG', 'module_name': 'SYSTEM', 'description': '修改系统配置参数'},
        
        # 渠道管理权限
        {'permission_name': '渠道查看', 'permission_code': 'CHANNEL_VIEW', 'module_name': 'CHANNEL', 'description': '查看渠道列表'},
        {'permission_name': '渠道管理', 'permission_code': 'CHANNEL_MANAGE', 'module_name': 'CHANNEL', 'description': '添加、修改、删除渠道'},
        {'permission_name': '渠道导入', 'permission_code': 'CHANNEL_IMPORT', 'module_name': 'CHANNEL', 'description': '批量导入渠道数据'},
        
        # 客户登记权限
        {'permission_name': '客户登记', 'permission_code': 'CUSTOMER_REGISTER', 'module_name': 'CUSTOMER', 'description': '登记新客户信息'},
        {'permission_name': '客户查看', 'permission_code': 'CUSTOMER_VIEW', 'module_name': 'CUSTOMER', 'description': '查看客户登记信息'},
        {'permission_name': '客户修改', 'permission_code': 'CUSTOMER_EDIT', 'module_name': 'CUSTOMER', 'description': '修改客户登记信息'},
        
        # 跟进管理权限
        {'permission_name': '跟进添加', 'permission_code': 'FOLLOW_UP_ADD', 'module_name': 'FOLLOW_UP', 'description': '添加客户跟进记录'},
        {'permission_name': '跟进查看', 'permission_code': 'FOLLOW_UP_VIEW', 'module_name': 'FOLLOW_UP', 'description': '查看跟进记录'},
        
        # 数据管理权限
        {'permission_name': '映射管理', 'permission_code': 'MAPPING_MANAGE', 'module_name': 'DATA', 'description': '管理现场小组映射关系'},
        {'permission_name': '消费导入', 'permission_code': 'CONSUMPTION_IMPORT', 'module_name': 'DATA', 'description': '导入客户消费数据'},
        
        # 统计报表权限
        {'permission_name': '报表查看', 'permission_code': 'REPORT_VIEW', 'module_name': 'REPORT', 'description': '查看统计报表'},
        {'permission_name': '报表导出', 'permission_code': 'REPORT_EXPORT', 'module_name': 'REPORT', 'description': '导出报表数据'}
    ]
    
    for perm_data in permissions_data:
        permission = Permission(**perm_data)
        db.session.add(permission)
    
    # 创建系统配置
    config_data = [
        {'config_key': 'card_number_max_length', 'config_value': '10', 'config_description': '卡号最大长度', 'config_type': 'integer'},
        {'config_key': 'consultation_content_max_length', 'config_value': '500', 'config_description': '咨询内容最大长度', 'config_type': 'integer'},
        {'config_key': 'follow_up_content_max_length', 'config_value': '500', 'config_description': '跟进内容最大长度', 'config_type': 'integer'},
        {'config_key': 'session_timeout_minutes', 'config_value': '30', 'config_description': '会话超时时间（分钟）', 'config_type': 'integer'},
        {'config_key': 'password_min_length', 'config_value': '8', 'config_description': '密码最小长度', 'config_type': 'integer'},
        {'config_key': 'system_name', 'config_value': '老客回访与跟进系统', 'config_description': '系统名称', 'config_type': 'string'}
    ]
    
    for config_item in config_data:
        system_config = SystemConfig(**config_item)
        db.session.add(system_config)
    
    db.session.commit()
    print("基础数据初始化完成")

def setup_first_admin():
    """设置第一个管理员账号"""
    with app.app_context():
        from app import db
        from app.models import User, Role

        # 检查是否已有管理员
        admin_role = Role.query.filter_by(role_code='ADMIN').first()
        if admin_role and User.query.filter_by(role_id=admin_role.id).first():
            return False
        
        print("\n=== 创建管理员账号 ===")
        username = input("请输入管理员用户名: ").strip()
        if not username:
            print("用户名不能为空")
            return False
        
        # 检查用户名是否已存在
        if User.query.filter_by(username=username).first():
            print("用户名已存在")
            return False
        
        real_name = input("请输入真实姓名: ").strip()
        if not real_name:
            print("真实姓名不能为空")
            return False
        
        import getpass
        password = getpass.getpass("请输入密码: ")
        if len(password) < 8:
            print("密码长度不能少于8位")
            return False
        
        confirm_password = getpass.getpass("请确认密码: ")
        if password != confirm_password:
            print("两次输入的密码不一致")
            return False
        
        try:
            from werkzeug.security import generate_password_hash
            
            admin_user = User(
                username=username,
                password_hash=generate_password_hash(password),
                real_name=real_name,
                department='系统管理部',
                role_id=admin_role.id,
                is_active=True
            )
            
            db.session.add(admin_user)
            db.session.commit()
            
            print(f"管理员账号 '{username}' 创建成功！")
            return True
            
        except Exception as e:
            print(f"创建管理员账号失败: {e}")
            db.session.rollback()
            return False

def main():
    """主函数"""
    print("=== 老客回访与跟进系统 ===")
    print("正在启动系统...")
    
    # 创建应用
    global app
    app = create_application()
    
    # 检查数据库连接
    if not check_database_connection(app):
        print("请检查数据库配置和连接")
        return
    
    # 初始化数据库
    if not initialize_database(app):
        print("数据库初始化失败")
        return
    
    # 设置管理员账号
    with app.app_context():
        from app.models import User, Role
        admin_role = Role.query.filter_by(role_code='ADMIN').first()
        if not admin_role or not User.query.filter_by(role_id=admin_role.id).first():
            if not setup_first_admin():
                print("管理员账号设置失败")
                return
    
    print("\n系统启动成功！")
    print("访问地址: http://localhost:5000")
    print("按 Ctrl+C 停止服务")
    
    # 启动应用
    try:
        app.run(host='0.0.0.0', port=5000, debug=app.config.get('DEBUG', False))
    except KeyboardInterrupt:
        print("\n系统已停止")

if __name__ == '__main__':
    main()
