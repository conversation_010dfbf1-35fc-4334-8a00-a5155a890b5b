from flask_wtf import FlaskForm
from wtforms import StringField, SelectField, SelectMultipleField, SubmitField, TextAreaField, RadioField, BooleanField, IntegerField
from wtforms.validators import DataRequired, Length, NumberRange
from wtforms.widgets import CheckboxInput, ListWidget

class MultiCheckboxField(SelectMultipleField):
    """多选框字段"""
    widget = ListWidget(prefix_label=False)
    option_widget = CheckboxInput()

    def pre_validate(self, form):
        """重写预验证，避免选项验证问题"""
        if self.data:
            for choice in self.data:
                if choice and (choice, choice) not in self.choices:
                    # 如果选项不在choices中，尝试添加它
                    self.choices.append((choice, choice))
        super().pre_validate(form)

class DataProcessingForm(FlaskForm):
    """数据处理任务表单"""
    task_name = StringField('任务名称', validators=[
        DataRequired(message='任务名称不能为空'),
        Length(min=2, max=200, message='任务名称长度必须在2-200个字符之间')
    ])
    
    task_type = RadioField('处理类型', choices=[
        ('forward', '正向盘 - 基于指定季度表的会员分析多个季度业绩'),
        ('result', '结果盘 - 基于多个季度表的会员并集分析')
    ], validators=[DataRequired(message='请选择处理类型')])

    # 正向盘：基础会员来源（单选）
    base_quarter_table = SelectField('基础会员来源', choices=[], validators=[])

    # 结果盘：季度表选择（多选）
    quarter_tables = MultiCheckboxField('季度表选择', choices=[], validators=[])

    # 分析季度（多选）
    quarters = MultiCheckboxField('分析季度', choices=[], validators=[])
    
    departments = MultiCheckboxField('科室选择', choices=[
        ('皮肤', '皮肤科'),
        ('注射', '注射科'),
        ('口腔', '口腔科'),
        ('整形', '整形科'),
        ('毛发', '毛发科')
    ], default=['皮肤', '注射'])

    # TOP数量配置
    category_top_count = IntegerField('品类TOP数',
        validators=[
            DataRequired(message='品类TOP数不能为空'),
            NumberRange(min=1, max=20, message='品类TOP数必须在1-20之间')
        ],
        default=5,
        description='每个科室显示的品类TOP数量'
    )

    item_top_count = IntegerField('品项TOP数',
        validators=[
            DataRequired(message='品项TOP数不能为空'),
            NumberRange(min=1, max=50, message='品项TOP数必须在1-50之间')
        ],
        default=15,
        description='每个科室显示的品项TOP数量'
    )

    # 映射选择
    use_level1_mapping = BooleanField('使用一级分类映射', default=False)
    use_level2_mapping = BooleanField('使用二级分类映射', default=False)
    use_level3_mapping = BooleanField('使用三级分类映射', default=False)
    use_field_mapping = BooleanField('使用现场映射', default=False)
    use_field_group = BooleanField('使用现场小组映射', default=False)

    # TOP排名计算时间范围（确定哪些品类/品项进入TOP排名）
    top_ranking_start_date = StringField('TOP排名统计开始日期', render_kw={'type': 'date', 'placeholder': '选择开始日期'})
    top_ranking_end_date = StringField('TOP排名统计结束日期', render_kw={'type': 'date', 'placeholder': '选择结束日期'})

    # TOP列值计算时间范围（计算每个会员的消费金额）
    top_value_start_date = StringField('TOP列值计算开始日期', render_kw={'type': 'date', 'placeholder': '选择开始日期'})
    top_value_end_date = StringField('TOP列值计算结束日期', render_kw={'type': 'date', 'placeholder': '选择结束日期'})

    description = TextAreaField('任务描述', render_kw={'rows': 3})

    submit = SubmitField('开始处理')
    
    def __init__(self, *args, **kwargs):
        super(DataProcessingForm, self).__init__(*args, **kwargs)
        # 动态设置默认选择的科室
        if not self.departments.data:
            self.departments.data = ['皮肤', '注射']
