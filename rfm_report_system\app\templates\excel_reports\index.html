{% extends "base.html" %}

{% block title %}Excel报表系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-file-excel me-2 text-success"></i>Excel报表系统</h2>
                <div>
                    <a href="{{ url_for('excel_reports.custom_report') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>自定义报表
                    </a>
                </div>
            </div>
            <p class="text-muted">基于Excel公式的智能报表生成系统</p>
        </div>
    </div>

    <!-- 功能介绍 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle me-2"></i>系统特性</h5>
                <ul class="mb-0">
                    <li><strong>Excel公式支持</strong>：支持IF、SUM、VLOOKUP等常用Excel函数</li>
                    <li><strong>模板化报表</strong>：预定义的报表模板，一键生成</li>
                    <li><strong>自定义报表</strong>：灵活配置数据源和计算逻辑</li>
                    <li><strong>实时计算</strong>：基于最新数据实时计算报表结果</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 预定义报表模板 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-table me-2"></i>预定义报表模板</h5>
                </div>
                <div class="card-body">
                    {% if templates %}
                    <div class="row">
                        {% for template_name in templates %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card border-primary">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-chart-bar me-2 text-primary"></i>{{ template_name }}
                                    </h6>
                                    <p class="card-text text-muted small">
                                        {% if template_name == "会员RFM分析报表" %}
                                        基于Excel公式的会员RFM分析，自动计算R、F、M值和综合等级
                                        {% elif template_name == "科室业绩分析报表" %}
                                        科室业绩统计分析，包含总业绩、会员数量、平均客单价等指标
                                        {% elif template_name == "TOP品类分析报表" %}
                                        TOP品类业绩分析，自动排名和TOP5标识
                                        {% else %}
                                        {{ template_name }}
                                        {% endif %}
                                    </p>
                                    <div class="d-flex gap-2">
                                        <a href="{{ url_for('excel_reports.view_template', template_name=template_name) }}" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye me-1"></i>查看
                                        </a>
                                        <button class="btn btn-outline-success btn-sm" 
                                                onclick="previewReport('{{ template_name }}')">
                                            <i class="fas fa-search me-1"></i>预览
                                        </button>
                                        <button class="btn btn-success btn-sm" 
                                                onclick="generateReport('{{ template_name }}')">
                                            <i class="fas fa-download me-1"></i>生成
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-inbox fa-3x mb-3"></i>
                        <p>暂无可用的报表模板</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 预览模态框 -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">报表预览</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="previewContent">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在生成预览...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-success" id="generateFromPreview">生成完整报表</button>
            </div>
        </div>
    </div>
</div>

<!-- 加载提示模态框 -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">处理中...</span>
                </div>
                <p id="loadingMessage">正在生成报表，请稍候...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentTemplate = '';

// 预览报表
function previewReport(templateName) {
    currentTemplate = templateName;
    $('#previewModal').modal('show');
    
    $.ajax({
        url: '{{ url_for("excel_reports.preview_report") }}',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({template_name: templateName}),
        success: function(response) {
            if (response.success) {
                displayPreview(response.preview);
            } else {
                $('#previewContent').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        预览失败: ${response.message}
                    </div>
                `);
            }
        },
        error: function() {
            $('#previewContent').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    预览请求失败，请稍后重试
                </div>
            `);
        }
    });
}

// 显示预览数据
function displayPreview(preview) {
    let html = `
        <div class="mb-3">
            <span class="badge bg-info">总计 ${preview.total_rows} 行数据</span>
            <span class="badge bg-secondary">预览前 10 行</span>
        </div>
        <div class="table-responsive">
            <table class="table table-striped table-sm">
                <thead class="table-dark">
                    <tr>
    `;
    
    // 表头
    preview.columns.forEach(col => {
        html += `<th>${col}</th>`;
    });
    html += '</tr></thead><tbody>';
    
    // 数据行
    preview.data.forEach(row => {
        html += '<tr>';
        row.forEach(cell => {
            html += `<td>${cell !== null ? cell : ''}</td>`;
        });
        html += '</tr>';
    });
    
    html += '</tbody></table></div>';
    $('#previewContent').html(html);
}

// 生成报表
function generateReport(templateName) {
    currentTemplate = templateName;
    $('#loadingModal').modal('show');
    
    $.ajax({
        url: `{{ url_for("excel_reports.generate_report", template_name="TEMPLATE") }}`.replace('TEMPLATE', templateName),
        method: 'POST',
        success: function(response) {
            $('#loadingModal').modal('hide');
            
            if (response.success) {
                // 显示成功消息并提供下载链接
                Swal.fire({
                    icon: 'success',
                    title: '报表生成成功！',
                    text: response.message,
                    showCancelButton: true,
                    confirmButtonText: '立即下载',
                    cancelButtonText: '稍后下载'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = response.download_url;
                    }
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: '生成失败',
                    text: response.message
                });
            }
        },
        error: function() {
            $('#loadingModal').modal('hide');
            Swal.fire({
                icon: 'error',
                title: '请求失败',
                text: '报表生成请求失败，请稍后重试'
            });
        }
    });
}

// 从预览生成完整报表
$('#generateFromPreview').click(function() {
    $('#previewModal').modal('hide');
    generateReport(currentTemplate);
});
</script>
{% endblock %}
