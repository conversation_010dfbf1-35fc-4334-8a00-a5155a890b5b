# RFM报表处理系统 - 快速开始指南

## 🚀 5分钟快速部署

### 前提条件
- Python 3.8+ 已安装
- MySQL 5.7+ 服务器运行中
- 具备数据库创建权限

### 方法一：自动安装（推荐）

```bash
# 1. 下载并进入项目目录
cd rfm_report_system

# 2. 运行自动安装脚本
python install.py

# 3. 编辑配置文件
# 修改 .env 文件中的数据库连接信息

# 4. 一键启动
# Windows:
start.bat
# Linux/Mac:
./start.sh
```

### 方法二：手动安装

```bash
# 1. 创建虚拟环境
python -m venv venv

# 2. 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# 3. 安装依赖
pip install -r requirements.txt

# 4. 配置环境
cp .env.example .env
# 编辑 .env 文件

# 5. 初始化数据库
python init_db.py

# 6. 启动系统
python run.py
```

## 📋 配置清单

### .env 文件配置
```bash
# 应用密钥（必须修改）
SECRET_KEY=your-very-secret-key-here

# 系统管理数据库配置
SYSTEM_DB_HOST=localhost
SYSTEM_DB_PORT=3306
SYSTEM_DB_USER=root
SYSTEM_DB_PASSWORD=your-password

# 应用环境
FLASK_ENV=development
LOG_LEVEL=INFO
```

### MySQL权限要求
系统需要以下MySQL权限：
- CREATE DATABASE
- CREATE TABLE
- SELECT, INSERT, UPDATE, DELETE
- INDEX

## 🎯 首次使用流程

### 1. 登录系统
- 访问: http://localhost:5000
- 使用初始化时创建的管理员账户登录

### 2. 配置业务数据库
1. 点击"配置管理" → "数据库配置"
2. 点击"添加配置"
3. 填写业务数据库连接信息
4. 点击"测试连接"
5. 选择数据库并保存
6. 激活配置

### 3. 准备数据表
确保业务数据库包含以下表：

#### 季度客户表（必需）
- 表名格式: `{年份}Q{季度}_customer_data`
- 例如: `24Q3_customer_data`, `25Q1_customer_data`
- 必须包含: `会员卡号` 字段

#### 客户执行明细表（必需）
- 表名: `客户执行明细表`
- 必需字段:
  - `执行日期` (DATE)
  - `一级分类` (VARCHAR) - 科室
  - `二级分类` (VARCHAR) - 品类  
  - `三级分类` (VARCHAR) - 品项
  - `执行业绩（真实金额）` (DECIMAL)
  - `会员卡号` (VARCHAR)

### 4. 创建第一个分析任务
1. 点击"数据处理" → "新建任务"
2. 填写任务信息:
   - 任务名称: 如"2024Q4季度分析"
   - 处理类型: 选择"正向盘"或"结果盘"
   - 季度表: 选择要分析的季度表
   - 分析季度: 选择要计算业绩的季度
   - 科室: 默认选择"皮肤"和"注射"
3. 点击"开始处理"
4. 等待处理完成
5. 下载Excel结果文件

## 🔧 常见问题解决

### 数据库连接失败
```bash
# 检查MySQL服务状态
# Windows:
net start mysql
# Linux:
sudo systemctl start mysql

# 测试连接
mysql -h localhost -u root -p
```

### 依赖包安装失败
```bash
# 升级pip
pip install --upgrade pip

# 清理缓存重新安装
pip cache purge
pip install -r requirements.txt
```

### 权限错误
```bash
# Linux/Mac设置执行权限
chmod +x start.sh
chmod +x *.py

# Windows以管理员身份运行
```

### 端口被占用
```bash
# 查看端口占用
# Windows:
netstat -ano | findstr :5000
# Linux/Mac:
lsof -i :5000

# 修改端口（在run.py中）
app.run(port=5001)
```

## 📊 数据处理说明

### 处理类型选择

#### 正向盘
- 适用场景: 分析特定季度的客户群体
- 数据来源: 单个季度客户表
- 用途: 季度客户分析、新客分析

#### 结果盘  
- 适用场景: 分析多个季度的客户并集
- 数据来源: 多个季度客户表的并集
- 用途: 历史客户分析、流失客户分析

### 科室映射关系
- **皮肤**: 皮肤美容项目、形体美容项目
- **注射**: 注射美容项目
- **口腔**: 口腔美容项目
- **整形**: 检验项目、整形美容项目、麻醉项目
- **毛发**: 毛发种植项目

### 输出结果说明
生成的Excel文件包含：
- 基础会员信息（来自季度表）
- 各季度执行业绩
- 各科室季度业绩
- TOP品类排名业绩
- TOP品项排名业绩

## 🛠️ 系统管理

### 用户管理
```bash
# 创建新用户
python manage.py create-user

# 列出所有用户
python manage.py list-users

# 重置密码
python reset_password.py
```

### 任务管理
```bash
# 查看任务列表
python manage.py list-tasks

# 清理失败任务
python manage.py clean-tasks --status failed
```

### 系统检查
```bash
# 检查系统环境
python check_system.py

# 检查系统健康状态
python manage.py check-health
```

## 📈 性能优化建议

### 数据库优化
- 为常用查询字段添加索引
- 定期清理历史任务数据
- 使用数据库连接池

### 系统优化
- 增加服务器内存
- 使用SSD存储
- 配置日志轮转

### 大数据量处理
- 分批处理数据
- 使用异步任务队列
- 优化SQL查询

## 🔒 安全建议

### 生产环境部署
- 修改默认密钥
- 使用HTTPS
- 配置防火墙
- 定期备份数据

### 用户管理
- 定期更换密码
- 禁用不必要的用户
- 监控登录日志

## 📞 技术支持

### 日志查看
- 应用日志: `logs/app.log`
- 任务错误: 在任务详情页面查看

### 常用命令
```bash
# 查看系统状态
python manage.py check-health

# 重启系统
# 停止: Ctrl+C
# 启动: python run.py

# 备份数据库
mysqldump -u root -p rfm_office > backup.sql
```

---

🎉 恭喜！您已成功部署RFM报表处理系统。

如需更多帮助，请参考 README.md 和 PROJECT_STRUCTURE.md 文件。
