{% extends "base.html" %}

{% block title %}统计报表 - 老客回访与跟进系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-chart-bar"></i> 统计报表</h2>
        <p class="text-muted">查看客户数据统计和业务分析</p>
    </div>
</div>

<div class="row">
    <div class="col-md-4 mb-3">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-chart-line fa-3x text-primary mb-3"></i>
                <h5>数据统计</h5>
                <p class="text-muted">查看客户登记、跟进等统计数据</p>
                <a href="{{ url_for('report.statistics') }}" class="btn btn-primary">
                    <i class="fas fa-chart-bar"></i> 查看统计
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-file-excel fa-3x text-success mb-3"></i>
                <h5>数据导出</h5>
                <p class="text-muted">导出客户数据和报表</p>
                <button class="btn btn-success" onclick="exportData()">
                    <i class="fas fa-download"></i> 导出数据
                </button>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-chart-pie fa-3x text-warning mb-3"></i>
                <h5>渠道分析</h5>
                <p class="text-muted">分析各渠道的效果</p>
                <button class="btn btn-warning" onclick="channelAnalysis()">
                    <i class="fas fa-chart-pie"></i> 渠道分析
                </button>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-tachometer-alt"></i> 数据概览</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="border-end">
                            <h3 class="text-primary" id="total-customers">-</h3>
                            <p class="text-muted">总客户数</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border-end">
                            <h3 class="text-success" id="followed-customers">-</h3>
                            <p class="text-muted">已跟进客户</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border-end">
                            <h3 class="text-warning" id="pending-customers">-</h3>
                            <p class="text-muted">待跟进客户</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <h3 class="text-info" id="conversion-rate">-</h3>
                        <p class="text-muted">转化率</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-calendar"></i> 本月趋势</h5>
            </div>
            <div class="card-body">
                <canvas id="monthlyTrendChart" height="200"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-tags"></i> 渠道分布</h5>
            </div>
            <div class="card-body">
                <canvas id="channelDistributionChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-list"></i> 快速报表</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>按时间查看</h6>
                        <div class="list-group">
                            <a href="#" class="list-group-item list-group-item-action" onclick="viewReport('today')">
                                <i class="fas fa-calendar-day"></i> 今日数据
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" onclick="viewReport('week')">
                                <i class="fas fa-calendar-week"></i> 本周数据
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" onclick="viewReport('month')">
                                <i class="fas fa-calendar-alt"></i> 本月数据
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>按类型查看</h6>
                        <div class="list-group">
                            <a href="#" class="list-group-item list-group-item-action" onclick="viewReport('consultant')">
                                <i class="fas fa-user-tie"></i> 顾问业绩
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" onclick="viewReport('channel')">
                                <i class="fas fa-tags"></i> 渠道效果
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" onclick="viewReport('follow-up')">
                                <i class="fas fa-comments"></i> 跟进情况
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// 页面加载完成后初始化数据
document.addEventListener('DOMContentLoaded', function() {
    loadOverviewData();
    initCharts();
});

function loadOverviewData() {
    // 这里可以通过AJAX加载实际数据
    // 暂时显示示例数据
    document.getElementById('total-customers').textContent = '0';
    document.getElementById('followed-customers').textContent = '0';
    document.getElementById('pending-customers').textContent = '0';
    document.getElementById('conversion-rate').textContent = '0%';
}

function initCharts() {
    // 月度趋势图
    const monthlyCtx = document.getElementById('monthlyTrendChart').getContext('2d');
    new Chart(monthlyCtx, {
        type: 'line',
        data: {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
            datasets: [{
                label: '客户登记数',
                data: [0, 0, 0, 0, 0, 0],
                borderColor: 'rgb(75, 192, 192)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
    
    // 渠道分布图
    const channelCtx = document.getElementById('channelDistributionChart').getContext('2d');
    new Chart(channelCtx, {
        type: 'doughnut',
        data: {
            labels: ['线上推广', '线下活动', '老客推荐', '其他'],
            datasets: [{
                data: [0, 0, 0, 0],
                backgroundColor: [
                    'rgb(255, 99, 132)',
                    'rgb(54, 162, 235)',
                    'rgb(255, 205, 86)',
                    'rgb(75, 192, 192)'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

function exportData() {
    alert('数据导出功能待实现');
}

function channelAnalysis() {
    alert('渠道分析功能待实现');
}

function viewReport(type) {
    alert('查看 ' + type + ' 报表功能待实现');
}
</script>
{% endblock %}
