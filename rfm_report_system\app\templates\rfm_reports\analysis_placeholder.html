{% extends "base.html" %}

{% block title %}{{ analysis_type }} - {{ report_type }}报表{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 页面标题 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-chart-bar text-primary me-2"></i>
                    {{ analysis_type }} 分析
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">首页</a></li>
                        <li class="breadcrumb-item"><a href="{{ url_for('rfm_reports.index') }}">RFM报表</a></li>
                        <li class="breadcrumb-item">
                            <a href="{{ url_for('rfm_reports.' + report_type + '_panel') }}">
                                {{ report_type }}报表
                            </a>
                        </li>
                        <li class="breadcrumb-item active">{{ analysis_type }}</li>
                    </ol>
                </nav>
            </div>

            <!-- 分析内容 -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                分析详情
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if data_source %}
                            <div class="alert alert-info">
                                <h6 class="alert-heading">
                                    <i class="fas fa-database me-2"></i>
                                    数据源信息
                                </h6>
                                <p class="mb-2"><strong>数据源名称：</strong>{{ data_source.source_task_name }}</p>
                                <p class="mb-2"><strong>报表类型：</strong>{{ data_source.report_type }}</p>
                                <p class="mb-2"><strong>数据记录：</strong>{{ data_source.imported_records }} 条</p>
                                <p class="mb-2"><strong>包含季度：</strong>{{ data_source.quarters | join(', ') }}</p>
                                <p class="mb-0"><strong>导入时间：</strong>{{ data_source.imported_at }}</p>
                            </div>
                            {% endif %}

                            <div class="alert alert-warning">
                                <h6 class="alert-heading">
                                    <i class="fas fa-construction me-2"></i>
                                    功能开发中
                                </h6>
                                <p class="mb-2">
                                    <strong>分析类型：</strong>{{ analysis_type }}<br>
                                    <strong>所属模块：</strong>{{ section }}<br>
                                    {% if sub_analysis %}
                                    <strong>子分析：</strong>{{ sub_analysis }}<br>
                                    {% endif %}
                                </p>
                                <hr>
                                <p class="mb-0">
                                    此分析功能正在开发中，敬请期待！完成后将提供详细的数据分析、图表展示和导出功能。
                                </p>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                            <h6 class="text-muted">数据分析</h6>
                                            <p class="small text-muted">多维度数据分析和趋势展示</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <i class="fas fa-download fa-3x text-muted mb-3"></i>
                                            <h6 class="text-muted">数据导出</h6>
                                            <p class="small text-muted">支持Excel、PDF等格式导出</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="d-flex justify-content-between">
                                <button class="btn btn-secondary" onclick="history.back()">
                                    <i class="fas fa-arrow-left me-1"></i> 返回
                                </button>
                                <div>
                                    <button class="btn btn-outline-primary" disabled>
                                        <i class="fas fa-chart-bar me-1"></i> 查看分析
                                    </button>
                                    <button class="btn btn-outline-success" disabled>
                                        <i class="fas fa-download me-1"></i> 导出数据
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
