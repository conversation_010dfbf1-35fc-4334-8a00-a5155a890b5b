# -*- coding: utf-8 -*-
"""
基础Repository类
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.exc import SQLAlchemyError
from app import db

class BaseRepository:
    """基础Repository类，提供通用的数据访问方法"""
    
    def __init__(self, model_class):
        self.model_class = model_class
    
    def create(self, data: Dict[str, Any]) -> Optional[Any]:
        """创建新记录"""
        try:
            entity = self.model_class(**data)
            db.session.add(entity)
            db.session.commit()
            return entity
        except SQLAlchemyError as e:
            db.session.rollback()
            raise e
    
    def get_by_id(self, entity_id: int) -> Optional[Any]:
        """根据ID获取记录"""
        return self.model_class.query.get(entity_id)
    
    def get_all(self) -> List[Any]:
        """获取所有记录"""
        return self.model_class.query.all()
    
    def get_active(self) -> List[Any]:
        """获取所有激活的记录（如果模型有is_active字段）"""
        if hasattr(self.model_class, 'is_active'):
            return self.model_class.query.filter_by(is_active=True).all()
        return self.get_all()
    
    def update(self, entity_id: int, data: Dict[str, Any]) -> bool:
        """更新记录"""
        try:
            entity = self.get_by_id(entity_id)
            if not entity:
                return False
            
            for key, value in data.items():
                if hasattr(entity, key):
                    setattr(entity, key, value)
            
            db.session.commit()
            return True
        except SQLAlchemyError as e:
            db.session.rollback()
            raise e
    
    def delete(self, entity_id: int) -> bool:
        """删除记录"""
        try:
            entity = self.get_by_id(entity_id)
            if not entity:
                return False
            
            db.session.delete(entity)
            db.session.commit()
            return True
        except SQLAlchemyError as e:
            db.session.rollback()
            raise e
    
    def soft_delete(self, entity_id: int) -> bool:
        """软删除（设置is_active为False）"""
        if hasattr(self.model_class, 'is_active'):
            return self.update(entity_id, {'is_active': False})
        return self.delete(entity_id)
    
    def find_by_criteria(self, criteria: Dict[str, Any]) -> List[Any]:
        """根据条件查找记录"""
        query = self.model_class.query
        for key, value in criteria.items():
            if hasattr(self.model_class, key):
                query = query.filter(getattr(self.model_class, key) == value)
        return query.all()
    
    def find_one_by_criteria(self, criteria: Dict[str, Any]) -> Optional[Any]:
        """根据条件查找单个记录"""
        query = self.model_class.query
        for key, value in criteria.items():
            if hasattr(self.model_class, key):
                query = query.filter(getattr(self.model_class, key) == value)
        return query.first()
    
    def count(self, criteria: Dict[str, Any] = None) -> int:
        """统计记录数量"""
        query = self.model_class.query
        if criteria:
            for key, value in criteria.items():
                if hasattr(self.model_class, key):
                    query = query.filter(getattr(self.model_class, key) == value)
        return query.count()
    
    def paginate(self, page: int = 1, per_page: int = 20, criteria: Dict[str, Any] = None):
        """分页查询"""
        query = self.model_class.query
        if criteria:
            for key, value in criteria.items():
                if hasattr(self.model_class, key):
                    query = query.filter(getattr(self.model_class, key) == value)
        return query.paginate(page=page, per_page=per_page, error_out=False)
    
    def exists(self, criteria: Dict[str, Any]) -> bool:
        """检查记录是否存在"""
        return self.find_one_by_criteria(criteria) is not None