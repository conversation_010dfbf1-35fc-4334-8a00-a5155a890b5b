#!/usr/bin/env python3
"""
系统环境检查脚本
检查系统依赖和配置是否正确
"""

import sys
import os
import importlib
import subprocess
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✓ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"✗ Python版本过低: {version.major}.{version.minor}.{version.micro}")
        print("  需要Python 3.8或更高版本")
        return False

def check_required_packages():
    """检查必需的Python包"""
    print("\n检查Python包...")
    
    required_packages = [
        'flask',
        'flask_sqlalchemy',
        'flask_login',
        'flask_wtf',
        'wtforms',
        'pymysql',
        'pandas',
        'openpyxl',
        'xlsxwriter',
        'python_dotenv',
        'bcrypt',
        'cryptography',
        'werkzeug',
        'jinja2',
        'click',
        'itsdangerous',
        'markupsafe'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            # 特殊处理一些包名
            import_name = package
            if package == 'python_dotenv':
                import_name = 'dotenv'
            elif package == 'flask_sqlalchemy':
                import_name = 'flask_sqlalchemy'
            elif package == 'flask_login':
                import_name = 'flask_login'
            elif package == 'flask_wtf':
                import_name = 'flask_wtf'
            
            importlib.import_module(import_name)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} (未安装)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少以下包，请运行: pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_mysql_connectivity():
    """检查MySQL连接"""
    print("\n检查MySQL连接...")
    try:
        import pymysql
        # 这里只是检查pymysql是否可用，不实际连接数据库
        print("✓ PyMySQL驱动可用")
        print("  注意: 请确保MySQL服务器正在运行并且连接参数正确")
        return True
    except ImportError:
        print("✗ PyMySQL驱动不可用")
        return False

def check_file_structure():
    """检查文件结构"""
    print("\n检查文件结构...")
    
    required_files = [
        'app.py',
        'run.py',
        'init_db.py',
        'config.py',
        'requirements.txt',
        'app/__init__.py',
        'app/models.py',
        'app/auth/__init__.py',
        'app/main/__init__.py',
        'app/config_mgmt/__init__.py',
        'app/data_processing/__init__.py',
        'app/templates/base.html'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path} (缺失)")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n缺少以下文件: {', '.join(missing_files)}")
        return False
    
    return True

def check_environment_config():
    """检查环境配置"""
    print("\n检查环境配置...")
    
    if os.path.exists('.env'):
        print("✓ .env 配置文件存在")
        
        # 读取配置文件检查关键配置
        try:
            from dotenv import load_dotenv
            load_dotenv()
            
            secret_key = os.getenv('SECRET_KEY')
            if secret_key and secret_key != 'your-very-secret-key-here':
                print("✓ SECRET_KEY 已配置")
            else:
                print("⚠ SECRET_KEY 使用默认值，建议修改")
            
            db_host = os.getenv('SYSTEM_DB_HOST')
            if db_host:
                print(f"✓ 数据库主机: {db_host}")
            else:
                print("⚠ 数据库主机未配置，将使用默认值")
            
        except Exception as e:
            print(f"⚠ 读取配置文件时出错: {e}")
        
        return True
    else:
        print("✗ .env 配置文件不存在")
        if os.path.exists('.env.example'):
            print("  可以复制 .env.example 为 .env 并修改配置")
        return False

def check_directories():
    """检查必要的目录"""
    print("\n检查目录结构...")
    
    required_dirs = [
        'app/templates',
        'app/templates/auth',
        'app/templates/main',
        'app/templates/config_mgmt',
        'app/templates/data_processing',
        'app/templates/errors',
        'logs',
        'uploads',
        'uploads/results'
    ]
    
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"✓ {dir_path}/")
        else:
            print(f"⚠ {dir_path}/ (不存在，将自动创建)")
            try:
                os.makedirs(dir_path, exist_ok=True)
                print(f"  已创建目录: {dir_path}/")
            except Exception as e:
                print(f"  创建目录失败: {e}")

def check_permissions():
    """检查文件权限"""
    print("\n检查文件权限...")
    
    # 检查当前目录的写权限
    try:
        test_file = 'test_write_permission.tmp'
        with open(test_file, 'w') as f:
            f.write('test')
        os.remove(test_file)
        print("✓ 当前目录具有写权限")
    except Exception as e:
        print(f"✗ 当前目录没有写权限: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("RFM报表处理系统环境检查")
    print("=" * 40)
    
    checks = [
        check_python_version,
        check_required_packages,
        check_mysql_connectivity,
        check_file_structure,
        check_environment_config,
        check_directories,
        check_permissions
    ]
    
    results = []
    for check in checks:
        try:
            result = check()
            results.append(result)
        except Exception as e:
            print(f"✗ 检查过程中出错: {e}")
            results.append(False)
    
    print("\n" + "=" * 40)
    print("检查结果汇总:")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✓ 所有检查通过 ({passed}/{total})")
        print("\n系统环境配置正确，可以启动系统")
        return True
    else:
        print(f"⚠ 部分检查未通过 ({passed}/{total})")
        print("\n请根据上述提示修复问题后重新检查")
        return False

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n检查已取消")
        sys.exit(1)
    except Exception as e:
        print(f"\n检查过程中发生错误: {e}")
        sys.exit(1)
