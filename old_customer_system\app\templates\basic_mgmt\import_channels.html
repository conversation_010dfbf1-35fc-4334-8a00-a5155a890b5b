{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">批量导入渠道</h1>
    <a href="{{ url_for('basic_mgmt.channels') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>返回渠道管理
    </a>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-upload me-2"></i>上传Excel文件
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.file.label(class="form-label") }}
                        {{ form.file(class="form-control") }}
                        {% if form.file.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.file.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            支持 .xlsx 和 .xls 格式的Excel文件
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('basic_mgmt.channels') }}" class="btn btn-outline-secondary me-md-2">取消</a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>导入说明
                </h5>
            </div>
            <div class="card-body">
                <h6>Excel文件格式要求：</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>必需列：<strong>渠道名称</strong></li>
                    <li><i class="fas fa-check text-success me-2"></i>可选列：渠道分类、简码</li>
                    <li><i class="fas fa-check text-success me-2"></i>第一行为列标题</li>
                    <li><i class="fas fa-check text-success me-2"></i>支持xlsx、xls格式</li>
                </ul>
                
                <hr>
                
                <h6>注意事项：</h6>
                <ul class="list-unstyled text-muted small">
                    <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>重复的渠道名称将被跳过</li>
                    <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>空行将被自动忽略</li>
                    <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>如果简码为空，系统将自动生成</li>
                </ul>
                
                <hr>
                
                <div class="d-grid">
                    <a href="{{ url_for('basic_mgmt.download_channel_template') }}" class="btn btn-outline-primary">
                        <i class="fas fa-download me-2"></i>下载Excel模板
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-table me-2"></i>模板示例
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th>渠道名称</th>
                                <th>渠道分类</th>
                                <th>简码</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>百度推广</td>
                                <td>线上推广</td>
                                <td>BDTG</td>
                            </tr>
                            <tr>
                                <td>微信朋友圈</td>
                                <td>社交媒体</td>
                                <td>WXPYQ</td>
                            </tr>
                            <tr>
                                <td>合作医院</td>
                                <td>合作伙伴</td>
                                <td>HZYY</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 文件选择预览
document.getElementById('file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const fileName = file.name;
        const fileSize = (file.size / 1024 / 1024).toFixed(2);
        console.log(`选择文件: ${fileName} (${fileSize} MB)`);
    }
});
</script>
{% endblock %}