#!/usr/bin/env python3
"""
测试现场映射功能调试
"""

import pandas as pd
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_field_mapping_trigger():
    """测试现场映射功能是否被正确触发"""
    
    print("=== 测试现场映射功能触发 ===")
    
    # 模拟数据处理任务的配置
    mapping_options = {
        'use_level1_mapping': False,
        'use_level2_mapping': False,
        'use_level3_mapping': False,
        'use_field_mapping': True,  # 启用现场映射
        'use_field_group': True     # 启用现场小组映射
    }
    
    print(f"映射选项配置: {mapping_options}")
    
    # 检查现场映射是否启用
    if mapping_options.get('use_field_mapping', False):
        print("✅ 现场映射已启用")
    else:
        print("❌ 现场映射未启用")
    
    # 模拟数据列
    test_columns = [
        '会员卡号',
        '23Q4_现场', '24Q1_现场', '24Q2_现场', '24Q3_现场',
        '23Q4_综合等级', '24Q1_综合等级', '24Q2_综合等级', '24Q3_综合等级',
        '23Q4_执行业绩', '24Q1_执行业绩', '24Q2_执行业绩', '24Q3_执行业绩'
    ]
    
    print(f"\n模拟数据列: {test_columns}")
    
    # 查找现场相关列
    field_columns = []
    for col in test_columns:
        if '现场' in col and '现场小组' not in col:
            field_columns.append(col)
    
    print(f"找到的现场列: {field_columns}")
    
    if field_columns:
        print("✅ 找到现场列，应该会添加现场小组和最新现场列")
    else:
        print("❌ 未找到现场列，不会添加现场小组和最新现场列")
    
    # 模拟现场映射处理
    if mapping_options.get('use_field_mapping', False) and field_columns:
        print("\n=== 模拟现场映射处理 ===")
        
        # 创建测试数据
        test_data = {
            '会员卡号': ['000001', '000002', '000003'],
            '23Q4_现场': ['张三', '', '王五'],
            '24Q1_现场': ['', '李四', ''],
            '24Q2_现场': ['', '', '赵六'],
            '24Q3_现场': ['不在盘内', '不在盘内', '不在盘内']
        }
        
        df = pd.DataFrame(test_data)
        print("原始数据:")
        print(df)
        
        # 模拟最新现场计算
        def get_latest_field(row, field_columns):
            """获取最新现场值"""
            # 按时间排序（从新到旧）
            sorted_columns = ['24Q3_现场', '24Q2_现场', '24Q1_现场', '23Q4_现场']
            
            for col in sorted_columns:
                if col in field_columns:
                    value = row.get(col)
                    if pd.notna(value) and value != '' and value != '不在盘内':
                        return value
            return '其他咨询'
        
        # 计算最新现场
        latest_fields = []
        for _, row in df.iterrows():
            latest_field = get_latest_field(row, field_columns)
            latest_fields.append(latest_field)
        
        # 模拟现场映射
        field_mappings = {
            '张三': '李四',
            '王五': '赵六'
        }
        
        field_group_mappings = {
            '李四': '王婆小组',
            '赵六': '李婆小组'
        }
        
        mapped_fields = []
        field_groups = []
        
        for field in latest_fields:
            if field and field != '其他咨询':
                mapped_field = field_mappings.get(field, field)
                field_group = field_group_mappings.get(mapped_field, '')
            else:
                mapped_field = field
                field_group = ''
            
            mapped_fields.append(mapped_field)
            field_groups.append(field_group)
        
        # 添加新列
        df.insert(1, '现场小组', field_groups)
        df.insert(2, '最新现场', mapped_fields)
        
        print("\n处理后数据:")
        print(df)
        
        print("\n✅ 现场映射功能模拟完成")
        print(f"现场小组列: {field_groups}")
        print(f"最新现场列: {mapped_fields}")
    
    else:
        print("\n❌ 现场映射功能未触发")

def check_form_configuration():
    """检查表单配置"""
    print("\n=== 检查表单配置 ===")
    
    # 检查前端表单是否正确配置
    template_path = "app/templates/data_processing/new_task.html"
    
    if os.path.exists(template_path):
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        if 'use_field_mapping' in content:
            print("✅ 前端模板包含现场映射选项")
        else:
            print("❌ 前端模板缺少现场映射选项")
            
        if '同时添加咨询小组和最新咨询列' in content:
            print("✅ 前端模板包含现场映射说明")
        else:
            print("❌ 前端模板缺少现场映射说明")
    else:
        print("❌ 前端模板文件不存在")

if __name__ == "__main__":
    test_field_mapping_trigger()
    check_form_configuration()
