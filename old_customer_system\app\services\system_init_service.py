# -*- coding: utf-8 -*-
"""
系统初始化服务
"""

import os
from typing import Dict, <PERSON><PERSON>, Optional
from flask import current_app
from app import db
from app.models import User, Department, SystemSetting
from app.services.database_service import DatabaseService

class SystemInitService:
    """系统初始化服务"""
    
    @staticmethod
    def check_system_status() -> Dict:
        """检查系统状态"""
        status = {
            'database_connected': False,
            'tables_created': False,
            'admin_exists': False,
            'system_initialized': False
        }
        
        try:
            # 检查数据库连接
            from sqlalchemy import text
            db.session.execute(text('SELECT 1'))
            status['database_connected'] = True
            
            # 检查表是否存在
            from sqlalchemy import inspect
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()
            required_tables = ['users', 'departments', 'channels', 'customers', 'system_settings']
            
            if all(table in tables for table in required_tables):
                status['tables_created'] = True
                
                # 检查管理员是否存在
                admin_count = User.query.filter_by(role='admin').count()
                if admin_count > 0:
                    status['admin_exists'] = True
                    status['system_initialized'] = True
            
        except Exception as e:
            current_app.logger.error(f"检查系统状态失败: {str(e)}")
        
        return status
    
    @staticmethod
    def initialize_database(db_config: Dict) -> Tuple[bool, str]:
        """初始化数据库"""
        try:
            host = db_config.get('host', 'localhost')
            port = int(db_config.get('port', 3306))
            username = db_config.get('username', 'root')
            password = db_config.get('password', '')
            database = db_config.get('database', 'Old_Customer_System')
            
            # 测试连接
            success, message = DatabaseService.test_connection(host, port, username, password)
            if not success:
                return False, message
            
            # 检查数据库是否存在
            db_exists, db_message = DatabaseService.check_database_exists(host, port, username, password, database)
            
            if not db_exists:
                # 创建数据库
                success, message = DatabaseService.create_database(host, port, username, password, database)
                if not success:
                    return False, message
            
            # 更新应用配置
            connection_string = f"mysql+pymysql://{username}:{password}@{host}:{port}/{database}?charset=utf8mb4"
            current_app.config['SQLALCHEMY_DATABASE_URI'] = connection_string
            
            # 直接使用SQLAlchemy创建表，不依赖Flask-SQLAlchemy的重新初始化
            from sqlalchemy import create_engine, MetaData
            from app.models import User, Department, Channel, Customer, OnsiteGroup, OnsiteGroupMember, BusinessData, SystemSetting
            
            # 创建新的引擎
            engine = create_engine(connection_string, **current_app.config.get('SQLALCHEMY_ENGINE_OPTIONS', {}))
            
            # 创建所有表
            db.metadata.create_all(engine)
            
            # 关闭引擎连接
            engine.dispose()
            
            # 创建默认数据
            SystemInitService._create_default_data()
            
            return True, "数据库初始化成功"
            
        except Exception as e:
            return False, f"数据库初始化失败: {str(e)}"
    
    @staticmethod
    def reset_database(db_config: Dict) -> Tuple[bool, str]:
        """重置数据库（清空数据并重建）"""
        try:
            host = db_config.get('host', 'localhost')
            port = int(db_config.get('port', 3306))
            username = db_config.get('username', 'root')
            password = db_config.get('password', '')
            database = db_config.get('database', 'Old_Customer_System')
            
            # 删除数据库
            success, message = DatabaseService.drop_database(host, port, username, password, database)
            if not success:
                return False, message
            
            # 重新初始化
            return SystemInitService.initialize_database(db_config)
            
        except Exception as e:
            return False, f"重置数据库失败: {str(e)}"
    
    @staticmethod
    def _create_default_data():
        """创建默认数据"""
        try:
            # 创建默认部门
            if not Department.query.first():
                departments = [
                    Department(name='管理部', description='系统管理部门'),
                    Department(name='网络咨询部', description='网络咨询部门'),
                    Department(name='现场咨询部', description='现场咨询部门'),
                ]
                for dept in departments:
                    db.session.add(dept)
            
            # 创建默认系统设置
            default_settings = [
                ('card_number_length', '10', '卡号长度设置'),
                ('system_name', '老客登记信息反馈系统', '系统名称'),
                ('version', '1.0.0', '系统版本'),
                ('initialized', 'true', '系统是否已初始化'),
            ]
            
            for key, value, desc in default_settings:
                if not SystemSetting.query.filter_by(setting_key=key).first():
                    setting = SystemSetting(
                        setting_key=key,
                        setting_value=value,
                        description=desc
                    )
                    db.session.add(setting)
            
            db.session.commit()
            
        except Exception as e:
            db.session.rollback()
            raise e
    
    @staticmethod
    def save_database_config(db_config: Dict) -> bool:
        """保存数据库配置到环境文件"""
        try:
            env_file = os.path.join(current_app.root_path, '..', '.env')
            
            # 构建数据库连接字符串
            connection_string = f"mysql+pymysql://{db_config['username']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}?charset=utf8mb4"
            
            # 读取现有的.env文件内容
            env_content = []
            if os.path.exists(env_file):
                with open(env_file, 'r', encoding='utf-8') as f:
                    env_content = f.readlines()
            
            # 更新或添加数据库配置
            database_url_found = False
            for i, line in enumerate(env_content):
                if line.startswith('DATABASE_URL='):
                    env_content[i] = f'DATABASE_URL={connection_string}\n'
                    database_url_found = True
                    break
            
            if not database_url_found:
                env_content.append(f'DATABASE_URL={connection_string}\n')
            
            # 写入.env文件
            with open(env_file, 'w', encoding='utf-8') as f:
                f.writelines(env_content)
            
            return True
            
        except Exception as e:
            current_app.logger.error(f"保存数据库配置失败: {str(e)}")
            return False
    
    @staticmethod
    def get_database_config_from_env() -> Optional[Dict]:
        """从环境变量获取数据库配置"""
        try:
            database_url = current_app.config.get('SQLALCHEMY_DATABASE_URI')
            if not database_url:
                return None
            
            # 解析数据库连接字符串
            # 格式: mysql+pymysql://username:password@host:port/database?charset=utf8mb4
            import re
            pattern = r'mysql\+pymysql://([^:]+):([^@]*)@([^:]+):(\d+)/([^?]+)'
            match = re.match(pattern, database_url)
            
            if match:
                return {
                    'username': match.group(1),
                    'password': match.group(2),
                    'host': match.group(3),
                    'port': int(match.group(4)),
                    'database': match.group(5)
                }
            
            return None
            
        except Exception as e:
            current_app.logger.error(f"解析数据库配置失败: {str(e)}")
            return None