{% extends "base.html" %}

{% block title %}数据统计 - 老客回访与跟进系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-chart-bar"></i> 数据统计</h2>
        <p class="text-muted">查看客户登记、跟进等统计数据</p>
    </div>
</div>

<!-- 筛选条件 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-filter"></i> 筛选条件</h5>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="start_date" class="form-label">开始日期</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" 
                               value="{{ request.args.get('start_date', '') }}">
                    </div>
                    <div class="col-md-3">
                        <label for="end_date" class="form-label">结束日期</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" 
                               value="{{ request.args.get('end_date', '') }}">
                    </div>
                    <div class="col-md-3">
                        <label for="consultant_id" class="form-label">现场顾问</label>
                        <select class="form-select" id="consultant_id" name="consultant_id">
                            <option value="">全部</option>
                            {% for consultant in consultants %}
                            <option value="{{ consultant.id }}" 
                                    {% if request.args.get('consultant_id') == consultant.id|string %}selected{% endif %}>
                                {{ consultant.real_name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="channel_id" class="form-label">激活渠道</label>
                        <select class="form-select" id="channel_id" name="channel_id">
                            <option value="">全部</option>
                            {% for channel in channels %}
                            <option value="{{ channel.id }}" 
                                    {% if request.args.get('channel_id') == channel.id|string %}selected{% endif %}>
                                {{ channel.channel_name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> 查询
                        </button>
                        <a href="{{ url_for('report.statistics') }}" class="btn btn-secondary">
                            <i class="fas fa-redo"></i> 重置
                        </a>
                        <button type="button" class="btn btn-success" onclick="exportData()">
                            <i class="fas fa-download"></i> 导出
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>总客户数</h4>
                        <h2>{{ statistics.total_customers }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>待跟进</h4>
                        <h2>{{ statistics.pending_follow_up }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>跟进后到院</h4>
                        <h2>{{ statistics.visited_after_follow_up }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>总消费金额</h4>
                        <h2>¥{{ "%.2f"|format(statistics.total_consumption) }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-yen-sign fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 比率统计 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-percentage"></i> 跟进率</h5>
            </div>
            <div class="card-body">
                <div class="progress mb-2" style="height: 25px;">
                    {% set follow_up_rate = ((statistics.total_customers - statistics.pending_follow_up) / statistics.total_customers * 100) if statistics.total_customers > 0 else 0 %}
                    <div class="progress-bar bg-success" style="width: {{ follow_up_rate }}%">
                        {{ "%.1f"|format(follow_up_rate) }}%
                    </div>
                </div>
                <small class="text-muted">
                    已跟进 {{ statistics.total_customers - statistics.pending_follow_up }} / {{ statistics.total_customers }} 位客户
                </small>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-line"></i> 转化率</h5>
            </div>
            <div class="card-body">
                <div class="progress mb-2" style="height: 25px;">
                    {% set conversion_rate = (statistics.visited_after_follow_up / statistics.total_customers * 100) if statistics.total_customers > 0 else 0 %}
                    <div class="progress-bar bg-primary" style="width: {{ conversion_rate }}%">
                        {{ "%.1f"|format(conversion_rate) }}%
                    </div>
                </div>
                <small class="text-muted">
                    {{ statistics.visited_after_follow_up }} / {{ statistics.total_customers }} 位客户跟进后到院
                </small>
            </div>
        </div>
    </div>
</div>

<!-- 详细数据表格 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-table"></i> 详细数据</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>统计说明：</strong>
                    <ul class="mb-0 mt-2">
                        <li>总客户数：在选定时间范围内登记的客户总数</li>
                        <li>待跟进：还没有任何跟进记录的客户数量</li>
                        <li>跟进后到院：有跟进记录且有消费记录的客户数量</li>
                        <li>总消费金额：所有客户的消费金额总和</li>
                    </ul>
                </div>
                
                {% if statistics.total_customers == 0 %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-chart-bar fa-3x mb-3"></i>
                    <p>当前筛选条件下暂无数据</p>
                    <p class="small">请调整筛选条件或检查数据录入情况</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function exportData() {
    // 获取当前筛选条件
    const params = new URLSearchParams(window.location.search);
    
    // 构建导出URL
    const exportUrl = '{{ url_for("report.statistics") }}?' + params.toString() + '&export=excel';
    
    // 创建下载链接
    const link = document.createElement('a');
    link.href = exportUrl;
    link.download = '统计数据_' + new Date().toISOString().split('T')[0] + '.xlsx';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 设置默认日期范围（最近30天）
document.addEventListener('DOMContentLoaded', function() {
    const startDate = document.getElementById('start_date');
    const endDate = document.getElementById('end_date');
    
    if (!startDate.value && !endDate.value) {
        const today = new Date();
        const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
        
        endDate.value = today.toISOString().split('T')[0];
        startDate.value = thirtyDaysAgo.toISOString().split('T')[0];
    }
});
</script>
{% endblock %}
