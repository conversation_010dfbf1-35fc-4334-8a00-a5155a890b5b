#!/usr/bin/env python3
"""系统资源监控工具"""

import time
import logging
from typing import Dict, <PERSON><PERSON>

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    logging.warning("psutil未安装，将使用默认批次大小")


class ResourceMonitor:
    """系统资源监控器"""
    
    def __init__(self, cpu_threshold: float = 80.0, memory_threshold: float = 80.0):
        """
        初始化资源监控器
        
        Args:
            cpu_threshold: CPU使用率阈值 (%)
            memory_threshold: 内存使用率阈值 (%)
        """
        self.cpu_threshold = cpu_threshold
        self.memory_threshold = memory_threshold
        self.logger = logging.getLogger(__name__)
        
        # 批次大小配置
        self.min_batch_size = 10      # 最小批次大小
        self.max_batch_size = 500     # 最大批次大小
        self.default_batch_size = 50  # 默认批次大小
        
        # 性能历史记录
        self.performance_history = []
        self.max_history_size = 10
    
    def get_system_resources(self) -> Dict[str, float]:
        """获取当前系统资源使用情况"""
        if not PSUTIL_AVAILABLE:
            return {
                'cpu_percent': 50.0,  # 默认值
                'memory_percent': 50.0,
                'available': False
            }
        
        try:
            # 获取CPU使用率 (1秒采样)
            cpu_percent = psutil.cpu_percent(interval=0.1)
            
            # 获取内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # 获取磁盘IO (可选)
            disk_io = psutil.disk_io_counters()
            
            return {
                'cpu_percent': cpu_percent,
                'memory_percent': memory_percent,
                'memory_available_gb': memory.available / (1024**3),
                'disk_read_mb_s': getattr(disk_io, 'read_bytes', 0) / (1024**2) if disk_io else 0,
                'disk_write_mb_s': getattr(disk_io, 'write_bytes', 0) / (1024**2) if disk_io else 0,
                'available': True
            }
        except Exception as e:
            self.logger.error(f"获取系统资源失败: {e}")
            return {
                'cpu_percent': 50.0,
                'memory_percent': 50.0,
                'available': False
            }
    
    def calculate_optimal_batch_size(self, operation_type: str = 'import') -> int:
        """
        根据系统资源计算最优批次大小
        
        Args:
            operation_type: 操作类型 ('import' 或 'delete')
            
        Returns:
            最优批次大小
        """
        resources = self.get_system_resources()
        
        if not resources['available']:
            return self.default_batch_size
        
        cpu_percent = resources['cpu_percent']
        memory_percent = resources['memory_percent']
        
        # 计算资源利用率
        cpu_utilization = cpu_percent / 100.0
        memory_utilization = memory_percent / 100.0
        
        # 计算可用资源余量
        cpu_headroom = max(0, (self.cpu_threshold - cpu_percent) / self.cpu_threshold)
        memory_headroom = max(0, (self.memory_threshold - memory_percent) / self.memory_threshold)
        
        # 取较小的余量作为限制因子
        resource_headroom = min(cpu_headroom, memory_headroom)
        
        # 根据资源余量计算批次大小
        if resource_headroom > 0.5:  # 资源充足
            batch_size = int(self.max_batch_size * 0.8)  # 使用80%的最大批次
        elif resource_headroom > 0.3:  # 资源适中
            batch_size = int(self.max_batch_size * 0.5)  # 使用50%的最大批次
        elif resource_headroom > 0.1:  # 资源紧张
            batch_size = int(self.max_batch_size * 0.2)  # 使用20%的最大批次
        else:  # 资源不足
            batch_size = self.min_batch_size
        
        # 根据操作类型调整
        if operation_type == 'delete':
            batch_size = int(batch_size * 1.2)  # 删除操作可以稍微大一些
        
        # 确保在合理范围内
        batch_size = max(self.min_batch_size, min(self.max_batch_size, batch_size))
        
        # 记录性能数据
        self._record_performance(resources, batch_size, operation_type)
        
        self.logger.info(f"系统资源 - CPU: {cpu_percent:.1f}%, 内存: {memory_percent:.1f}%, "
                        f"计算批次大小: {batch_size} ({operation_type})")
        
        return batch_size
    
    def calculate_optimal_delay(self, batch_size: int, operation_type: str = 'import') -> float:
        """
        根据批次大小和系统资源计算最优延迟时间
        
        Args:
            batch_size: 批次大小
            operation_type: 操作类型
            
        Returns:
            延迟时间 (秒)
        """
        resources = self.get_system_resources()
        
        if not resources['available']:
            return 0.05  # 默认50ms
        
        cpu_percent = resources['cpu_percent']
        memory_percent = resources['memory_percent']
        
        # 基础延迟
        base_delay = 0.01  # 10ms
        
        # 根据资源使用率调整延迟
        if cpu_percent > self.cpu_threshold or memory_percent > self.memory_threshold:
            # 资源紧张，增加延迟
            delay_multiplier = 1 + (max(cpu_percent, memory_percent) - 80) / 20
            delay = base_delay * delay_multiplier
        else:
            # 资源充足，减少延迟
            resource_utilization = max(cpu_percent, memory_percent) / 100
            delay = base_delay * (0.5 + resource_utilization * 0.5)
        
        # 根据批次大小调整
        if batch_size > 100:
            delay *= 1.2  # 大批次需要更多延迟
        elif batch_size < 50:
            delay *= 0.8  # 小批次可以减少延迟
        
        # 确保延迟在合理范围内
        delay = max(0.005, min(0.5, delay))  # 5ms到500ms之间
        
        return delay
    
    def _record_performance(self, resources: Dict, batch_size: int, operation_type: str):
        """记录性能数据"""
        performance_data = {
            'timestamp': time.time(),
            'cpu_percent': resources['cpu_percent'],
            'memory_percent': resources['memory_percent'],
            'batch_size': batch_size,
            'operation_type': operation_type
        }
        
        self.performance_history.append(performance_data)
        
        # 保持历史记录大小
        if len(self.performance_history) > self.max_history_size:
            self.performance_history.pop(0)
    
    def get_performance_stats(self) -> Dict:
        """获取性能统计信息"""
        if not self.performance_history:
            return {
                'avg_cpu_percent': 0,
                'avg_memory_percent': 0,
                'avg_batch_size': self.default_batch_size,
                'sample_count': 0,
                'psutil_available': PSUTIL_AVAILABLE
            }

        recent_data = self.performance_history[-5:]  # 最近5次记录

        avg_cpu = sum(d['cpu_percent'] for d in recent_data) / len(recent_data)
        avg_memory = sum(d['memory_percent'] for d in recent_data) / len(recent_data)
        avg_batch_size = sum(d['batch_size'] for d in recent_data) / len(recent_data)

        return {
            'avg_cpu_percent': avg_cpu,
            'avg_memory_percent': avg_memory,
            'avg_batch_size': avg_batch_size,
            'sample_count': len(recent_data),
            'psutil_available': PSUTIL_AVAILABLE
        }


# 全局资源监控器实例
resource_monitor = ResourceMonitor()
