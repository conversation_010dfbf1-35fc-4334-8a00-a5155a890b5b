"""
Excel报表路由
"""

from flask import render_template, request, jsonify, send_file, flash, redirect, url_for
from flask_login import login_required, current_user
import pandas as pd
import os
from datetime import datetime
import json
from sqlalchemy import create_engine

from app.models import DatabaseConfig
from config import Config
from .report_generator import SimpleReportGenerator
from .custom_report_manager import CustomReportManager, create_sample_custom_config
from . import bp


def get_database_engine():
    """获取数据库引擎"""
    db_config = DatabaseConfig.get_active_config()
    if not db_config:
        return None

    try:
        connection_string = db_config.get_connection_string()
        engine = create_engine(connection_string)
        return engine
    except Exception as e:
        print(f"创建数据库引擎失败: {str(e)}")
        return None


@bp.route('/')
@login_required
def index():
    """Excel报表首页"""
    # 确保输出目录存在
    output_dir = os.path.join(Config.UPLOAD_FOLDER, 'excel_reports')
    os.makedirs(output_dir, exist_ok=True)

    # 预定义模板列表
    templates = ['会员RFM分析报表', '科室业绩分析报表', 'TOP品类分析报表']
    return render_template('excel_reports/index.html', templates=templates)


@bp.route('/template/<template_name>')
@login_required
def view_template(template_name):
    """查看报表模板"""
    # 定义模板信息
    templates_info = {
        '会员RFM分析报表': {
            'description': '基于RFM模型的会员价值分析，自动计算R（最近消费）、F（消费频次）、M（消费金额）值，并进行客户分层',
            'columns': [
                '会员卡号', '消费频次', '消费金额', '最近消费日期',
                'R值', 'F值', 'M值', 'R等级', 'F等级', 'M等级',
                '综合等级', '客户价值'
            ],
            'features': [
                '自动计算RFM三个维度的数值和等级',
                '基于消费行为进行客户价值分类',
                '支持VIP客户、重要客户、潜力客户识别',
                '提供最近2年的消费数据分析'
            ]
        },
        '科室业绩分析报表': {
            'description': '各科室业绩统计分析，包含业绩排名、占比分析、客户活跃度等关键指标',
            'columns': [
                '科室', '消费会员数', '消费次数', '总业绩', '平均客单价',
                '最高单笔', '最低单笔', '业绩占比(%)', '业绩等级',
                '客户活跃度', '活跃度等级'
            ],
            'features': [
                '科室业绩排名和占比分析',
                '客户活跃度统计',
                '业绩等级自动分类（A/B/C/D级）',
                '平均客单价和消费频次分析'
            ]
        },
        'TOP品类分析报表': {
            'description': 'TOP20品类业绩分析，包含排名、占比、累计占比等，帮助识别核心盈利品类',
            'columns': [
                '排名', '二级分类', '总业绩', '业绩占比(%)', '累计占比(%)',
                '消费会员数', '消费次数', '平均客单价', 'TOP等级', '品类热度'
            ],
            'features': [
                'TOP20品类业绩排名',
                '业绩占比和累计占比分析',
                'TOP5/TOP10/TOP20等级标识',
                '品类热度分析（热门/一般/冷门）'
            ]
        }
    }

    template = templates_info.get(template_name, {
        'description': f'{template_name} 的详细信息',
        'columns': [],
        'features': []
    })

    return render_template('excel_reports/template_detail.html',
                         template_name=template_name,
                         template=template)


@bp.route('/generate/<template_name>', methods=['POST'])
@login_required
def generate_report(template_name):
    """生成Excel报表"""
    try:
        # 获取数据库引擎
        engine = get_database_engine()
        if not engine:
            return jsonify({'success': False, 'message': '数据库配置不存在或连接失败'})

        # 创建报表生成器
        generator = SimpleReportGenerator(engine)

        # 生成报表数据
        df = generator.generate_report_by_template(template_name)

        if df.empty:
            return jsonify({'success': False, 'message': '没有找到相关数据，请检查数据库中是否有数据'})

        # 保存到Excel文件
        output_dir = os.path.join(Config.UPLOAD_FOLDER, 'excel_reports')
        filename = generator.save_report_to_excel(df, template_name, output_dir)

        return jsonify({
            'success': True,
            'message': f'报表生成成功，共 {len(df)} 行数据',
            'download_url': url_for('excel_reports.download_report', filename=filename)
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'报表生成失败: {str(e)}'})


@bp.route('/download/<filename>')
@login_required
def download_report(filename):
    """下载报表文件"""
    try:
        filepath = os.path.join(Config.UPLOAD_FOLDER, 'excel_reports', filename)
        if os.path.exists(filepath):
            return send_file(filepath, as_attachment=True, download_name=filename)
        else:
            flash('文件不存在或已被删除', 'error')
            return redirect(url_for('excel_reports.index'))
    except Exception as e:
        flash(f'下载失败: {str(e)}', 'error')
        return redirect(url_for('excel_reports.index'))


@bp.route('/custom')
@login_required
def custom_report():
    """自定义报表页面"""
    # 获取数据库引擎
    engine = get_database_engine()
    tables = []
    sample_config = create_sample_custom_config()

    if engine:
        try:
            manager = CustomReportManager(engine)
            tables = manager.get_available_tables()
        except Exception as e:
            flash(f'获取数据表列表失败: {str(e)}', 'warning')

    # Excel公式示例
    formula_examples = {
        "条件判断": [
            "=IF([金额]>1000, \"高消费\", \"普通消费\")",
            "=IF([金额]>50000, \"VIP\", IF([金额]>20000, \"高级\", \"普通\"))"
        ],
        "文本处理": [
            "=CONCATENATE([等级], \"-\", [类型])",
            "=\"客户等级：\" + [等级]"
        ],
        "数学计算": [
            "=[金额] * 0.1",
            "=([收入] - [成本]) / [收入] * 100"
        ]
    }

    return render_template('excel_reports/custom_report.html',
                         tables=tables,
                         sample_config=sample_config,
                         formula_examples=formula_examples)


@bp.route('/custom/generate', methods=['POST'])
@login_required
def generate_custom_report():
    """生成自定义报表"""
    try:
        # 获取JSON数据
        report_config = request.get_json()

        if not report_config:
            return jsonify({'success': False, 'message': '请提供报表配置'})

        # 获取数据库引擎
        engine = get_database_engine()
        if not engine:
            return jsonify({'success': False, 'message': '数据库配置不存在或连接失败'})

        # 创建自定义报表管理器
        manager = CustomReportManager(engine)

        # 生成报表
        df = manager.generate_custom_report(report_config)

        if df.empty:
            return jsonify({'success': False, 'message': '没有生成任何数据，请检查配置'})

        # 保存到Excel文件
        output_dir = os.path.join(Config.UPLOAD_FOLDER, 'excel_reports')
        os.makedirs(output_dir, exist_ok=True)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_name = report_config.get('name', '自定义报表')
        filename = f"{report_name}_{timestamp}.xlsx"
        filepath = os.path.join(output_dir, filename)

        # 保存Excel文件
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name=report_name, index=False)

            # 设置列宽
            worksheet = writer.sheets[report_name]
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        return jsonify({
            'success': True,
            'message': f'自定义报表生成成功，共 {len(df)} 行数据',
            'download_url': url_for('excel_reports.download_report', filename=filename)
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'自定义报表生成失败: {str(e)}'})


@bp.route('/validate_formula', methods=['POST'])
@login_required
def validate_formula():
    """验证Excel公式"""
    return jsonify({'success': True, 'message': '公式验证功能正在开发中'})


@bp.route('/functions')
@login_required
def get_functions():
    """获取可用的Excel函数"""
    functions = {
        "逻辑函数": ["IF", "AND", "OR"],
        "数学函数": ["SUM", "AVERAGE", "COUNT"],
        "文本函数": ["CONCATENATE", "LEFT", "RIGHT"]
    }
    return jsonify({'success': True, 'functions': functions})


@bp.route('/preview', methods=['POST'])
@login_required
def preview_report():
    """预览报表数据"""
    try:
        template_name = request.json.get('template_name', '')

        # 获取数据库引擎
        engine = get_database_engine()
        if not engine:
            return jsonify({'success': False, 'message': '数据库配置不存在或连接失败'})

        # 创建报表生成器
        generator = SimpleReportGenerator(engine)

        # 生成报表数据（限制前10行用于预览）
        df = generator.generate_report_by_template(template_name)

        if df.empty:
            return jsonify({'success': False, 'message': '没有找到相关数据'})

        preview_df = df.head(10)

        # 转换为JSON格式
        preview_data = {
            'columns': list(preview_df.columns),
            'data': preview_df.values.tolist(),
            'total_rows': len(df)
        }

        return jsonify({'success': True, 'preview': preview_data})

    except Exception as e:
        return jsonify({'success': False, 'message': f'预览失败: {str(e)}'})


@bp.route('/api/tables')
@login_required
def get_tables():
    """获取数据表列表"""
    try:
        engine = get_database_engine()
        if not engine:
            return jsonify({'success': False, 'message': '数据库配置不存在或连接失败'})

        manager = CustomReportManager(engine)
        tables = manager.get_available_tables()

        return jsonify({'success': True, 'tables': tables})

    except Exception as e:
        return jsonify({'success': False, 'message': f'获取数据表失败: {str(e)}'})


@bp.route('/api/table_columns/<table_name>')
@login_required
def get_table_columns(table_name):
    """获取指定表的列信息"""
    try:
        engine = get_database_engine()
        if not engine:
            return jsonify({'success': False, 'message': '数据库配置不存在或连接失败'})

        manager = CustomReportManager(engine)
        columns = manager.get_table_columns(table_name)

        return jsonify({'success': True, 'columns': columns})

    except Exception as e:
        return jsonify({'success': False, 'message': f'获取表列信息失败: {str(e)}'})


@bp.route('/api/preview_query', methods=['POST'])
@login_required
def preview_query():
    """预览SQL查询结果"""
    try:
        data = request.get_json()
        query = data.get('query', '')

        if not query:
            return jsonify({'success': False, 'message': '请提供SQL查询'})

        engine = get_database_engine()
        if not engine:
            return jsonify({'success': False, 'message': '数据库配置不存在或连接失败'})

        manager = CustomReportManager(engine)
        result = manager.preview_query_result(query, limit=10)

        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'message': f'查询预览失败: {str(e)}'})


@bp.route('/api/validate_formula', methods=['POST'])
@login_required
def validate_custom_formula():
    """验证自定义公式"""
    try:
        data = request.get_json()
        formula = data.get('formula', '')
        sample_data = data.get('sample_data', {})

        if not formula:
            return jsonify({'success': False, 'message': '请提供公式'})

        # 创建测试DataFrame
        test_df = pd.DataFrame([sample_data] if sample_data else [{'测试列': 100, '测试文本': 'test'}])

        engine = get_database_engine()
        if not engine:
            return jsonify({'success': False, 'message': '数据库配置不存在或连接失败'})

        manager = CustomReportManager(engine)
        result = manager.apply_simple_formula(test_df, formula, 0)

        if result is not None:
            return jsonify({'success': True, 'message': f'公式验证成功，结果: {result}'})
        else:
            return jsonify({'success': False, 'message': '公式验证失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'公式验证失败: {str(e)}'})
