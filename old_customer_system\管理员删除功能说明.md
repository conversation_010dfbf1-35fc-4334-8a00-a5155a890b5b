# 管理员删除功能说明

## 功能概述

为管理员用户添加了客户信息删除功能，支持单个删除和批量删除，确保数据管理的灵活性和安全性。

## 权限控制

### 权限要求
- **角色限制**: 只有管理员（admin）角色可以使用删除功能
- **权限代码**: `delete_customer`
- **权限检查**: 使用 `@require_permission('delete_customer')` 装饰器

### 权限配置
```python
ROLE_PERMISSIONS = {
    'admin': [
        'delete_customer',  # 删除客户权限
        # ... 其他权限
    ],
    # 其他角色没有删除权限
}
```

## 功能特性

### 1. 单个删除
- **触发方式**: 点击客户行操作列的删除按钮
- **确认机制**: 弹出确认对话框显示客户卡号
- **安全提示**: "确定要删除客户 {卡号} 吗？此操作不可恢复！"

### 2. 批量删除
- **选择方式**: 通过复选框选择多个客户
- **全选功能**: 表头复选框可以全选/取消全选
- **批量按钮**: 选中客户后显示"批量删除"按钮
- **确认信息**: 显示所有要删除的客户卡号列表

### 3. 安全机制
- **CSRF保护**: 防止跨站请求伪造攻击
- **权限验证**: 后端双重权限检查
- **事务管理**: 数据库操作使用事务，失败时自动回滚
- **错误处理**: 完善的异常处理和用户提示

## 界面设计

### 表格结构
```
┌──────┬────────┬──────────┬──────────┬──────────┬────────┐
│ 选择 │  卡号  │ 现场顾问 │ 网资顾问 │ 跟进状态 │  操作  │
├──────┼────────┼──────────┼──────────┼──────────┼────────┤
│ ☐   │ 111111 │  张三    │  李四    │  已跟进  │ 👁️✏️🗑️ │
│ ☐   │ 222222 │  王五    │  赵六    │  待跟进  │ 👁️✏️🗑️ │
└──────┴────────┴──────────┴──────────┴──────────┴────────┘
```

### 界面元素
- **复选框列**: 仅管理员可见，用于批量选择
- **全选复选框**: 表头位置，控制所有行的选择状态
- **删除按钮**: 操作列红色垃圾桶图标
- **批量删除按钮**: 顶部工具栏，选中客户后显示

## 技术实现

### 后端路由

#### 单个删除
```python
@bp.route('/delete_customer/<int:customer_id>', methods=['POST'])
@login_required
@require_permission('delete_customer')
def delete_customer(customer_id):
    """删除单个客户"""
    customer = Customer.query.get_or_404(customer_id)
    
    try:
        card_number = customer.card_number
        db.session.delete(customer)
        db.session.commit()
        flash(f'客户 {card_number} 已成功删除', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'删除客户失败：{str(e)}', 'error')
    
    return redirect(url_for('registration.customers'))
```

#### 批量删除
```python
@bp.route('/batch_delete_customers', methods=['POST'])
@login_required
@require_permission('delete_customer')
def batch_delete_customers():
    """批量删除客户"""
    customer_ids = request.form.getlist('customer_ids')
    
    if not customer_ids:
        flash('请选择要删除的客户', 'warning')
        return redirect(url_for('registration.customers'))
    
    try:
        customer_ids = [int(id) for id in customer_ids]
        customers = Customer.query.filter(Customer.id.in_(customer_ids)).all()
        
        deleted_cards = [customer.card_number for customer in customers]
        
        for customer in customers:
            db.session.delete(customer)
        
        db.session.commit()
        flash(f'成功删除 {len(customers)} 个客户：{", ".join(deleted_cards)}', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash(f'批量删除失败：{str(e)}', 'error')
    
    return redirect(url_for('registration.customers'))
```

### 前端交互

#### JavaScript功能
```javascript
// 全选/取消全选
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.customer-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateBatchDeleteButton();
}

// 更新批量删除按钮显示状态
function updateBatchDeleteButton() {
    const checkboxes = document.querySelectorAll('.customer-checkbox:checked');
    const batchDeleteBtn = document.getElementById('batchDeleteBtn');
    
    if (checkboxes.length > 0) {
        batchDeleteBtn.style.display = 'inline-block';
    } else {
        batchDeleteBtn.style.display = 'none';
    }
}

// 单个删除确认
function deleteCustomer(customerId, cardNumber) {
    if (confirm(`确定要删除客户 ${cardNumber} 吗？此操作不可恢复！`)) {
        // 提交删除表单
    }
}

// 批量删除确认
function batchDeleteCustomers() {
    const checkboxes = document.querySelectorAll('.customer-checkbox:checked');
    
    if (checkboxes.length === 0) {
        alert('请选择要删除的客户');
        return;
    }
    
    // 获取客户卡号列表
    const cardNumbers = [];
    checkboxes.forEach(checkbox => {
        const row = checkbox.closest('tr');
        const cardNumber = row.querySelector('td:nth-child(2) strong').textContent;
        cardNumbers.push(cardNumber);
    });
    
    if (confirm(`确定要删除以下 ${checkboxes.length} 个客户吗？\n${cardNumbers.join(', ')}\n\n此操作不可恢复！`)) {
        document.getElementById('batchDeleteForm').submit();
    }
}
```

## 使用说明

### 访问方式
1. 使用管理员账号登录系统
2. 访问客户管理页面：`http://127.0.0.1:5000/registration/customers`
3. 在客户列表中可以看到删除功能

### 操作步骤

#### 单个删除
1. 找到要删除的客户行
2. 点击操作列的红色删除按钮（🗑️）
3. 在确认对话框中点击"确定"
4. 系统显示删除成功提示

#### 批量删除
1. 勾选要删除的客户（可使用表头复选框全选）
2. 点击顶部出现的"批量删除"按钮
3. 在确认对话框中查看要删除的客户列表
4. 点击"确定"执行批量删除
5. 系统显示删除成功提示

### 注意事项
- 删除操作不可恢复，请谨慎操作
- 只有管理员可以看到和使用删除功能
- 删除前会显示确认对话框
- 删除失败时会显示错误信息并回滚操作

## 安全考虑

1. **权限控制**: 严格的角色权限检查
2. **CSRF保护**: 防止跨站请求伪造
3. **数据验证**: 后端验证客户ID的有效性
4. **事务安全**: 使用数据库事务确保数据一致性
5. **错误处理**: 完善的异常处理机制
6. **用户确认**: 删除前的二次确认机制

## 测试验证

系统已通过以下测试：
- ✅ 权限控制测试
- ✅ 单个删除功能测试
- ✅ 批量删除功能测试
- ✅ CSRF保护测试
- ✅ 错误处理测试
- ✅ 界面交互测试

删除功能已完整实现并可正常使用！
