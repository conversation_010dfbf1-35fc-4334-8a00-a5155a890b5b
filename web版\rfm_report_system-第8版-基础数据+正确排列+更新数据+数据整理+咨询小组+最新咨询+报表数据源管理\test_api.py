#!/usr/bin/env python3
"""
测试API路径
"""

from app import create_app

def test_api_routes():
    """测试API路由"""
    app = create_app()
    
    with app.test_client() as client:
        # 测试路由是否存在
        routes = []
        for rule in app.url_map.iter_rules():
            if 'api' in rule.rule:
                routes.append({
                    'endpoint': rule.endpoint,
                    'methods': list(rule.methods),
                    'rule': rule.rule
                })
        
        print("📋 API路由列表:")
        for route in routes:
            print(f"   {route['rule']} -> {route['endpoint']} {route['methods']}")
        
        # 检查特定的API路径
        api_paths = [
            '/data/api/start_task/1',
            '/data/api/stop_task/1',
            '/data/api/task_status/1'
        ]
        
        print(f"\n🧪 测试API路径:")
        for path in api_paths:
            try:
                # 不登录的情况下测试路径是否存在
                response = client.get(path)
                print(f"   {path} -> 状态码: {response.status_code}")
                if response.status_code == 404:
                    print(f"      ❌ 路径不存在")
                elif response.status_code == 302:
                    print(f"      ✅ 路径存在，重定向到登录页面")
                elif response.status_code == 405:
                    print(f"      ✅ 路径存在，但方法不允许")
                else:
                    print(f"      ✅ 路径存在")
            except Exception as e:
                print(f"   {path} -> 错误: {e}")

if __name__ == '__main__':
    test_api_routes()
