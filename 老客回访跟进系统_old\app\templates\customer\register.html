{% extends "base.html" %}

{% block title %}客户登记 - 老客回访与跟进系统{% endblock %}

{% block extra_css %}
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
<style>
.select2-container--bootstrap-5 .select2-selection {
    min-height: 38px;
}
.select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
    line-height: 36px;
}
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-user-plus"></i> 客户登记</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('customer.index') }}">客户管理</a></li>
                <li class="breadcrumb-item active">客户登记</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-user"></i> 客户信息</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="card_number" class="form-label">客户卡号 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="card_number" name="card_number" 
                                       placeholder="请输入客户卡号" required>
                                <div class="form-text">卡号只能包含数字，最多10位</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="last_visit_date" class="form-label">最近来院时间 <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="last_visit_date" name="last_visit_date" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="assigned_consultant_id" class="form-label">分配现场顾问 <span class="text-danger">*</span></label>
                                <select class="form-select" id="assigned_consultant_id" name="assigned_consultant_id" required>
                                    <option value="">请选择现场顾问</option>
                                </select>
                                <div class="form-text">支持输入姓名或拼音首字母搜索</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="activation_channel_id" class="form-label">激活渠道 <span class="text-danger">*</span></label>
                                <select class="form-select" id="activation_channel_id" name="activation_channel_id" required>
                                    <option value="">请选择激活渠道</option>
                                </select>
                                <div class="form-text">支持输入渠道名称或拼音首字母搜索</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="consultation_content" class="form-label">咨询内容 <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="consultation_content" name="consultation_content" 
                                  rows="4" placeholder="请输入客户咨询的具体内容" required></textarea>
                        <div class="form-text">最多500字</div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('customer.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 保存登记
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-info-circle"></i> 登记说明</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li><i class="fas fa-check text-success"></i> 客户卡号必须准确无误</li>
                    <li><i class="fas fa-check text-success"></i> 选择合适的现场顾问进行跟进</li>
                    <li><i class="fas fa-check text-success"></i> 激活渠道用于统计分析</li>
                    <li><i class="fas fa-check text-success"></i> 咨询内容要详细具体</li>
                    <li><i class="fas fa-check text-success"></i> 最近来院时间影响跟进优先级</li>
                </ul>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-users"></i> 现场顾问</h6>
            </div>
            <div class="card-body">
                {% if consultants %}
                    {% for consultant in consultants %}
                    <div class="mb-2">
                        <span class="badge bg-primary">{{ consultant.real_name }}</span>
                        <small class="text-muted">{{ consultant.department }}</small>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted small">暂无可用的现场顾问</p>
                {% endif %}
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-tags"></i> 激活渠道</h6>
            </div>
            <div class="card-body">
                {% if channels %}
                    {% for channel in channels %}
                    <div class="mb-2">
                        <span class="badge bg-info">{{ channel.channel_category }}</span>
                        <small>{{ channel.channel_name }}</small>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted small">暂无可用的激活渠道</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
$(document).ready(function() {
    // 初始化现场顾问选择框
    $('#assigned_consultant_id').select2({
        theme: 'bootstrap-5',
        placeholder: '请选择现场顾问',
        allowClear: true,
        ajax: {
            url: '{{ url_for("api.get_consultants") }}',
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    search: params.term || ''
                };
            },
            processResults: function (data) {
                if (data.success) {
                    return {
                        results: data.data.map(function(item) {
                            return {
                                id: item.id,
                                text: item.text,
                                name: item.name,
                                department: item.department,
                                pinyin: item.pinyin
                            };
                        })
                    };
                }
                return { results: [] };
            },
            cache: true
        },
        minimumInputLength: 0
    });

    // 初始化激活渠道选择框
    $('#activation_channel_id').select2({
        theme: 'bootstrap-5',
        placeholder: '请选择激活渠道',
        allowClear: true,
        ajax: {
            url: '{{ url_for("api.get_channels") }}',
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    search: params.term || ''
                };
            },
            processResults: function (data) {
                if (data.success) {
                    return {
                        results: data.data.map(function(item) {
                            return {
                                id: item.id,
                                text: item.text,
                                category: item.category,
                                name: item.name,
                                pinyin: item.pinyin
                            };
                        })
                    };
                }
                return { results: [] };
            },
            cache: true
        },
        minimumInputLength: 0
    });
});

// 字符计数
document.getElementById('consultation_content').addEventListener('input', function() {
    const maxLength = 500;
    const currentLength = this.value.length;
    const remaining = maxLength - currentLength;

    // 可以添加字符计数显示
    if (remaining < 0) {
        this.value = this.value.substring(0, maxLength);
    }
});

// 卡号验证
document.getElementById('card_number').addEventListener('input', function() {
    // 只允许数字
    this.value = this.value.replace(/[^0-9]/g, '');

    // 限制长度
    if (this.value.length > 10) {
        this.value = this.value.substring(0, 10);
    }
});
</script>
{% endblock %}
