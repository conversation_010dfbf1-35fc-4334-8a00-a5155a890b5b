import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    """基础配置类"""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-change-in-production'

    # 时区配置
    TIMEZONE = 'Asia/Shanghai'  # 中国时区
    
    # 系统管理数据库配置
    SYSTEM_DB_HOST = os.environ.get('SYSTEM_DB_HOST') or 'localhost'
    SYSTEM_DB_PORT = int(os.environ.get('SYSTEM_DB_PORT') or 3306)
    SYSTEM_DB_USER = os.environ.get('SYSTEM_DB_USER') or 'root'
    SYSTEM_DB_PASSWORD = os.environ.get('SYSTEM_DB_PASSWORD') or ''
    SYSTEM_DB_NAME = 'rfm_office'
    
    # SQLAlchemy配置
    SQLALCHEMY_DATABASE_URI = f"mysql+pymysql://{SYSTEM_DB_USER}:{SYSTEM_DB_PASSWORD}@{SYSTEM_DB_HOST}:{SYSTEM_DB_PORT}/{SYSTEM_DB_NAME}?charset=utf8mb4"
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'connect_args': {'connect_timeout': 10}
    }
    
    # 文件上传配置
    MAX_CONTENT_LENGTH = 100 * 1024 * 1024  # 100MB
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
    
    # 数据处理配置
    DATA_PROCESSING_TIMEOUT = 300  # 5分钟
    MAX_RECORDS_PER_BATCH = 50000  # 批处理记录数
    
    # 安全配置
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600  # 1小时
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    LOG_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs', 'app.log')

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    LOG_LEVEL = 'DEBUG'

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    LOG_LEVEL = 'WARNING'

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
