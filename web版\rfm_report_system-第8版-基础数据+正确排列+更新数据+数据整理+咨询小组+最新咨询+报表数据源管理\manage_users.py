#!/usr/bin/env python3
"""
用户管理脚本
"""

import getpass
import sys
from app import create_app
from app.models import User, db


def create_user():
    """创建新用户"""
    app = create_app()
    
    with app.app_context():
        print("创建新用户")
        print("-" * 30)
        
        username = input("用户名: ").strip()
        if not username:
            print("❌ 用户名不能为空")
            return
        
        # 检查用户是否已存在
        if User.query.filter_by(username=username).first():
            print(f"❌ 用户 '{username}' 已存在")
            return
        
        email = input("邮箱: ").strip()
        if not email:
            print("❌ 邮箱不能为空")
            return
        
        # 安全地输入密码
        password = getpass.getpass("密码: ")
        if len(password) < 6:
            print("❌ 密码长度至少6位")
            return
        
        password_confirm = getpass.getpass("确认密码: ")
        if password != password_confirm:
            print("❌ 两次输入的密码不一致")
            return
        
        try:
            # 创建用户
            user = User(username=username, email=email)
            user.set_password(password)
            
            db.session.add(user)
            db.session.commit()
            
            print(f"✅ 用户 '{username}' 创建成功")
            
        except Exception as e:
            print(f"❌ 创建用户失败: {e}")
            db.session.rollback()


def reset_password():
    """重置用户密码"""
    app = create_app()
    
    with app.app_context():
        print("重置用户密码")
        print("-" * 30)
        
        username = input("用户名: ").strip()
        if not username:
            print("❌ 用户名不能为空")
            return
        
        user = User.query.filter_by(username=username).first()
        if not user:
            print(f"❌ 用户 '{username}' 不存在")
            return
        
        # 安全地输入新密码
        password = getpass.getpass("新密码: ")
        if len(password) < 6:
            print("❌ 密码长度至少6位")
            return
        
        password_confirm = getpass.getpass("确认新密码: ")
        if password != password_confirm:
            print("❌ 两次输入的密码不一致")
            return
        
        try:
            user.set_password(password)
            db.session.commit()
            
            print(f"✅ 用户 '{username}' 密码重置成功")
            
        except Exception as e:
            print(f"❌ 重置密码失败: {e}")
            db.session.rollback()


def list_users():
    """列出所有用户"""
    app = create_app()
    
    with app.app_context():
        users = User.query.all()
        
        print("系统用户列表")
        print("-" * 50)
        print(f"{'ID':<5} {'用户名':<15} {'邮箱':<25} {'创建时间'}")
        print("-" * 50)
        
        for user in users:
            print(f"{user.id:<5} {user.username:<15} {user.email:<25} {user.created_at.strftime('%Y-%m-%d %H:%M')}")
        
        print(f"\n总计: {len(users)} 个用户")


def show_login_info():
    """显示登录信息"""
    app = create_app()
    
    with app.app_context():
        users = User.query.all()
        
        if not users:
            print("❌ 系统中没有用户，请先运行 'python init_db.py' 初始化")
            return
        
        print("🔐 系统登录信息")
        print("=" * 40)
        print("访问地址: http://localhost:5000")
        print("\n可用用户:")
        for user in users:
            print(f"  用户名: {user.username}")
            print(f"  邮箱: {user.email}")
            print(f"  创建时间: {user.created_at.strftime('%Y-%m-%d %H:%M')}")
            print("-" * 30)
        
        print("💡 如需重置密码，请运行: python manage_users.py reset")


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用户管理脚本")
        print("=" * 30)
        print("用法:")
        print("  python manage_users.py create   - 创建新用户")
        print("  python manage_users.py reset    - 重置用户密码")
        print("  python manage_users.py list     - 列出所有用户")
        print("  python manage_users.py info     - 显示登录信息")
        return
    
    command = sys.argv[1].lower()
    
    if command == 'create':
        create_user()
    elif command == 'reset':
        reset_password()
    elif command == 'list':
        list_users()
    elif command == 'info':
        show_login_info()
    else:
        print(f"❌ 未知命令: {command}")
        print("可用命令: create, reset, list, info")


if __name__ == '__main__':
    main()
