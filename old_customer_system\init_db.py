# -*- coding: utf-8 -*-
"""
数据库初始化脚本
"""

import os
import sys
from flask import Flask
from app import create_app, db
from app.models import User, Department, Channel, SystemSetting

def init_database():
    """初始化数据库"""
    app = create_app()
    
    with app.app_context():
        try:
            # 创建所有表
            db.create_all()
            print("✓ 数据库表创建成功")
            
            # 创建默认部门
            if not Department.query.first():
                departments = [
                    Department(name='管理部', description='系统管理部门'),
                    Department(name='网络咨询部', description='网络咨询部门'),
                    Department(name='现场咨询部', description='现场咨询部门'),
                ]
                for dept in departments:
                    db.session.add(dept)
                print("✓ 默认部门创建成功")
            
            # 创建默认系统设置
            default_settings = [
                ('card_number_length', '10', '卡号长度设置'),
                ('system_name', '老客登记信息反馈系统', '系统名称'),
                ('version', '1.0.0', '系统版本'),
            ]
            
            for key, value, desc in default_settings:
                if not SystemSetting.query.filter_by(setting_key=key).first():
                    setting = SystemSetting(
                        setting_key=key,
                        setting_value=value,
                        description=desc
                    )
                    db.session.add(setting)
            print("✓ 默认系统设置创建成功")
            
            db.session.commit()
            print("✓ 数据库初始化完成")
            
        except Exception as e:
            print(f"✗ 数据库初始化失败: {str(e)}")
            db.session.rollback()
            return False
    
    return True

def create_admin_user():
    """创建管理员用户"""
    app = create_app()
    
    with app.app_context():
        # 检查是否已存在管理员
        if User.query.filter_by(role='admin').first():
            print("管理员账号已存在")
            return True
        
        print("创建管理员账号...")
        username = input("请输入管理员用户名: ").strip()
        if not username:
            print("用户名不能为空")
            return False
        
        # 检查用户名是否已存在
        if User.query.filter_by(username=username).first():
            print("用户名已存在")
            return False
        
        password = input("请输入管理员密码: ").strip()
        if not password:
            print("密码不能为空")
            return False
        
        real_name = input("请输入管理员真实姓名: ").strip()
        if not real_name:
            print("真实姓名不能为空")
            return False
        
        try:
            # 创建管理员用户
            admin_user = User(
                username=username,
                real_name=real_name,
                role='admin',
                is_active=True
            )
            admin_user.set_password(password)
            
            db.session.add(admin_user)
            db.session.commit()
            
            print(f"✓ 管理员账号 '{username}' 创建成功")
            return True
            
        except Exception as e:
            print(f"✗ 创建管理员账号失败: {str(e)}")
            db.session.rollback()
            return False

def main():
    """主函数"""
    print("老客登记信息反馈系统 - 数据库初始化")
    print("=" * 50)
    
    # 初始化数据库
    if not init_database():
        sys.exit(1)
    
    print()
    
    # 创建管理员账号
    if not create_admin_user():
        sys.exit(1)
    
    print()
    print("数据库初始化完成！")
    print("您现在可以启动系统并使用管理员账号登录。")

if __name__ == '__main__':
    main()