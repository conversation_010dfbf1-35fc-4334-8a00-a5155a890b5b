# 老客回访与跟进系统 - 系统设计方案

## 项目概述

本系统是为高端服务机构（如医美诊所）设计的老客户激活、咨询和跟进管理系统。系统采用基于角色的权限控制（RBAC），确保数据安全、权限分明且流程高效。

---

## 1. 系统总体架构

### 1.1 技术架构

```
┌─────────────────────────────────────────────────────────────┐
│                    前端展示层 (Web UI)                        │
├─────────────────────────────────────────────────────────────┤
│                    业务逻辑层 (Flask)                         │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层 (SQLAlchemy ORM)                │
├─────────────────────────────────────────────────────────────┤
│                    数据存储层 (MySQL)                         │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 系统架构组件

- **前端**: HTML5 + CSS3 + JavaScript + Bootstrap
- **后端**: Python Flask 框架
- **数据库**: MySQL 8.0+
- **ORM**: SQLAlchemy
- **认证**: Flask-Login + Session管理
- **权限**: 基于RBAC的自定义权限系统

### 1.3 部署架构

```
┌──────────────┐    ┌──────────────┐    ┌──────────────┐
│   用户浏览器   │────│  Web服务器    │────│  MySQL数据库  │
│              │    │  (Flask App) │    │              │
└──────────────┘    └──────────────┘    └──────────────┘
```

### 1.4 系统初始化流程

1. **首次部署检测**: 系统启动时检查数据库连接配置
2. **数据库初始化**: 自动创建 `Old_Customer_System` 数据库及所有表结构
3. **管理员注册**: 引导创建第一个管理员账号
4. **基础数据配置**: 初始化角色、权限等基础数据

---

## 2. 数据库设计 (MySQL)

### 2.1 数据库命名规范

- 数据库名: `Old_Customer_System`
- 表名: 使用下划线命名法，如 `user_accounts`
- 字段名: 使用下划线命名法，如 `created_time`
- 索引名: `idx_表名_字段名`

### 2.2 核心数据表设计

#### 2.2.1 用户账号表 (user_accounts)

```sql
CREATE TABLE user_accounts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    real_name VARCHAR(100) NOT NULL COMMENT '真实姓名',
    department VARCHAR(100) NOT NULL COMMENT '所属部门',
    role_id INT NOT NULL COMMENT '角色ID',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_login_time DATETIME COMMENT '最后登录时间',
    created_by INT COMMENT '创建人ID',
    
    INDEX idx_username (username),
    INDEX idx_role_id (role_id),
    INDEX idx_department (department),
    FOREIGN KEY (role_id) REFERENCES user_roles(id),
    FOREIGN KEY (created_by) REFERENCES user_accounts(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户账号表';
```

#### 2.2.2 角色表 (user_roles)

```sql
CREATE TABLE user_roles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    role_name VARCHAR(50) UNIQUE NOT NULL COMMENT '角色名称',
    role_code VARCHAR(50) UNIQUE NOT NULL COMMENT '角色代码',
    description TEXT COMMENT '角色描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_role_code (role_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';
```

#### 2.2.3 权限表 (permissions)

```sql
CREATE TABLE permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    permission_name VARCHAR(100) NOT NULL COMMENT '权限名称',
    permission_code VARCHAR(100) UNIQUE NOT NULL COMMENT '权限代码',
    module_name VARCHAR(50) NOT NULL COMMENT '所属模块',
    description TEXT COMMENT '权限描述',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_permission_code (permission_code),
    INDEX idx_module_name (module_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限表';
```

#### 2.2.4 角色权限关联表 (role_permissions)

```sql
CREATE TABLE role_permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    role_id INT NOT NULL COMMENT '角色ID',
    permission_id INT NOT NULL COMMENT '权限ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    UNIQUE KEY uk_role_permission (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES user_roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';
```

#### 2.2.5 渠道管理表 (channels)

```sql
CREATE TABLE channels (
    id INT PRIMARY KEY AUTO_INCREMENT,
    channel_category VARCHAR(100) NOT NULL COMMENT '渠道分类',
    channel_name VARCHAR(200) NOT NULL COMMENT '渠道名称',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by INT COMMENT '创建人ID',
    
    INDEX idx_channel_category (channel_category),
    INDEX idx_channel_name (channel_name),
    FOREIGN KEY (created_by) REFERENCES user_accounts(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='渠道管理表';
```

#### 2.2.6 客户登记表 (customer_registrations)

```sql
CREATE TABLE customer_registrations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    card_number VARCHAR(10) NOT NULL COMMENT '卡号',
    assigned_consultant_id INT NOT NULL COMMENT '所属现场顾问ID',
    activation_channel_id INT NOT NULL COMMENT '激活渠道ID',
    consultation_content TEXT NOT NULL COMMENT '咨询内容(最大500字)',
    last_visit_date DATE NOT NULL COMMENT '客户最近来院时间',
    registration_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '登记时间',
    registrar_id INT NOT NULL COMMENT '登记人ID',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否有效',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_card_number (card_number),
    INDEX idx_assigned_consultant (assigned_consultant_id),
    INDEX idx_activation_channel (activation_channel_id),
    INDEX idx_registrar (registrar_id),
    INDEX idx_registration_time (registration_time),
    INDEX idx_last_visit_date (last_visit_date),
    FOREIGN KEY (assigned_consultant_id) REFERENCES user_accounts(id),
    FOREIGN KEY (activation_channel_id) REFERENCES channels(id),
    FOREIGN KEY (registrar_id) REFERENCES user_accounts(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户登记表';
```

#### 2.2.7 跟进记录表 (follow_up_records)

```sql
CREATE TABLE follow_up_records (
    id INT PRIMARY KEY AUTO_INCREMENT,
    customer_registration_id INT NOT NULL COMMENT '客户登记ID',
    follow_up_content TEXT NOT NULL COMMENT '咨询跟进情况(最大500字)',
    follow_up_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '咨询跟进时间',
    consultant_id INT NOT NULL COMMENT '跟进顾问ID',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否有效',
    
    INDEX idx_customer_registration (customer_registration_id),
    INDEX idx_consultant (consultant_id),
    INDEX idx_follow_up_time (follow_up_time),
    FOREIGN KEY (customer_registration_id) REFERENCES customer_registrations(id) ON DELETE CASCADE,
    FOREIGN KEY (consultant_id) REFERENCES user_accounts(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='跟进记录表';
```

#### 2.2.8 现场小组映射表 (consultant_group_mapping)

```sql
CREATE TABLE consultant_group_mapping (
    id INT PRIMARY KEY AUTO_INCREMENT,
    consultant_id INT NOT NULL COMMENT '现场咨询员ID',
    group_name VARCHAR(100) NOT NULL COMMENT '现场小组名称',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否有效',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by INT COMMENT '创建人ID',

    INDEX idx_consultant (consultant_id),
    INDEX idx_group_name (group_name),
    FOREIGN KEY (consultant_id) REFERENCES user_accounts(id),
    FOREIGN KEY (created_by) REFERENCES user_accounts(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='现场小组映射表';
```

#### 2.2.9 客户消费记录表 (customer_consumption)

```sql
CREATE TABLE customer_consumption (
    id INT PRIMARY KEY AUTO_INCREMENT,
    card_number VARCHAR(10) NOT NULL COMMENT '卡号',
    visit_date DATE COMMENT '到院日期',
    consumption_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '消费金额',
    import_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '导入时间',
    import_batch VARCHAR(100) COMMENT '导入批次号',
    imported_by INT COMMENT '导入人ID',

    INDEX idx_card_number (card_number),
    INDEX idx_visit_date (visit_date),
    INDEX idx_import_batch (import_batch),
    FOREIGN KEY (imported_by) REFERENCES user_accounts(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户消费记录表';
```

#### 2.2.10 系统配置表 (system_config)

```sql
CREATE TABLE system_config (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_description TEXT COMMENT '配置描述',
    config_type VARCHAR(50) DEFAULT 'string' COMMENT '配置类型',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updated_by INT COMMENT '更新人ID',

    INDEX idx_config_key (config_key),
    FOREIGN KEY (updated_by) REFERENCES user_accounts(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';
```

### 2.3 数据库索引策略

#### 2.3.1 主要查询场景索引

```sql
-- 客户登记查询优化
CREATE INDEX idx_customer_reg_composite ON customer_registrations(registrar_id, registration_time DESC);
CREATE INDEX idx_customer_reg_consultant ON customer_registrations(assigned_consultant_id, registration_time DESC);

-- 跟进记录查询优化
CREATE INDEX idx_follow_up_composite ON follow_up_records(consultant_id, follow_up_time DESC);

-- 统计报表查询优化
CREATE INDEX idx_consumption_date_amount ON customer_consumption(visit_date, consumption_amount);
CREATE INDEX idx_registration_channel_date ON customer_registrations(activation_channel_id, registration_time);
```

#### 2.3.2 数据完整性约束

```sql
-- 卡号长度检查约束
ALTER TABLE customer_registrations
ADD CONSTRAINT chk_card_number_length
CHECK (LENGTH(card_number) <= 10 AND card_number REGEXP '^[0-9]+$');

-- 咨询内容长度约束
ALTER TABLE customer_registrations
ADD CONSTRAINT chk_consultation_content_length
CHECK (CHAR_LENGTH(consultation_content) <= 500);

-- 跟进内容长度约束
ALTER TABLE follow_up_records
ADD CONSTRAINT chk_follow_up_content_length
CHECK (CHAR_LENGTH(follow_up_content) <= 500);
```

---

## 3. 核心模块功能详述

### 3.1 系统初始化模块

#### 3.1.1 数据库连接配置
- **功能**: 首次启动时引导用户配置MySQL连接信息
- **配置项**: 主机地址、端口、用户名、密码、数据库名
- **验证机制**: 连接测试、权限验证、字符集检查

#### 3.1.2 数据库结构初始化
- **自动创建**: 数据库 `Old_Customer_System`
- **表结构创建**: 执行所有DDL语句创建表结构
- **基础数据初始化**: 插入默认角色、权限、系统配置

#### 3.1.3 管理员账号创建
- **注册流程**: 用户名、密码、确认密码、真实姓名
- **安全要求**: 密码强度验证、用户名唯一性检查
- **权限分配**: 自动分配管理员角色和所有权限

### 3.2 用户认证与会话管理模块

#### 3.2.1 登录功能
```python
# 登录验证逻辑
def authenticate_user(username, password):
    user = User.query.filter_by(username=username, is_active=True).first()
    if user and check_password_hash(user.password_hash, password):
        # 更新最后登录时间
        user.last_login_time = datetime.now()
        db.session.commit()
        return user
    return None
```

#### 3.2.2 会话管理
- **会话超时**: 30分钟无操作自动登出
- **并发控制**: 同一账号最多允许3个并发会话
- **安全登出**: 清除所有会话数据和临时文件

#### 3.2.3 密码管理
- **密码策略**: 最少8位，包含大小写字母、数字
- **密码加密**: 使用bcrypt进行哈希加密
- **密码重置**: 管理员可重置任意用户密码

### 3.3 账号管理模块（管理员专属）

#### 3.3.1 账号创建功能
```python
# 账号创建逻辑
def create_user_account(username, initial_password, real_name, department, role_id, created_by):
    # 验证用户名唯一性
    if User.query.filter_by(username=username).first():
        raise ValueError("用户名已存在")

    # 创建新用户
    new_user = User(
        username=username,
        password_hash=generate_password_hash(initial_password),
        real_name=real_name,
        department=department,
        role_id=role_id,
        created_by=created_by
    )
    db.session.add(new_user)
    db.session.commit()
    return new_user
```

#### 3.3.2 账号管理功能
- **账号列表**: 分页显示、搜索过滤、状态筛选
- **账号编辑**: 修改基本信息、部门调整、角色变更
- **账号状态**: 启用/停用账号、批量操作
- **操作日志**: 记录所有账号管理操作

### 3.4 渠道管理模块

#### 3.4.1 渠道维护功能
```python
# 渠道管理逻辑
class ChannelManager:
    def add_channel(self, category, name, created_by):
        channel = Channel(
            channel_category=category,
            channel_name=name,
            created_by=created_by
        )
        db.session.add(channel)
        db.session.commit()
        return channel

    def batch_import_channels(self, excel_file, created_by):
        # Excel批量导入逻辑
        df = pd.read_excel(excel_file)
        for index, row in df.iterrows():
            self.add_channel(row['渠道分类'], row['渠道名称'], created_by)
```

#### 3.4.2 Excel批量导入
- **文件格式**: 支持.xlsx、.xls格式
- **数据验证**: 必填字段检查、重复数据检测
- **导入结果**: 成功/失败统计、错误详情报告
- **模板下载**: 提供标准Excel模板下载

### 3.5 客户登记与跟进模块

#### 3.5.1 网络咨询登记功能
```python
# 客户登记逻辑
class CustomerRegistrationManager:
    def create_registration(self, card_number, assigned_consultant_id,
                          activation_channel_id, consultation_content,
                          last_visit_date, registrar_id):
        # 验证卡号格式和长度
        if not self.validate_card_number(card_number):
            raise ValueError("卡号格式不正确")

        # 验证现场顾问角色
        consultant = User.query.get(assigned_consultant_id)
        if not consultant or consultant.role.role_code != 'FIELD_CONSULTANT':
            raise ValueError("所选顾问不是现场咨询角色")

        registration = CustomerRegistration(
            card_number=card_number,
            assigned_consultant_id=assigned_consultant_id,
            activation_channel_id=activation_channel_id,
            consultation_content=consultation_content,
            last_visit_date=last_visit_date,
            registrar_id=registrar_id
        )
        db.session.add(registration)
        db.session.commit()
        return registration

    def validate_card_number(self, card_number):
        # 获取系统配置的卡号长度规则
        max_length = SystemConfig.get_config('card_number_max_length', 10)
        return (card_number.isdigit() and
                len(card_number) <= int(max_length))
```

#### 3.5.2 信息列表展示功能
```python
# 数据权限过滤逻辑
class DataPermissionFilter:
    @staticmethod
    def filter_by_user_role(query, user):
        if user.role.role_code == 'ADMIN':
            return query  # 管理员查看所有
        elif user.role.role_code == 'GENERAL_MANAGER':
            return query  # 经营院长查看所有
        elif user.role.role_code == 'DEPARTMENT_MANAGER':
            # 部门主管查看本部门员工的数据
            dept_users = User.query.filter_by(department=user.department).all()
            user_ids = [u.id for u in dept_users]
            return query.filter(CustomerRegistration.registrar_id.in_(user_ids))
        elif user.role.role_code == 'NETWORK_CONSULTANT':
            # 网络咨询只看自己的数据
            return query.filter(CustomerRegistration.registrar_id == user.id)
        elif user.role.role_code == 'FIELD_CONSULTANT':
            # 现场咨询看分配给自己的客户
            return query.filter(CustomerRegistration.assigned_consultant_id == user.id)
        else:
            return query.filter(False)  # 其他角色无权限
```

#### 3.5.3 现场咨询跟进功能
```python
# 跟进记录管理
class FollowUpManager:
    def add_follow_up(self, customer_registration_id, follow_up_content, consultant_id):
        # 验证权限：只能跟进分配给自己的客户
        registration = CustomerRegistration.query.get(customer_registration_id)
        if not registration or registration.assigned_consultant_id != consultant_id:
            raise PermissionError("无权限跟进此客户")

        follow_up = FollowUpRecord(
            customer_registration_id=customer_registration_id,
            follow_up_content=follow_up_content,
            consultant_id=consultant_id
        )
        db.session.add(follow_up)
        db.session.commit()
        return follow_up

    def get_latest_follow_up(self, customer_registration_id):
        return FollowUpRecord.query.filter_by(
            customer_registration_id=customer_registration_id,
            is_active=True
        ).order_by(FollowUpRecord.follow_up_time.desc()).first()
```

### 3.6 数据管理与统计模块

#### 3.6.1 参考值映射管理
```python
# 现场小组映射管理
class ConsultantGroupManager:
    def update_mapping(self, consultant_id, group_name, created_by):
        # 检查是否已存在映射
        existing = ConsultantGroupMapping.query.filter_by(
            consultant_id=consultant_id,
            is_active=True
        ).first()

        if existing:
            existing.is_active = False  # 软删除旧映射

        # 创建新映射
        new_mapping = ConsultantGroupMapping(
            consultant_id=consultant_id,
            group_name=group_name,
            created_by=created_by
        )
        db.session.add(new_mapping)
        db.session.commit()
        return new_mapping

    def batch_import_mapping(self, excel_file, created_by):
        df = pd.read_excel(excel_file)
        for index, row in df.iterrows():
            consultant = User.query.filter_by(real_name=row['现场咨询员']).first()
            if consultant:
                self.update_mapping(consultant.id, row['现场小组'], created_by)
```

#### 3.6.2 客户消费数据导入
```python
# 消费数据批量导入
class ConsumptionDataManager:
    def batch_import_consumption(self, excel_file, imported_by):
        import_batch = f"BATCH_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        df = pd.read_excel(excel_file)

        success_count = 0
        error_list = []

        for index, row in df.iterrows():
            try:
                consumption = CustomerConsumption(
                    card_number=str(row['卡号']),
                    visit_date=pd.to_datetime(row['到院日期']).date(),
                    consumption_amount=float(row['消费金额']) if pd.notna(row['消费金额']) else 0.00,
                    import_batch=import_batch,
                    imported_by=imported_by
                )
                db.session.add(consumption)
                success_count += 1
            except Exception as e:
                error_list.append(f"第{index+2}行: {str(e)}")

        db.session.commit()
        return {
            'success_count': success_count,
            'error_count': len(error_list),
            'error_details': error_list,
            'import_batch': import_batch
        }
```

#### 3.6.3 统计报表功能
```python
# 统计报表生成
class StatisticsReportManager:
    def generate_report(self, filters):
        # 基础查询构建
        query = db.session.query(
            CustomerRegistration,
            FollowUpRecord,
            CustomerConsumption,
            ConsultantGroupMapping
        ).outerjoin(
            FollowUpRecord,
            CustomerRegistration.id == FollowUpRecord.customer_registration_id
        ).outerjoin(
            CustomerConsumption,
            CustomerRegistration.card_number == CustomerConsumption.card_number
        ).outerjoin(
            ConsultantGroupMapping,
            CustomerRegistration.assigned_consultant_id == ConsultantGroupMapping.consultant_id
        )

        # 应用筛选条件
        if filters.get('group_name'):
            query = query.filter(ConsultantGroupMapping.group_name == filters['group_name'])

        if filters.get('consultant_id'):
            query = query.filter(CustomerRegistration.assigned_consultant_id == filters['consultant_id'])

        if filters.get('channel_id'):
            query = query.filter(CustomerRegistration.activation_channel_id == filters['channel_id'])

        if filters.get('start_date') and filters.get('end_date'):
            query = query.filter(
                CustomerRegistration.registration_time.between(
                    filters['start_date'], filters['end_date']
                )
            )

        # 统计计算
        results = query.all()

        return {
            'total_customers': len(set([r.CustomerRegistration.id for r in results])),
            'pending_follow_up': self.count_pending_follow_up(results),
            'visited_after_follow_up': self.count_visited_after_follow_up(results),
            'total_consumption': sum([r.CustomerConsumption.consumption_amount or 0 for r in results])
        }
```

---

## 4. 角色与权限模型 (RBAC)

### 4.1 角色定义与权限矩阵

#### 4.1.1 系统角色定义
```sql
-- 初始化角色数据
INSERT INTO user_roles (role_name, role_code, description) VALUES
('系统管理员', 'ADMIN', '拥有系统所有功能的操作权限'),
('经营院长', 'GENERAL_MANAGER', '可查看所有登记信息和统计报表，但不能进行登记和修改操作'),
('部门主管', 'DEPARTMENT_MANAGER', '可查看其所属部门下所有员工登记的信息和相关统计报表'),
('网络咨询', 'NETWORK_CONSULTANT', '负责登记客户，只能查看和修改自己名下的登记信息'),
('现场咨询', 'FIELD_CONSULTANT', '负责跟进客户，只能查看分配给自己的客户信息，并填写跟进情况');
```

#### 4.1.2 权限定义
```sql
-- 初始化权限数据
INSERT INTO permissions (permission_name, permission_code, module_name, description) VALUES
-- 系统管理权限
('用户管理', 'USER_MANAGE', 'SYSTEM', '创建、编辑、删除用户账号'),
('角色管理', 'ROLE_MANAGE', 'SYSTEM', '管理系统角色和权限'),
('系统配置', 'SYSTEM_CONFIG', 'SYSTEM', '修改系统配置参数'),

-- 渠道管理权限
('渠道查看', 'CHANNEL_VIEW', 'CHANNEL', '查看渠道列表'),
('渠道管理', 'CHANNEL_MANAGE', 'CHANNEL', '添加、修改、删除渠道'),
('渠道导入', 'CHANNEL_IMPORT', 'CHANNEL', '批量导入渠道数据'),

-- 客户登记权限
('客户登记', 'CUSTOMER_REGISTER', 'CUSTOMER', '登记新客户信息'),
('客户查看', 'CUSTOMER_VIEW', 'CUSTOMER', '查看客户登记信息'),
('客户修改', 'CUSTOMER_EDIT', 'CUSTOMER', '修改客户登记信息'),

-- 跟进管理权限
('跟进添加', 'FOLLOW_UP_ADD', 'FOLLOW_UP', '添加客户跟进记录'),
('跟进查看', 'FOLLOW_UP_VIEW', 'FOLLOW_UP', '查看跟进记录'),

-- 数据管理权限
('映射管理', 'MAPPING_MANAGE', 'DATA', '管理现场小组映射关系'),
('消费导入', 'CONSUMPTION_IMPORT', 'DATA', '导入客户消费数据'),

-- 统计报表权限
('报表查看', 'REPORT_VIEW', 'REPORT', '查看统计报表'),
('报表导出', 'REPORT_EXPORT', 'REPORT', '导出报表数据');
```

#### 4.1.3 角色权限分配
```sql
-- 管理员权限分配（所有权限）
INSERT INTO role_permissions (role_id, permission_id)
SELECT 1, id FROM permissions;

-- 经营院长权限分配（只读权限）
INSERT INTO role_permissions (role_id, permission_id)
SELECT 2, id FROM permissions
WHERE permission_code IN ('CHANNEL_VIEW', 'CUSTOMER_VIEW', 'FOLLOW_UP_VIEW', 'REPORT_VIEW', 'REPORT_EXPORT');

-- 部门主管权限分配
INSERT INTO role_permissions (role_id, permission_id)
SELECT 3, id FROM permissions
WHERE permission_code IN ('CHANNEL_VIEW', 'CUSTOMER_VIEW', 'FOLLOW_UP_VIEW', 'REPORT_VIEW', 'REPORT_EXPORT');

-- 网络咨询权限分配
INSERT INTO role_permissions (role_id, permission_id)
SELECT 4, id FROM permissions
WHERE permission_code IN ('CHANNEL_VIEW', 'CUSTOMER_REGISTER', 'CUSTOMER_VIEW', 'CUSTOMER_EDIT', 'FOLLOW_UP_VIEW');

-- 现场咨询权限分配
INSERT INTO role_permissions (role_id, permission_id)
SELECT 5, id FROM permissions
WHERE permission_code IN ('CUSTOMER_VIEW', 'FOLLOW_UP_ADD', 'FOLLOW_UP_VIEW');
```

### 4.2 权限验证机制

#### 4.2.1 装饰器权限验证
```python
from functools import wraps
from flask import session, jsonify, request

def require_permission(permission_code):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if 'user_id' not in session:
                return jsonify({'error': '未登录'}), 401

            user = User.query.get(session['user_id'])
            if not user or not user.is_active:
                return jsonify({'error': '用户无效'}), 401

            # 检查用户是否有指定权限
            if not user.has_permission(permission_code):
                return jsonify({'error': '权限不足'}), 403

            return f(*args, **kwargs)
        return decorated_function
    return decorator

# 用户模型中的权限检查方法
class User(db.Model):
    def has_permission(self, permission_code):
        return db.session.query(
            db.exists().where(
                db.and_(
                    RolePermission.role_id == self.role_id,
                    Permission.id == RolePermission.permission_id,
                    Permission.permission_code == permission_code
                )
            )
        ).scalar()
```

#### 4.2.2 数据权限过滤
```python
class DataPermissionMixin:
    @classmethod
    def filter_by_permission(cls, query, user):
        """根据用户权限过滤数据"""
        if user.role.role_code == 'ADMIN':
            return query
        elif user.role.role_code == 'GENERAL_MANAGER':
            return query
        elif user.role.role_code == 'DEPARTMENT_MANAGER':
            # 部门主管只能看本部门数据
            dept_users = User.query.filter_by(department=user.department).all()
            user_ids = [u.id for u in dept_users]
            return query.filter(cls.registrar_id.in_(user_ids))
        elif user.role.role_code == 'NETWORK_CONSULTANT':
            # 网络咨询只能看自己的数据
            return query.filter(cls.registrar_id == user.id)
        elif user.role.role_code == 'FIELD_CONSULTANT':
            # 现场咨询看分配给自己的客户
            return query.filter(cls.assigned_consultant_id == user.id)
        else:
            return query.filter(False)
```

### 4.3 安全机制

#### 4.3.1 会话安全
```python
# 会话配置
app.config['SESSION_COOKIE_SECURE'] = True  # HTTPS only
app.config['SESSION_COOKIE_HTTPONLY'] = True  # 防止XSS
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'  # CSRF防护
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(minutes=30)  # 30分钟超时
```

#### 4.3.2 操作日志记录
```python
class AuditLog(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user_accounts.id'))
    action = db.Column(db.String(100), nullable=False)
    resource_type = db.Column(db.String(50), nullable=False)
    resource_id = db.Column(db.String(50))
    details = db.Column(db.Text)
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.Text)
    created_time = db.Column(db.DateTime, default=datetime.utcnow)

def log_action(user_id, action, resource_type, resource_id=None, details=None):
    log = AuditLog(
        user_id=user_id,
        action=action,
        resource_type=resource_type,
        resource_id=resource_id,
        details=details,
        ip_address=request.remote_addr,
        user_agent=request.user_agent.string
    )
    db.session.add(log)
    db.session.commit()
```

---

## 5. API 接口设计建议

### 5.1 RESTful API 设计原则

#### 5.1.1 URL 设计规范
```
基础URL: /api/v1/

认证相关:
POST   /api/v1/auth/login          # 用户登录
POST   /api/v1/auth/logout         # 用户登出
PUT    /api/v1/auth/password       # 修改密码

用户管理:
GET    /api/v1/users               # 获取用户列表
POST   /api/v1/users               # 创建用户
GET    /api/v1/users/{id}          # 获取用户详情
PUT    /api/v1/users/{id}          # 更新用户信息
DELETE /api/v1/users/{id}          # 删除用户

渠道管理:
GET    /api/v1/channels            # 获取渠道列表
POST   /api/v1/channels            # 创建渠道
PUT    /api/v1/channels/{id}       # 更新渠道
DELETE /api/v1/channels/{id}       # 删除渠道
POST   /api/v1/channels/import     # 批量导入渠道

客户登记:
GET    /api/v1/registrations       # 获取登记列表
POST   /api/v1/registrations       # 创建登记
GET    /api/v1/registrations/{id}  # 获取登记详情
PUT    /api/v1/registrations/{id}  # 更新登记
DELETE /api/v1/registrations/{id}  # 删除登记

跟进管理:
GET    /api/v1/follow-ups          # 获取跟进列表
POST   /api/v1/follow-ups          # 添加跟进记录
GET    /api/v1/follow-ups/{id}     # 获取跟进详情

统计报表:
GET    /api/v1/reports/statistics  # 获取统计数据
POST   /api/v1/reports/export      # 导出报表
```

#### 5.1.2 请求响应格式标准
```json
// 成功响应格式
{
    "success": true,
    "data": {
        // 具体数据内容
    },
    "message": "操作成功",
    "timestamp": "2024-01-01T12:00:00Z"
}

// 错误响应格式
{
    "success": false,
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "数据验证失败",
        "details": [
            {
                "field": "card_number",
                "message": "卡号格式不正确"
            }
        ]
    },
    "timestamp": "2024-01-01T12:00:00Z"
}

// 分页响应格式
{
    "success": true,
    "data": {
        "items": [...],
        "pagination": {
            "page": 1,
            "per_page": 20,
            "total": 100,
            "pages": 5
        }
    }
}
```

### 5.2 核心API接口详细设计

#### 5.2.1 用户认证接口
```python
# 登录接口
@app.route('/api/v1/auth/login', methods=['POST'])
def login():
    """
    用户登录
    Request Body:
    {
        "username": "string",
        "password": "string"
    }
    """
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')

    user = authenticate_user(username, password)
    if user:
        session['user_id'] = user.id
        session['username'] = user.username
        session['role_code'] = user.role.role_code

        return jsonify({
            'success': True,
            'data': {
                'user_id': user.id,
                'username': user.username,
                'real_name': user.real_name,
                'role': user.role.role_name,
                'permissions': [p.permission_code for p in user.role.permissions]
            },
            'message': '登录成功'
        })
    else:
        return jsonify({
            'success': False,
            'error': {
                'code': 'INVALID_CREDENTIALS',
                'message': '用户名或密码错误'
            }
        }), 401
```

#### 5.2.2 客户登记接口
```python
# 创建客户登记
@app.route('/api/v1/registrations', methods=['POST'])
@require_permission('CUSTOMER_REGISTER')
def create_registration():
    """
    创建客户登记
    Request Body:
    {
        "card_number": "string",
        "assigned_consultant_id": "integer",
        "activation_channel_id": "integer",
        "consultation_content": "string",
        "last_visit_date": "YYYY-MM-DD"
    }
    """
    data = request.get_json()

    try:
        registration = CustomerRegistrationManager().create_registration(
            card_number=data['card_number'],
            assigned_consultant_id=data['assigned_consultant_id'],
            activation_channel_id=data['activation_channel_id'],
            consultation_content=data['consultation_content'],
            last_visit_date=datetime.strptime(data['last_visit_date'], '%Y-%m-%d').date(),
            registrar_id=session['user_id']
        )

        # 记录操作日志
        log_action(session['user_id'], 'CREATE', 'CUSTOMER_REGISTRATION', registration.id)

        return jsonify({
            'success': True,
            'data': {
                'id': registration.id,
                'card_number': registration.card_number
            },
            'message': '客户登记成功'
        })

    except ValueError as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'VALIDATION_ERROR',
                'message': str(e)
            }
        }), 400
```

#### 5.2.3 统计报表接口
```python
# 获取统计数据
@app.route('/api/v1/reports/statistics', methods=['GET'])
@require_permission('REPORT_VIEW')
def get_statistics():
    """
    获取统计数据
    Query Parameters:
    - group_name: 现场小组名称
    - consultant_id: 现场顾问ID
    - channel_id: 渠道ID
    - start_date: 开始日期 (YYYY-MM-DD)
    - end_date: 结束日期 (YYYY-MM-DD)
    """
    filters = {
        'group_name': request.args.get('group_name'),
        'consultant_id': request.args.get('consultant_id', type=int),
        'channel_id': request.args.get('channel_id', type=int),
        'start_date': request.args.get('start_date'),
        'end_date': request.args.get('end_date')
    }

    # 移除空值
    filters = {k: v for k, v in filters.items() if v is not None}

    # 应用数据权限过滤
    current_user = User.query.get(session['user_id'])
    if current_user.role.role_code == 'DEPARTMENT_MANAGER':
        # 部门主管只能看本部门数据
        dept_users = User.query.filter_by(department=current_user.department).all()
        filters['registrar_ids'] = [u.id for u in dept_users]

    report_data = StatisticsReportManager().generate_report(filters)

    return jsonify({
        'success': True,
        'data': report_data,
        'message': '统计数据获取成功'
    })
```

### 5.3 数据验证与错误处理

#### 5.3.1 输入数据验证
```python
from marshmallow import Schema, fields, validate, ValidationError

class CustomerRegistrationSchema(Schema):
    card_number = fields.Str(
        required=True,
        validate=[
            validate.Length(max=10),
            validate.Regexp(r'^\d+$', error='卡号只能包含数字')
        ]
    )
    assigned_consultant_id = fields.Int(required=True)
    activation_channel_id = fields.Int(required=True)
    consultation_content = fields.Str(
        required=True,
        validate=validate.Length(max=500, error='咨询内容不能超过500字')
    )
    last_visit_date = fields.Date(required=True)

def validate_request_data(schema_class):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            schema = schema_class()
            try:
                data = schema.load(request.get_json())
                request.validated_data = data
                return f(*args, **kwargs)
            except ValidationError as err:
                return jsonify({
                    'success': False,
                    'error': {
                        'code': 'VALIDATION_ERROR',
                        'message': '数据验证失败',
                        'details': err.messages
                    }
                }), 400
        return decorated_function
    return decorator
```

#### 5.3.2 全局错误处理
```python
@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'error': {
            'code': 'NOT_FOUND',
            'message': '请求的资源不存在'
        }
    }), 404

@app.errorhandler(500)
def internal_error(error):
    db.session.rollback()
    return jsonify({
        'success': False,
        'error': {
            'code': 'INTERNAL_ERROR',
            'message': '服务器内部错误'
        }
    }), 500

@app.errorhandler(Exception)
def handle_exception(e):
    # 记录错误日志
    app.logger.error(f'Unhandled exception: {str(e)}', exc_info=True)

    return jsonify({
        'success': False,
        'error': {
            'code': 'UNKNOWN_ERROR',
            'message': '未知错误'
        }
    }), 500
```

---

## 6. 假设与改进建议

### 6.1 需求分析中的假设

#### 6.1.1 业务流程假设
1. **客户唯一性**: 假设卡号在系统中是客户的唯一标识，不存在一个客户多个卡号的情况
2. **角色固定性**: 假设用户的角色相对固定，不会频繁变更，如需变更需要管理员操作
3. **部门结构**: 假设组织架构相对稳定，部门名称作为字符串存储足够满足需求
4. **跟进频次**: 假设一个客户可以有多次跟进记录，没有频次限制

#### 6.1.2 技术实现假设
1. **并发量**: 假设系统并发用户数不超过100人，采用单机部署方案
2. **数据量**: 假设年客户登记量在10万条以内，历史数据保留3年
3. **网络环境**: 假设部署在内网环境，安全要求相对较低
4. **浏览器兼容**: 假设用户使用现代浏览器（Chrome、Firefox、Edge等）

### 6.2 发现的需求模糊点

#### 6.2.1 权限边界不清晰
**问题**: 部门主管的数据查看范围定义不够明确
**建议**:
- 明确部门主管是否可以查看其他部门的汇总统计数据
- 定义跨部门协作场景下的数据访问权限
- 考虑增加临时授权机制

#### 6.2.2 数据更新策略未定义
**问题**: 客户消费数据导入时的重复数据处理策略不明确
**建议**:
- 定义重复数据的判断标准（卡号+日期？）
- 制定数据冲突时的处理策略（覆盖、跳过、累加）
- 增加数据导入前的预览和确认机制

#### 6.2.3 系统配置灵活性不足
**问题**: 卡号长度规则等配置项缺乏详细说明
**建议**:
- 增加更多可配置项：会话超时时间、密码策略、文件上传限制等
- 提供配置项的在线修改界面
- 增加配置变更的审计日志

### 6.3 功能增强建议

#### 6.3.1 数据安全增强
```python
# 建议增加数据加密存储
class EncryptedField(db.TypeDecorator):
    impl = db.Text

    def process_bind_param(self, value, dialect):
        if value is not None:
            return encrypt_data(value)
        return value

    def process_result_value(self, value, dialect):
        if value is not None:
            return decrypt_data(value)
        return value

# 敏感字段加密
class CustomerRegistration(db.Model):
    consultation_content = db.Column(EncryptedField)  # 加密存储咨询内容
```

#### 6.3.2 操作审计增强
```python
# 建议增加详细的操作审计
class DetailedAuditLog(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user_accounts.id'))
    action = db.Column(db.String(50), nullable=False)
    resource_type = db.Column(db.String(50), nullable=False)
    resource_id = db.Column(db.String(50))
    old_values = db.Column(db.JSON)  # 修改前的值
    new_values = db.Column(db.JSON)  # 修改后的值
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.Text)
    created_time = db.Column(db.DateTime, default=datetime.utcnow)
```

#### 6.3.3 性能优化建议
1. **数据库优化**:
   - 增加分区表支持，按时间分区存储历史数据
   - 实现读写分离，提高查询性能
   - 增加Redis缓存层，缓存热点数据

2. **前端优化**:
   - 实现虚拟滚动，支持大数据量列表展示
   - 增加数据懒加载，提高页面加载速度
   - 实现离线缓存，提高用户体验

3. **系统监控**:
   - 增加系统性能监控指标
   - 实现异常告警机制
   - 提供系统健康检查接口

### 6.4 扩展性考虑

#### 6.4.1 多机构支持
```sql
-- 建议增加机构表支持多机构部署
CREATE TABLE organizations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    org_name VARCHAR(200) NOT NULL COMMENT '机构名称',
    org_code VARCHAR(50) UNIQUE NOT NULL COMMENT '机构代码',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='机构表';

-- 用户表增加机构字段
ALTER TABLE user_accounts ADD COLUMN org_id INT COMMENT '所属机构ID';
ALTER TABLE user_accounts ADD FOREIGN KEY (org_id) REFERENCES organizations(id);
```

#### 6.4.2 工作流引擎集成
```python
# 建议增加工作流支持，实现复杂的业务流程
class WorkflowDefinition(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    workflow_name = db.Column(db.String(100), nullable=False)
    workflow_config = db.Column(db.JSON)  # 工作流配置
    is_active = db.Column(db.Boolean, default=True)

class WorkflowInstance(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    workflow_id = db.Column(db.Integer, db.ForeignKey('workflow_definition.id'))
    business_id = db.Column(db.String(50))  # 业务对象ID
    current_step = db.Column(db.String(50))
    status = db.Column(db.String(20))
    created_time = db.Column(db.DateTime, default=datetime.utcnow)
```

### 6.5 部署与运维建议

#### 6.5.1 容器化部署
```dockerfile
# 建议使用Docker容器化部署
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 5000

CMD ["gunicorn", "--bind", "0.0.0.0:5000", "wsgi:app"]
```

#### 6.5.2 自动化运维
```yaml
# 建议使用docker-compose进行服务编排
version: '3.8'
services:
  web:
    build: .
    ports:
      - "5000:5000"
    environment:
      - DATABASE_URL=mysql://user:pass@db:3306/Old_Customer_System
    depends_on:
      - db
      - redis

  db:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpass
      MYSQL_DATABASE: Old_Customer_System
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:6-alpine

volumes:
  mysql_data:
```

---

## 总结

本系统设计方案基于RBAC权限模型，采用Flask + MySQL技术栈，实现了老客户回访与跟进的完整业务流程。设计充分考虑了数据安全、权限控制、系统扩展性等关键因素，为高端服务机构提供了一套完整的客户管理解决方案。

在实际开发过程中，建议按照模块化的方式逐步实现，先完成核心功能，再逐步添加增强功能。同时，应该根据实际业务需求对本设计方案进行适当调整和优化。
```
```
