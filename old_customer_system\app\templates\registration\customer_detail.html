{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">客户详情</h1>
    <div class="btn-group">
        {% if current_user.role in ['admin', 'online_consultant'] or (current_user.role == 'manager' and customer.creator.department_id == current_user.department_id) %}
        <a href="{{ url_for('registration.edit_customer', customer_id=customer.id) }}" class="btn btn-primary">
            <i class="fas fa-edit me-2"></i>编辑信息
        </a>
        {% endif %}
        <a href="{{ url_for('registration.customers') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>返回列表
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <!-- 基本信息 -->
        <div class="card mb-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>基本信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td width="120"><strong>客户卡号:</strong></td>
                                <td><code class="fs-5">{{ customer.card_number }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>所属现场:</strong></td>
                                <td>
                                    <span class="badge bg-info">{{ customer.onsite_consultant.real_name }}</span>
                                    <small class="text-muted">({{ customer.onsite_consultant.username }})</small>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>所属网资:</strong></td>
                                <td>
                                    <span class="badge bg-success">{{ customer.online_consultant.real_name }}</span>
                                    <small class="text-muted">({{ customer.online_consultant.username }})</small>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td width="120"><strong>激活渠道:</strong></td>
                                <td>
                                    <span class="badge bg-secondary">{{ customer.channel.name }}</span>
                                    {% if customer.channel.category %}
                                    <br><small class="text-muted">{{ customer.channel.category }}</small>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>最近来院:</strong></td>
                                <td>
                                    {% if customer.last_visit_date %}
                                        {{ customer.last_visit_date.strftime('%Y年%m月%d日') }}
                                    {% else %}
                                        <span class="text-muted">未记录</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>登记时间:</strong></td>
                                <td>{{ customer.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 咨询内容 -->
        <div class="card mb-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-comment-dots me-2"></i>咨询内容
                </h5>
            </div>
            <div class="card-body">
                {% if customer.inquiry_content %}
                    <p class="mb-0">{{ customer.inquiry_content }}</p>
                {% else %}
                    <p class="text-muted mb-0">暂无咨询内容记录</p>
                {% endif %}
            </div>
        </div>
        
        <!-- 跟进情况 -->
        <div class="card mb-3">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tasks me-2"></i>跟进情况
                </h5>
                {% if current_user.role == 'onsite_consultant' and customer.onsite_consultant_id == current_user.id %}
                <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#followUpModal">
                    <i class="fas fa-plus me-1"></i>添加跟进
                </button>
                {% endif %}
            </div>
            <div class="card-body">
                {% if customer.follow_up_note %}
                    <div class="alert alert-light">
                        <p class="mb-2">{{ customer.follow_up_note }}</p>
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    跟进时间: {{ customer.follow_up_time.strftime('%Y-%m-%d %H:%M') if customer.follow_up_time else '未记录' }}
                                </small>
                            </div>
                            <div class="col-md-6 text-md-end">
                                <small class="text-muted">
                                    <i class="fas fa-user me-1"></i>
                                    跟进人: {{ customer.follow_up_consultant.real_name if customer.follow_up_consultant else '未记录' }}
                                </small>
                            </div>
                        </div>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                        <p class="text-muted">暂无跟进记录</p>
                        {% if current_user.role == 'onsite_consultant' and customer.onsite_consultant_id == current_user.id %}
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#followUpModal">
                            <i class="fas fa-plus me-2"></i>添加首次跟进
                        </button>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- 操作记录 -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>操作记录
                </h6>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">信息登记</h6>
                            <p class="timeline-text">
                                {{ customer.creator.real_name }} 登记了客户信息
                            </p>
                            <small class="text-muted">{{ customer.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                        </div>
                    </div>
                    
                    {% if customer.updated_at != customer.created_at %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-warning"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">信息更新</h6>
                            <p class="timeline-text">客户信息被修改</p>
                            <small class="text-muted">{{ customer.updated_at.strftime('%Y-%m-%d %H:%M') }}</small>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if customer.follow_up_time %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-info"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">跟进记录</h6>
                            <p class="timeline-text">
                                {{ customer.follow_up_consultant.real_name if customer.follow_up_consultant else '系统' }} 添加了跟进记录
                            </p>
                            <small class="text-muted">{{ customer.follow_up_time.strftime('%Y-%m-%d %H:%M') }}</small>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- 统计信息 -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>统计信息
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary mb-1">
                                {{ (customer.created_at.now() - customer.created_at).days }}
                            </h4>
                            <small class="text-muted">登记天数</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info mb-1">
                            {% if customer.follow_up_note %}1{% else %}0{% endif %}
                        </h4>
                        <small class="text-muted">跟进次数</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 跟进模态框 -->
{% if current_user.role == 'onsite_consultant' and customer.onsite_consultant_id == current_user.id %}
<div class="modal fade" id="followUpModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加跟进记录</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('registration.update_follow_up', customer_id=customer.id) }}">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <div class="mb-3">
                        <label for="follow_up_note" class="form-label">跟进内容</label>
                        <textarea class="form-control" id="follow_up_note" name="follow_up_note" rows="4" 
                                  placeholder="请输入跟进情况..." required></textarea>
                        <div class="form-text">最多500个字符</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存跟进</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<style>
/* 时间线样式 */
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -23px;
    top: 5px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #dee2e6;
}

.timeline-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.timeline-text {
    font-size: 0.85rem;
    margin-bottom: 5px;
    color: #6c757d;
}

/* 客户卡号样式 */
code {
    background-color: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 600;
}

/* 徽章样式调整 */
.badge {
    font-size: 0.8rem;
}
</style>

<script>
$(document).ready(function() {
    // 跟进表单AJAX提交
    $('#followUpModal form').on('submit', function(e) {
        e.preventDefault();
        
        const content = $('#follow_up_note').val().trim();
        if (!content) {
            alert('请输入跟进内容');
            return false;
        }
        
        if (!confirm('确定要保存这条跟进记录吗？')) {
            return false;
        }
        
        // 显示加载状态
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.text();
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>保存中...');
        
        // AJAX提交
        $.ajax({
            url: $(this).attr('action'),
            method: 'POST',
            data: $(this).serialize(),
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // 显示成功消息
                    alert('跟进信息保存成功！');
                    // 关闭模态框
                    $('#followUpModal').modal('hide');
                    // 刷新页面以显示最新的跟进信息
                    location.reload();
                } else {
                    alert('保存失败: ' + response.message);
                }
            },
            error: function(xhr, status, error) {
                alert('保存失败: ' + error);
            },
            complete: function() {
                // 恢复按钮状态
                submitBtn.prop('disabled', false).text(originalText);
            }
        });
    });
    
    // 字符计数
    $('#follow_up_note').on('input', function() {
        const maxLength = 500;
        const currentLength = $(this).val().length;
        const remaining = maxLength - currentLength;
        
        let color = 'text-muted';
        if (remaining < 50) color = 'text-warning';
        if (remaining < 10) color = 'text-danger';
        
        $(this).siblings('.form-text').html(`还可输入 <span class="${color}">${remaining}</span> 个字符`);
    });
    
    // 模态框关闭时清空表单
    $('#followUpModal').on('hidden.bs.modal', function() {
        $('#follow_up_note').val('');
        $(this).find('.form-text').html('最多500个字符');
    });
});
</script>
{% endblock %}