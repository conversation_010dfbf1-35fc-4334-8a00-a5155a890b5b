"""
Excel公式引擎
支持在Python中执行Excel公式逻辑
"""

import pandas as pd
import numpy as np
import re
from typing import Dict, Any, List, Union
import logging
from datetime import datetime, timedelta


class ExcelFormulaEngine:
    """Excel公式计算引擎"""
    
    def __init__(self, logger=None):
        self.logger = logger or logging.getLogger(__name__)
        self.data_context = {}  # 数据上下文
        self.formula_cache = {}  # 公式缓存
        
    def set_data_context(self, data: Dict[str, pd.DataFrame]):
        """设置数据上下文"""
        self.data_context = data
        self.logger.info(f"设置数据上下文，包含 {len(data)} 个数据表")
        
    def execute_formula(self, formula: str, row_data: Dict = None) -> Any:
        """执行Excel公式"""
        try:
            # 预处理公式
            processed_formula = self._preprocess_formula(formula, row_data)
            
            # 执行公式
            result = self._evaluate_formula(processed_formula, row_data)
            
            return result
        except Exception as e:
            self.logger.error(f"公式执行失败: {formula}, 错误: {str(e)}")
            return None
    
    def _preprocess_formula(self, formula: str, row_data: Dict = None) -> str:
        """预处理公式，转换Excel函数为Python代码"""
        if not formula.startswith('='):
            return formula
            
        # 移除等号
        formula = formula[1:]
        
        # 替换Excel函数为Python等价函数
        replacements = {
            'IF': 'self._excel_if',
            'SUM': 'self._excel_sum',
            'SUMIF': 'self._excel_sumif',
            'SUMIFS': 'self._excel_sumifs',
            'COUNTIF': 'self._excel_countif',
            'COUNTIFS': 'self._excel_countifs',
            'VLOOKUP': 'self._excel_vlookup',
            'INDEX': 'self._excel_index',
            'MATCH': 'self._excel_match',
            'CONCATENATE': 'self._excel_concatenate',
            'LEFT': 'self._excel_left',
            'RIGHT': 'self._excel_right',
            'MID': 'self._excel_mid',
            'LEN': 'len',
            'UPPER': 'str.upper',
            'LOWER': 'str.lower',
            'TODAY': 'self._excel_today',
            'NOW': 'self._excel_now',
            'YEAR': 'self._excel_year',
            'MONTH': 'self._excel_month',
            'DAY': 'self._excel_day',
            'MAX': 'max',
            'MIN': 'min',
            'AVERAGE': 'self._excel_average',
            'ROUND': 'round',
            'ABS': 'abs'
        }
        
        for excel_func, python_func in replacements.items():
            pattern = r'\b' + excel_func + r'\('
            formula = re.sub(pattern, python_func + '(', formula, flags=re.IGNORECASE)
        
        # 处理单元格引用（如果有行数据）
        if row_data:
            formula = self._replace_cell_references(formula, row_data)
            
        return formula
    
    def _replace_cell_references(self, formula: str, row_data: Dict) -> str:
        """替换单元格引用为实际值"""
        # 简单的列引用替换（如 A1 -> row_data['A']）
        for col_name, value in row_data.items():
            # 替换列名引用
            formula = formula.replace(f'[{col_name}]', f'"{value}"' if isinstance(value, str) else str(value))
            
        return formula
    
    def _evaluate_formula(self, formula: str, row_data: Dict = None) -> Any:
        """安全地评估公式"""
        try:
            # 创建安全的执行环境
            safe_dict = {
                'self': self,
                'pd': pd,
                'np': np,
                '__builtins__': {},
                'row_data': row_data or {}
            }
            
            # 执行公式
            result = eval(formula, safe_dict)
            return result
        except Exception as e:
            self.logger.error(f"公式评估失败: {formula}, 错误: {str(e)}")
            return None
    
    # Excel函数实现
    def _excel_if(self, condition, true_value, false_value):
        """IF函数"""
        return true_value if condition else false_value
    
    def _excel_sum(self, *args):
        """SUM函数"""
        total = 0
        for arg in args:
            if isinstance(arg, (list, tuple)):
                total += sum(x for x in arg if isinstance(x, (int, float)))
            elif isinstance(arg, (int, float)):
                total += arg
        return total
    
    def _excel_sumif(self, range_data, criteria, sum_range=None):
        """SUMIF函数"""
        if sum_range is None:
            sum_range = range_data
            
        total = 0
        for i, value in enumerate(range_data):
            if self._match_criteria(value, criteria):
                if i < len(sum_range):
                    total += sum_range[i] if isinstance(sum_range[i], (int, float)) else 0
        return total
    
    def _excel_countif(self, range_data, criteria):
        """COUNTIF函数"""
        count = 0
        for value in range_data:
            if self._match_criteria(value, criteria):
                count += 1
        return count
    
    def _excel_vlookup(self, lookup_value, table_array, col_index, exact_match=True):
        """VLOOKUP函数"""
        try:
            if isinstance(table_array, str) and table_array in self.data_context:
                df = self.data_context[table_array]
                # 在第一列查找值
                for idx, row in df.iterrows():
                    if row.iloc[0] == lookup_value:
                        return row.iloc[col_index - 1]  # Excel是1基索引
            return None
        except Exception:
            return None
    
    def _excel_concatenate(self, *args):
        """CONCATENATE函数"""
        return ''.join(str(arg) for arg in args)
    
    def _excel_left(self, text, num_chars):
        """LEFT函数"""
        return str(text)[:num_chars]
    
    def _excel_right(self, text, num_chars):
        """RIGHT函数"""
        return str(text)[-num_chars:]
    
    def _excel_mid(self, text, start_num, num_chars):
        """MID函数"""
        return str(text)[start_num-1:start_num-1+num_chars]
    
    def _excel_today(self):
        """TODAY函数"""
        return datetime.now().date()
    
    def _excel_now(self):
        """NOW函数"""
        return datetime.now()
    
    def _excel_year(self, date_value):
        """YEAR函数"""
        if isinstance(date_value, datetime):
            return date_value.year
        return None
    
    def _excel_month(self, date_value):
        """MONTH函数"""
        if isinstance(date_value, datetime):
            return date_value.month
        return None
    
    def _excel_day(self, date_value):
        """DAY函数"""
        if isinstance(date_value, datetime):
            return date_value.day
        return None
    
    def _excel_average(self, *args):
        """AVERAGE函数"""
        values = []
        for arg in args:
            if isinstance(arg, (list, tuple)):
                values.extend(x for x in arg if isinstance(x, (int, float)))
            elif isinstance(arg, (int, float)):
                values.append(arg)
        return sum(values) / len(values) if values else 0
    
    def _match_criteria(self, value, criteria):
        """匹配条件"""
        if isinstance(criteria, str):
            if criteria.startswith('>='):
                return value >= float(criteria[2:])
            elif criteria.startswith('<='):
                return value <= float(criteria[2:])
            elif criteria.startswith('>'):
                return value > float(criteria[1:])
            elif criteria.startswith('<'):
                return value < float(criteria[1:])
            elif criteria.startswith('='):
                return value == criteria[1:]
            else:
                return value == criteria
        else:
            return value == criteria


class ExcelReportEngine:
    """Excel报表引擎"""
    
    def __init__(self, formula_engine: ExcelFormulaEngine, logger=None):
        self.formula_engine = formula_engine
        self.logger = logger or logging.getLogger(__name__)
        self.report_templates = {}
        
    def load_report_template(self, template_name: str, template_config: Dict):
        """加载报表模板配置"""
        self.report_templates[template_name] = template_config
        self.logger.info(f"加载报表模板: {template_name}")
        
    def generate_report(self, template_name: str, data_context: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """生成报表"""
        if template_name not in self.report_templates:
            raise ValueError(f"报表模板 {template_name} 不存在")
            
        template = self.report_templates[template_name]
        self.formula_engine.set_data_context(data_context)
        
        # 创建结果DataFrame
        result_df = pd.DataFrame()
        
        # 处理每个计算列
        for column_config in template.get('columns', []):
            column_name = column_config['name']
            formula = column_config['formula']
            
            self.logger.info(f"计算列: {column_name}")
            
            # 根据数据源计算列值
            if 'source_data' in column_config:
                source_df = data_context[column_config['source_data']]
                values = []
                
                for idx, row in source_df.iterrows():
                    row_dict = row.to_dict()
                    value = self.formula_engine.execute_formula(formula, row_dict)
                    values.append(value)
                
                result_df[column_name] = values
            else:
                # 单一值计算
                value = self.formula_engine.execute_formula(formula)
                result_df[column_name] = [value]
        
        return result_df
