#!/usr/bin/env python3
"""检查JavaScript语法错误"""

import re
import os

def check_js_syntax(file_path):
    """检查JavaScript语法"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 提取script标签中的JavaScript代码
    script_pattern = r'<script[^>]*>(.*?)</script>'
    scripts = re.findall(script_pattern, content, re.DOTALL)
    
    if not scripts:
        print("没有找到JavaScript代码")
        return
    
    js_code = scripts[0]  # 取第一个script标签的内容
    
    # 检查常见的语法错误
    lines = js_code.split('\n')
    
    print(f"检查JavaScript代码，共 {len(lines)} 行")
    
    # 检查括号匹配
    open_brackets = 0
    open_braces = 0
    open_parens = 0
    
    for i, line in enumerate(lines, 1):
        line = line.strip()
        if not line or line.startswith('//'):
            continue
            
        # 计算括号
        open_brackets += line.count('[') - line.count(']')
        open_braces += line.count('{') - line.count('}')
        open_parens += line.count('(') - line.count(')')
        
        # 检查模板字符串
        if '`' in line:
            backtick_count = line.count('`')
            if backtick_count % 2 != 0:
                print(f"第 {i} 行可能有未闭合的模板字符串: {line}")
        
        # 检查常见错误模式
        if line.endswith(',') and ('{' in line or '}' in line):
            print(f"第 {i} 行可能有多余的逗号: {line}")
        
        if ')' in line and line.strip().endswith(')'):
            # 检查是否有多余的括号
            temp_line = line
            while ')' in temp_line:
                pos = temp_line.rfind(')')
                if pos > 0 and temp_line[pos-1] == ')':
                    print(f"第 {i} 行可能有多余的括号: {line}")
                    break
                temp_line = temp_line[:pos]
    
    print(f"括号匹配检查:")
    print(f"  方括号: {open_brackets} (应该为0)")
    print(f"  花括号: {open_braces} (应该为0)")
    print(f"  圆括号: {open_parens} (应该为0)")
    
    # 查找第1118行附近的代码
    if len(lines) >= 1118:
        print(f"\n第1118行附近的代码:")
        start = max(0, 1118 - 10)
        end = min(len(lines), 1118 + 10)
        for i in range(start, end):
            marker = " >>> " if i == 1117 else "     "  # 1117因为是0-based
            print(f"{marker}{i+1:4d}: {lines[i]}")

    # 检查每个函数的括号匹配
    print(f"\n检查函数括号匹配:")
    in_function = False
    function_name = ""
    function_braces = 0

    for i, line in enumerate(lines, 1):
        line = line.strip()
        if line.startswith('function ') or 'function(' in line:
            in_function = True
            function_name = line.split('(')[0].replace('function ', '').strip()
            function_braces = 0
            print(f"开始函数: {function_name} (第{i}行)")

        if in_function:
            function_braces += line.count('{') - line.count('}')
            if function_braces == 0 and '{' in line:
                print(f"结束函数: {function_name} (第{i}行)")
                in_function = False
            elif function_braces < 0:
                print(f"❌ 函数 {function_name} 在第{i}行有多余的 '}}': {line}")
                in_function = False

if __name__ == '__main__':
    file_path = 'app/templates/rfm_reports/data_source_management.html'
    if os.path.exists(file_path):
        check_js_syntax(file_path)
    else:
        print(f"文件不存在: {file_path}")
