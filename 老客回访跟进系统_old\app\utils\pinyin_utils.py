"""
中文拼音转换工具
"""
import re

# 中文字符到拼音首字母的映射表（常用汉字）
PINYIN_DICT = {
    '阿': 'A', '啊': 'A', '爱': 'A', '安': 'A', '按': 'A', '暗': 'A',
    '八': 'B', '把': 'B', '白': 'B', '百': 'B', '班': 'B', '办': 'B', '帮': 'B', '包': 'B', '保': 'B', '报': 'B', '北': 'B', '被': 'B', '本': 'B', '比': 'B', '边': 'B', '变': 'B', '表': 'B', '别': 'B', '并': 'B', '不': 'B',
    '才': 'C', '采': 'C', '参': 'C', '草': 'C', '层': 'C', '查': 'C', '长': 'C', '常': 'C', '场': 'C', '车': 'C', '成': 'C', '城': 'C', '出': 'C', '处': 'C', '川': 'C', '传': 'C', '创': 'C', '春': 'C', '次': 'C', '从': 'C', '村': 'C', '陈': 'C',
    '大': 'D', '带': 'D', '单': 'D', '当': 'D', '到': 'D', '道': 'D', '的': 'D', '得': 'D', '地': 'D', '第': 'D', '点': 'D', '电': 'D', '定': 'D', '东': 'D', '动': 'D', '都': 'D', '对': 'D', '多': 'D', '度': 'D',
    '而': 'E', '儿': 'E', '二': 'E',
    '发': 'F', '法': 'F', '反': 'F', '方': 'F', '放': 'F', '非': 'F', '分': 'F', '风': 'F', '服': 'F', '府': 'F', '副': 'F', '复': 'F',
    '该': 'G', '改': 'G', '感': 'G', '干': 'G', '刚': 'G', '高': 'G', '告': 'G', '个': 'G', '给': 'G', '根': 'G', '更': 'G', '工': 'G', '公': 'G', '功': 'G', '共': 'G', '关': 'G', '管': 'G', '光': 'G', '广': 'G', '规': 'G', '国': 'G', '过': 'G', '顾': 'G',
    '还': 'H', '海': 'H', '好': 'H', '号': 'H', '和': 'H', '合': 'H', '很': 'H', '后': 'H', '候': 'H', '花': 'H', '华': 'H', '化': 'H', '话': 'H', '环': 'H', '换': 'H', '回': 'H', '会': 'H', '活': 'H', '火': 'H',
    '机': 'J', '基': 'J', '及': 'J', '级': 'J', '即': 'J', '极': 'J', '几': 'J', '己': 'J', '记': 'J', '技': 'J', '际': 'J', '加': 'J', '家': 'J', '价': 'J', '间': 'J', '建': 'J', '见': 'J', '将': 'J', '交': 'J', '教': 'J', '接': 'J', '结': 'J', '解': 'J', '介': 'J', '金': 'J', '今': 'J', '进': 'J', '近': 'J', '经': 'J', '就': 'J', '局': 'J', '决': 'J', '军': 'J',
    '开': 'K', '看': 'K', '可': 'K', '科': 'K', '客': 'K', '空': 'K', '口': 'K', '快': 'K', '况': 'K',
    '来': 'L', '老': 'L', '了': 'L', '类': 'L', '里': 'L', '理': 'L', '力': 'L', '立': 'L', '利': 'L', '连': 'L', '联': 'L', '量': 'L', '两': 'L', '林': 'L', '流': 'L', '六': 'L', '龙': 'L', '路': 'L', '论': 'L', '落': 'L', '李': 'L', '刘': 'L',
    '马': 'M', '吗': 'M', '买': 'M', '卖': 'M', '满': 'M', '毛': 'M', '么': 'M', '没': 'M', '每': 'M', '美': 'M', '门': 'M', '们': 'M', '民': 'M', '明': 'M', '名': 'M', '目': 'M', '母': 'M',
    '那': 'N', '哪': 'N', '南': 'N', '难': 'N', '内': 'N', '能': 'N', '你': 'N', '年': 'N', '农': 'N', '女': 'N',
    '哦': 'O', '欧': 'O',
    '排': 'P', '盘': 'P', '跑': 'P', '配': 'P', '平': 'P', '品': 'P', '普': 'P',
    '七': 'Q', '其': 'Q', '起': 'Q', '气': 'Q', '前': 'Q', '钱': 'Q', '强': 'Q', '情': 'Q', '请': 'Q', '区': 'Q', '去': 'Q', '全': 'Q', '确': 'Q', '群': 'Q',
    '然': 'R', '让': 'R', '人': 'R', '认': 'R', '任': 'R', '日': 'R', '如': 'R', '入': 'R', '容': 'R',
    '三': 'S', '色': 'S', '山': 'S', '商': 'S', '上': 'S', '少': 'S', '社': 'S', '设': 'S', '生': 'S', '声': 'S', '省': 'S', '时': 'S', '十': 'S', '实': 'S', '使': 'S', '是': 'S', '市': 'S', '事': 'S', '手': 'S', '首': 'S', '受': 'S', '书': 'S', '数': 'S', '水': 'S', '说': 'S', '思': 'S', '四': 'S', '送': 'S', '苏': 'S', '算': 'S', '所': 'S',
    '他': 'T', '她': 'T', '它': 'T', '台': 'T', '太': 'T', '谈': 'T', '特': 'T', '提': 'T', '体': 'T', '天': 'T', '条': 'T', '听': 'T', '通': 'T', '同': 'T', '头': 'T', '图': 'T', '土': 'T', '团': 'T', '推': 'T', '拖': 'T',
    '外': 'W', '完': 'W', '万': 'W', '王': 'W', '网': 'W', '往': 'W', '为': 'W', '位': 'W', '文': 'W', '问': 'W', '我': 'W', '无': 'W', '五': 'W', '物': 'W', '络': 'L',
    '西': 'X', '希': 'X', '系': 'X', '下': 'X', '先': 'X', '现': 'X', '线': 'X', '相': 'X', '想': 'X', '向': 'X', '项': 'X', '小': 'X', '校': 'X', '新': 'X', '信': 'X', '行': 'X', '形': 'X', '性': 'X', '修': 'X', '学': 'X', '选': 'X', '训': 'X', '寻': 'X',
    '要': 'Y', '也': 'Y', '业': 'Y', '一': 'Y', '医': 'Y', '以': 'Y', '已': 'Y', '意': 'Y', '因': 'Y', '应': 'Y', '用': 'Y', '有': 'Y', '又': 'Y', '于': 'Y', '与': 'Y', '语': 'Y', '元': 'Y', '原': 'Y', '员': 'Y', '院': 'Y', '月': 'Y', '越': 'Y', '云': 'Y', '运': 'Y',
    '在': 'Z', '再': 'Z', '早': 'Z', '怎': 'Z', '增': 'Z', '展': 'Z', '站': 'Z', '张': 'Z', '长': 'Z', '找': 'Z', '这': 'Z', '着': 'Z', '真': 'Z', '正': 'Z', '政': 'Z', '之': 'Z', '知': 'Z', '直': 'Z', '只': 'Z', '制': 'Z', '中': 'Z', '种': 'Z', '重': 'Z', '主': 'Z', '住': 'Z', '注': 'Z', '专': 'Z', '转': 'Z', '状': 'Z', '准': 'Z', '资': 'Z', '自': 'Z', '总': 'Z', '走': 'Z', '组': 'Z', '作': 'Z', '做': 'Z', '赵': 'Z', '咨': 'Z', '询': 'X'
}

def get_pinyin_first_letter(text):
    """
    获取中文文本的拼音首字母
    
    Args:
        text (str): 输入的中文文本
        
    Returns:
        str: 拼音首字母组合
    """
    if not text:
        return ''
    
    result = []
    for char in text:
        if char in PINYIN_DICT:
            result.append(PINYIN_DICT[char])
        elif char.isalpha():
            # 如果是英文字母，直接转大写
            result.append(char.upper())
        elif char.isdigit():
            # 如果是数字，保持原样
            result.append(char)
        # 其他字符（标点符号等）忽略
    
    return ''.join(result)

def match_search_term(text, search_term):
    """
    检查文本是否匹配搜索词（支持中文、拼音首字母、英文）
    
    Args:
        text (str): 要搜索的文本
        search_term (str): 搜索词
        
    Returns:
        bool: 是否匹配
    """
    if not text or not search_term:
        return True
    
    text = text.strip()
    search_term = search_term.strip().upper()
    
    if not search_term:
        return True
    
    # 1. 直接匹配（忽略大小写）
    if search_term.lower() in text.lower():
        return True
    
    # 2. 拼音首字母匹配
    pinyin_letters = get_pinyin_first_letter(text)
    if search_term in pinyin_letters:
        return True
    
    # 3. 部分拼音首字母匹配（连续匹配）
    if len(search_term) > 1:
        for i in range(len(pinyin_letters) - len(search_term) + 1):
            if pinyin_letters[i:i+len(search_term)] == search_term:
                return True
    
    return False

def add_search_data_to_options(options_list, text_field='name', value_field='id'):
    """
    为选项列表添加搜索数据
    
    Args:
        options_list (list): 选项列表
        text_field (str): 文本字段名
        value_field (str): 值字段名
        
    Returns:
        list: 添加了搜索数据的选项列表
    """
    result = []
    for option in options_list:
        if hasattr(option, text_field):
            text = getattr(option, text_field)
            search_data = {
                'text': text,
                'pinyin': get_pinyin_first_letter(text),
                'value': getattr(option, value_field) if hasattr(option, value_field) else option.get(value_field)
            }
            
            # 如果是对象，转换为字典
            if hasattr(option, '__dict__'):
                option_dict = {
                    value_field: getattr(option, value_field),
                    text_field: text,
                    'search_data': search_data
                }
            else:
                option_dict = option.copy()
                option_dict['search_data'] = search_data
            
            result.append(option_dict)
        else:
            result.append(option)
    
    return result

# 测试函数
if __name__ == '__main__':
    # 测试拼音首字母转换
    test_names = ['张三', '李四', '王五', '赵六', '陈七', 'John Smith', '美容顾问', '网络推广']
    
    print("拼音首字母转换测试:")
    for name in test_names:
        pinyin = get_pinyin_first_letter(name)
        print(f"{name} -> {pinyin}")
    
    print("\n搜索匹配测试:")
    test_cases = [
        ('张三', 'z'),
        ('张三', 'zs'),
        ('张三', '张'),
        ('美容顾问', 'mr'),
        ('美容顾问', 'mrgw'),
        ('美容顾问', '美容'),
        ('John Smith', 'j'),
        ('John Smith', 'js'),
        ('网络推广', 'wl'),
        ('网络推广', 'wltg'),
    ]
    
    for text, search in test_cases:
        result = match_search_term(text, search)
        print(f"'{text}' 匹配 '{search}': {result}")
