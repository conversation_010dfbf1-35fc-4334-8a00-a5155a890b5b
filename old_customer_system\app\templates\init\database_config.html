{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card mt-5">
                <div class="card-header text-center">
                    <h4><i class="fas fa-database me-2"></i>数据库配置</h4>
                    <p class="text-muted mb-0">请配置MySQL数据库连接信息</p>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            {{ form.host.label(class="form-label") }}
                            {{ form.host(class="form-control") }}
                            {% if form.host.errors %}
                                <div class="text-danger">
                                    {% for error in form.host.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.port.label(class="form-label") }}
                            {{ form.port(class="form-control") }}
                            {% if form.port.errors %}
                                <div class="text-danger">
                                    {% for error in form.port.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.username.label(class="form-label") }}
                            {{ form.username(class="form-control") }}
                            {% if form.username.errors %}
                                <div class="text-danger">
                                    {% for error in form.username.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.password.label(class="form-label") }}
                            {{ form.password(class="form-control") }}
                            <small class="form-text text-muted">如果没有密码请留空</small>
                            {% if form.password.errors %}
                                <div class="text-danger">
                                    {% for error in form.password.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.database.label(class="form-label") }}
                            {{ form.database(class="form-control") }}
                            {% if form.database.errors %}
                                <div class="text-danger">
                                    {% for error in form.database.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        {{ form.submit(class="btn btn-primary w-100") }}
                    </form>
                </div>
            </div>
            
            <div class="text-center mt-3">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    请确保MySQL服务已启动，并且提供的用户具有创建数据库的权限
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}