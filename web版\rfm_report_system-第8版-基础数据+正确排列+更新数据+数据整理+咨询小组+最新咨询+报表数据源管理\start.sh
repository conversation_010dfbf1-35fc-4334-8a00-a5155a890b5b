#!/bin/bash

echo "========================================"
echo "    RFM报表处理系统启动脚本"
echo "========================================"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python 3.8+"
    exit 1
fi

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo "创建虚拟环境..."
    python3 -m venv venv
    if [ $? -ne 0 ]; then
        echo "错误: 创建虚拟环境失败"
        exit 1
    fi
fi

# 激活虚拟环境
echo "激活虚拟环境..."
source venv/bin/activate

# 安装依赖
echo "检查并安装依赖..."
pip install -r requirements.txt

# 检查环境配置文件
if [ ! -f ".env" ]; then
    echo "创建环境配置文件..."
    cp .env.example .env
    echo
    echo "请编辑 .env 文件配置数据库连接信息"
    echo "然后重新运行此脚本"
    exit 0
fi

# 检查是否需要初始化数据库
python3 -c "from app import create_app; from app.models import User; app = create_app(); app.app_context().push(); exit(0 if User.query.count() > 0 else 1)" 2>/dev/null
if [ $? -ne 0 ]; then
    echo
    echo "检测到首次运行，开始初始化数据库..."
    python3 init_db.py
    if [ $? -ne 0 ]; then
        echo "数据库初始化失败"
        exit 1
    fi
fi

# 启动系统
echo
echo "启动RFM报表处理系统..."
echo "访问地址: http://localhost:5000"
echo "按 Ctrl+C 停止服务"
echo
python3 run.py
