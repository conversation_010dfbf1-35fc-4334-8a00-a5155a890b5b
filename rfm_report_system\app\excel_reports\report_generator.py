"""
简化的报表生成器
实现预定义模板的一键生成功能
"""

import pandas as pd
import logging
from datetime import datetime, timedelta
from typing import Dict, Any
import os


class SimpleReportGenerator:
    """简化的报表生成器"""
    
    def __init__(self, db_engine, logger=None):
        self.db_engine = db_engine
        self.logger = logger or logging.getLogger(__name__)
        
    def generate_rfm_analysis_report(self) -> pd.DataFrame:
        """生成会员RFM分析报表"""
        try:
            self.logger.info("开始生成会员RFM分析报表")
            
            # 获取会员基础数据
            query = """
            SELECT 
                会员卡号,
                COUNT(*) as 消费频次,
                SUM(执行业绩（真实金额）) as 消费金额,
                MAX(执行日期) as 最近消费日期,
                MIN(执行日期) as 首次消费日期,
                DATEDIFF(CURDATE(), MAX(执行日期)) as 最近消费间隔天数
            FROM 客户执行明细表 
            WHERE 会员卡号 IS NOT NULL 
              AND 执行业绩（真实金额） > 0
              AND 执行日期 >= DATE_SUB(CURDATE(), INTERVAL 2 YEAR)
            GROUP BY 会员卡号
            HAVING 消费金额 > 0
            ORDER BY 消费金额 DESC
            LIMIT 5000
            """
            
            df = pd.read_sql(query, self.db_engine)
            
            if df.empty:
                self.logger.warning("没有找到会员数据")
                return pd.DataFrame()
            
            # 计算RFM值
            df['R值'] = df['最近消费间隔天数'].fillna(999)
            df['F值'] = df['消费频次']
            df['M值'] = df['消费金额']
            
            # R值分级 (最近消费间隔天数，越小越好)
            df['R等级'] = df['R值'].apply(lambda x: 
                '高' if x <= 30 else 
                '中' if x <= 90 else 
                '低'
            )
            
            # F值分级 (消费频次，越高越好)
            df['F等级'] = df['F值'].apply(lambda x: 
                '高' if x >= 10 else 
                '中' if x >= 5 else 
                '低'
            )
            
            # M值分级 (消费金额，越高越好)
            df['M等级'] = df['M值'].apply(lambda x: 
                '高' if x >= 50000 else 
                '中' if x >= 20000 else 
                '低'
            )
            
            # 综合等级
            df['综合等级'] = df['R等级'] + df['F等级'] + df['M等级']
            
            # 客户价值分类
            df['客户价值'] = df['综合等级'].apply(lambda x:
                'VIP客户' if x == '高高高' else
                '重要客户' if x.count('高') >= 2 else
                '潜力客户' if x.count('中') >= 2 else
                '普通客户'
            )
            
            # 选择输出列
            result_df = df[[
                '会员卡号', '消费频次', '消费金额', '最近消费日期', 
                'R值', 'F值', 'M值', 'R等级', 'F等级', 'M等级', 
                '综合等级', '客户价值'
            ]].copy()
            
            # 格式化数值
            result_df['消费金额'] = result_df['消费金额'].round(2)
            result_df['最近消费日期'] = pd.to_datetime(result_df['最近消费日期']).dt.strftime('%Y-%m-%d')
            
            self.logger.info(f"会员RFM分析报表生成完成，共 {len(result_df)} 行数据")
            return result_df
            
        except Exception as e:
            self.logger.error(f"生成会员RFM分析报表失败: {str(e)}")
            raise
    
    def generate_department_performance_report(self) -> pd.DataFrame:
        """生成科室业绩分析报表"""
        try:
            self.logger.info("开始生成科室业绩分析报表")
            
            # 获取科室业绩数据
            query = """
            SELECT 
                科室,
                COUNT(DISTINCT 会员卡号) as 消费会员数,
                COUNT(*) as 消费次数,
                SUM(执行业绩（真实金额）) as 总业绩,
                AVG(执行业绩（真实金额）) as 平均客单价,
                MAX(执行业绩（真实金额）) as 最高单笔,
                MIN(执行业绩（真实金额）) as 最低单笔
            FROM 客户执行明细表 
            WHERE 科室 IS NOT NULL 
              AND 执行业绩（真实金额） > 0
              AND 执行日期 >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR)
            GROUP BY 科室
            HAVING 总业绩 > 0
            ORDER BY 总业绩 DESC
            """
            
            df = pd.read_sql(query, self.db_engine)
            
            if df.empty:
                self.logger.warning("没有找到科室数据")
                return pd.DataFrame()
            
            # 计算业绩占比
            total_revenue = df['总业绩'].sum()
            df['业绩占比(%)'] = (df['总业绩'] / total_revenue * 100).round(2)
            
            # 业绩等级
            df['业绩等级'] = df['总业绩'].apply(lambda x:
                'A级' if x >= df['总业绩'].quantile(0.8) else
                'B级' if x >= df['总业绩'].quantile(0.6) else
                'C级' if x >= df['总业绩'].quantile(0.4) else
                'D级'
            )
            
            # 客户活跃度
            df['客户活跃度'] = df['消费次数'] / df['消费会员数']
            df['活跃度等级'] = df['客户活跃度'].apply(lambda x:
                '高活跃' if x >= 3 else
                '中活跃' if x >= 2 else
                '低活跃'
            )
            
            # 格式化数值
            df['总业绩'] = df['总业绩'].round(2)
            df['平均客单价'] = df['平均客单价'].round(2)
            df['最高单笔'] = df['最高单笔'].round(2)
            df['最低单笔'] = df['最低单笔'].round(2)
            df['客户活跃度'] = df['客户活跃度'].round(2)
            
            self.logger.info(f"科室业绩分析报表生成完成，共 {len(df)} 行数据")
            return df
            
        except Exception as e:
            self.logger.error(f"生成科室业绩分析报表失败: {str(e)}")
            raise
    
    def generate_top_category_report(self) -> pd.DataFrame:
        """生成TOP品类分析报表"""
        try:
            self.logger.info("开始生成TOP品类分析报表")
            
            # 获取品类业绩数据
            query = """
            SELECT 
                二级分类,
                COUNT(DISTINCT 会员卡号) as 消费会员数,
                COUNT(*) as 消费次数,
                SUM(执行业绩（真实金额）) as 总业绩,
                AVG(执行业绩（真实金额）) as 平均客单价
            FROM 客户执行明细表 
            WHERE 二级分类 IS NOT NULL 
              AND 二级分类 != ''
              AND 执行业绩（真实金额） > 0
              AND 执行日期 >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR)
            GROUP BY 二级分类
            HAVING 总业绩 > 0
            ORDER BY 总业绩 DESC
            LIMIT 20
            """
            
            df = pd.read_sql(query, self.db_engine)
            
            if df.empty:
                self.logger.warning("没有找到品类数据")
                return pd.DataFrame()
            
            # 添加排名
            df['排名'] = range(1, len(df) + 1)
            
            # 计算业绩占比
            total_revenue = df['总业绩'].sum()
            df['业绩占比(%)'] = (df['总业绩'] / total_revenue * 100).round(2)
            
            # 累计占比
            df['累计占比(%)'] = df['业绩占比(%)'].cumsum().round(2)
            
            # TOP标识
            df['TOP等级'] = df['排名'].apply(lambda x:
                'TOP5' if x <= 5 else
                'TOP10' if x <= 10 else
                'TOP20'
            )
            
            # 品类热度
            df['品类热度'] = df['消费次数'].apply(lambda x:
                '热门' if x >= df['消费次数'].quantile(0.8) else
                '一般' if x >= df['消费次数'].quantile(0.5) else
                '冷门'
            )
            
            # 格式化数值
            df['总业绩'] = df['总业绩'].round(2)
            df['平均客单价'] = df['平均客单价'].round(2)
            
            # 重新排列列顺序
            df = df[['排名', '二级分类', '总业绩', '业绩占比(%)', '累计占比(%)', 
                    '消费会员数', '消费次数', '平均客单价', 'TOP等级', '品类热度']]
            
            self.logger.info(f"TOP品类分析报表生成完成，共 {len(df)} 行数据")
            return df
            
        except Exception as e:
            self.logger.error(f"生成TOP品类分析报表失败: {str(e)}")
            raise
    
    def generate_report_by_template(self, template_name: str) -> pd.DataFrame:
        """根据模板名称生成报表"""
        template_mapping = {
            '会员RFM分析报表': self.generate_rfm_analysis_report,
            '科室业绩分析报表': self.generate_department_performance_report,
            'TOP品类分析报表': self.generate_top_category_report
        }
        
        if template_name not in template_mapping:
            raise ValueError(f"不支持的报表模板: {template_name}")
        
        return template_mapping[template_name]()
    
    def save_report_to_excel(self, df: pd.DataFrame, template_name: str, output_dir: str) -> str:
        """保存报表到Excel文件"""
        try:
            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{template_name}_{timestamp}.xlsx"
            filepath = os.path.join(output_dir, filename)
            
            # 保存到Excel
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name=template_name, index=False)
                
                # 获取工作表并设置列宽
                worksheet = writer.sheets[template_name]
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            self.logger.info(f"报表已保存到: {filepath}")
            return filename
            
        except Exception as e:
            self.logger.error(f"保存报表到Excel失败: {str(e)}")
            raise
