<!DOCTYPE html>
<html>
<head>
    <title>API测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; }
        .error { background-color: #f8d7da; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>RFM API测试页面</h1>
    
    <div>
        <button onclick="testAPI()">测试 /rfm/api/available-tasks</button>
        <button onclick="checkLogin()">检查登录状态</button>
        <button onclick="clearResults()">清空结果</button>
    </div>
    
    <div id="results"></div>
    
    <script>
        function addResult(message, isError = false) {
            const div = document.createElement('div');
            div.className = 'result ' + (isError ? 'error' : 'success');
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            document.getElementById('results').appendChild(div);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        function checkLogin() {
            fetch('/auth/check-login', {
                credentials: 'same-origin'
            })
            .then(response => {
                addResult(`登录检查响应状态: ${response.status}`);
                return response.text();
            })
            .then(text => {
                addResult(`登录检查响应: ${text}`);
            })
            .catch(error => {
                addResult(`登录检查错误: ${error.message}`, true);
            });
        }
        
        function testAPI() {
            addResult('开始测试API...');
            
            fetch('/rfm/api/available-tasks', {
                method: 'GET',
                credentials: 'same-origin',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => {
                addResult(`API响应状态: ${response.status}`);
                addResult(`API响应头: ${JSON.stringify(Object.fromEntries(response.headers.entries()))}`);
                
                if (response.status === 302) {
                    addResult('被重定向到登录页面', true);
                    return null;
                }
                
                return response.text();
            })
            .then(text => {
                if (text === null) return;
                
                addResult(`API原始响应: ${text}`);
                
                try {
                    const data = JSON.parse(text);
                    addResult(`API解析后数据: ${JSON.stringify(data, null, 2)}`);
                    
                    if (data.success) {
                        addResult(`成功获取 ${data.tasks.length} 个任务`);
                    } else {
                        addResult(`API返回错误: ${data.error}`, true);
                    }
                } catch (e) {
                    addResult(`JSON解析失败: ${e.message}`, true);
                }
            })
            .catch(error => {
                addResult(`API请求失败: ${error.message}`, true);
            });
        }
    </script>
</body>
</html>
