/**
 * 咨询归属RFM基盘体量筛选器增强版
 * 实现确认式筛选和多列布局优化
 */

// 筛选器状态管理
const FilterManager = {
    // 当前筛选状态
    currentState: {
        subdivisionLevels: [],
        memberCardLevels: [],
        hasChanges: false,
        isLoading: false
    },
    
    // 原始数据缓存
    originalData: null,
    
    // 缓存管理
    cache: {
        data: new Map(),
        maxSize: 50,
        ttl: 5 * 60 * 1000, // 5分钟TTL
        
        // 生成缓存键
        generateKey: function(filterData) {
            const key = {
                dataSourceId: currentDataSource?.id,
                subdivisionLevels: [...filterData.subdivision_levels].sort(),
                memberCardLevels: [...filterData.member_card_levels].sort()
            };
            return JSON.stringify(key);
        },
        
        // 获取缓存数据
        get: function(key) {
            const cached = this.data.get(key);
            if (!cached) return null;
            
            // 检查是否过期
            if (Date.now() - cached.timestamp > this.ttl) {
                this.data.delete(key);
                return null;
            }
            
            return cached.data;
        },
        
        // 设置缓存数据
        set: function(key, data) {
            // 如果缓存已满，删除最旧的条目
            if (this.data.size >= this.maxSize) {
                const firstKey = this.data.keys().next().value;
                this.data.delete(firstKey);
            }
            
            this.data.set(key, {
                data: data,
                timestamp: Date.now()
            });
        },
        
        // 清除缓存
        clear: function() {
            this.data.clear();
        }
    },
    
    // 请求管理
    requestManager: {
        activeRequests: new Map(),
        
        // 检查是否有相同的请求正在进行
        isDuplicateRequest: function(key) {
            return this.activeRequests.has(key);
        },
        
        // 添加活动请求
        addRequest: function(key, promise) {
            this.activeRequests.set(key, promise);
            
            // 请求完成后清除
            promise.finally(() => {
                this.activeRequests.delete(key);
            });
            
            return promise;
        },
        
        // 获取活动请求
        getRequest: function(key) {
            return this.activeRequests.get(key);
        }
    },
    
    // 初始化筛选器
    initialize: function(container, reportData, quarters) {
        this.originalData = reportData;
        this.renderEnhancedFilter(container, reportData, quarters);
        this.initializeEventListeners(container);
        this.loadInitialState(reportData);
    },
    
    // 渲染增强版筛选器
    renderEnhancedFilter: function(container, reportData, quarters) {
        if (!reportData || !reportData.filter_options) {
            container.innerHTML = '<div class="alert alert-warning">暂无筛选选项</div>';
            return;
        }

        const filterOptions = reportData.filter_options;
        
        let filterHtml = `
            <div class="card mb-3" id="enhancedFilterCard">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-filter me-2"></i>数据筛选器
                            <span class="badge bg-info ms-2" id="filterChangeIndicator" style="display: none;">有变更</span>
                        </h6>
                        <div class="btn-group" role="group">
                            <button class="btn btn-sm btn-outline-primary" id="selectAllFilters" title="选择所有筛选条件">
                                <i class="fas fa-check-double me-1"></i>全选
                            </button>
                            <button class="btn btn-sm btn-outline-secondary" id="clearAllFilters" title="清空所有筛选条件">
                                <i class="fas fa-times me-1"></i>清空
                            </button>
                            <button class="btn btn-sm btn-outline-info" id="resetFilters" title="重置为初始状态">
                                <i class="fas fa-undo me-1"></i>重置
                            </button>
                            <button class="btn btn-sm btn-success" id="confirmFilters" disabled title="应用当前筛选条件">
                                <i class="fas fa-check me-1"></i>确认筛选
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 筛选器搜索功能 -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group input-group-sm">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" id="subdivisionSearch" placeholder="搜索细分等级..." title="输入关键词快速查找细分等级选项">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="input-group input-group-sm">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" id="cardLevelSearch" placeholder="搜索会员卡级..." title="输入关键词快速查找会员卡级选项">
                            </div>
                        </div>
                    </div>
                    
                    <!-- 操作提示 -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="alert alert-light border-0 py-2 mb-0">
                                <small class="text-muted">
                                    <i class="fas fa-lightbulb me-1"></i>
                                    <strong>使用提示：</strong>
                                    选择筛选条件后点击"确认筛选"按钮更新数据 | 
                                    使用搜索框快速定位选项 | 
                                    支持多选和批量操作
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 细分等级筛选 -->
                    <div class="mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <label class="form-label fw-bold mb-0">
                                <i class="fas fa-layer-group me-1"></i>细分等级筛选：
                                <span class="badge bg-secondary" id="subdivisionSelectedCount">0</span>
                            </label>
                            <div class="btn-group btn-group-sm" role="group">
                                <button class="btn btn-outline-success" data-action="select-all" data-target="subdivision" title="选择所有细分等级">
                                    <i class="fas fa-check-square me-1"></i>全选
                                </button>
                                <button class="btn btn-outline-danger" data-action="select-none" data-target="subdivision" title="取消选择所有细分等级">
                                    <i class="fas fa-square me-1"></i>全不选
                                </button>
                            </div>
                        </div>
                        <div class="row" id="subdivisionFilter">
                            ${this.generateMultiColumnCheckboxes(filterOptions.subdivision_levels, 'subdivision', 'sub')}
                        </div>
                    </div>
                    
                    <!-- 会员卡级筛选 -->
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <label class="form-label fw-bold mb-0">
                                <i class="fas fa-credit-card me-1"></i>会员卡级筛选：
                                <span class="badge bg-secondary" id="cardLevelSelectedCount">0</span>
                            </label>
                            <div class="btn-group btn-group-sm" role="group">
                                <button class="btn btn-outline-success" data-action="select-all" data-target="card_level" title="选择所有会员卡级">
                                    <i class="fas fa-check-square me-1"></i>全选
                                </button>
                                <button class="btn btn-outline-danger" data-action="select-none" data-target="card_level" title="取消选择所有会员卡级">
                                    <i class="fas fa-square me-1"></i>全不选
                                </button>
                            </div>
                        </div>
                        <div class="row" id="cardLevelFilter">
                            ${this.generateMultiColumnCheckboxes(filterOptions.member_card_levels, 'card_level', 'card')}
                        </div>
                    </div>
                    
                    <!-- 筛选状态显示 -->
                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-info mb-0" id="filterStatus">
                                <div class="d-flex justify-content-between align-items-center">
                                    <small id="filterStatusText">
                                        <i class="fas fa-info-circle me-1"></i>
                                        请选择筛选条件后点击"确认筛选"按钮
                                    </small>
                                    <div id="filterLoadingIndicator" style="display: none;">
                                        <i class="fas fa-spinner fa-spin me-1"></i>
                                        <small>正在筛选数据...</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        container.innerHTML = filterHtml + `
            <div id="consultantDataTable">
                <div class="text-center py-3">
                    <i class="fas fa-table fa-2x text-muted mb-3"></i>
                    <p class="text-muted">请选择筛选条件并点击"确认筛选"查看数据</p>
                </div>
            </div>
        `;
    },
    
    // 生成多列复选框HTML
    generateMultiColumnCheckboxes: function(items, filterType, idPrefix) {
        if (!items || items.length === 0) return '';
        
        // 根据选项数量动态计算列数
        let columnCount;
        if (filterType === 'subdivision') {
            columnCount = items.length > 12 ? 4 : (items.length > 6 ? 3 : 2);
        } else {
            columnCount = items.length > 8 ? 3 : (items.length > 4 ? 2 : 1);
        }
        
        const itemsPerColumn = Math.ceil(items.length / columnCount);
        const colClass = columnCount === 4 ? 'col-md-3' : 
                        (columnCount === 3 ? 'col-md-4' : 
                        (columnCount === 2 ? 'col-md-6' : 'col-md-12'));
        
        let html = '';
        for (let col = 0; col < columnCount; col++) {
            html += `<div class="${colClass}">`;
            const startIndex = col * itemsPerColumn;
            const endIndex = Math.min(startIndex + itemsPerColumn, items.length);
            
            for (let i = startIndex; i < endIndex; i++) {
                const item = items[i];
                const safeId = item.replace(/[^a-zA-Z0-9]/g, '_');
                const checkboxId = `${idPrefix}_${safeId}`;
                
                html += `
                    <div class="form-check form-check-sm mb-1 filter-option" data-filter-type="${filterType}" data-original-text="${item}">
                        <input class="form-check-input consultant-filter" 
                               type="checkbox" 
                               value="${item}" 
                               id="${checkboxId}" 
                               checked 
                               data-filter-type="${filterType}">
                        <label class="form-check-label small text-truncate" 
                               for="${checkboxId}" 
                               title="${item}"
                               style="max-width: 200px; display: inline-block;">
                            ${item}
                        </label>
                    </div>
                `;
            }
            html += `</div>`;
        }
        
        return html;
    },
    
    // 初始化事件监听器
    initializeEventListeners: function(container) {
        // 筛选器复选框变化事件（仅更新状态，不触发数据刷新）
        const filterCheckboxes = container.querySelectorAll('.consultant-filter');
        filterCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                this.updateFilterState();
                this.updateUI();
            });
        });

        // 全选/全不选按钮事件
        const actionButtons = container.querySelectorAll('[data-action]');
        actionButtons.forEach(button => {
            button.addEventListener('click', () => {
                const action = button.dataset.action;
                const target = button.dataset.target;
                
                if (action === 'select-all') {
                    this.selectAllFilters(target, true);
                } else if (action === 'select-none') {
                    this.selectAllFilters(target, false);
                }
                
                this.updateFilterState();
                this.updateUI();
            });
        });

        // 全局操作按钮事件
        this.initializeGlobalButtons(container);
        
        // 搜索功能
        this.initializeSearchFunctionality(container);
        
        // 确认筛选按钮事件
        const confirmButton = container.querySelector('#confirmFilters');
        if (confirmButton) {
            confirmButton.addEventListener('click', () => {
                this.applyFilter();
            });
        }
    },
    
    // 初始化全局按钮
    initializeGlobalButtons: function(container) {
        const selectAllBtn = container.querySelector('#selectAllFilters');
        const clearAllBtn = container.querySelector('#clearAllFilters');
        const resetBtn = container.querySelector('#resetFilters');
        
        if (selectAllBtn) {
            selectAllBtn.addEventListener('click', () => {
                this.selectAllFilters('subdivision', true);
                this.selectAllFilters('card_level', true);
                this.updateFilterState();
                this.updateUI();
            });
        }
        
        if (clearAllBtn) {
            clearAllBtn.addEventListener('click', () => {
                this.selectAllFilters('subdivision', false);
                this.selectAllFilters('card_level', false);
                this.updateFilterState();
                this.updateUI();
            });
        }
        
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.resetToInitialState();
            });
        }
    },
    
    // 初始化搜索功能
    initializeSearchFunctionality: function(container) {
        const subdivisionSearch = container.querySelector('#subdivisionSearch');
        const cardLevelSearch = container.querySelector('#cardLevelSearch');
        
        if (subdivisionSearch) {
            subdivisionSearch.addEventListener('input', (e) => {
                this.filterOptions('subdivision', e.target.value);
            });
        }
        
        if (cardLevelSearch) {
            cardLevelSearch.addEventListener('input', (e) => {
                this.filterOptions('card_level', e.target.value);
            });
        }
    },
    
    // 筛选选项显示
    filterOptions: function(filterType, searchText) {
        const selector = `[data-filter-type="${filterType}"]`;
        const options = document.querySelectorAll(`.filter-option${selector}`);
        
        options.forEach(option => {
            const originalText = option.dataset.originalText.toLowerCase();
            const matches = originalText.includes(searchText.toLowerCase());
            option.style.display = matches ? 'block' : 'none';
        });
    },
    
    // 全选/全不选指定类型的筛选器
    selectAllFilters: function(filterType, checked) {
        const selector = `input[data-filter-type="${filterType}"]`;
        const checkboxes = document.querySelectorAll(selector);
        
        checkboxes.forEach(checkbox => {
            // 只对可见的选项进行操作
            const parentOption = checkbox.closest('.filter-option');
            if (!parentOption || parentOption.style.display !== 'none') {
                checkbox.checked = checked;
            }
        });
    },
    
    // 更新筛选状态
    updateFilterState: function() {
        const subdivisionLevels = [];
        const memberCardLevels = [];
        
        document.querySelectorAll('input[data-filter-type="subdivision"]:checked').forEach(checkbox => {
            subdivisionLevels.push(checkbox.value);
        });
        
        document.querySelectorAll('input[data-filter-type="card_level"]:checked').forEach(checkbox => {
            memberCardLevels.push(checkbox.value);
        });

        // 检查是否有变更
        const hasChanges = this.hasStateChanged(subdivisionLevels, memberCardLevels);
        
        this.currentState = {
            subdivisionLevels,
            memberCardLevels,
            hasChanges,
            isLoading: this.currentState.isLoading
        };
    },
    
    // 检查状态是否有变更
    hasStateChanged: function(subdivisionLevels, memberCardLevels) {
        if (!this.originalData || !this.originalData.filter_options) return false;
        
        const originalSubdivision = this.originalData.filter_options.subdivision_levels;
        const originalCardLevel = this.originalData.filter_options.member_card_levels;
        
        return subdivisionLevels.length !== originalSubdivision.length ||
               memberCardLevels.length !== originalCardLevel.length ||
               !subdivisionLevels.every(level => originalSubdivision.includes(level)) ||
               !memberCardLevels.every(level => originalCardLevel.includes(level));
    },
    
    // 更新UI状态
    updateUI: function() {
        this.updateCountBadges();
        this.updateConfirmButton();
        this.updateChangeIndicator();
        this.updateStatusText();
    },
    
    // 更新计数徽章
    updateCountBadges: function() {
        const subdivisionCount = document.querySelectorAll('input[data-filter-type="subdivision"]:checked').length;
        const cardLevelCount = document.querySelectorAll('input[data-filter-type="card_level"]:checked').length;
        
        const subdivisionBadge = document.getElementById('subdivisionSelectedCount');
        const cardLevelBadge = document.getElementById('cardLevelSelectedCount');
        
        if (subdivisionBadge) {
            subdivisionBadge.textContent = subdivisionCount;
            subdivisionBadge.className = subdivisionCount > 0 ? 'badge bg-primary' : 'badge bg-secondary';
        }
        
        if (cardLevelBadge) {
            cardLevelBadge.textContent = cardLevelCount;
            cardLevelBadge.className = cardLevelCount > 0 ? 'badge bg-primary' : 'badge bg-secondary';
        }
    },
    
    // 更新确认按钮状态
    updateConfirmButton: function() {
        const confirmButton = document.getElementById('confirmFilters');
        if (!confirmButton) return;
        
        const hasValidSelection = this.currentState.subdivisionLevels.length > 0 && 
                                 this.currentState.memberCardLevels.length > 0;
        
        confirmButton.disabled = !hasValidSelection || this.currentState.isLoading;
        
        if (this.currentState.isLoading) {
            confirmButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>筛选中...';
        } else {
            confirmButton.innerHTML = '<i class="fas fa-check me-1"></i>确认筛选';
        }
    },
    
    // 更新变更指示器
    updateChangeIndicator: function() {
        const indicator = document.getElementById('filterChangeIndicator');
        if (indicator) {
            indicator.style.display = this.currentState.hasChanges ? 'inline-block' : 'none';
        }
    },
    
    // 更新状态文本
    updateStatusText: function() {
        const statusText = document.getElementById('filterStatusText');
        if (!statusText) return;
        
        const subdivisionCount = this.currentState.subdivisionLevels.length;
        const cardLevelCount = this.currentState.memberCardLevels.length;
        
        if (subdivisionCount === 0 || cardLevelCount === 0) {
            statusText.innerHTML = `
                <i class="fas fa-exclamation-triangle me-1 text-warning"></i>
                请至少选择一个细分等级和一个会员卡级
            `;
        } else {
            statusText.innerHTML = `
                <i class="fas fa-info-circle me-1"></i>
                已选择: ${subdivisionCount} 个细分等级, ${cardLevelCount} 个会员卡级
                ${this.currentState.hasChanges ? '<span class="text-primary ms-2">（有变更，请点击确认筛选）</span>' : ''}
            `;
        }
    },
    
    // 加载初始状态
    loadInitialState: function(reportData) {
        if (!reportData || !reportData.filter_options) return;
        
        // 默认全选
        this.selectAllFilters('subdivision', true);
        this.selectAllFilters('card_level', true);
        
        this.updateFilterState();
        this.updateUI();
        
        // 自动应用初始筛选
        setTimeout(() => {
            this.applyFilter();
        }, 100);
    },
    
    // 重置到初始状态
    resetToInitialState: function() {
        this.selectAllFilters('subdivision', true);
        this.selectAllFilters('card_level', true);
        
        // 清空搜索框
        const subdivisionSearch = document.getElementById('subdivisionSearch');
        const cardLevelSearch = document.getElementById('cardLevelSearch');
        
        if (subdivisionSearch) {
            subdivisionSearch.value = '';
            this.filterOptions('subdivision', '');
        }
        
        if (cardLevelSearch) {
            cardLevelSearch.value = '';
            this.filterOptions('card_level', '');
        }
        
        this.updateFilterState();
        this.updateUI();
    },
    
    // 应用筛选
    applyFilter: function() {
        if (this.currentState.isLoading) return;
        
        // 验证筛选条件
        if (this.currentState.subdivisionLevels.length === 0 || this.currentState.memberCardLevels.length === 0) {
            this.showError('请至少选择一个细分等级和一个会员卡级');
            return;
        }

        // 保存当前状态用于回滚
        this.saveCurrentStateForRollback();

        // 设置加载状态
        this.setLoadingState(true);
        
        const filterData = {
            subdivision_levels: this.currentState.subdivisionLevels,
            member_card_levels: this.currentState.memberCardLevels
        };

        // 发送筛选请求（带重试机制）
        this.sendFilterRequestWithRetry(filterData, 3);
    },
    
    // 保存当前状态用于回滚
    saveCurrentStateForRollback: function() {
        this.rollbackState = {
            subdivisionLevels: [...this.currentState.subdivisionLevels],
            memberCardLevels: [...this.currentState.memberCardLevels],
            hasChanges: this.currentState.hasChanges
        };
    },
    
    // 带重试机制的筛选请求
    sendFilterRequestWithRetry: function(filterData, maxRetries, currentRetry = 1) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时
        
        fetch(`/rfm/api/consultant-attribution-filter/${currentDataSource.id}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(filterData),
            signal: controller.signal
        })
        .then(response => {
            clearTimeout(timeoutId);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // 更新数据表格
                const quarters = data.quarters || [];
                this.renderDataTable('consultantDataTable', data.data, quarters);
                
                // 更新筛选结果状态
                this.updateFilterResultStatus(data);
                
                // 保存筛选状态
                this.saveFilterState(filterData);
                
                // 清除回滚状态
                this.rollbackState = null;
                
            } else {
                throw new Error(data.error || '服务器返回错误');
            }
        })
        .catch(error => {
            clearTimeout(timeoutId);
            console.error(`筛选请求失败 (尝试 ${currentRetry}/${maxRetries}):`, error);
            
            if (currentRetry < maxRetries && !controller.signal.aborted) {
                // 自动重试
                const retryDelay = Math.min(1000 * Math.pow(2, currentRetry - 1), 5000); // 指数退避，最大5秒
                this.showRetryMessage(currentRetry, maxRetries, retryDelay);
                
                setTimeout(() => {
                    this.sendFilterRequestWithRetry(filterData, maxRetries, currentRetry + 1);
                }, retryDelay);
            } else {
                // 重试次数用完或被取消，显示错误并提供恢复选项
                this.handleFilterError(error, filterData, maxRetries);
            }
        })
        .finally(() => {
            if (currentRetry >= maxRetries || controller.signal.aborted) {
                this.setLoadingState(false);
            }
        });
    },
    
    // 显示重试消息
    showRetryMessage: function(currentRetry, maxRetries, delay) {
        const statusDiv = document.getElementById('filterStatus');
        const statusText = document.getElementById('filterStatusText');
        
        if (statusDiv && statusText) {
            statusText.innerHTML = `
                <i class="fas fa-exclamation-triangle me-1 text-warning"></i>
                网络请求失败，正在进行第 ${currentRetry} 次重试 (共 ${maxRetries} 次)...
                <small class="ms-2">${Math.ceil(delay/1000)} 秒后重试</small>
            `;
            statusDiv.className = 'alert alert-warning mb-0';
        }
    },
    
    // 处理筛选错误
    handleFilterError: function(error, filterData, maxRetries) {
        let errorMessage = '筛选请求失败';
        let suggestions = [];
        
        if (error.name === 'AbortError') {
            errorMessage = '请求超时';
            suggestions.push('检查网络连接');
            suggestions.push('稍后重试');
        } else if (error.message.includes('HTTP 5')) {
            errorMessage = '服务器内部错误';
            suggestions.push('请稍后重试');
            suggestions.push('如果问题持续，请联系管理员');
        } else if (error.message.includes('HTTP 4')) {
            errorMessage = '请求参数错误';
            suggestions.push('请检查筛选条件');
            suggestions.push('尝试重置筛选器');
        } else if (error.message.includes('Failed to fetch')) {
            errorMessage = '网络连接失败';
            suggestions.push('检查网络连接');
            suggestions.push('确认服务器是否可访问');
        } else {
            errorMessage = error.message || '未知错误';
            suggestions.push('请稍后重试');
        }
        
        this.showErrorWithRecovery(errorMessage, suggestions, filterData);
    },
    
    // 显示带恢复选项的错误信息
    showErrorWithRecovery: function(errorMessage, suggestions, filterData) {
        const statusDiv = document.getElementById('filterStatus');
        const statusText = document.getElementById('filterStatusText');
        
        if (statusDiv && statusText) {
            let suggestionsHtml = suggestions.map(s => `<li>${s}</li>`).join('');
            
            statusText.innerHTML = `
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        <strong>${errorMessage}</strong>
                        <ul class="mb-0 mt-1 small">
                            ${suggestionsHtml}
                        </ul>
                    </div>
                    <div class="btn-group btn-group-sm ms-2" role="group">
                        <button class="btn btn-outline-primary btn-sm" onclick="FilterManager.retryFilter('${JSON.stringify(filterData).replace(/"/g, '&quot;')}')">
                            <i class="fas fa-redo me-1"></i>重试
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="FilterManager.rollbackFilter()">
                            <i class="fas fa-undo me-1"></i>回滚
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="FilterManager.resetToInitialState()">
                            <i class="fas fa-refresh me-1"></i>重置
                        </button>
                    </div>
                </div>
            `;
            statusDiv.className = 'alert alert-danger mb-0';
        }
    },
    
    // 重试筛选
    retryFilter: function(filterDataStr) {
        try {
            const filterData = JSON.parse(filterDataStr.replace(/&quot;/g, '"'));
            this.setLoadingState(true);
            this.sendFilterRequestWithRetry(filterData, 3);
        } catch (error) {
            console.error('重试筛选失败:', error);
            this.showError('重试失败，请手动重新筛选');
        }
    },
    
    // 回滚筛选状态
    rollbackFilter: function() {
        if (!this.rollbackState) {
            this.showError('没有可回滚的状态');
            return;
        }
        
        // 恢复到之前的状态
        this.currentState.subdivisionLevels = [...this.rollbackState.subdivisionLevels];
        this.currentState.memberCardLevels = [...this.rollbackState.memberCardLevels];
        this.currentState.hasChanges = this.rollbackState.hasChanges;
        
        // 更新UI
        this.restoreUIFromState();
        this.updateUI();
        
        // 清除回滚状态
        this.rollbackState = null;
        
        this.showSuccess('已回滚到之前的筛选状态');
    },
    
    // 从状态恢复UI
    restoreUIFromState: function() {
        // 先清空所有选择
        document.querySelectorAll('.consultant-filter').forEach(checkbox => {
            checkbox.checked = false;
        });
        
        // 恢复细分等级选择
        this.currentState.subdivisionLevels.forEach(level => {
            const checkbox = document.querySelector(`input[data-filter-type="subdivision"][value="${level}"]`);
            if (checkbox) {
                checkbox.checked = true;
            }
        });
        
        // 恢复会员卡级选择
        this.currentState.memberCardLevels.forEach(level => {
            const checkbox = document.querySelector(`input[data-filter-type="card_level"][value="${level}"]`);
            if (checkbox) {
                checkbox.checked = true;
            }
        });
    },
    
    // 显示成功信息
    showSuccess: function(message) {
        const statusDiv = document.getElementById('filterStatus');
        const statusText = document.getElementById('filterStatusText');
        
        if (statusDiv && statusText) {
            statusText.innerHTML = `
                <i class="fas fa-check-circle me-1 text-success"></i>
                ${message}
            `;
            statusDiv.className = 'alert alert-success mb-0';
        }
        
        // 3秒后恢复正常状态
        setTimeout(() => {
            this.updateUI();
            const statusDiv = document.getElementById('filterStatus');
            if (statusDiv) {
                statusDiv.className = 'alert alert-info mb-0';
            }
        }, 3000);
    },
    
    // 设置加载状态
    setLoadingState: function(loading) {
        this.currentState.isLoading = loading;
        
        const loadingIndicator = document.getElementById('filterLoadingIndicator');
        const filterCard = document.getElementById('enhancedFilterCard');
        
        if (loadingIndicator) {
            loadingIndicator.style.display = loading ? 'block' : 'none';
        }
        
        if (filterCard) {
            const inputs = filterCard.querySelectorAll('input, button');
            inputs.forEach(input => {
                if (input.id !== 'subdivisionSearch' && input.id !== 'cardLevelSearch') {
                    input.disabled = loading;
                }
            });
        }
        
        this.updateUI();
        
        if (loading) {
            const dataTable = document.getElementById('consultantDataTable');
            if (dataTable) {
                dataTable.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-spinner fa-spin fa-2x text-primary mb-3"></i>
                        <h5 class="text-primary">正在筛选数据...</h5>
                        <p class="text-muted">请稍候，正在根据您的筛选条件处理数据</p>
                    </div>
                `;
            }
        }
    },
    
    // 渲染数据表格
    renderDataTable: function(containerId, reportData, quarters) {
        const container = document.getElementById(containerId);
        if (!container) return;

        if (!reportData || !reportData.data || reportData.data.length === 0) {
            container.innerHTML = `
                <div class="alert alert-warning text-center">
                    <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                    <h5>暂无数据</h5>
                    <p class="mb-0">当前筛选条件下没有找到匹配的数据，请尝试调整筛选条件</p>
                </div>
            `;
            return;
        }

        // 生成表格
        let tableHtml = `
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead style="background-color: #212529 !important;">
                        <tr>
                            <th rowspan="2" class="align-middle" style="color: white !important; background-color: #212529 !important;">现场小组</th>
                            <th rowspan="2" class="align-middle" style="color: white !important; background-color: #212529 !important;">最新现场</th>
        `;

        quarters.forEach(quarter => {
            tableHtml += `<th colspan="2" class="text-center" style="color: white !important; background-color: #212529 !important;">${quarter}</th>`;
        });

        tableHtml += `
                        </tr>
                        <tr>
        `;

        quarters.forEach(quarter => {
            tableHtml += `
                <th class="text-center" style="color: white !important; background-color: #212529 !important;">锁盘老客人数</th>
                <th class="text-center" style="color: white !important; background-color: #212529 !important;">环比增长</th>
            `;
        });

        tableHtml += `
                        </tr>
                    </thead>
                    <tbody>
        `;

        // 按现场小组和最新现场分组数据
        const groupedData = {};
        reportData.data.forEach(row => {
            const key = `${row.现场小组}_${row.最新现场}`;
            if (!groupedData[key]) {
                groupedData[key] = {
                    现场小组: row.现场小组,
                    最新现场: row.最新现场,
                    quarters: {}
                };
            }
            groupedData[key].quarters[row.季度] = {
                锁盘老客人数: row.锁盘老客人数,
                环比增长: row.环比增长
            };
        });

        // 生成表格行
        Object.values(groupedData).forEach(group => {
            const isSubtotal = group.现场小组 && group.现场小组.includes('小计');
            const isTotal = group.现场小组 && group.现场小组.includes('合计');
            const rowClass = isSubtotal ? 'table-warning fw-bold' : (isTotal ? 'table-info fw-bold' : '');

            tableHtml += `
                <tr class="${rowClass}">
                    <td>${group.现场小组}</td>
                    <td>${group.最新现场}</td>
            `;

            quarters.forEach(quarter => {
                const quarterData = group.quarters[quarter] || {};
                const count = quarterData.锁盘老客人数 || 0;
                const growth = quarterData.环比增长 || '-';

                tableHtml += `
                    <td class="text-end">${count.toLocaleString()}</td>
                    <td class="text-end">${growth}</td>
                `;
            });

            tableHtml += '</tr>';
        });

        tableHtml += `
                    </tbody>
                </table>
            </div>
        `;

        container.innerHTML = tableHtml;
    },
    
    // 更新筛选结果状态
    updateFilterResultStatus: function(data) {
        const statusDiv = document.getElementById('filterStatus');
        if (!statusDiv) return;
        
        if (data.total_records !== undefined && data.filtered_records !== undefined) {
            const filterRate = data.total_records > 0 ? 
                ((data.filtered_records / data.total_records) * 100).toFixed(1) : 0;
            
            const statusText = document.getElementById('filterStatusText');
            if (statusText) {
                statusText.innerHTML = `
                    <i class="fas fa-check-circle me-1 text-success"></i>
                    筛选完成: 共 <strong>${data.filtered_records.toLocaleString()}</strong> 条记录 
                    (占总数据的 <strong>${filterRate}%</strong>, 总计 ${data.total_records.toLocaleString()} 条)
                `;
            }
            
            // 根据筛选结果调整状态样式
            statusDiv.className = data.filtered_records > 0 ? 
                'alert alert-success mb-0' : 'alert alert-warning mb-0';
        }
    },
    
    // 显示错误信息
    showError: function(message) {
        const statusDiv = document.getElementById('filterStatus');
        if (statusDiv) {
            const statusText = document.getElementById('filterStatusText');
            if (statusText) {
                statusText.innerHTML = `
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    ${message}
                `;
            }
            statusDiv.className = 'alert alert-danger mb-0';
        }
        
        // 3秒后恢复正常状态
        setTimeout(() => {
            this.updateUI();
            const statusDiv = document.getElementById('filterStatus');
            if (statusDiv) {
                statusDiv.className = 'alert alert-info mb-0';
            }
        }, 3000);
    },
    
    // 保存筛选状态
    saveFilterState: function(filterData) {
        try {
            const stateData = {
                dataSourceId: currentDataSource.id,
                subdivisionLevels: filterData.subdivision_levels,
                memberCardLevels: filterData.member_card_levels,
                timestamp: Date.now()
            };
            localStorage.setItem('rfm_consultant_filter_state', JSON.stringify(stateData));
        } catch (error) {
            console.warn('无法保存筛选状态:', error);
        }
    }
};

// 导出FilterManager供全局使用
window.FilterManager = FilterManager;    //
 应用筛选（带缓存和请求去重）
    applyFilterWithCache: function() {
        if (this.currentState.isLoading) return;
        
        // 验证筛选条件
        if (this.currentState.subdivisionLevels.length === 0 || this.currentState.memberCardLevels.length === 0) {
            this.showError('请至少选择一个细分等级和一个会员卡级');
            return;
        }

        const filterData = {
            subdivision_levels: this.currentState.subdivisionLevels,
            member_card_levels: this.currentState.memberCardLevels
        };

        // 生成缓存键
        const cacheKey = this.cache.generateKey(filterData);
        
        // 检查缓存
        const cachedData = this.cache.get(cacheKey);
        if (cachedData) {
            console.log('使用缓存数据');
            this.renderDataTable('consultantDataTable', cachedData.data, cachedData.quarters);
            this.updateFilterResultStatus(cachedData);
            this.saveFilterState(filterData);
            return;
        }

        // 检查是否有相同的请求正在进行
        if (this.requestManager.isDuplicateRequest(cacheKey)) {
            console.log('相同请求正在进行中，等待结果');
            const existingRequest = this.requestManager.getRequest(cacheKey);
            if (existingRequest) {
                this.setLoadingState(true);
                existingRequest.then(data => {
                    if (data && data.success) {
                        this.renderDataTable('consultantDataTable', data.data, data.quarters);
                        this.updateFilterResultStatus(data);
                        this.saveFilterState(filterData);
                    }
                }).finally(() => {
                    this.setLoadingState(false);
                });
            }
            return;
        }

        // 保存当前状态用于回滚
        this.saveCurrentStateForRollback();

        // 设置加载状态
        this.setLoadingState(true);

        // 创建新的请求
        const requestPromise = this.sendFilterRequestWithCache(filterData, cacheKey, 3);
        this.requestManager.addRequest(cacheKey, requestPromise);
    },
    
    // 带缓存的筛选请求
    sendFilterRequestWithCache: function(filterData, cacheKey, maxRetries, currentRetry = 1) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时
        
        return fetch(`/rfm/api/consultant-attribution-filter/${currentDataSource.id}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(filterData),
            signal: controller.signal
        })
        .then(response => {
            clearTimeout(timeoutId);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // 缓存结果
                this.cache.set(cacheKey, {
                    data: data.data,
                    quarters: data.quarters || [],
                    total_records: data.total_records,
                    filtered_records: data.filtered_records
                });
                
                // 更新数据表格
                const quarters = data.quarters || [];
                this.renderDataTable('consultantDataTable', data.data, quarters);
                
                // 更新筛选结果状态
                this.updateFilterResultStatus(data);
                
                // 保存筛选状态
                this.saveFilterState(filterData);
                
                // 清除回滚状态
                this.rollbackState = null;
                
                return data;
            } else {
                throw new Error(data.error || '服务器返回错误');
            }
        })
        .catch(error => {
            clearTimeout(timeoutId);
            console.error(`筛选请求失败 (尝试 ${currentRetry}/${maxRetries}):`, error);
            
            if (currentRetry < maxRetries && !controller.signal.aborted) {
                // 自动重试
                const retryDelay = Math.min(1000 * Math.pow(2, currentRetry - 1), 5000); // 指数退避，最大5秒
                this.showRetryMessage(currentRetry, maxRetries, retryDelay);
                
                return new Promise((resolve, reject) => {
                    setTimeout(() => {
                        this.sendFilterRequestWithCache(filterData, cacheKey, maxRetries, currentRetry + 1)
                            .then(resolve)
                            .catch(reject);
                    }, retryDelay);
                });
            } else {
                // 重试次数用完或被取消，显示错误并提供恢复选项
                this.handleFilterError(error, filterData, maxRetries);
                throw error;
            }
        })
        .finally(() => {
            if (currentRetry >= maxRetries || controller.signal.aborted) {
                this.setLoadingState(false);
            }
        });
    },
    
    // 清除过期缓存
    clearExpiredCache: function() {
        const now = Date.now();
        for (const [key, value] of this.cache.data.entries()) {
            if (now - value.timestamp > this.cache.ttl) {
                this.cache.data.delete(key);
            }
        }
    },
    
    // 获取缓存统计信息
    getCacheStats: function() {
        return {
            size: this.cache.data.size,
            maxSize: this.cache.maxSize,
            hitRate: this.cacheHits / (this.cacheHits + this.cacheMisses) || 0,
            activeRequests: this.requestManager.activeRequests.size
        };
    }
};

// 初始化缓存统计
FilterManager.cacheHits = 0;
FilterManager.cacheMisses = 0;

// 定期清理过期缓存
setInterval(() => {
    if (window.FilterManager) {
        FilterManager.clearExpiredCache();
    }
}, 60000); // 每分钟清理一次

// 修改原有的applyFilter方法，使其使用缓存版本
const originalApplyFilter = FilterManager.applyFilter;
FilterManager.applyFilter = function() {
    this.applyFilterWithCache();
};