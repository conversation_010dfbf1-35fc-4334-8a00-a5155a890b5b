# RFM报表处理系统
# 应用包初始化文件

import os
import logging
from logging.handlers import RotatingFileHandler
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from config import config

# 导入db实例
from app.models import db
login_manager = LoginManager()

def create_app(config_name=None):
    """应用工厂函数"""
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'default')

    app = Flask(__name__)
    app.config.from_object(config[config_name])

    # 初始化扩展
    db.init_app(app)
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = '请先登录以访问此页面。'
    login_manager.login_message_category = 'info'

    # 创建必要的目录
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    os.makedirs(os.path.dirname(app.config['LOG_FILE']), exist_ok=True)

    # 配置日志
    if not app.debug and not app.testing:
        if not os.path.exists('logs'):
            os.mkdir('logs')
        file_handler = RotatingFileHandler(
            app.config['LOG_FILE'],
            maxBytes=10240000,
            backupCount=10
        )
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(getattr(logging, app.config['LOG_LEVEL']))
        app.logger.addHandler(file_handler)
        app.logger.setLevel(getattr(logging, app.config['LOG_LEVEL']))
        app.logger.info('RFM报表系统启动')

    # 注册蓝图
    from app.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')

    from app.main import bp as main_bp
    app.register_blueprint(main_bp)

    from app.config_mgmt import bp as config_bp
    app.register_blueprint(config_bp, url_prefix='/config')

    from app.data_update import bp as data_update_bp
    app.register_blueprint(data_update_bp, url_prefix='/data_update')

    from app.data_processing import bp as data_bp
    app.register_blueprint(data_bp, url_prefix='/data')

    from app.mapping import bp as mapping_bp
    app.register_blueprint(mapping_bp, url_prefix='/mapping')

    from app.excel_reports import bp as excel_reports_bp
    app.register_blueprint(excel_reports_bp, url_prefix='/excel_reports')

    from app.rfm_reports import bp as rfm_reports_bp
    app.register_blueprint(rfm_reports_bp, url_prefix='/rfm')

    # 用户加载器
    @login_manager.user_loader
    def load_user(user_id):
        from app.models import User
        return User.query.get(int(user_id))

    # 错误处理
    @app.errorhandler(404)
    def not_found_error(error):
        from flask import render_template
        return render_template('errors/404.html'), 404

    @app.errorhandler(500)
    def internal_error(error):
        from flask import render_template
        db.session.rollback()
        return render_template('errors/500.html'), 500

    # 添加自定义过滤器
    @app.template_filter('datetime_from_iso')
    def datetime_from_iso_filter(iso_string):
        """将ISO格式的时间字符串转换为datetime对象"""
        from datetime import datetime
        try:
            return datetime.fromisoformat(iso_string.replace('Z', '+00:00'))
        except:
            return datetime.utcnow()

    @app.template_filter('local_datetime')
    def local_datetime_filter(dt, format_str='%Y-%m-%d %H:%M:%S'):
        """将UTC时间转换为本地时间并格式化"""
        from app.utils.datetime_utils import format_local_datetime
        return format_local_datetime(dt, format_str)

    @app.template_filter('format_duration')
    def format_duration_filter(duration):
        """格式化时长显示"""
        from app.utils.datetime_utils import format_duration
        return format_duration(duration)

    # 注册CLI命令
    from app import cli
    cli.init_app(app)

    return app
