#!/usr/bin/env python3
"""
快速修复脚本 - 重新创建一个用户用于测试
"""

import os
import sys
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from config import config

def create_test_user():
    """创建测试用户"""
    app = Flask(__name__)
    app.config.from_object(config['default'])
    
    db = SQLAlchemy()
    db.init_app(app)
    
    with app.app_context():
        # 导入模型
        from app.models import User
        
        # 检查用户是否存在
        existing_user = User.query.filter_by(username='admin').first()
        if existing_user:
            print("测试用户已存在")
            return
        
        # 创建新用户
        user = User(username='admin')
        user.set_password('123456')
        db.session.add(user)
        db.session.commit()
        
        print("测试用户创建成功:")
        print("用户名: admin")
        print("密码: 123456")

if __name__ == '__main__':
    try:
        create_test_user()
    except Exception as e:
        print(f"创建用户失败: {e}")
        import traceback
        traceback.print_exc()
