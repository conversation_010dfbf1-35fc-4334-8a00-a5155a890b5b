{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">系统设置</h1>
</div>

<div class="row">
    <div class="col-md-3">
        <div class="list-group">
            <a href="{{ url_for('basic_mgmt.general_settings') }}" class="list-group-item list-group-item-action">
                <i class="fas fa-cog me-2"></i>通用设置
            </a>
            <a href="{{ url_for('basic_mgmt.field_rules') }}" class="list-group-item list-group-item-action">
                <i class="fas fa-list-ul me-2"></i>字段规则
            </a>
        </div>
    </div>
    
    <div class="col-md-9">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>系统信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>系统名称:</strong></td>
                                <td>{{ settings.get('system_name', '老客登记信息反馈系统') }}</td>
                            </tr>
                            <tr>
                                <td><strong>系统版本:</strong></td>
                                <td>{{ settings.get('system_version', '1.0.0') }}</td>
                            </tr>
                            <tr>
                                <td><strong>卡号长度:</strong></td>
                                <td>{{ settings.get('card_number_length', '10') }} 位</td>
                            </tr>
                            <tr>
                                <td><strong>咨询内容最大长度:</strong></td>
                                <td>{{ settings.get('inquiry_content_max_length', '500') }} 字</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>跟进情况最大长度:</strong></td>
                                <td>{{ settings.get('follow_up_note_max_length', '500') }} 字</td>
                            </tr>
                            <tr>
                                <td><strong>自动生成简码:</strong></td>
                                <td>
                                    {% if settings.get('auto_generate_simple_code', 'true') == 'true' %}
                                        <span class="badge bg-success">启用</span>
                                    {% else %}
                                        <span class="badge bg-secondary">禁用</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>重复检查:</strong></td>
                                <td>
                                    {% if settings.get('enable_duplicate_check', 'true') == 'true' %}
                                        <span class="badge bg-success">启用</span>
                                    {% else %}
                                        <span class="badge bg-secondary">禁用</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>数据保留天数:</strong></td>
                                <td>{{ settings.get('data_retention_days', '365') }} 天</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                {% if settings.get('settings_note') %}
                <hr>
                <div class="alert alert-info">
                    <strong>设置说明:</strong><br>
                    {{ settings.get('settings_note') }}
                </div>
                {% endif %}
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>快速操作
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <a href="{{ url_for('basic_mgmt.general_settings') }}" class="btn btn-primary btn-lg w-100 mb-3">
                            <i class="fas fa-cog me-2"></i>修改通用设置
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="{{ url_for('basic_mgmt.field_rules') }}" class="btn btn-info btn-lg w-100 mb-3">
                            <i class="fas fa-list-ul me-2"></i>配置字段规则
                        </a>
                    </div>
                </div>
                
                <hr>
                
                <div class="row">
                    <div class="col-md-12">
                        <form method="POST" action="{{ url_for('basic_mgmt.reset_settings') }}" 
                              onsubmit="return confirm('确定要重置所有设置为默认值吗？此操作不可撤销！')">
                            {{ csrf_token() }}
                            <button type="submit" class="btn btn-outline-danger">
                                <i class="fas fa-undo me-2"></i>重置为默认设置
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}