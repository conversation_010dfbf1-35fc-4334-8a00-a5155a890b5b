#!/usr/bin/env python3
"""
检查数据库状态脚本
在迁移前检查当前数据库结构和数据
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from sqlalchemy import text

def check_database_status():
    """检查数据库状态"""
    print("=" * 50)
    print("检查数据库状态")
    print("=" * 50)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 检查是否存在departments表
            print("\n1. 检查部门表是否存在...")
            dept_table_exists = db.session.execute(text("""
                SELECT COUNT(*) as count FROM information_schema.tables 
                WHERE table_schema = DATABASE() 
                AND table_name = 'departments'
            """)).fetchone()
            
            if dept_table_exists[0] > 0:
                print("✓ departments表已存在")
                # 检查部门数据
                dept_count = db.session.execute(text("SELECT COUNT(*) FROM departments")).fetchone()
                print(f"  - 部门数量: {dept_count[0]}")
            else:
                print("⚠ departments表不存在，需要创建")
            
            # 2. 检查用户表结构
            print("\n2. 检查用户表结构...")
            user_columns = db.session.execute(text("""
                SELECT column_name, data_type, is_nullable 
                FROM information_schema.columns 
                WHERE table_schema = DATABASE() 
                AND table_name = 'user_accounts'
                ORDER BY ordinal_position
            """)).fetchall()
            
            print("用户表字段:")
            has_department = False
            has_department_id = False
            
            for column in user_columns:
                print(f"  - {column[0]} ({column[1]}) {'NULL' if column[2] == 'YES' else 'NOT NULL'}")
                if column[0] == 'department':
                    has_department = True
                if column[0] == 'department_id':
                    has_department_id = True
            
            # 3. 检查用户数据
            print("\n3. 检查用户数据...")
            user_count = db.session.execute(text("SELECT COUNT(*) FROM user_accounts")).fetchone()
            print(f"用户总数: {user_count[0]}")
            
            if has_department:
                # 检查现有部门数据
                dept_data = db.session.execute(text("""
                    SELECT department, COUNT(*) as count 
                    FROM user_accounts 
                    WHERE department IS NOT NULL AND department != ''
                    GROUP BY department
                    ORDER BY count DESC
                """)).fetchall()
                
                print("现有部门分布:")
                for dept, count in dept_data:
                    print(f"  - {dept}: {count} 个用户")
            
            if has_department_id:
                # 检查department_id字段
                dept_id_data = db.session.execute(text("""
                    SELECT COUNT(*) as total,
                           COUNT(department_id) as with_dept_id
                    FROM user_accounts
                """)).fetchone()
                print(f"department_id字段状态: {dept_id_data[1]}/{dept_id_data[0]} 用户已分配部门ID")
            
            # 4. 检查其他相关表
            print("\n4. 检查其他相关表...")
            tables_to_check = ['user_roles', 'permissions', 'role_permissions', 'channels']
            
            for table in tables_to_check:
                try:
                    count = db.session.execute(text(f"SELECT COUNT(*) FROM {table}")).fetchone()
                    print(f"  - {table}: {count[0]} 条记录")
                except Exception as e:
                    print(f"  - {table}: 表不存在或查询失败")
            
            # 5. 给出迁移建议
            print("\n" + "=" * 50)
            print("迁移建议:")
            print("=" * 50)
            
            if dept_table_exists[0] == 0:
                print("🔧 需要创建departments表")
            
            if has_department and not has_department_id:
                print("🔧 需要添加department_id字段到用户表")
                print("🔧 需要迁移现有部门数据")
            elif has_department and has_department_id:
                print("⚠️ 用户表同时存在department和department_id字段")
                print("🔧 可能需要完成数据迁移")
            elif not has_department and has_department_id:
                print("✅ 用户表已使用department_id字段")
            
            print("\n建议操作:")
            if dept_table_exists[0] == 0 or (has_department and not has_department_id):
                print("1. 运行迁移脚本: python migrate_departments.py")
            else:
                print("1. 数据库结构看起来已经更新，可以尝试启动系统")
            
            print("2. 如果遇到问题，可以运行测试脚本: python test_department_system.py")
            
        except Exception as e:
            print(f"❌ 检查过程中出现错误: {e}")
            raise

if __name__ == '__main__':
    check_database_status()