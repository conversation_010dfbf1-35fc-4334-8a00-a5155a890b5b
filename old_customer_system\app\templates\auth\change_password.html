{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card mt-5">
                <div class="card-header text-center">
                    <h4><i class="fas fa-key me-2"></i>修改密码</h4>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            {{ form.old_password.label(class="form-label") }}
                            {{ form.old_password(class="form-control") }}
                            {% if form.old_password.errors %}
                                <div class="text-danger">
                                    {% for error in form.old_password.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.new_password.label(class="form-label") }}
                            {{ form.new_password(class="form-control") }}
                            {% if form.new_password.errors %}
                                <div class="text-danger">
                                    {% for error in form.new_password.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.new_password2.label(class="form-label") }}
                            {{ form.new_password2(class="form-control") }}
                            {% if form.new_password2.errors %}
                                <div class="text-danger">
                                    {% for error in form.new_password2.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="d-grid gap-2">
                            {{ form.submit(class="btn btn-primary") }}
                            <a href="{{ url_for('main.dashboard') }}" class="btn btn-secondary">取消</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}