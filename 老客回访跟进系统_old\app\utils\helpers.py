"""
辅助工具函数
"""
import os
import uuid
from datetime import datetime, date
from decimal import Decimal, InvalidOperation

def generate_batch_id(prefix="BATCH"):
    """生成批次ID"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    unique_id = str(uuid.uuid4())[:8]
    return f"{prefix}_{timestamp}_{unique_id}"

def generate_unique_filename(original_filename):
    """生成唯一文件名"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    unique_id = str(uuid.uuid4())[:8]
    
    if '.' in original_filename:
        name, ext = original_filename.rsplit('.', 1)
        return f"{name}_{timestamp}_{unique_id}.{ext}"
    else:
        return f"{original_filename}_{timestamp}_{unique_id}"

def format_datetime(dt, format_str='%Y-%m-%d %H:%M:%S'):
    """格式化日期时间"""
    if dt is None:
        return ""
    
    if isinstance(dt, str):
        return dt
    
    return dt.strftime(format_str)

def format_date(d, format_str='%Y-%m-%d'):
    """格式化日期"""
    if d is None:
        return ""
    
    if isinstance(d, str):
        return d
    
    return d.strftime(format_str)

def format_currency(amount, currency_symbol='¥'):
    """格式化货币金额"""
    if amount is None:
        return f"{currency_symbol}0.00"
    
    try:
        amount = float(amount)
        return f"{currency_symbol}{amount:,.2f}"
    except (ValueError, TypeError):
        return f"{currency_symbol}0.00"

def safe_int(value, default=0):
    """安全转换为整数"""
    if value is None:
        return default
    
    try:
        return int(value)
    except (ValueError, TypeError):
        return default

def safe_float(value, default=0.0):
    """安全转换为浮点数"""
    if value is None:
        return default
    
    try:
        return float(value)
    except (ValueError, TypeError):
        return default

def safe_decimal(value, default=None):
    """安全转换为Decimal"""
    if value is None:
        return default or Decimal('0.00')
    
    try:
        return Decimal(str(value))
    except (InvalidOperation, ValueError, TypeError):
        return default or Decimal('0.00')

def safe_str(value, default=""):
    """安全转换为字符串"""
    if value is None:
        return default
    
    return str(value).strip()

def truncate_text(text, max_length=50, suffix="..."):
    """截断文本"""
    if not text:
        return ""
    
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix

def get_file_size_mb(file_path):
    """获取文件大小（MB）"""
    try:
        size_bytes = os.path.getsize(file_path)
        return round(size_bytes / (1024 * 1024), 2)
    except OSError:
        return 0

def ensure_dir_exists(dir_path):
    """确保目录存在"""
    if not os.path.exists(dir_path):
        os.makedirs(dir_path, exist_ok=True)
    return dir_path

def clean_filename(filename):
    """清理文件名，移除不安全字符"""
    import re
    # 移除或替换不安全字符
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # 移除连续的下划线
    filename = re.sub(r'_+', '_', filename)
    # 移除开头和结尾的下划线和点
    filename = filename.strip('_.')
    return filename

def parse_date_string(date_str, formats=None):
    """解析日期字符串"""
    if not date_str:
        return None
    
    if formats is None:
        formats = ['%Y-%m-%d', '%Y/%m/%d', '%d/%m/%Y', '%d-%m-%Y']
    
    for fmt in formats:
        try:
            return datetime.strptime(date_str, fmt).date()
        except ValueError:
            continue
    
    return None

def calculate_age(birth_date, reference_date=None):
    """计算年龄"""
    if not birth_date:
        return None
    
    if reference_date is None:
        reference_date = date.today()
    
    if isinstance(birth_date, str):
        birth_date = parse_date_string(birth_date)
    
    if birth_date is None:
        return None
    
    age = reference_date.year - birth_date.year
    if reference_date.month < birth_date.month or \
       (reference_date.month == birth_date.month and reference_date.day < birth_date.day):
        age -= 1
    
    return age

def get_quarter(date_obj):
    """获取季度"""
    if isinstance(date_obj, str):
        date_obj = parse_date_string(date_obj)
    
    if date_obj is None:
        return None
    
    month = date_obj.month
    if month <= 3:
        return 1
    elif month <= 6:
        return 2
    elif month <= 9:
        return 3
    else:
        return 4

def get_week_number(date_obj):
    """获取周数"""
    if isinstance(date_obj, str):
        date_obj = parse_date_string(date_obj)
    
    if date_obj is None:
        return None
    
    return date_obj.isocalendar()[1]

def mask_sensitive_data(data, mask_char='*'):
    """遮蔽敏感数据"""
    if not data:
        return data
    
    data_str = str(data)
    length = len(data_str)
    
    if length <= 2:
        return mask_char * length
    elif length <= 4:
        return data_str[0] + mask_char * (length - 2) + data_str[-1]
    else:
        visible_chars = min(2, length // 3)
        return data_str[:visible_chars] + mask_char * (length - 2 * visible_chars) + data_str[-visible_chars:]

def generate_random_password(length=12):
    """生成随机密码"""
    import random
    import string
    
    # 确保包含各种字符类型
    chars = string.ascii_letters + string.digits
    password = [
        random.choice(string.ascii_lowercase),
        random.choice(string.ascii_uppercase),
        random.choice(string.digits),
    ]
    
    # 填充剩余长度
    for _ in range(length - 3):
        password.append(random.choice(chars))
    
    # 打乱顺序
    random.shuffle(password)
    return ''.join(password)

def validate_and_convert_excel_date(value):
    """验证并转换Excel日期值"""
    if value is None or value == '':
        return None
    
    # 如果已经是date对象
    if isinstance(value, date):
        return value
    
    # 如果是datetime对象
    if isinstance(value, datetime):
        return value.date()
    
    # 如果是字符串
    if isinstance(value, str):
        return parse_date_string(value.strip())
    
    # 如果是数字（Excel日期序列号）
    try:
        # Excel日期从1900-01-01开始计算
        from datetime import timedelta
        excel_epoch = date(1900, 1, 1)
        # Excel有一个bug，认为1900年是闰年，所以需要减去2天
        if float(value) > 59:
            return excel_epoch + timedelta(days=float(value) - 2)
        else:
            return excel_epoch + timedelta(days=float(value) - 1)
    except (ValueError, TypeError):
        return None

def chunk_list(lst, chunk_size):
    """将列表分块"""
    for i in range(0, len(lst), chunk_size):
        yield lst[i:i + chunk_size]

def deep_merge_dict(dict1, dict2):
    """深度合并字典"""
    result = dict1.copy()
    
    for key, value in dict2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = deep_merge_dict(result[key], value)
        else:
            result[key] = value
    
    return result

def get_client_ip(request):
    """获取客户端IP地址"""
    # 检查代理头
    if request.headers.get('X-Forwarded-For'):
        return request.headers.get('X-Forwarded-For').split(',')[0].strip()
    elif request.headers.get('X-Real-IP'):
        return request.headers.get('X-Real-IP')
    else:
        return request.remote_addr

def is_mobile_user_agent(user_agent_string):
    """判断是否为移动设备"""
    mobile_keywords = [
        'Mobile', 'Android', 'iPhone', 'iPad', 'Windows Phone',
        'BlackBerry', 'Opera Mini', 'IEMobile'
    ]
    
    return any(keyword in user_agent_string for keyword in mobile_keywords)
