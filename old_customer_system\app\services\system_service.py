# -*- coding: utf-8 -*-
"""
系统设置服务
"""

from typing import Dict, Any, Optional
from app import db
from app.models import SystemSetting

class SystemService:
    """系统设置业务逻辑服务"""
    
    @staticmethod
    def get_all_settings() -> Dict[str, Any]:
        """获取所有系统设置"""
        settings = {}
        
        # 默认设置
        default_settings = {
            'card_number_length': '10',
            'inquiry_content_max_length': '500',
            'follow_up_note_max_length': '500',
            'system_name': '老客登记信息反馈系统',
            'system_version': '1.0.0',
            'auto_generate_simple_code': 'true',
            'enable_duplicate_check': 'true',
            'require_last_visit_date': 'false',
            'data_retention_days': '365',
            'settings_note': ''
        }
        
        # 从数据库获取设置
        db_settings = SystemSetting.query.all()
        for setting in db_settings:
            settings[setting.setting_key] = setting.setting_value
        
        # 合并默认设置
        for key, default_value in default_settings.items():
            if key not in settings:
                settings[key] = default_value
        
        return settings
    
    @staticmethod
    def get_setting(key: str, default: Any = None) -> Any:
        """获取单个系统设置"""
        setting = SystemSetting.query.filter_by(setting_key=key).first()
        if setting:
            # 尝试转换数据类型
            value = setting.setting_value
            if value.lower() in ['true', 'false']:
                return value.lower() == 'true'
            try:
                return int(value)
            except ValueError:
                return value
        return default
    
    @staticmethod
    def update_settings(settings_data: Dict[str, Any]) -> bool:
        """更新系统设置"""
        try:
            for key, value in settings_data.items():
                # 转换布尔值为字符串
                if isinstance(value, bool):
                    value = 'true' if value else 'false'
                else:
                    value = str(value)
                
                # 查找或创建设置
                setting = SystemSetting.query.filter_by(setting_key=key).first()
                if setting:
                    setting.setting_value = value
                else:
                    setting = SystemSetting(
                        setting_key=key,
                        setting_value=value,
                        description=SystemService._get_setting_description(key)
                    )
                    db.session.add(setting)
            
            db.session.commit()
            return True
            
        except Exception as e:
            db.session.rollback()
            raise e
    
    @staticmethod
    def get_field_rules() -> Dict[str, str]:
        """获取字段规则说明"""
        rules = {}
        
        # 默认规则
        default_rules = {
            'card_number_rule': '纯数字，长度可在系统设置中配置（默认10位，最大18位）',
            'onsite_consultant_rule': '下拉选择，选项为所有在职的"现场咨询"岗位人员',
            'online_consultant_rule': '自动填充为当前操作登记的"网络咨询"人员，不可修改',
            'channel_rule': '下拉选择，选项为"渠道管理"中所有已启用的渠道',
            'inquiry_content_rule': '文本域，最大长度在系统设置中配置（默认500字）',
            'last_visit_date_rule': '日期选择器，可选择过去的任意日期',
            'follow_up_note_rule': '文本域，最大长度在系统设置中配置（默认500字）',
            'follow_up_time_rule': '系统自动记录"咨询跟进情况"字段被最后一次修改时的时间戳',
            'create_time_rule': '系统自动记录当前服务器时间，不可修改'
        }
        
        # 从数据库获取规则
        db_rules = SystemSetting.query.filter(
            SystemSetting.setting_key.like('%_rule')
        ).all()
        
        for rule in db_rules:
            rules[rule.setting_key] = rule.setting_value
        
        # 合并默认规则
        for key, default_value in default_rules.items():
            if key not in rules:
                rules[key] = default_value
        
        return rules
    
    @staticmethod
    def update_field_rules(rules_data: Dict[str, str]) -> bool:
        """更新字段规则"""
        try:
            for key, value in rules_data.items():
                if not key.endswith('_rule'):
                    continue
                
                # 查找或创建规则
                rule = SystemSetting.query.filter_by(setting_key=key).first()
                if rule:
                    rule.setting_value = value
                else:
                    rule = SystemSetting(
                        setting_key=key,
                        setting_value=value,
                        description=f'{key}字段规则说明'
                    )
                    db.session.add(rule)
            
            db.session.commit()
            return True
            
        except Exception as e:
            db.session.rollback()
            raise e
    
    @staticmethod
    def _get_setting_description(key: str) -> str:
        """获取设置项的描述"""
        descriptions = {
            'card_number_length': '卡号长度设置',
            'inquiry_content_max_length': '咨询内容最大长度设置',
            'follow_up_note_max_length': '跟进情况最大长度设置',
            'system_name': '系统名称',
            'system_version': '系统版本',
            'auto_generate_simple_code': '是否自动生成简码',
            'enable_duplicate_check': '是否启用重复检查',
            'require_last_visit_date': '最近来院时间是否必填',
            'data_retention_days': '数据保留天数',
            'settings_note': '设置说明备注'
        }
        return descriptions.get(key, f'{key}设置项')
    
    @staticmethod
    def reset_to_defaults() -> bool:
        """重置为默认设置"""
        try:
            # 删除所有现有设置
            SystemSetting.query.delete()
            
            # 创建默认设置
            default_settings = [
                ('card_number_length', '10', '卡号长度设置'),
                ('inquiry_content_max_length', '500', '咨询内容最大长度设置'),
                ('follow_up_note_max_length', '500', '跟进情况最大长度设置'),
                ('system_name', '老客登记信息反馈系统', '系统名称'),
                ('system_version', '1.0.0', '系统版本'),
                ('auto_generate_simple_code', 'true', '是否自动生成简码'),
                ('enable_duplicate_check', 'true', '是否启用重复检查'),
                ('require_last_visit_date', 'false', '最近来院时间是否必填'),
                ('data_retention_days', '365', '数据保留天数'),
            ]
            
            for key, value, desc in default_settings:
                setting = SystemSetting(
                    setting_key=key,
                    setting_value=value,
                    description=desc
                )
                db.session.add(setting)
            
            db.session.commit()
            return True
            
        except Exception as e:
            db.session.rollback()
            raise e
    
    @staticmethod
    def validate_card_number(card_number: str) -> tuple[bool, str]:
        """验证卡号格式"""
        if not card_number:
            return False, "卡号不能为空"
        
        if not card_number.isdigit():
            return False, "卡号必须为纯数字"
        
        # 获取配置的卡号长度
        card_length = SystemService.get_setting('card_number_length', 10)
        
        if len(card_number) != card_length:
            return False, f"卡号长度必须为{card_length}位"
        
        return True, ""
    
    @staticmethod
    def get_content_max_length(field_type: str) -> int:
        """获取内容字段的最大长度"""
        if field_type == 'inquiry_content':
            return SystemService.get_setting('inquiry_content_max_length', 500)
        elif field_type == 'follow_up_note':
            return SystemService.get_setting('follow_up_note_max_length', 500)
        else:
            return 500