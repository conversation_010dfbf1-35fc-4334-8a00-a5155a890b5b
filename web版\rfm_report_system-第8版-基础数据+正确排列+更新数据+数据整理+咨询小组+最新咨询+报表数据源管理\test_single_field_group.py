#!/usr/bin/env python3
"""
测试现场小组单列功能
"""

import pandas as pd
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_single_field_group_column():
    """测试现场小组单列功能"""
    
    print("=== 测试现场小组单列功能 ===")
    
    # 创建测试数据
    test_data = {
        '会员卡号': ['000001', '000002', '000003', '000004'],
        '23Q4_现场': ['张三', '', '王五', '不在盘内'],
        '24Q1_现场': ['', '李四', '不在盘内', '不在盘内'],
        '24Q2_现场': ['', '', '赵六', ''],
        '24Q3_现场': ['不在盘内', '不在盘内', '不在盘内', '不在盘内']
    }
    
    df = pd.DataFrame(test_data)
    print("原始数据:")
    print(df)
    print()
    
    # 模拟现场映射和现场小组映射
    field_mappings = {
        '张三': '李四',
        '王五': '赵六',
        '李四': '李四',  # 自映射
        '赵六': '赵六'   # 自映射
    }
    
    field_group_mappings = {
        '李四': '王婆小组',
        '赵六': '李婆小组'
    }
    
    def get_latest_field(row, field_columns):
        """获取最新现场值，跳过'不在盘内'"""
        for col in field_columns:
            value = row.get(col)
            if pd.notna(value) and value != '' and value != '不在盘内':
                return value
        return '其他咨询'
    
    def apply_mapping(value, mapping_dict):
        """应用映射"""
        return mapping_dict.get(value, value)
    
    # 现场列按时间排序（从新到旧）
    field_columns = ['24Q3_现场', '24Q2_现场', '24Q1_现场', '23Q4_现场']
    
    # 计算最新现场和现场小组
    latest_fields = []
    field_groups = []
    
    for _, row in df.iterrows():
        # 获取最新现场
        latest_field = get_latest_field(row, field_columns)
        
        # 应用现场映射
        if latest_field and latest_field != '其他咨询':
            mapped_field = apply_mapping(latest_field, field_mappings)
            # 应用现场小组映射
            field_group = apply_mapping(mapped_field, field_group_mappings)
        else:
            mapped_field = latest_field
            field_group = ''
        
        latest_fields.append(mapped_field)
        field_groups.append(field_group)
    
    # 添加单独的现场小组列和最新现场列
    df.insert(1, '现场小组', field_groups)
    df.insert(2, '最新现场', latest_fields)
    
    print("处理后数据（只有1个现场小组列）:")
    print(df)
    print()
    
    # 验证结果
    expected_results = [
        {'card': '000001', 'latest_field': '李四', 'field_group': '王婆小组'},
        {'card': '000002', 'latest_field': '李四', 'field_group': '王婆小组'},
        {'card': '000003', 'latest_field': '赵六', 'field_group': '李婆小组'},
        {'card': '000004', 'latest_field': '其他咨询', 'field_group': ''}
    ]
    
    print("=== 验证结果 ===")
    all_passed = True
    
    for i, expected in enumerate(expected_results):
        actual_latest = df.iloc[i]['最新现场']
        actual_group = df.iloc[i]['现场小组']
        
        if actual_latest == expected['latest_field'] and actual_group == expected['field_group']:
            print(f"✅ {expected['card']}: 最新现场={actual_latest}, 现场小组={actual_group}")
        else:
            print(f"❌ {expected['card']}: 预期(最新现场={expected['latest_field']}, 现场小组={expected['field_group']}), 实际(最新现场={actual_latest}, 现场小组={actual_group})")
            all_passed = False
    
    # 检查列结构
    print(f"\n=== 列结构检查 ===")
    print(f"总列数: {len(df.columns)}")
    print(f"列名: {list(df.columns)}")
    
    # 检查是否只有1个现场小组列
    field_group_columns = [col for col in df.columns if '现场小组' in col]
    print(f"现场小组相关列: {field_group_columns}")
    
    if len(field_group_columns) == 1 and field_group_columns[0] == '现场小组':
        print("✅ 现场小组列数量正确：只有1列")
    else:
        print(f"❌ 现场小组列数量错误：应该只有1列，实际有{len(field_group_columns)}列")
        all_passed = False
    
    # 检查是否没有季度现场小组列
    quarterly_field_group_columns = [col for col in df.columns if '现场小组' in col and ('Q' in col)]
    if len(quarterly_field_group_columns) == 0:
        print("✅ 没有季度现场小组列")
    else:
        print(f"❌ 存在季度现场小组列: {quarterly_field_group_columns}")
        all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！现场小组单列功能正确。")
    else:
        print("\n❌ 部分测试失败，需要检查逻辑。")
    
    return all_passed

def test_column_sorting():
    """测试列排序是否正确"""
    
    print("\n=== 测试列排序 ===")
    
    # 模拟列名
    test_columns = [
        '会员卡号',
        '现场小组',  # 单独的现场小组列
        '最新现场',  # 单独的最新现场列
        '23Q4_现场', '24Q1_现场', '24Q2_现场', '24Q3_现场',  # 季度现场列
        '23Q4_综合等级', '24Q1_综合等级', '24Q2_综合等级', '24Q3_综合等级',
        '23Q4_执行业绩', '24Q1_执行业绩', '24Q2_执行业绩', '24Q3_执行业绩'
    ]
    
    print("测试列名:")
    for i, col in enumerate(test_columns):
        print(f"  {i+1:2d}. {col}")
    
    # 检查现场小组列
    field_group_columns = [col for col in test_columns if '现场小组' in col]
    print(f"\n现场小组相关列: {field_group_columns}")
    
    if len(field_group_columns) == 1 and field_group_columns[0] == '现场小组':
        print("✅ 现场小组列配置正确")
    else:
        print("❌ 现场小组列配置错误")
    
    # 检查现场列
    field_columns = [col for col in test_columns if '现场' in col and '现场小组' not in col]
    print(f"现场列: {field_columns}")
    
    expected_field_columns = ['最新现场', '23Q4_现场', '24Q1_现场', '24Q2_现场', '24Q3_现场']
    if field_columns == expected_field_columns:
        print("✅ 现场列配置正确")
    else:
        print("❌ 现场列配置错误")

if __name__ == "__main__":
    success = test_single_field_group_column()
    test_column_sorting()
    
    print("\n📋 总结:")
    print("1. ✅ 现场小组只有1列，不按季度生成")
    print("2. ✅ 现场小组基于最新现场列的映射结果")
    print("3. ✅ 最新现场跳过'不在盘内'，获取有效值")
    print("4. ✅ 映射流程：原始现场 → 现场映射 → 现场小组映射")
