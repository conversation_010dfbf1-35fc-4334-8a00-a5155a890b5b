"""
系统测试脚本
"""
import os
import sys

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from config import get_config
        print("✅ config 模块导入成功")
    except Exception as e:
        print(f"❌ config 模块导入失败: {e}")
        return False
    
    try:
        from app import create_app, db
        print("✅ app 模块导入成功")
    except Exception as e:
        print(f"❌ app 模块导入失败: {e}")
        return False
    
    try:
        from app.models import User, Role, Permission
        print("✅ models 模块导入成功")
    except Exception as e:
        print(f"❌ models 模块导入失败: {e}")
        return False
    
    return True

def test_database_connection():
    """测试数据库连接"""
    print("测试数据库连接...")
    
    try:
        from app import create_app, db
        from config import get_config
        
        config_class = get_config()
        app = create_app(config_class)
        
        with app.app_context():
            # 尝试连接数据库
            db.engine.connect()
            print("✅ 数据库连接成功")
            return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_database_tables():
    """测试数据库表"""
    print("测试数据库表...")
    
    try:
        from app import create_app, db
        from app.models import Role, User
        from config import get_config
        
        config_class = get_config()
        app = create_app(config_class)
        
        with app.app_context():
            # 检查角色表
            role_count = Role.query.count()
            print(f"✅ 角色表记录数: {role_count}")
            
            # 检查用户表
            user_count = User.query.count()
            print(f"✅ 用户表记录数: {user_count}")
            
            if role_count > 0 and user_count > 0:
                print("✅ 数据库表测试通过")
                return True
            else:
                print("⚠️ 数据库表为空，可能需要初始化")
                return False
    except Exception as e:
        print(f"❌ 数据库表测试失败: {e}")
        return False

def test_app_creation():
    """测试应用创建"""
    print("测试应用创建...")
    
    try:
        from app import create_app
        from config import get_config
        
        config_class = get_config()
        app = create_app(config_class)
        
        print(f"✅ Flask应用创建成功")
        print(f"   应用名称: {app.name}")
        print(f"   调试模式: {app.debug}")
        print(f"   注册的蓝图: {list(app.blueprints.keys())}")
        
        return True
    except Exception as e:
        print(f"❌ 应用创建失败: {e}")
        return False

def main():
    """主函数"""
    print("老客回访与跟进系统 - 系统测试")
    print("="*40)
    
    # 确保在正确的目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"当前工作目录: {os.getcwd()}")
    
    # 检查文件
    print("\n检查关键文件...")
    required_files = [
        'config.py',
        'app/__init__.py',
        'app/models/__init__.py',
        'app/models/user.py',
        'app/views/__init__.py',
        'app/views/auth.py',
        'app/views/main.py'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n❌ 缺少关键文件: {', '.join(missing_files)}")
        return False
    
    print("\n" + "="*40)
    
    # 运行测试
    tests = [
        ("模块导入", test_imports),
        ("应用创建", test_app_creation),
        ("数据库连接", test_database_connection),
        ("数据库表", test_database_tables)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}测试:")
        print("-" * 20)
        result = test_func()
        results.append((test_name, result))
    
    # 总结
    print("\n" + "="*40)
    print("测试结果总结:")
    print("="*40)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n通过率: {passed}/{len(results)} ({passed/len(results)*100:.1f}%)")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！系统可以正常启动")
        print("运行命令: python quick_start.py")
    elif passed >= len(results) - 1:
        print("\n⚠️ 大部分测试通过，系统基本可用")
        if not results[-1][1]:  # 如果数据库表测试失败
            print("建议运行: python init_db.py")
    else:
        print("\n❌ 多个测试失败，请检查系统配置")
        print("建议:")
        print("1. 检查数据库配置 (config.py)")
        print("2. 安装缺失的依赖包")
        print("3. 确保MySQL服务正在运行")

if __name__ == '__main__':
    main()
