#!/usr/bin/env python3
"""
调试后台任务执行问题
"""

import threading
import time
from app import create_app
from app.models import DataProcessingTask, DatabaseConfig
from app.data_processing.routes import process_data_background

def test_background_task_execution():
    """测试后台任务执行"""
    app = create_app()
    
    with app.app_context():
        # 获取最新的等待中任务
        pending_task = DataProcessingTask.query.filter_by(status='pending').order_by(DataProcessingTask.created_at.desc()).first()
        
        if not pending_task:
            print("❌ 没有找到等待中的任务")
            return
        
        print(f"📋 找到等待中的任务:")
        print(f"   ID: {pending_task.id}")
        print(f"   名称: {pending_task.task_name}")
        print(f"   状态: {pending_task.status}")
        print(f"   进度: {pending_task.progress}%")
        
        # 测试1: 直接调用后台函数
        print(f"\n🧪 测试1: 直接调用后台处理函数")
        try:
            print(f"   开始执行任务 {pending_task.id}...")
            process_data_background(pending_task.id)
            
            # 重新查询任务状态
            pending_task = DataProcessingTask.query.get(pending_task.id)
            print(f"   执行完成！")
            print(f"   最终状态: {pending_task.status}")
            print(f"   最终进度: {pending_task.progress}%")
            print(f"   错误信息: {pending_task.error_message}")
            
        except Exception as e:
            print(f"   ❌ 直接调用失败: {e}")
            import traceback
            traceback.print_exc()

def test_thread_execution():
    """测试线程执行"""
    app = create_app()
    
    with app.app_context():
        # 获取最新的等待中任务
        pending_task = DataProcessingTask.query.filter_by(status='pending').order_by(DataProcessingTask.created_at.desc()).first()
        
        if not pending_task:
            print("❌ 没有找到等待中的任务")
            return
        
        print(f"\n🧪 测试2: 线程执行")
        print(f"   任务ID: {pending_task.id}")
        
        # 创建线程
        def thread_wrapper():
            try:
                print(f"   线程开始执行...")
                with app.app_context():
                    process_data_background(pending_task.id)
                print(f"   线程执行完成")
            except Exception as e:
                print(f"   ❌ 线程执行失败: {e}")
                import traceback
                traceback.print_exc()
        
        thread = threading.Thread(target=thread_wrapper)
        thread.daemon = True
        thread.start()
        
        # 等待线程完成或超时
        thread.join(timeout=60)  # 等待60秒
        
        if thread.is_alive():
            print(f"   ⚠️  线程仍在运行中...")
        else:
            print(f"   ✅ 线程已完成")
        
        # 检查任务状态
        pending_task = DataProcessingTask.query.get(pending_task.id)
        print(f"   任务状态: {pending_task.status}")
        print(f"   任务进度: {pending_task.progress}%")

def check_database_connection():
    """检查数据库连接"""
    app = create_app()
    
    with app.app_context():
        print(f"\n🔗 检查数据库连接:")
        
        # 检查系统数据库
        try:
            task_count = DataProcessingTask.query.count()
            print(f"   ✅ 系统数据库连接正常，任务数: {task_count}")
        except Exception as e:
            print(f"   ❌ 系统数据库连接失败: {e}")
            return False
        
        # 检查业务数据库
        active_config = DatabaseConfig.get_active_config()
        if not active_config:
            print(f"   ❌ 没有激活的业务数据库配置")
            return False
        
        print(f"   业务数据库配置: {active_config.name}")
        
        try:
            from app.data_processing.processor import RFMDataProcessor
            processor = RFMDataProcessor(active_config)
            
            if processor.connect_database():
                quarter_tables = processor.get_quarter_tables()
                print(f"   ✅ 业务数据库连接正常，季度表数: {len(quarter_tables)}")
                return True
            else:
                print(f"   ❌ 业务数据库连接失败")
                return False
                
        except Exception as e:
            print(f"   ❌ 业务数据库测试失败: {e}")
            return False

def main():
    """主函数"""
    print("🔍 调试后台任务执行问题")
    print("=" * 50)
    
    # 检查数据库连接
    if not check_database_connection():
        print("❌ 数据库连接有问题，无法继续测试")
        return
    
    # 测试后台任务执行
    test_background_task_execution()
    
    # 如果需要，也可以测试线程执行
    # test_thread_execution()

if __name__ == '__main__':
    main()
