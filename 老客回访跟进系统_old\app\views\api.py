"""
API接口视图控制器
"""
from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user
from app import db
from app.models.customer import CustomerRegistration, FollowUpRecord, Channel
from app.models.user import User
from app.utils.permissions import require_permission
from app.utils.validators import validate_card_number, validate_content_length
from datetime import datetime

bp = Blueprint('api', __name__)

@bp.route('/customers', methods=['GET'])
@require_permission('CUSTOMER_VIEW')
def get_customers():
    """获取客户列表API"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    # 限制每页最大数量
    per_page = min(per_page, 100)
    
    # 根据权限过滤查询
    from app.utils.permissions import filter_query_by_permission
    query = CustomerRegistration.query
    query = filter_query_by_permission(query, CustomerRegistration, current_user)
    
    # 分页查询
    customers = query.order_by(CustomerRegistration.registration_time.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # 构建响应数据
    items = []
    for customer in customers.items:
        latest_follow_up = customer.get_latest_follow_up()
        items.append({
            'id': customer.id,
            'card_number': customer.card_number,
            'registrar_name': customer.registrar.real_name,
            'consultant_name': customer.assigned_consultant.real_name,
            'channel_name': customer.activation_channel.channel_name,
            'consultation_content': customer.consultation_content,
            'last_visit_date': customer.last_visit_date.strftime('%Y-%m-%d'),
            'registration_time': customer.registration_time.strftime('%Y-%m-%d %H:%M:%S'),
            'latest_follow_up': {
                'content': latest_follow_up.follow_up_content if latest_follow_up else None,
                'time': latest_follow_up.follow_up_time.strftime('%Y-%m-%d %H:%M:%S') if latest_follow_up else None
            }
        })
    
    return jsonify({
        'success': True,
        'data': {
            'items': items,
            'pagination': {
                'page': customers.page,
                'per_page': customers.per_page,
                'total': customers.total,
                'pages': customers.pages,
                'has_prev': customers.has_prev,
                'has_next': customers.has_next
            }
        }
    })

@bp.route('/customers', methods=['POST'])
@require_permission('CUSTOMER_REGISTER')
def create_customer():
    """创建客户登记API"""
    data = request.get_json()
    
    if not data:
        return jsonify({
            'success': False,
            'error': {'message': '请提供JSON数据'}
        }), 400
    
    # 验证必需字段
    required_fields = ['card_number', 'assigned_consultant_id', 'activation_channel_id', 
                      'consultation_content', 'last_visit_date']
    
    for field in required_fields:
        if field not in data:
            return jsonify({
                'success': False,
                'error': {'message': f'缺少必需字段: {field}'}
            }), 400
    
    # 验证数据
    is_valid, message = validate_card_number(data['card_number'])
    if not is_valid:
        return jsonify({
            'success': False,
            'error': {'message': message}
        }), 400
    
    is_valid, message = validate_content_length(data['consultation_content'], "咨询内容")
    if not is_valid:
        return jsonify({
            'success': False,
            'error': {'message': message}
        }), 400
    
    try:
        # 解析日期
        last_visit_date = datetime.strptime(data['last_visit_date'], '%Y-%m-%d').date()
        
        # 验证现场顾问是否存在
        consultant = User.query.get(data['assigned_consultant_id'])
        if not consultant or consultant.role.role_code != 'FIELD_CONSULTANT':
            return jsonify({
                'success': False,
                'error': {'message': '指定的现场顾问不存在或角色不正确'}
            }), 400
        
        # 验证渠道是否存在
        channel = Channel.query.get(data['activation_channel_id'])
        if not channel or not channel.is_active:
            return jsonify({
                'success': False,
                'error': {'message': '指定的渠道不存在或已停用'}
            }), 400
        
        # 创建客户登记
        registration = CustomerRegistration(
            card_number=data['card_number'],
            assigned_consultant_id=data['assigned_consultant_id'],
            activation_channel_id=data['activation_channel_id'],
            consultation_content=data['consultation_content'],
            last_visit_date=last_visit_date,
            registrar_id=current_user.id
        )
        
        db.session.add(registration)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'data': {
                'id': registration.id,
                'card_number': registration.card_number,
                'registration_time': registration.registration_time.strftime('%Y-%m-%d %H:%M:%S')
            },
            'message': '客户登记成功'
        })
        
    except ValueError as e:
        return jsonify({
            'success': False,
            'error': {'message': '日期格式不正确，请使用YYYY-MM-DD格式'}
        }), 400
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': {'message': '客户登记失败'}
        }), 500

@bp.route('/follow-ups', methods=['POST'])
@require_permission('FOLLOW_UP_ADD')
def create_follow_up():
    """添加跟进记录API"""
    data = request.get_json()
    
    if not data:
        return jsonify({
            'success': False,
            'error': {'message': '请提供JSON数据'}
        }), 400
    
    # 验证必需字段
    required_fields = ['customer_registration_id', 'follow_up_content']
    
    for field in required_fields:
        if field not in data:
            return jsonify({
                'success': False,
                'error': {'message': f'缺少必需字段: {field}'}
            }), 400
    
    # 验证跟进内容
    is_valid, message = validate_content_length(data['follow_up_content'], "跟进内容")
    if not is_valid:
        return jsonify({
            'success': False,
            'error': {'message': message}
        }), 400
    
    try:
        # 验证客户登记是否存在
        registration = CustomerRegistration.query.get(data['customer_registration_id'])
        if not registration:
            return jsonify({
                'success': False,
                'error': {'message': '客户登记不存在'}
            }), 404
        
        # 检查权限：只能跟进分配给自己的客户
        if registration.assigned_consultant_id != current_user.id and not current_user.is_admin():
            return jsonify({
                'success': False,
                'error': {'message': '您无权跟进此客户'}
            }), 403
        
        # 创建跟进记录
        follow_up = FollowUpRecord(
            customer_registration_id=data['customer_registration_id'],
            follow_up_content=data['follow_up_content'],
            consultant_id=current_user.id
        )
        
        db.session.add(follow_up)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'data': {
                'id': follow_up.id,
                'follow_up_time': follow_up.follow_up_time.strftime('%Y-%m-%d %H:%M:%S')
            },
            'message': '跟进记录添加成功'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': {'message': '跟进记录添加失败'}
        }), 500

@bp.route('/channels', methods=['GET'])
@require_permission('CHANNEL_VIEW')
def get_channels():
    """获取渠道列表API"""
    search = request.args.get('search', '').strip()

    query = Channel.query.filter_by(is_active=True)

    # 如果有搜索词，进行搜索
    if search:
        from app.utils.pinyin_utils import match_search_term
        # 先获取所有渠道，然后在Python中进行匹配
        all_channels = query.all()
        filtered_channels = []

        for channel in all_channels:
            # 搜索渠道分类和名称
            full_text = f"{channel.channel_category} {channel.channel_name}"
            if (match_search_term(channel.channel_category, search) or
                match_search_term(channel.channel_name, search) or
                match_search_term(full_text, search)):
                filtered_channels.append(channel)

        channels = filtered_channels
    else:
        channels = query.all()

    items = []
    for channel in channels:
        from app.utils.pinyin_utils import get_pinyin_first_letter
        items.append({
            'id': channel.id,
            'text': f"{channel.channel_category} - {channel.channel_name}",
            'category': channel.channel_category,
            'name': channel.channel_name,
            'pinyin': get_pinyin_first_letter(f"{channel.channel_category}{channel.channel_name}"),
            'created_time': channel.created_time.strftime('%Y-%m-%d %H:%M:%S')
        })

    return jsonify({
        'success': True,
        'data': items
    })

@bp.route('/consultants', methods=['GET'])
@require_permission('CUSTOMER_VIEW')
def get_consultants():
    """获取现场顾问列表API"""
    search = request.args.get('search', '').strip()

    from app.models.user import Role, User
    field_role = Role.query.filter_by(role_code='FIELD_CONSULTANT').first()

    if not field_role:
        return jsonify({
            'success': True,
            'data': []
        })

    query = User.query.filter_by(role_id=field_role.id, is_active=True)

    # 如果有搜索词，进行搜索
    if search:
        from app.utils.pinyin_utils import match_search_term
        # 先获取所有顾问，然后在Python中进行匹配
        all_consultants = query.all()
        filtered_consultants = []

        for consultant in all_consultants:
            # 搜索姓名和部门
            department_name = consultant.department.department_name if consultant.department else ''
            full_text = f"{consultant.real_name} {department_name}"
            if (match_search_term(consultant.real_name, search) or
                match_search_term(department_name, search) or
                match_search_term(full_text, search)):
                filtered_consultants.append(consultant)

        consultants = filtered_consultants
    else:
        consultants = query.all()

    items = []
    for consultant in consultants:
        from app.utils.pinyin_utils import get_pinyin_first_letter
        department_name = consultant.department.department_name if consultant.department else '未分配部门'
        items.append({
            'id': consultant.id,
            'text': f"{consultant.real_name} - {department_name}",
            'name': consultant.real_name,
            'department': department_name,
            'pinyin': get_pinyin_first_letter(f"{consultant.real_name}{department_name}"),
        })

    return jsonify({
        'success': True,
        'data': items
    })

@bp.route('/registrars', methods=['GET'])
@require_permission('CUSTOMER_VIEW')
def get_registrars():
    """获取网络咨询员列表API"""
    search = request.args.get('search', '').strip()

    from app.models.user import Role, User
    network_role = Role.query.filter_by(role_code='NETWORK_CONSULTANT').first()

    if not network_role:
        return jsonify({
            'success': True,
            'data': []
        })

    query = User.query.filter_by(role_id=network_role.id, is_active=True)

    # 如果有搜索词，进行搜索
    if search:
        from app.utils.pinyin_utils import match_search_term
        # 先获取所有咨询员，然后在Python中进行匹配
        all_registrars = query.all()
        filtered_registrars = []

        for registrar in all_registrars:
            # 搜索姓名和部门
            department_name = registrar.department.department_name if registrar.department else ''
            full_text = f"{registrar.real_name} {department_name}"
            if (match_search_term(registrar.real_name, search) or
                match_search_term(department_name, search) or
                match_search_term(full_text, search)):
                filtered_registrars.append(registrar)

        registrars = filtered_registrars
    else:
        registrars = query.all()

    items = []
    for registrar in registrars:
        from app.utils.pinyin_utils import get_pinyin_first_letter
        department_name = registrar.department.department_name if registrar.department else '未分配部门'
        items.append({
            'id': registrar.id,
            'text': f"{registrar.real_name} - {department_name}",
            'name': registrar.real_name,
            'department': department_name,
            'pinyin': get_pinyin_first_letter(f"{registrar.real_name}{department_name}"),
        })

    return jsonify({
        'success': True,
        'data': items
    })

@bp.route('/field-consultants', methods=['GET'])
@require_permission('CUSTOMER_VIEW')
def get_field_consultants():
    """获取现场咨询员列表API"""
    from app.models.user import Role
    
    field_role = Role.query.filter_by(role_code='FIELD_CONSULTANT').first()
    if not field_role:
        return jsonify({
            'success': True,
            'data': []
        })
    
    consultants = User.query.filter_by(
        role_id=field_role.id,
        is_active=True
    ).all()
    
    items = []
    for consultant in consultants:
        items.append({
            'id': consultant.id,
            'real_name': consultant.real_name,
            'department': consultant.department
        })
    
    return jsonify({
        'success': True,
        'data': items
    })

@bp.errorhandler(404)
def api_not_found(error):
    """API 404错误处理"""
    return jsonify({
        'success': False,
        'error': {
            'code': 'NOT_FOUND',
            'message': '请求的API接口不存在'
        }
    }), 404

@bp.errorhandler(500)
def api_internal_error(error):
    """API 500错误处理"""
    db.session.rollback()
    return jsonify({
        'success': False,
        'error': {
            'code': 'INTERNAL_ERROR',
            'message': '服务器内部错误'
        }
    }), 500
