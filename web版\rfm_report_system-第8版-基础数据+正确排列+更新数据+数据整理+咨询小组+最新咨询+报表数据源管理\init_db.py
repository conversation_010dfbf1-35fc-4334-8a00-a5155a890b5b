#!/usr/bin/env python3
"""
数据库初始化脚本
用于创建系统管理数据库和初始化表结构
"""

import os
import sys
import pymysql
from flask import Flask
from app.models import db, User, SystemSettings
from config import config

def create_system_database():
    """创建系统管理数据库"""
    config_obj = config['default']()
    
    # 连接MySQL服务器（不指定数据库）
    connection = pymysql.connect(
        host=config_obj.SYSTEM_DB_HOST,
        port=config_obj.SYSTEM_DB_PORT,
        user=config_obj.SYSTEM_DB_USER,
        password=config_obj.SYSTEM_DB_PASSWORD,
        charset='utf8mb4'
    )
    
    try:
        with connection.cursor() as cursor:
            # 创建数据库
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {config_obj.SYSTEM_DB_NAME} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print(f"数据库 {config_obj.SYSTEM_DB_NAME} 创建成功或已存在")
        connection.commit()
    finally:
        connection.close()

def init_database():
    """初始化数据库表和基础数据"""
    app = Flask(__name__)
    app.config.from_object(config['default'])
    
    db.init_app(app)
    
    with app.app_context():
        # 创建所有表
        db.create_all()
        print("数据库表创建成功")

        # 检查新增的表
        from sqlalchemy import inspect
        inspector = inspect(db.engine)
        tables = inspector.get_table_names()

        if 'category_mappings' in tables:
            print("✅ 品类映射表创建成功")
        if 'mapping_templates' in tables:
            print("✅ 映射模板表创建成功")
        
        # 检查是否已有用户
        if User.query.count() == 0:
            print("检测到首次初始化，请创建管理员账户：")
            
            while True:
                username = input("请输入管理员用户名: ").strip()
                if username:
                    break
                print("用户名不能为空")
            
            while True:
                password = input("请输入管理员密码: ").strip()
                if len(password) >= 6:
                    break
                print("密码长度至少6位")
            
            # 创建管理员用户
            admin_user = User(username=username)
            admin_user.set_password(password)
            db.session.add(admin_user)
            
            # 设置系统初始化标志
            SystemSettings.set_setting(
                'system_initialized', 
                'true', 
                '系统是否已完成初始化'
            )
            
            # 设置注册功能状态（首次初始化后禁用）
            SystemSettings.set_setting(
                'registration_enabled', 
                'false', 
                '是否允许新用户注册'
            )
            
            db.session.commit()
            print(f"管理员账户 '{username}' 创建成功")
        else:
            print("系统已初始化，跳过用户创建")

def reset_admin_password():
    """重置管理员密码"""
    app = Flask(__name__)
    app.config.from_object(config['default'])
    
    db.init_app(app)
    
    with app.app_context():
        users = User.query.all()
        if not users:
            print("系统中没有用户，请先运行初始化")
            return
        
        print("现有用户列表：")
        for i, user in enumerate(users, 1):
            print(f"{i}. {user.username}")
        
        while True:
            try:
                choice = int(input("请选择要重置密码的用户编号: "))
                if 1 <= choice <= len(users):
                    selected_user = users[choice - 1]
                    break
                else:
                    print("无效的选择")
            except ValueError:
                print("请输入有效的数字")
        
        while True:
            new_password = input(f"请输入用户 '{selected_user.username}' 的新密码: ").strip()
            if len(new_password) >= 6:
                break
            print("密码长度至少6位")
        
        selected_user.set_password(new_password)
        db.session.commit()
        print(f"用户 '{selected_user.username}' 的密码已重置")

def main():
    """主函数"""
    if len(sys.argv) > 1:
        command = sys.argv[1]
        if command == 'reset-password':
            try:
                reset_admin_password()
            except Exception as e:
                print(f"重置密码失败: {e}")
                sys.exit(1)
        else:
            print("未知命令。可用命令: reset-password")
            sys.exit(1)
    else:
        try:
            print("开始初始化系统数据库...")
            create_system_database()
            init_database()
            print("系统初始化完成！")
        except Exception as e:
            print(f"初始化失败: {e}")
            sys.exit(1)

if __name__ == '__main__':
    main()
