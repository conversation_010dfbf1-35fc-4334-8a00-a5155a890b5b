# -*- coding: utf-8 -*-
"""
系统初始化路由
"""

from flask import render_template, redirect, url_for, flash, request, session
from app.init import bp
from app.forms.init_forms import DatabaseConfigForm, AdminRegistrationForm
from app.services.system_init_service import SystemInitService
from app.services.database_service import DatabaseService

@bp.route('/check')
def check():
    """检查系统状态"""
    status = SystemInitService.check_system_status()
    
    if status['system_initialized']:
        # 系统已初始化，重定向到登录页面
        return redirect(url_for('auth.login'))
    elif status['database_connected'] and status['tables_created'] and not status['admin_exists']:
        # 数据库已连接，表已创建，但没有管理员，跳转到管理员注册
        return redirect(url_for('init.register_admin'))
    else:
        # 需要配置数据库
        return redirect(url_for('init.database_config'))

@bp.route('/database-config', methods=['GET', 'POST'])
def database_config():
    """数据库配置页面"""
    form = DatabaseConfigForm()
    
    # 尝试从现有配置加载默认值
    existing_config = SystemInitService.get_database_config_from_env()
    if existing_config and request.method == 'GET':
        form.host.data = existing_config.get('host', 'localhost')
        form.port.data = existing_config.get('port', 3306)
        form.username.data = existing_config.get('username', 'root')
        form.database.data = existing_config.get('database', 'Old_Customer_System')
    
    if form.validate_on_submit():
        db_config = {
            'host': form.host.data,
            'port': form.port.data,
            'username': form.username.data,
            'password': form.password.data,
            'database': form.database.data
        }
        
        # 测试数据库连接
        success, message = DatabaseService.test_connection(
            db_config['host'], db_config['port'], 
            db_config['username'], db_config['password']
        )
        
        if success:
            # 保存配置到session
            session['db_config'] = db_config
            flash('数据库连接测试成功！', 'success')
            return redirect(url_for('init.confirm_database'))
        else:
            flash(f'数据库连接失败: {message}', 'error')
    
    return render_template('init/database_config.html', form=form, title='数据库配置')

@bp.route('/confirm-database', methods=['GET', 'POST'])
def confirm_database():
    """确认数据库操作"""
    db_config = session.get('db_config')
    if not db_config:
        return redirect(url_for('init.database_config'))
    
    # 检查数据库是否存在
    db_exists, db_message = DatabaseService.check_database_exists(
        db_config['host'], db_config['port'],
        db_config['username'], db_config['password'],
        db_config['database']
    )
    
    if request.method == 'POST':
        action = request.form.get('action')
        
        if action == 'initialize':
            # 初始化数据库
            success, message = SystemInitService.initialize_database(db_config)
            if success:
                # 保存配置
                SystemInitService.save_database_config(db_config)
                flash('数据库初始化成功！', 'success')
                return redirect(url_for('init.register_admin'))
            else:
                flash(f'数据库初始化失败: {message}', 'error')
        
        elif action == 'reset':
            # 重置数据库
            success, message = SystemInitService.reset_database(db_config)
            if success:
                # 保存配置
                SystemInitService.save_database_config(db_config)
                flash('数据库重置成功！', 'success')
                return redirect(url_for('init.register_admin'))
            else:
                flash(f'数据库重置失败: {message}', 'error')
        
        elif action == 'use_existing':
            # 使用现有数据库
            # 更新应用配置
            connection_string = f"mysql+pymysql://{db_config['username']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}?charset=utf8mb4"
            from flask import current_app
            current_app.config['SQLALCHEMY_DATABASE_URI'] = connection_string
            
            # 保存配置
            SystemInitService.save_database_config(db_config)
            flash('数据库配置已更新！', 'success')
            return redirect(url_for('init.check'))
    
    # 获取数据库信息
    db_info = None
    if db_exists:
        db_info = DatabaseService.get_database_info(
            db_config['host'], db_config['port'],
            db_config['username'], db_config['password'],
            db_config['database']
        )
    
    return render_template('init/confirm_database.html', 
                         db_config=db_config, 
                         db_exists=db_exists, 
                         db_message=db_message,
                         db_info=db_info,
                         title='确认数据库操作')

@bp.route('/register-admin', methods=['GET', 'POST'])
def register_admin():
    """管理员注册页面"""
    form = AdminRegistrationForm()
    
    if form.validate_on_submit():
        try:
            from app import db
            from app.models import User
            
            # 创建管理员用户
            admin_user = User(
                username=form.username.data,
                real_name=form.real_name.data,
                role='admin',
                is_active=True
            )
            admin_user.set_password(form.password.data)
            
            db.session.add(admin_user)
            db.session.commit()
            
            flash(f'管理员账号 "{form.username.data}" 创建成功！', 'success')
            return redirect(url_for('init.complete'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'创建管理员账号失败: {str(e)}', 'error')
    
    return render_template('init/register_admin.html', form=form, title='创建管理员账号')

@bp.route('/complete')
def complete():
    """初始化完成页面"""
    return render_template('init/complete.html', title='初始化完成')