{% extends "base.html" %}

{% block title %}管理员仪表板 - 老客回访与跟进系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-tachometer-alt"></i> 管理员仪表板</h2>
        <p class="text-muted">欢迎回来，{{ current_user.real_name }}！</p>
    </div>
</div>

<div class="row">
    <!-- 系统统计 -->
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>用户总数</h4>
                        <h2 id="total-users">-</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>客户总数</h4>
                        <h2 id="total-customers">-</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-friends fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>待跟进</h4>
                        <h2 id="pending-follow-up">-</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>今日登记</h4>
                        <h2 id="today-registrations">-</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-plus-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <!-- 快速操作 -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bolt"></i> 快速操作</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6 mb-3">
                        <a href="{{ url_for('admin.users') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-users"></i><br>用户管理
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="{{ url_for('admin.system_config') }}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-cog"></i><br>系统配置
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="{{ url_for('customer.index') }}" class="btn btn-outline-success w-100">
                            <i class="fas fa-user-friends"></i><br>客户管理
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="{{ url_for('channel.index') }}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-tags"></i><br>渠道管理
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="{{ url_for('report.statistics') }}" class="btn btn-outline-info w-100">
                            <i class="fas fa-chart-bar"></i><br>统计报表
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 系统信息 -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> 系统信息</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><i class="fas fa-server"></i> 系统版本</td>
                        <td>v1.0.0</td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-database"></i> 数据库</td>
                        <td>MySQL</td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-clock"></i> 运行时间</td>
                        <td id="uptime">-</td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-calendar"></i> 最后登录</td>
                        <td>{{ current_user.last_login_time.strftime('%Y-%m-%d %H:%M:%S') if current_user.last_login_time else '首次登录' }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <!-- 最近活动 -->
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-history"></i> 最近活动</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>用户</th>
                                <th>操作</th>
                                <th>详情</th>
                            </tr>
                        </thead>
                        <tbody id="recent-activities">
                            <tr>
                                <td colspan="4" class="text-center text-muted">
                                    <i class="fas fa-spinner fa-spin"></i> 加载中...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
// 页面加载完成后获取统计数据
document.addEventListener('DOMContentLoaded', function() {
    // 这里可以添加AJAX请求获取实时数据
    // 暂时显示静态数据
    document.getElementById('total-users').textContent = '5';
    document.getElementById('total-customers').textContent = '0';
    document.getElementById('pending-follow-up').textContent = '0';
    document.getElementById('today-registrations').textContent = '0';
    document.getElementById('uptime').textContent = '刚刚启动';
    
    // 清空活动列表
    document.getElementById('recent-activities').innerHTML = 
        '<tr><td colspan="4" class="text-center text-muted">暂无活动记录</td></tr>';
});
</script>
{% endblock %}
