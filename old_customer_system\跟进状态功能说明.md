# 客户管理系统 - 跟进到院状态和跟进消费金额功能

## 功能概述

在客户管理的客户列表中新增了两个字段：
1. **跟进到院状态** - 显示客户的跟进和到院情况
2. **跟进消费金额** - 显示客户跟进后的消费总金额

## 业务逻辑

### 跟进到院状态判断逻辑

```
如果客户没有跟进记录 → 空
如果客户有跟进记录：
  ├─ 查找跟进时间之后的到院记录
  ├─ 如果存在到院记录 → "已到院"
  └─ 如果不存在到院记录 → 空
```

### 跟进消费金额计算逻辑

```
如果客户没有跟进记录 → 空
如果客户有跟进记录：
  ├─ 统计跟进时间之后的所有消费记录总金额
  ├─ 如果金额 > 0 → 显示具体金额
  └─ 如果金额 <= 0 → 空
```

## 数据表关联

### 客户表 (customers)
- `card_number` - 卡号
- `registration_date` - 登记时间  
- `follow_up_time` - 跟进时间

### 到院明细表 (visit_records)
- `member_card_number` - 会员卡号
- `visit_date` - 来院日期

### 消费明细表 (consumption_records)  
- `member_card_number` - 会员卡号
- `consumption_date` - 消费日期
- `amount` - 消费金额

## 实现细节

### 1. 模型方法 (app/models.py)

```python
class Customer(db.Model):
    def get_follow_up_visit_status(self):
        """获取跟进到院状态"""
        if not self.follow_up_time:
            return "待跟进"
        
        visit_after_followup = VisitRecord.query.filter(
            VisitRecord.member_card_number == self.card_number,
            VisitRecord.visit_date > self.follow_up_time.date()
        ).first()

        return "已到院" if visit_after_followup else ""

    def get_follow_up_consumption_amount(self):
        """获取跟进消费金额"""
        if not self.follow_up_time:
            return 0.0

        total_amount = db.session.query(func.sum(ConsumptionRecord.amount)).filter(
            ConsumptionRecord.member_card_number == self.card_number,
            ConsumptionRecord.consumption_date > self.follow_up_time.date()
        ).scalar()
        
        amount = float(total_amount) if total_amount else 0.0
        # 如果金额<=0，返回None表示空
        return amount if amount > 0 else None
```

### 2. 模板修改 (app/templates/registration/customers.html)

**表头新增字段：**
```html
<th>跟进到院状态</th>
<th>跟进消费金额</th>
```

**表格内容新增：**
```html
<td>
    {% set visit_status = customer.get_follow_up_visit_status() %}
    {% if visit_status == "已到院" %}
        <span class="badge bg-success">{{ visit_status }}</span>
    {% else %}
        <span class="text-muted">-</span>
    {% endif %}
</td>
<td>
    {% set consumption_amount = customer.get_follow_up_consumption_amount() %}
    {% if consumption_amount %}
        <span class="text-success fw-bold">¥{{ "%.2f"|format(consumption_amount) }}</span>
    {% else %}
        <span class="text-muted">-</span>
    {% endif %}
</td>
```

## 示例数据

| 卡号    | 登记时间 | 跟进时间 | 到院时间 | 消费时间 | 跟进到院状态 | 跟进消费金额 |
|---------|----------|----------|----------|----------|-------------|-------------|
| TEST001 | 07-23    | 无       | -        | -        | -           | -           |
| TEST002 | 07-24    | 07-25    | -        | -        | -           | -           |
| TEST003 | 07-22    | 07-24    | 07-26    | 07-27    | 已到院      | ¥1500.50    |

### 时间逻辑验证：
- **TEST001**: 无跟进记录 → 状态空，金额空
- **TEST002**: 登记(07-24) → 跟进(07-25) → 无到院 → 状态空，金额空
- **TEST003**: 登记(07-22) → 跟进(07-24) → 到院(07-26) → 消费(07-27) → 已到院，¥1500.50

## 状态说明

- **已到院**：跟进后客户已到院
- **空白(-)** ：客户未跟进或跟进后未到院

## 金额说明

- **具体金额**：跟进后有消费且金额>0
- **空白(-)** ：客户未跟进或跟进后无消费或消费金额<=0

## 测试验证

### 创建测试数据
```bash
python test_follow_up_status.py
```

### 检查测试数据
```bash
python check_test_data.py
```

### 功能演示
```bash
python demo_follow_up_status.py
```

## Web界面访问

访问客户列表页面：`http://127.0.0.1:5000/registration/customers`

新增的两个字段已添加到客户列表表格中，可以直观地查看每个客户的跟进状态和消费情况。

## 技术特点

1. **数据库查询优化**：使用SQLAlchemy的聚合函数进行高效的金额统计
2. **状态逻辑清晰**：通过时间比较准确判断客户状态
3. **界面友好**：使用Bootstrap徽章和颜色区分不同状态
4. **数据准确**：严格按照登记时间作为基准进行统计

## 注意事项

1. **时间逻辑**：跟进时间必须在登记时间之后
2. **跟进消费金额**：只统计跟进时间**之后**的消费
3. **到院状态判断**：基于来院日期是否在跟进时间**之后**
4. **金额显示**：所有金额显示保留两位小数
5. **状态显示**：使用不同颜色的徽章便于识别
6. **数据完整性**：确保登记 → 跟进 → 到院 → 消费的时间顺序正确
