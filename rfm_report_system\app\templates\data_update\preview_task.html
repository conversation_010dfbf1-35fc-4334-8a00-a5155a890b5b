{% extends "base.html" %}

{% block title %}预览数据更新任务{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题和导航 -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('data_update.index') }}">数据更新</a></li>
                    <li class="breadcrumb-item"><a href="javascript:history.back()">创建任务</a></li>
                    <li class="breadcrumb-item active">预览任务</li>
                </ol>
            </nav>
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-eye me-2 text-primary"></i>预览数据更新任务</h2>
                    <p class="text-muted">
                        数据库: {{ task_info.db_name }} | 
                        {% if preview_info.operation_type == 'create_table' %}
                        新表: {{ preview_info.new_table_name }}
                        {% else %}
                        表: {{ task_info.table_name }}
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作摘要 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle me-2"></i>操作摘要</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                {% if preview_info.operation_type == 'append' %}
                                <i class="fas fa-plus-circle fa-3x text-success mb-2"></i>
                                <h6>追加数据</h6>
                                {% elif preview_info.operation_type == 'update' %}
                                <i class="fas fa-sync-alt fa-3x text-warning mb-2"></i>
                                <h6>更新数据</h6>
                                {% elif preview_info.operation_type == 'create_table' %}
                                <i class="fas fa-table fa-3x text-info mb-2"></i>
                                <h6>创建新表</h6>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-9">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="border-start border-primary border-3 ps-3">
                                        <h6 class="text-primary mb-1">Excel数据</h6>
                                        <p class="mb-0">{{ preview_info.preview.excel_rows }} 行</p>
                                        <p class="small text-muted">{{ preview_info.preview.excel_columns }} 列</p>
                                    </div>
                                </div>
                                {% if preview_info.operation_type != 'create_table' %}
                                <div class="col-md-4">
                                    <div class="border-start border-info border-3 ps-3">
                                        <h6 class="text-info mb-1">目标表</h6>
                                        <p class="mb-0">{{ preview_info.preview.target_table.current_rows }} 行</p>
                                        <p class="small text-muted">{{ preview_info.preview.target_table.columns|length }} 列</p>
                                    </div>
                                </div>
                                {% endif %}
                                <div class="col-md-4">
                                    <div class="border-start border-success border-3 ps-3">
                                        <h6 class="text-success mb-1">操作结果</h6>
                                        {% if preview_info.operation_type == 'append' %}
                                        <p class="mb-0">新增 {{ preview_info.preview.excel_rows }} 行</p>
                                        <p class="small text-muted">总计 {{ preview_info.preview.target_table.current_rows + preview_info.preview.excel_rows }} 行</p>
                                        {% elif preview_info.operation_type == 'update' %}
                                        <p class="mb-0">替换为 {{ preview_info.preview.excel_rows }} 行</p>
                                        <p class="small text-muted">删除 {{ preview_info.preview.target_table.current_rows }} 行</p>
                                        {% elif preview_info.operation_type == 'create_table' %}
                                        <p class="mb-0">创建新表</p>
                                        <p class="small text-muted">{{ preview_info.preview.excel_rows }} 行数据</p>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 列名验证结果 -->
    {% if preview_info.operation_type != 'create_table' %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-check-circle me-2"></i>列名验证</h5>
                </div>
                <div class="card-body">
                    {% if preview_info.preview.column_validation.is_match %}
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>验证通过</strong> - Excel文件的列名与数据库表完全匹配
                    </div>
                    {% else %}
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>验证失败</strong> - {{ preview_info.preview.column_validation.message }}
                    </div>
                    {% endif %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6>数据库表列名</h6>
                            <div class="border rounded p-2 bg-light">
                                {% for col in preview_info.preview.column_validation.comparison.db_columns %}
                                <span class="badge bg-primary me-1 mb-1">{{ col }}</span>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>Excel文件列名</h6>
                            <div class="border rounded p-2 bg-light">
                                {% for col in preview_info.preview.column_validation.comparison.excel_columns %}
                                <span class="badge bg-info me-1 mb-1">{{ col }}</span>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 数据类型推断（仅创建新表时显示） -->
    {% if preview_info.operation_type == 'create_table' %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-cogs me-2"></i>数据类型推断</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead class="table-dark">
                                <tr>
                                    <th>列名</th>
                                    <th>推断类型</th>
                                    <th>示例数据</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for col_name in preview_info.preview.excel_column_names %}
                                <tr>
                                    <td><code>{{ col_name }}</code></td>
                                    <td><span class="badge bg-info">{{ preview_info.preview.inferred_types[col_name] }}</span></td>
                                    <td class="small text-muted">
                                        {% for row in preview_info.preview.sample_data[:3] %}
                                        {{ row[col_name] if row[col_name] is not none else 'NULL' }}{% if not loop.last %}, {% endif %}
                                        {% endfor %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 数据预览 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-table me-2"></i>数据预览（前10行）</h5>
                </div>
                <div class="card-body">
                    {% if preview_info.preview.sample_data %}
                    <div class="table-responsive">
                        <table class="table table-sm table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    {% for col in preview_info.preview.excel_column_names %}
                                    <th>{{ col }}</th>
                                    {% endfor %}
                                </tr>
                            </thead>
                            <tbody>
                                {% for row in preview_info.preview.sample_data %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    {% for col in preview_info.preview.excel_column_names %}
                                    <td>{{ row[col] if row[col] is not none else '' }}</td>
                                    {% endfor %}
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>Excel文件中没有数据行
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 操作确认 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-exclamation-triangle me-2"></i>操作确认</h5>
                </div>
                <div class="card-body">
                    {% if preview_info.operation_type == 'update' %}
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>高风险操作警告</h6>
                        <p class="mb-2">此操作将：</p>
                        <ul class="mb-2">
                            <li>清空表 <strong>{{ task_info.table_name }}</strong> 中的所有现有数据（共 {{ preview_info.preview.target_table.current_rows }} 条记录）</li>
                            <li>导入新数据（共 {{ preview_info.preview.excel_rows }} 条记录）</li>
                            <li>此过程不可逆转，除非使用撤销功能</li>
                        </ul>
                        <p class="mb-0">请确认您已备份重要数据，并理解此操作的后果。</p>
                    </div>
                    {% endif %}

                    {% if preview_info.preview.column_validation is defined and not preview_info.preview.column_validation.is_match %}
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-times-circle me-2"></i>无法执行操作</h6>
                        <p class="mb-0">由于列名验证失败，无法执行此操作。请修正Excel文件后重新上传。</p>
                    </div>
                    {% endif %}

                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-secondary" onclick="cancelTask()">
                            <i class="fas fa-times me-2"></i>取消任务
                        </button>
                        
                        <div>
                            {% if preview_info.preview.column_validation is not defined or preview_info.preview.column_validation.is_match %}
                            {% if preview_info.operation_type == 'update' %}
                            <button type="button" class="btn btn-warning me-2" onclick="showConfirmDialog()">
                                <i class="fas fa-exclamation-triangle me-2"></i>确认并执行
                            </button>
                            {% else %}
                            <button type="button" class="btn btn-success" onclick="executeTask()">
                                <i class="fas fa-check me-2"></i>确认执行
                            </button>
                            {% endif %}
                            {% else %}
                            <button type="button" class="btn btn-success" disabled>
                                <i class="fas fa-ban me-2"></i>无法执行
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 高风险操作确认对话框 -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle me-2"></i>高风险操作确认
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>此操作将清空表中的所有数据，此过程不可逆转。</p>
                <p>请输入 <strong>CONFIRM</strong> 以继续操作：</p>
                <input type="text" class="form-control" id="confirmInput" placeholder="输入 CONFIRM">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-warning" id="confirmExecuteBtn" disabled onclick="executeTask()">
                    <i class="fas fa-check me-2"></i>确认执行
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 显示确认对话框
function showConfirmDialog() {
    $('#confirmModal').modal('show');
    $('#confirmInput').val('').focus();
}

// 监听确认输入
$('#confirmInput').on('input', function() {
    const value = $(this).val();
    $('#confirmExecuteBtn').prop('disabled', value !== 'CONFIRM');
});

// 执行任务
function executeTask() {
    $('#confirmModal').modal('hide');

    // 显示简单的执行进度
    let executeDialog = Swal.fire({
        title: '正在执行数据操作...',
        html: '<div class="text-center"><i class="fas fa-cog fa-spin fa-2x text-success mb-3"></i><br><span id="executeStatus">正在准备执行...</span><br><small class="text-muted mt-2">请勿关闭页面，操作正在进行中...</small></div>',
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false
    });

    // 更新执行状态的函数
    function updateExecuteStatus(message) {
        const statusElement = document.getElementById('executeStatus');
        if (statusElement) {
            statusElement.textContent = message;
        }
    }

    // 模拟执行进度
    setTimeout(() => updateExecuteStatus('正在验证数据...'), 300);
    setTimeout(() => updateExecuteStatus('正在连接数据库...'), 800);
    setTimeout(() => updateExecuteStatus('正在执行数据操作...'), 1200);

    $.ajax({
        url: '{{ url_for("data_update.execute_task") }}',
        method: 'POST',
        success: function(response) {
            updateExecuteStatus('操作完成！');

            setTimeout(() => {
                Swal.close();

                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: '操作成功！',
                        html: `
                            <div class="alert alert-success">
                                <strong>${response.message}</strong>
                            </div>
                            <div class="text-start">
                                <small class="text-muted">
                                    操作时间: ${new Date().toLocaleString()}<br>
                                    ${response.result ? '影响行数: ' + (response.result.rows_inserted || response.result.rows_after || 0) + ' 行' : ''}
                                </small>
                            </div>
                        `,
                        showCancelButton: true,
                        confirmButtonText: '返回首页',
                        cancelButtonText: '查看详情'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.href = '{{ url_for("data_update.index") }}';
                        } else {
                            showResultDetails(response.result);
                        }
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: '操作失败',
                        text: response.message
                    });
                }
            }, 800);
        },
        error: function() {
            Swal.close();
            Swal.fire({
                icon: 'error',
                title: '请求失败',
                text: '操作执行失败，请稍后重试'
            });
        }
    });
}

// 取消任务
function cancelTask() {
    Swal.fire({
        title: '确认取消？',
        text: '取消后将清理临时文件并返回首页',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: '确认取消',
        cancelButtonText: '继续操作'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '{{ url_for("data_update.cancel_task") }}',
                method: 'POST',
                success: function(response) {
                    if (response.success) {
                        window.location.href = '{{ url_for("data_update.index") }}';
                    } else {
                        Swal.fire('错误', response.message, 'error');
                    }
                },
                error: function() {
                    Swal.fire('错误', '取消任务失败', 'error');
                }
            });
        }
    });
}

// 显示结果详情
function showResultDetails(result) {
    let content = '<div class="table-responsive"><table class="table table-sm">';
    
    for (const [key, value] of Object.entries(result)) {
        if (key !== 'timestamp') {
            content += `<tr><td><strong>${key}:</strong></td><td>${value}</td></tr>`;
        }
    }
    
    content += '</table></div>';
    
    Swal.fire({
        title: '操作详情',
        html: content,
        width: '600px',
        confirmButtonText: '返回首页'
    }).then(() => {
        window.location.href = '{{ url_for("data_update.index") }}';
    });
}
</script>
{% endblock %}
