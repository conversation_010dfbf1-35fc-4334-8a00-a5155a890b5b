# -*- coding: utf-8 -*-
"""
部门管理相关表单
"""

from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, BooleanField, SubmitField, SelectField
from wtforms.validators import DataRequired, Length, Optional, ValidationError
from app.models import Department

class DepartmentForm(FlaskForm):
    """部门表单（创建/编辑）"""
    name = StringField('部门名称', validators=[
        DataRequired(message='部门名称不能为空'),
        Length(min=2, max=100, message='部门名称长度必须在2-100个字符之间')
    ])
    description = TextAreaField('部门描述', validators=[
        Optional(),
        Length(max=500, message='部门描述长度不能超过500个字符')
    ])
    is_active = BooleanField('激活状态', default=True)
    submit = SubmitField('保存')
    
    def __init__(self, department=None, *args, **kwargs):
        super(DepartmentForm, self).__init__(*args, **kwargs)
        self.department = department
    
    def validate_name(self, name):
        """验证部门名称唯一性"""
        department = Department.query.filter_by(name=name.data).first()
        if department and (not self.department or department.id != self.department.id):
            raise ValidationError('部门名称已存在，请选择其他名称。')

class DepartmentSearchForm(FlaskForm):
    """部门搜索表单"""
    query = StringField('搜索', validators=[Optional()])
    is_active = SelectField('状态筛选', choices=[
        ('', '全部状态'),
        ('1', '激活'),
        ('0', '停用')
    ], validators=[Optional()])
    submit = SubmitField('搜索')