#!/usr/bin/env python3
"""
修复渠道管理性能问题的脚本
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from sqlalchemy import text

def optimize_channel_performance():
    """优化渠道管理性能"""
    print("=" * 50)
    print("优化渠道管理性能")
    print("=" * 50)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 添加数据库索引
            print("1. 添加数据库索引...")
            
            # 检查并添加渠道表索引
            indexes_to_add = [
                ("idx_channels_category_name", "channels", "channel_category, channel_name"),
                ("idx_channels_active_created", "channels", "is_active, created_time DESC"),
                ("idx_channels_created_desc", "channels", "created_time DESC"),
                ("idx_user_accounts_role_active", "user_accounts", "role_id, is_active"),
                ("idx_role_permissions_role", "role_permissions", "role_id"),
                ("idx_permissions_code", "permissions", "permission_code")
            ]
            
            for index_name, table_name, columns in indexes_to_add:
                try:
                    # 检查索引是否已存在
                    index_exists = db.session.execute(text(f"""
                        SELECT COUNT(*) FROM information_schema.statistics 
                        WHERE table_schema = DATABASE() 
                        AND table_name = '{table_name}' 
                        AND index_name = '{index_name}'
                    """)).scalar()
                    
                    if index_exists == 0:
                        db.session.execute(text(f"""
                            CREATE INDEX {index_name} ON {table_name} ({columns})
                        """))
                        print(f"  ✓ 添加索引: {index_name}")
                    else:
                        print(f"  ✓ 索引已存在: {index_name}")
                        
                except Exception as e:
                    print(f"  ❌ 添加索引失败 {index_name}: {e}")
            
            db.session.commit()
            
            # 2. 优化权限检查
            print("\n2. 优化权限检查...")
            
            # 创建权限缓存表（如果不存在）
            try:
                db.session.execute(text("""
                    CREATE TABLE IF NOT EXISTS user_permission_cache (
                        user_id INT NOT NULL,
                        permission_code VARCHAR(100) NOT NULL,
                        has_permission BOOLEAN NOT NULL,
                        cached_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        PRIMARY KEY (user_id, permission_code),
                        FOREIGN KEY (user_id) REFERENCES user_accounts(id) ON DELETE CASCADE,
                        INDEX idx_user_permission_cache_user (user_id),
                        INDEX idx_user_permission_cache_code (permission_code)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户权限缓存表'
                """))
                print("  ✓ 权限缓存表创建/检查完成")
            except Exception as e:
                print(f"  ❌ 权限缓存表创建失败: {e}")
            
            # 3. 预热权限缓存
            print("\n3. 预热权限缓存...")
            
            try:
                # 清空旧缓存
                db.session.execute(text("DELETE FROM user_permission_cache"))
                
                # 获取所有活跃用户和权限
                users = db.session.execute(text("""
                    SELECT u.id, u.role_id 
                    FROM user_accounts u 
                    WHERE u.is_active = 1
                """)).fetchall()
                
                permissions = db.session.execute(text("""
                    SELECT p.permission_code 
                    FROM permissions p
                """)).fetchall()
                
                cache_entries = 0
                for user_id, role_id in users:
                    for (permission_code,) in permissions:
                        # 检查用户是否有此权限
                        has_permission = db.session.execute(text("""
                            SELECT COUNT(*) > 0 
                            FROM role_permissions rp 
                            JOIN permissions p ON rp.permission_id = p.id 
                            WHERE rp.role_id = :role_id 
                            AND p.permission_code = :permission_code
                        """), {
                            'role_id': role_id,
                            'permission_code': permission_code
                        }).scalar()
                        
                        # 插入缓存
                        db.session.execute(text("""
                            INSERT INTO user_permission_cache (user_id, permission_code, has_permission)
                            VALUES (:user_id, :permission_code, :has_permission)
                        """), {
                            'user_id': user_id,
                            'permission_code': permission_code,
                            'has_permission': bool(has_permission)
                        })
                        cache_entries += 1
                
                db.session.commit()
                print(f"  ✓ 权限缓存预热完成，缓存了 {cache_entries} 条记录")
                
            except Exception as e:
                print(f"  ❌ 权限缓存预热失败: {e}")
                db.session.rollback()
            
            # 4. 优化渠道查询
            print("\n4. 优化渠道查询...")
            
            try:
                # 分析渠道表
                db.session.execute(text("ANALYZE TABLE channels"))
                print("  ✓ 渠道表分析完成")
                
                # 检查渠道数据分布
                category_count = db.session.execute(text("""
                    SELECT COUNT(DISTINCT channel_category) FROM channels
                """)).scalar()
                
                total_channels = db.session.execute(text("""
                    SELECT COUNT(*) FROM channels
                """)).scalar()
                
                print(f"  ✓ 渠道统计: {total_channels} 个渠道，{category_count} 个分类")
                
                if total_channels > 1000:
                    print("  ⚠️ 渠道数据量较大，建议考虑分页优化")
                
            except Exception as e:
                print(f"  ❌ 渠道查询优化失败: {e}")
            
            # 5. 创建优化后的权限检查函数
            print("\n5. 创建优化的权限检查函数...")
            
            optimized_permission_code = '''
def has_permission_cached(self, permission_code):
    """使用缓存的权限检查方法"""
    from app import db
    from sqlalchemy import text
    
    try:
        # 首先尝试从缓存获取
        cached_result = db.session.execute(text("""
            SELECT has_permission FROM user_permission_cache 
            WHERE user_id = :user_id AND permission_code = :permission_code
        """), {
            'user_id': self.id,
            'permission_code': permission_code
        }).fetchone()
        
        if cached_result is not None:
            return bool(cached_result[0])
        
        # 缓存未命中，执行原始查询
        result = db.session.query(
            db.exists().where(
                db.and_(
                    RolePermission.role_id == self.role_id,
                    Permission.id == RolePermission.permission_id,
                    Permission.permission_code == permission_code
                )
            )
        ).scalar()
        
        # 更新缓存
        db.session.execute(text("""
            INSERT INTO user_permission_cache (user_id, permission_code, has_permission)
            VALUES (:user_id, :permission_code, :has_permission)
            ON DUPLICATE KEY UPDATE 
            has_permission = VALUES(has_permission),
            cached_at = CURRENT_TIMESTAMP
        """), {
            'user_id': self.id,
            'permission_code': permission_code,
            'has_permission': bool(result)
        })
        
        return bool(result)
        
    except Exception as e:
        # 如果缓存查询失败，回退到原始方法
        return self.has_permission_original(permission_code)
'''
            
            print("  ✓ 优化的权限检查函数代码已准备")
            
            print("\n" + "=" * 50)
            print("渠道管理性能优化完成！")
            print("=" * 50)
            
            print("优化内容:")
            print("1. ✓ 添加了数据库索引以提高查询性能")
            print("2. ✓ 创建了权限缓存表")
            print("3. ✓ 预热了权限缓存数据")
            print("4. ✓ 优化了渠道查询")
            print("5. ✓ 准备了优化的权限检查函数")
            
            print("\n建议操作:")
            print("1. 重新启动系统: python start.py")
            print("2. 测试渠道管理页面的分页功能")
            print("3. 如果还有性能问题，可以考虑实施查询缓存")
            
            return True
            
        except Exception as e:
            print(f"❌ 优化过程中出现错误: {e}")
            db.session.rollback()
            raise

if __name__ == '__main__':
    success = optimize_channel_performance()
    sys.exit(0 if success else 1)