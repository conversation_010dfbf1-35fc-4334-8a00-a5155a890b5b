# 部门管理功能使用指南

## 功能概述

部门管理功能允许系统管理员创建和维护组织架构，用户在创建时需要选择所属部门，权限控制和数据查看都基于部门进行管理。

## 主要特性

### 1. 部门层级管理
- 支持多级部门结构（父部门-子部门）
- 部门名称和代码唯一性验证
- 灵活的排序功能

### 2. 用户部门关联
- 新增用户时必须选择所属部门
- 部门信息与权限控制关联
- 数据查看权限基于部门进行过滤

### 3. 权限控制优化
- 部门主管只能查看本部门员工的数据
- 基于部门的数据权限过滤
- 防止跨部门数据泄露

## 使用流程

### 1. 部门管理

#### 访问部门管理
1. 使用管理员账号登录系统
2. 进入"系统管理" -> "部门管理"
3. 查看现有部门列表

#### 创建新部门
1. 点击"新增部门"按钮
2. 填写部门信息：
   - **部门名称**：如"技术部"、"销售部"
   - **部门代码**：如"TECH"、"SALES"（建议使用英文大写）
   - **上级部门**：可选，用于创建层级结构
   - **排序顺序**：数字越小排序越靠前
   - **部门描述**：可选，描述部门职能
3. 点击"创建部门"保存

#### 编辑部门
1. 在部门列表中点击"编辑"按钮
2. 修改部门信息
3. 可以启用/停用部门
4. 注意：不能将自己设为上级部门，不能形成循环引用

#### 删除部门
1. 只有没有用户和子部门的部门才能删除
2. 点击"删除"按钮确认删除
3. 删除操作不可撤销

### 2. 用户管理

#### 创建用户（新流程）
1. 进入"系统管理" -> "用户管理"
2. 点击"新增用户"
3. 填写用户信息：
   - 用户名、真实姓名、密码
   - **所属部门**：从下拉列表中选择（必填）
   - 用户角色
4. 保存用户

#### 部门显示
- 用户列表中显示用户所属部门名称
- 支持按部门筛选用户
- 部门信息在用户详情中显示

### 3. 权限控制

#### 数据查看权限
- **系统管理员**：查看所有数据
- **经营院长**：查看所有数据
- **部门主管**：只能查看本部门员工的数据
- **网络咨询**：只能查看自己的数据
- **现场咨询**：只能查看分配给自己的客户

#### 权限验证
系统会自动根据用户所属部门进行权限过滤，确保数据安全。

## 数据迁移

### 自动迁移
系统提供了自动迁移脚本，将原有的部门字符串字段迁移为部门ID关联：

```bash
# 运行迁移脚本
python migrate_departments.py
```

### 迁移过程
1. 创建部门表
2. 分析现有用户的部门数据
3. 创建对应的部门记录
4. 更新用户表结构
5. 迁移数据关联
6. 备份原有字段

### 默认部门
如果系统中没有现有部门数据，会自动创建以下默认部门：
- 管理部 (ADMIN)
- 网络咨询部 (NETWORK)
- 现场咨询部 (FIELD)
- 技术部 (TECH)
- 市场部 (MARKET)
- 客服部 (SERVICE)

## 注意事项

### 1. 部门创建
- 部门名称和代码必须唯一
- 部门代码建议使用英文大写字母
- 删除部门前需要先处理该部门下的用户

### 2. 用户管理
- 新用户必须选择所属部门
- 用户的数据查看权限基于部门进行控制
- 部门主管只能管理本部门的数据

### 3. 权限控制
- 部门停用后不影响已有用户，但不能选择该部门
- 权限过滤基于部门ID进行，确保数据安全
- 跨部门数据访问需要相应权限

### 4. 系统维护
- 定期检查部门结构的合理性
- 及时处理离职员工的部门归属
- 保持部门代码的规范性

## 故障排除

### 常见问题

#### 1. 用户创建失败
**问题**：提示"请选择所属部门"
**解决**：确保已创建部门，并在用户创建时选择部门

#### 2. 权限异常
**问题**：用户无法查看应有的数据
**解决**：检查用户所属部门和角色权限配置

#### 3. 部门删除失败
**问题**：提示"该部门下还有用户"
**解决**：先将部门下的用户转移到其他部门，再删除部门

#### 4. 迁移失败
**问题**：数据迁移过程中出错
**解决**：检查数据库连接，确保有足够权限，查看错误日志

### 测试功能
运行测试脚本验证部门管理功能：

```bash
# 测试部门管理功能
python test_department_system.py
```

## 技术实现

### 数据库结构
```sql
-- 部门表
CREATE TABLE departments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    department_name VARCHAR(100) UNIQUE NOT NULL,
    department_code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    parent_id INT,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    FOREIGN KEY (parent_id) REFERENCES departments(id),
    FOREIGN KEY (created_by) REFERENCES user_accounts(id)
);

-- 用户表更新
ALTER TABLE user_accounts 
ADD COLUMN department_id INT NOT NULL,
ADD FOREIGN KEY (department_id) REFERENCES departments(id);
```

### API接口
- `GET /admin/departments` - 部门列表
- `POST /admin/departments/create` - 创建部门
- `GET /admin/departments/<id>/edit` - 编辑部门
- `POST /admin/departments/<id>/delete` - 删除部门

### 权限验证
```python
# 数据权限过滤示例
@classmethod
def filter_by_permission(cls, query, user):
    if user.role.role_code == 'DEPARTMENT_MANAGER':
        # 部门主管只能看本部门数据
        dept_users = User.query.filter_by(department_id=user.department_id).all()
        user_ids = [u.id for u in dept_users]
        return query.filter(cls.registrar_id.in_(user_ids))
    # ... 其他权限逻辑
```

## 更新日志

### v1.0.0 (2024-01-01)
- 新增部门管理功能
- 实现部门层级结构
- 更新用户创建流程
- 优化权限控制机制
- 提供数据迁移工具

---

如有问题或建议，请联系系统管理员。