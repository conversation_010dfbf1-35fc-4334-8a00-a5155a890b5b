{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">编辑用户 - {{ user.username }}</h1>
    <a href="{{ url_for('basic_mgmt.users') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>返回列表
    </a>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.username.label(class="form-label") }}
                                {{ form.username(class="form-control") }}
                                {% if form.username.errors %}
                                    <div class="text-danger">
                                        {% for error in form.username.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.real_name.label(class="form-label") }}
                                {{ form.real_name(class="form-control") }}
                                {% if form.real_name.errors %}
                                    <div class="text-danger">
                                        {% for error in form.real_name.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.role.label(class="form-label") }}
                                {{ form.role(class="form-select") }}
                                {% if form.role.errors %}
                                    <div class="text-danger">
                                        {% for error in form.role.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.department_id.label(class="form-label") }}
                                {{ form.department_id(class="form-select") }}
                                {% if form.department_id.errors %}
                                    <div class="text-danger">
                                        {% for error in form.department_id.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.simple_code.label(class="form-label") }}
                                {{ form.simple_code(class="form-control") }}
                                <small class="form-text text-muted">用于快速搜索的简码</small>
                                {% if form.simple_code.errors %}
                                    <div class="text-danger">
                                        {% for error in form.simple_code.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.password.label(class="form-label") }}
                                {{ form.password(class="form-control", placeholder="留空保持原密码不变") }}
                                <small class="form-text text-muted">留空表示不修改密码</small>
                                {% if form.password.errors %}
                                    <div class="text-danger">
                                        {% for error in form.password.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_active(class="form-check-input") }}
                            {{ form.is_active.label(class="form-check-label") }}
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('basic_mgmt.users') }}" class="btn btn-secondary me-md-2">取消</a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>用户信息
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <strong>创建时间:</strong><br>
                        {{ user.created_at.strftime('%Y-%m-%d %H:%M:%S') }}
                    </li>
                    <li class="mb-2">
                        <strong>最后登录:</strong><br>
                        {% if user.last_login %}
                            {{ user.last_login.strftime('%Y-%m-%d %H:%M:%S') }}
                        {% else %}
                            从未登录
                        {% endif %}
                    </li>
                    <li class="mb-2">
                        <strong>当前状态:</strong><br>
                        {% if user.is_active %}
                            <span class="badge bg-success">激活</span>
                        {% else %}
                            <span class="badge bg-secondary">停用</span>
                        {% endif %}
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}