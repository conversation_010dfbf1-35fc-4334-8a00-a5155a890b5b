"""
客户相关数据模型
"""
from datetime import datetime, date
from app import db

class Channel(db.Model):
    """渠道管理表"""
    __tablename__ = 'channels'
    
    id = db.Column(db.Integer, primary_key=True)
    channel_category = db.Column(db.String(100), nullable=False, comment='渠道分类')
    channel_name = db.Column(db.String(200), nullable=False, comment='渠道名称')
    is_active = db.Column(db.<PERSON>, default=True, comment='是否启用')
    created_time = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_time = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    created_by = db.Column(db.Integer, db.ForeignKey('user_accounts.id'), comment='创建人ID')
    
    # 关系
    creator = db.relationship('User', backref='created_channels', lazy=True)
    
    # 索引
    __table_args__ = (
        db.Index('idx_channel_category', 'channel_category'),
        db.Index('idx_channel_name', 'channel_name'),
    )
    
    def __repr__(self):
        return f'<Channel {self.channel_name}>'

class CustomerRegistration(db.Model):
    """客户登记表"""
    __tablename__ = 'customer_registrations'
    
    id = db.Column(db.Integer, primary_key=True)
    card_number = db.Column(db.String(10), nullable=False, comment='卡号')
    assigned_consultant_id = db.Column(db.Integer, db.ForeignKey('user_accounts.id'), nullable=False, comment='所属现场顾问ID')
    activation_channel_id = db.Column(db.Integer, db.ForeignKey('channels.id'), nullable=False, comment='激活渠道ID')
    consultation_content = db.Column(db.Text, nullable=False, comment='咨询内容(最大500字)')
    last_visit_date = db.Column(db.Date, nullable=False, comment='客户最近来院时间')
    registration_time = db.Column(db.DateTime, default=datetime.utcnow, comment='登记时间')
    registrar_id = db.Column(db.Integer, db.ForeignKey('user_accounts.id'), nullable=False, comment='登记人ID')
    is_active = db.Column(db.Boolean, default=True, comment='是否有效')
    updated_time = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    # 关系
    assigned_consultant = db.relationship('User', foreign_keys=[assigned_consultant_id], backref='assigned_customers', lazy=True)
    registrar = db.relationship('User', foreign_keys=[registrar_id], backref='registered_customers', lazy=True)
    activation_channel = db.relationship('Channel', backref='customer_registrations', lazy=True)
    follow_ups = db.relationship('FollowUpRecord', backref='customer_registration', lazy='dynamic', cascade='all, delete-orphan')
    
    # 索引
    __table_args__ = (
        db.Index('idx_card_number', 'card_number'),
        db.Index('idx_assigned_consultant', 'assigned_consultant_id'),
        db.Index('idx_activation_channel', 'activation_channel_id'),
        db.Index('idx_registrar', 'registrar_id'),
        db.Index('idx_registration_time', 'registration_time'),
        db.Index('idx_last_visit_date', 'last_visit_date'),
        # 卡号长度检查约束
        db.CheckConstraint('LENGTH(card_number) <= 10 AND card_number REGEXP "^[0-9]+$"', name='chk_card_number_length'),
        # 咨询内容长度约束
        db.CheckConstraint('CHAR_LENGTH(consultation_content) <= 500', name='chk_consultation_content_length'),
    )
    
    def get_latest_follow_up(self):
        """获取最新跟进记录"""
        return self.follow_ups.filter_by(is_active=True).order_by(FollowUpRecord.follow_up_time.desc()).first()
    
    def get_follow_up_count(self):
        """获取跟进次数"""
        return self.follow_ups.filter_by(is_active=True).count()
    
    def has_recent_consumption(self, days=30):
        """检查是否有近期消费记录"""
        from datetime import timedelta
        cutoff_date = date.today() - timedelta(days=days)
        return CustomerConsumption.query.filter(
            CustomerConsumption.card_number == self.card_number,
            CustomerConsumption.visit_date >= cutoff_date
        ).first() is not None
    
    @classmethod
    def filter_by_permission(cls, query, user):
        """根据用户权限过滤数据"""
        if user.role.role_code == 'ADMIN':
            return query
        elif user.role.role_code == 'GENERAL_MANAGER':
            return query
        elif user.role.role_code == 'DEPARTMENT_MANAGER':
            # 部门主管只能看本部门数据
            from app.models.user import User
            dept_users = User.query.filter_by(department_id=user.department_id).all()
            user_ids = [u.id for u in dept_users]
            return query.filter(cls.registrar_id.in_(user_ids))
        elif user.role.role_code == 'NETWORK_CONSULTANT':
            # 网络咨询只能看自己的数据
            return query.filter(cls.registrar_id == user.id)
        elif user.role.role_code == 'FIELD_CONSULTANT':
            # 现场咨询看分配给自己的客户
            return query.filter(cls.assigned_consultant_id == user.id)
        else:
            return query.filter(False)
    
    def __repr__(self):
        return f'<CustomerRegistration {self.card_number}>'

class FollowUpRecord(db.Model):
    """跟进记录表"""
    __tablename__ = 'follow_up_records'
    
    id = db.Column(db.Integer, primary_key=True)
    customer_registration_id = db.Column(db.Integer, db.ForeignKey('customer_registrations.id'), nullable=False, comment='客户登记ID')
    follow_up_content = db.Column(db.Text, nullable=False, comment='咨询跟进情况(最大500字)')
    follow_up_time = db.Column(db.DateTime, default=datetime.utcnow, comment='咨询跟进时间')
    consultant_id = db.Column(db.Integer, db.ForeignKey('user_accounts.id'), nullable=False, comment='跟进顾问ID')
    is_active = db.Column(db.Boolean, default=True, comment='是否有效')
    
    # 关系
    consultant = db.relationship('User', backref='follow_up_records', lazy=True)
    
    # 索引
    __table_args__ = (
        db.Index('idx_customer_registration', 'customer_registration_id'),
        db.Index('idx_consultant', 'consultant_id'),
        db.Index('idx_follow_up_time', 'follow_up_time'),
        # 跟进内容长度约束
        db.CheckConstraint('CHAR_LENGTH(follow_up_content) <= 500', name='chk_follow_up_content_length'),
    )
    
    def __repr__(self):
        return f'<FollowUpRecord {self.id}>'

class ConsultantGroupMapping(db.Model):
    """现场小组映射表"""
    __tablename__ = 'consultant_group_mapping'
    
    id = db.Column(db.Integer, primary_key=True)
    consultant_id = db.Column(db.Integer, db.ForeignKey('user_accounts.id'), nullable=False, comment='现场咨询员ID')
    group_name = db.Column(db.String(100), nullable=False, comment='现场小组名称')
    is_active = db.Column(db.Boolean, default=True, comment='是否有效')
    created_time = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_time = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    created_by = db.Column(db.Integer, db.ForeignKey('user_accounts.id'), comment='创建人ID')
    
    # 关系
    consultant = db.relationship('User', foreign_keys=[consultant_id], backref='group_mappings', lazy=True)
    creator = db.relationship('User', foreign_keys=[created_by], backref='created_mappings', lazy=True)
    
    # 索引
    __table_args__ = (
        db.Index('idx_consultant', 'consultant_id'),
        db.Index('idx_group_name', 'group_name'),
    )
    
    @classmethod
    def get_consultant_group(cls, consultant_id):
        """获取咨询员所属小组"""
        mapping = cls.query.filter_by(consultant_id=consultant_id, is_active=True).first()
        return mapping.group_name if mapping else None
    
    def __repr__(self):
        return f'<ConsultantGroupMapping {self.group_name}>'

class CustomerConsumption(db.Model):
    """客户消费记录表"""
    __tablename__ = 'customer_consumption'
    
    id = db.Column(db.Integer, primary_key=True)
    card_number = db.Column(db.String(10), nullable=False, comment='卡号')
    visit_date = db.Column(db.Date, comment='到院日期')
    consumption_amount = db.Column(db.Numeric(10, 2), default=0.00, comment='消费金额')
    import_time = db.Column(db.DateTime, default=datetime.utcnow, comment='导入时间')
    import_batch = db.Column(db.String(100), comment='导入批次号')
    imported_by = db.Column(db.Integer, db.ForeignKey('user_accounts.id'), comment='导入人ID')
    
    # 关系
    importer = db.relationship('User', backref='imported_consumptions', lazy=True)
    
    # 索引
    __table_args__ = (
        db.Index('idx_card_number', 'card_number'),
        db.Index('idx_visit_date', 'visit_date'),
        db.Index('idx_import_batch', 'import_batch'),
        db.Index('idx_consumption_date_amount', 'visit_date', 'consumption_amount'),
    )
    
    @classmethod
    def get_customer_total_consumption(cls, card_number, start_date=None, end_date=None):
        """获取客户总消费金额"""
        query = cls.query.filter_by(card_number=card_number)
        
        if start_date:
            query = query.filter(cls.visit_date >= start_date)
        if end_date:
            query = query.filter(cls.visit_date <= end_date)
        
        result = query.with_entities(db.func.sum(cls.consumption_amount)).scalar()
        return float(result) if result else 0.0
    
    @classmethod
    def get_customer_visit_count(cls, card_number, start_date=None, end_date=None):
        """获取客户到院次数"""
        query = cls.query.filter(
            cls.card_number == card_number,
            cls.visit_date.isnot(None)
        )
        
        if start_date:
            query = query.filter(cls.visit_date >= start_date)
        if end_date:
            query = query.filter(cls.visit_date <= end_date)
        
        return query.count()
    
    def __repr__(self):
        return f'<CustomerConsumption {self.card_number}>'
