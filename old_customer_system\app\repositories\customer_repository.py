# -*- coding: utf-8 -*-
"""
客户Repository
"""

from typing import List, Optional
from datetime import datetime, date
from sqlalchemy import or_, and_, func
from app.models import Customer, User
from .base_repository import BaseRepository

class CustomerRepository(BaseRepository):
    """客户数据访问类"""
    
    def __init__(self):
        super().__init__(Customer)
    
    def find_by_card_number(self, card_number: str) -> Optional[Customer]:
        """根据卡号查找客户"""
        return Customer.query.filter_by(card_number=card_number).first()
    
    def find_by_online_consultant(self, consultant_id: int) -> List[Customer]:
        """根据网络咨询人员查找客户"""
        return Customer.query.filter_by(online_consultant_id=consultant_id).all()
    
    def find_by_onsite_consultant(self, consultant_id: int) -> List[Customer]:
        """根据现场咨询人员查找客户"""
        return Customer.query.filter_by(onsite_consultant_id=consultant_id).all()
    
    def find_by_department(self, department_id: int) -> List[Customer]:
        """根据部门查找客户（通过网络咨询人员的部门）"""
        return Customer.query.join(
            User, Customer.online_consultant_id == User.id
        ).filter(User.department_id == department_id).all()
    
    def find_by_creator(self, creator_id: int) -> List[Customer]:
        """根据创建者查找客户"""
        return Customer.query.filter_by(created_by=creator_id).all()
    
    def find_by_channel(self, channel_id: int) -> List[Customer]:
        """根据渠道查找客户"""
        return Customer.query.filter_by(channel_id=channel_id).all()
    
    def find_pending_follow_up(self) -> List[Customer]:
        """查找待跟进的客户（没有跟进记录的）"""
        return Customer.query.filter(
            or_(
                Customer.follow_up_note.is_(None),
                Customer.follow_up_note == ''
            )
        ).all()
    
    def find_followed_up(self) -> List[Customer]:
        """查找已跟进的客户"""
        return Customer.query.filter(
            and_(
                Customer.follow_up_note.isnot(None),
                Customer.follow_up_note != ''
            )
        ).all()
    
    def find_by_date_range(self, start_date: date, end_date: date) -> List[Customer]:
        """根据创建日期范围查找客户"""
        return Customer.query.filter(
            Customer.created_at >= start_date,
            Customer.created_at <= end_date
        ).all()
    
    def find_by_visit_date_range(self, start_date: date, end_date: date) -> List[Customer]:
        """根据最近来院日期范围查找客户"""
        return Customer.query.filter(
            Customer.last_visit_date >= start_date,
            Customer.last_visit_date <= end_date
        ).all()
    
    def search_customers(self, query: str) -> List[Customer]:
        """搜索客户（根据卡号、咨询内容）"""
        if not query:
            return []
        
        search_filter = or_(
            Customer.card_number.like(f'%{query}%'),
            Customer.inquiry_content.like(f'%{query}%')
        )
        
        return Customer.query.filter(search_filter).all()
    
    def filter_by_user_permission(self, user: User, customers: List[Customer] = None) -> List[Customer]:
        """根据用户权限过滤客户数据"""
        if customers is None:
            customers = self.get_all()
        
        if user.role == 'admin' or user.role == 'director':
            # 管理员和经营院长可以查看所有客户
            return customers
        elif user.role == 'manager':
            # 部门主管只能查看本部门的客户
            return [c for c in customers if c.online_consultant.department_id == user.department_id]
        elif user.role == 'online_consultant':
            # 网络咨询只能查看自己登记的客户
            return [c for c in customers if c.created_by == user.id]
        elif user.role == 'onsite_consultant':
            # 现场咨询只能查看分配给自己的客户
            return [c for c in customers if c.onsite_consultant_id == user.id]
        else:
            return []
    
    def get_customers_with_permission(self, user: User, page: int = 1, per_page: int = 20):
        """根据用户权限获取客户列表（分页）"""
        query = Customer.query
        
        if user.role == 'admin' or user.role == 'director':
            # 管理员和经营院长可以查看所有客户
            pass
        elif user.role == 'manager':
            # 部门主管只能查看本部门的客户
            query = query.join(User, Customer.online_consultant_id == User.id).filter(
                User.department_id == user.department_id
            )
        elif user.role == 'online_consultant':
            # 网络咨询只能查看自己登记的客户
            query = query.filter(Customer.created_by == user.id)
        elif user.role == 'onsite_consultant':
            # 现场咨询只能查看分配给自己的客户
            query = query.filter(Customer.onsite_consultant_id == user.id)
        else:
            # 其他角色无权限
            query = query.filter(False)
        
        return query.order_by(Customer.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
    
    def update_follow_up(self, customer_id: int, follow_up_note: str, follow_up_by: int) -> bool:
        """更新跟进信息"""
        return self.update(customer_id, {
            'follow_up_note': follow_up_note,
            'follow_up_time': datetime.utcnow(),
            'follow_up_by': follow_up_by
        })
    
    def get_customer_statistics(self, user: User = None) -> dict:
        """获取客户统计信息"""
        query = Customer.query
        
        # 根据用户权限过滤
        if user:
            if user.role == 'manager':
                query = query.join(User, Customer.online_consultant_id == User.id).filter(
                    User.department_id == user.department_id
                )
            elif user.role == 'online_consultant':
                query = query.filter(Customer.created_by == user.id)
            elif user.role == 'onsite_consultant':
                query = query.filter(Customer.onsite_consultant_id == user.id)
        
        total_customers = query.count()
        
        # 已跟进客户数
        followed_up = query.filter(
            and_(
                Customer.follow_up_note.isnot(None),
                Customer.follow_up_note != ''
            )
        ).count()
        
        # 待跟进客户数
        pending_follow_up = total_customers - followed_up
        
        # 本月新增客户数
        current_month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        monthly_new = query.filter(Customer.created_at >= current_month_start).count()
        
        return {
            'total_customers': total_customers,
            'followed_up': followed_up,
            'pending_follow_up': pending_follow_up,
            'monthly_new': monthly_new
        }
    
    def get_recent_customers(self, user: User = None, limit: int = 10) -> List[Customer]:
        """获取最近的客户记录"""
        query = Customer.query
        
        # 根据用户权限过滤
        if user:
            if user.role == 'manager':
                query = query.join(User, Customer.online_consultant_id == User.id).filter(
                    User.department_id == user.department_id
                )
            elif user.role == 'online_consultant':
                query = query.filter(Customer.created_by == user.id)
            elif user.role == 'onsite_consultant':
                query = query.filter(Customer.onsite_consultant_id == user.id)
        
        return query.order_by(Customer.created_at.desc()).limit(limit).all()