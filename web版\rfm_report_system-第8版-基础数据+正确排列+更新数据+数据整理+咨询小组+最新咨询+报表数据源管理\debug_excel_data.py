#!/usr/bin/env python3
"""
调试Excel数据处理
"""

import pandas as pd

def debug_excel_data():
    """调试Excel数据处理"""
    
    try:
        # 读取测试数据
        df = pd.read_excel('complete_test_data.xlsx')
        
        print("数据列名:")
        for i, col in enumerate(df.columns):
            print(f"  {i+1:2d}. {col}")
        
        # 查找季度列
        quarter_columns = [col for col in df.columns if col.endswith('_综合等级') or col.endswith('_细分等级')]
        print(f"\n季度相关列 ({len(quarter_columns)} 个):")
        for col in quarter_columns:
            print(f"  - {col}")
        
        # 提取季度信息
        quarters = []
        for col in quarter_columns:
            if '_综合等级' in col:
                quarter = col.replace('_综合等级', '')
                quarters.append(quarter)
        
        quarters = sorted(list(set(quarters)))
        print(f"\n提取的季度 ({len(quarters)} 个): {quarters}")
        
        # 分析每个季度的数据
        result_data = []
        
        for quarter in quarters:
            comprehensive_col = f"{quarter}_综合等级"
            detailed_col = f"{quarter}_细分等级"
            
            print(f"\n处理季度: {quarter}")
            print(f"  综合等级列: {comprehensive_col} - 存在: {comprehensive_col in df.columns}")
            print(f"  细分等级列: {detailed_col} - 存在: {detailed_col in df.columns}")
            
            if comprehensive_col in df.columns and detailed_col in df.columns:
                # 按综合等级+细分等级分组统计
                grouped = df.groupby([comprehensive_col, detailed_col]).size().reset_index(name='资源人数')
                grouped['季度'] = quarter
                result_data.append(grouped)
                
                print(f"  分组结果: {len(grouped)} 行")
                print(f"  前3行:")
                for i, row in grouped.head(3).iterrows():
                    print(f"    {row[comprehensive_col]}{row[detailed_col]}: {row['资源人数']}")
        
        if result_data:
            # 合并所有季度数据
            all_data = pd.concat(result_data, ignore_index=True)
            print(f"\n合并后数据: {len(all_data)} 行")
            print("列名:", list(all_data.columns))
            
            # 查看季度分布
            quarter_counts = all_data['季度'].value_counts()
            print(f"\n季度分布:")
            for quarter, count in quarter_counts.items():
                print(f"  {quarter}: {count} 行")
            
            # 尝试透视表
            print(f"\n尝试创建透视表...")
            
            # 找到正确的列名
            level_cols = [col for col in all_data.columns if '等级' in col and col != '季度']
            print(f"等级列: {level_cols}")
            
            if len(level_cols) >= 2:
                pivot_table = all_data.pivot_table(
                    index=level_cols[:2], 
                    columns='季度', 
                    values='资源人数', 
                    fill_value=0
                )
                
                print(f"透视表形状: {pivot_table.shape}")
                print(f"透视表列: {list(pivot_table.columns)}")
                print(f"透视表前5行:")
                print(pivot_table.head())
        
    except Exception as e:
        print(f"调试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    debug_excel_data()
