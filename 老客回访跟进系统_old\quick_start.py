"""
快速启动脚本 - 解决路径和依赖问题
"""
import os
import sys

def main():
    """主函数"""
    print("老客回访与跟进系统 - 快速启动")
    print("="*40)
    
    # 确保在正确的目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"当前工作目录: {os.getcwd()}")
    
    # 检查关键文件是否存在
    required_files = ['init_db.py', 'run.py', 'config.py', 'app/__init__.py']
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少关键文件: {', '.join(missing_files)}")
        print("请确保在项目根目录运行此脚本")
        return
    
    print("✅ 关键文件检查通过")
    
    # 检查数据库是否已初始化
    try:
        print("检查数据库状态...")
        from app import create_app, db
        from config import get_config
        
        config_class = get_config()
        app = create_app(config_class)
        
        with app.app_context():
            from app.models import Role
            if Role.query.first():
                print("✅ 数据库已初始化")
                start_app = True
            else:
                print("⚠️ 数据库未初始化")
                choice = input("是否现在初始化数据库？(y/n): ").lower().strip()
                if choice == 'y':
                    print("正在初始化数据库...")
                    # 直接调用初始化函数
                    from init_db import main as init_main
                    if init_main():
                        print("✅ 数据库初始化成功")
                        start_app = True
                    else:
                        print("❌ 数据库初始化失败")
                        start_app = False
                else:
                    print("请先运行: python init_db.py")
                    start_app = False
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        print("请检查数据库配置或运行: python init_db.py")
        start_app = False
    
    if start_app:
        print("\n" + "="*50)
        print("🎉 启动老客回访与跟进系统")
        print("="*50)
        print("访问地址: http://localhost:5000")
        print("默认管理员账号: admin")
        print("默认密码: admin123")
        print("请在首次登录后立即修改密码！")
        print("按 Ctrl+C 停止服务")
        print("="*50)
        
        try:
            app.run(host='0.0.0.0', port=5000, debug=False)
        except KeyboardInterrupt:
            print("\n系统已停止")
        except Exception as e:
            print(f"❌ 系统启动失败: {e}")

if __name__ == '__main__':
    main()
