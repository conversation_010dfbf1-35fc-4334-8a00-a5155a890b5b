-- 老客登记信息反馈系统数据库表创建脚本
-- 数据库: Old_Customer_System

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS Old_Customer_System DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE Old_Customer_System;

-- 部门表
CREATE TABLE IF NOT EXISTS departments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    salt VARCHAR(32) NOT NULL,
    real_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'director', 'manager', 'online_consultant', 'onsite_consultant') NOT NULL,
    department_id INT,
    simple_code VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
    last_login TIMESTAMP NULL,
    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_department (department_id),
    INDEX idx_simple_code (simple_code),
    FOREIGN KEY (department_id) REFERENCES departments(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 渠道表
CREATE TABLE IF NOT EXISTS channels (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(200) NOT NULL,
    category VARCHAR(100),
    simple_code VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
    INDEX idx_name (name),
    INDEX idx_category (category),
    INDEX idx_simple_code (simple_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 客户表
CREATE TABLE IF NOT EXISTS customers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    card_number VARCHAR(18) NOT NULL,
    onsite_consultant_id INT NOT NULL,
    online_consultant_id INT NOT NULL,
    channel_id INT NOT NULL,
    inquiry_content TEXT,
    last_visit_date DATE,
    follow_up_note TEXT,
    follow_up_time TIMESTAMP NULL,
    follow_up_by INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    created_by INT NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
    UNIQUE KEY uk_card_number (card_number),
    INDEX idx_onsite_consultant (onsite_consultant_id),
    INDEX idx_online_consultant (online_consultant_id),
    INDEX idx_channel (channel_id),
    INDEX idx_created_by (created_by),
    INDEX idx_last_visit_date (last_visit_date),
    FOREIGN KEY (onsite_consultant_id) REFERENCES users(id),
    FOREIGN KEY (online_consultant_id) REFERENCES users(id),
    FOREIGN KEY (channel_id) REFERENCES channels(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (follow_up_by) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 现场小组表
CREATE TABLE IF NOT EXISTS onsite_groups (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 现场小组成员关系表
CREATE TABLE IF NOT EXISTS onsite_group_members (
    id INT PRIMARY KEY AUTO_INCREMENT,
    group_id INT NOT NULL,
    user_id INT NOT NULL,
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    UNIQUE KEY uk_group_user (group_id, user_id),
    FOREIGN KEY (group_id) REFERENCES onsite_groups(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 业务数据表（到院信息和消费金额）
CREATE TABLE IF NOT EXISTS business_data (
    id INT PRIMARY KEY AUTO_INCREMENT,
    card_number VARCHAR(18) NOT NULL,
    visit_date DATE NOT NULL,
    consumption_amount DECIMAL(10,2) DEFAULT 0 NOT NULL,
    data_source VARCHAR(100),
    imported_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    INDEX idx_card_number (card_number),
    INDEX idx_visit_date (visit_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 系统设置表
CREATE TABLE IF NOT EXISTS system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
    INDEX idx_key (setting_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入默认数据
INSERT IGNORE INTO departments (name, description) VALUES
('管理部', '系统管理部门'),
('网络咨询部', '网络咨询部门'),
('现场咨询部', '现场咨询部门');

INSERT IGNORE INTO system_settings (setting_key, setting_value, description) VALUES
('card_number_length', '10', '卡号长度设置'),
('system_name', '老客登记信息反馈系统', '系统名称'),
('version', '1.0.0', '系统版本');

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_customers_created_at ON customers(created_at);
CREATE INDEX IF NOT EXISTS idx_customers_updated_at ON customers(updated_at);
CREATE INDEX IF NOT EXISTS idx_business_data_card_visit ON business_data(card_number, visit_date);