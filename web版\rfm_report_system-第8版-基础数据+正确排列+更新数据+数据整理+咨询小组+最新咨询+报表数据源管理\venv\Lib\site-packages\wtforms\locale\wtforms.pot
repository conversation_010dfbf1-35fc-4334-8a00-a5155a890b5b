# Translations template for WTForms.
# Copyright (C) 2020 WTForms Team
# This file is distributed under the same license as the WTForms project.
# <AUTHOR> <EMAIL>, 2020.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: WTForms 3.0.0.dev\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2020-04-25 11:34-0700\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.8.0\n"

#: src/wtforms/validators.py:87
#, python-format
msgid "Invalid field name '%s'."
msgstr ""

#: src/wtforms/validators.py:98
#, python-format
msgid "Field must be equal to %(other_name)s."
msgstr ""

#: src/wtforms/validators.py:134
#, python-format
msgid "Field must be at least %(min)d character long."
msgid_plural "Field must be at least %(min)d characters long."
msgstr[0] ""
msgstr[1] ""

#: src/wtforms/validators.py:140
#, python-format
msgid "Field cannot be longer than %(max)d character."
msgid_plural "Field cannot be longer than %(max)d characters."
msgstr[0] ""
msgstr[1] ""

#: src/wtforms/validators.py:146
#, python-format
msgid "Field must be exactly %(max)d character long."
msgid_plural "Field must be exactly %(max)d characters long."
msgstr[0] ""
msgstr[1] ""

#: src/wtforms/validators.py:152
#, python-format
msgid "Field must be between %(min)d and %(max)d characters long."
msgstr ""

#: src/wtforms/validators.py:197
#, python-format
msgid "Number must be at least %(min)s."
msgstr ""

#: src/wtforms/validators.py:199
#, python-format
msgid "Number must be at most %(max)s."
msgstr ""

#: src/wtforms/validators.py:201
#, python-format
msgid "Number must be between %(min)s and %(max)s."
msgstr ""

#: src/wtforms/validators.py:269 src/wtforms/validators.py:294
msgid "This field is required."
msgstr ""

#: src/wtforms/validators.py:327
msgid "Invalid input."
msgstr ""

#: src/wtforms/validators.py:387
msgid "Invalid email address."
msgstr ""

#: src/wtforms/validators.py:423
msgid "Invalid IP address."
msgstr ""

#: src/wtforms/validators.py:466
msgid "Invalid Mac address."
msgstr ""

#: src/wtforms/validators.py:501
msgid "Invalid URL."
msgstr ""

#: src/wtforms/validators.py:522
msgid "Invalid UUID."
msgstr ""

#: src/wtforms/validators.py:553
#, python-format
msgid "Invalid value, must be one of: %(values)s."
msgstr ""

#: src/wtforms/validators.py:588
#, python-format
msgid "Invalid value, can't be any of: %(values)s."
msgstr ""

#: src/wtforms/csrf/core.py:96
msgid "Invalid CSRF Token."
msgstr ""

#: src/wtforms/csrf/session.py:63
msgid "CSRF token missing."
msgstr ""

#: src/wtforms/csrf/session.py:71
msgid "CSRF failed."
msgstr ""

#: src/wtforms/csrf/session.py:76
msgid "CSRF token expired."
msgstr ""

#: src/wtforms/fields/core.py:534
msgid "Invalid Choice: could not coerce."
msgstr ""

#: src/wtforms/fields/core.py:538
msgid "Choices cannot be None."
msgstr ""

#: src/wtforms/fields/core.py:545
msgid "Not a valid choice."
msgstr ""

#: src/wtforms/fields/core.py:573
msgid "Invalid choice(s): one or more data inputs could not be coerced."
msgstr ""

#: src/wtforms/fields/core.py:584
#, python-format
msgid "'%(value)s' is not a valid choice for this field."
msgstr ""

#: src/wtforms/fields/core.py:679 src/wtforms/fields/core.py:689
msgid "Not a valid integer value."
msgstr ""

#: src/wtforms/fields/core.py:760
msgid "Not a valid decimal value."
msgstr ""

#: src/wtforms/fields/core.py:788
msgid "Not a valid float value."
msgstr ""

#: src/wtforms/fields/core.py:853
msgid "Not a valid datetime value."
msgstr ""

#: src/wtforms/fields/core.py:871
msgid "Not a valid date value."
msgstr ""

#: src/wtforms/fields/core.py:889
msgid "Not a valid time value."
msgstr ""
