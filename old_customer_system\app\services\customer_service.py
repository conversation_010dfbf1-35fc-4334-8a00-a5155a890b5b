# -*- coding: utf-8 -*-
"""
客户服务
"""

from typing import Optional, Dict, List, Tuple
from datetime import datetime
from flask_login import current_user
from app import db
from app.models import Customer, User
from app.repositories.customer_repository import CustomerRepository
from app.services.permission_service import PermissionService

class CustomerService:
    """客户业务逻辑服务"""
    
    def __init__(self):
        self.customer_repo = CustomerRepository()
    
    def create_customer(self, customer_data: Dict, creator: User) -> Customer:
        """创建客户"""
        try:
            # 检查卡号是否已存在
            if self.customer_repo.find_by_card_number(customer_data['card_number']):
                raise ValueError("卡号已存在")
            
            # 创建客户对象
            customer = Customer(
                card_number=customer_data['card_number'],
                onsite_consultant_id=customer_data['onsite_consultant_id'],
                online_consultant_id=creator.id,  # 自动设置为当前登录用户
                channel_id=customer_data['channel_id'],
                inquiry_content=customer_data.get('inquiry_content'),
                last_visit_date=customer_data.get('last_visit_date'),
                created_by=creator.id
            )
            
            # 保存到数据库
            db.session.add(customer)
            db.session.commit()
            
            return customer
            
        except Exception as e:
            db.session.rollback()
            raise e
    
    def update_customer(self, customer_id: int, customer_data: Dict, operator: User) -> bool:
        """更新客户信息"""
        try:
            customer = self.customer_repo.get_by_id(customer_id)
            if not customer:
                return False
            
            # 检查权限
            if not PermissionService.can_edit_customer(operator, customer):
                raise ValueError("您没有权限编辑此客户信息")
            
            # 检查卡号唯一性（排除自己）
            if 'card_number' in customer_data:
                existing = self.customer_repo.find_by_card_number(customer_data['card_number'])
                if existing and existing.id != customer_id:
                    raise ValueError("卡号已存在")
            
            # 更新客户信息
            for key, value in customer_data.items():
                if hasattr(customer, key) and key not in ['created_by', 'created_at', 'online_consultant_id']:
                    setattr(customer, key, value)
            
            db.session.commit()
            return True
            
        except Exception as e:
            db.session.rollback()
            raise e
    
    def update_follow_up(self, customer_id: int, follow_up_note: str, operator: User) -> bool:
        """更新跟进信息"""
        try:
            customer = self.customer_repo.get_by_id(customer_id)
            if not customer:
                return False
            
            # 检查权限
            if not PermissionService.can_update_follow_up(operator, customer):
                raise ValueError("您没有权限更新此客户的跟进信息")
            
            # 更新跟进信息
            success = self.customer_repo.update_follow_up(customer_id, follow_up_note, operator.id)
            return success
            
        except Exception as e:
            db.session.rollback()
            raise e
    
    def get_customer_by_id(self, customer_id: int) -> Optional[Customer]:
        """根据ID获取客户"""
        return self.customer_repo.get_by_id(customer_id)
    
    def get_customer_by_card_number(self, card_number: str) -> Optional[Customer]:
        """根据卡号获取客户"""
        return self.customer_repo.find_by_card_number(card_number)
    
    def get_customers_with_permission(self, user: User, page: int = 1, per_page: int = 20):
        """根据用户权限获取客户列表（分页）"""
        return self.customer_repo.get_customers_with_permission(user, page, per_page)
    
    def search_customers(self, user: User, query: str) -> List[Customer]:
        """搜索客户"""
        customers = self.customer_repo.search_customers(query)
        return PermissionService.filter_customers_by_permission(user, customers)
    
    def get_customers_by_criteria(self, user: User, criteria: Dict) -> List[Customer]:
        """根据条件获取客户列表"""
        # 根据用户权限构建基础查询
        if user.role == 'admin' or user.role == 'director':
            customers = self.customer_repo.get_all()
        elif user.role == 'manager':
            customers = self.customer_repo.find_by_department(user.department_id)
        elif user.role == 'online_consultant':
            customers = self.customer_repo.find_by_creator(user.id)
        elif user.role == 'onsite_consultant':
            customers = self.customer_repo.find_by_onsite_consultant(user.id)
        else:
            customers = []
        
        # 应用筛选条件
        if criteria.get('onsite_consultant_id'):
            customers = [c for c in customers if c.onsite_consultant_id == criteria['onsite_consultant_id']]

        if criteria.get('online_consultant_id'):
            customers = [c for c in customers if c.online_consultant_id == criteria['online_consultant_id']]

        if criteria.get('channel_id'):
            customers = [c for c in customers if c.channel_id == criteria['channel_id']]

        if criteria.get('channel_category'):
            customers = [c for c in customers if c.channel.category == criteria['channel_category']]

        # 日期范围筛选 - 根据登记日期
        if criteria.get('start_date'):
            start_date = criteria['start_date']
            customers = [c for c in customers if c.registration_date >= start_date]

        if criteria.get('end_date'):
            end_date = criteria['end_date']
            customers = [c for c in customers if c.registration_date <= end_date]

        if criteria.get('has_follow_up') is not None:
            has_follow_up = bool(int(criteria['has_follow_up']))
            if has_follow_up:
                customers = [c for c in customers if c.follow_up_note and c.follow_up_note.strip()]
            else:
                customers = [c for c in customers if not c.follow_up_note or not c.follow_up_note.strip()]

        return customers
    
    def get_customer_statistics(self, user: User = None) -> Dict:
        """获取客户统计信息"""
        return self.customer_repo.get_customer_statistics(user)
    
    def get_recent_customers(self, user: User = None, limit: int = 10) -> List[Customer]:
        """获取最近的客户记录"""
        return self.customer_repo.get_recent_customers(user, limit)
    
    def can_access_customer(self, user: User, customer: Customer) -> bool:
        """检查用户是否可以访问客户"""
        return PermissionService.can_access_customer(user, customer)
    
    def can_edit_customer(self, user: User, customer: Customer) -> bool:
        """检查用户是否可以编辑客户"""
        return PermissionService.can_edit_customer(user, customer)
    
    def can_update_follow_up(self, user: User, customer: Customer) -> bool:
        """检查用户是否可以更新跟进信息"""
        return PermissionService.can_update_follow_up(user, customer)