{% extends "base.html" %}

{% block title %}添加跟进 - 老客回访与跟进系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-comments"></i> 添加跟进记录</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('customer.index') }}">客户管理</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('customer.view', id=registration.id) }}">客户详情</a></li>
                <li class="breadcrumb-item active">添加跟进</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-edit"></i> 跟进信息</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="follow_up_content" class="form-label">跟进内容 <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="follow_up_content" name="follow_up_content" 
                                  rows="6" placeholder="请详细描述本次跟进的内容..." required></textarea>
                        <div class="form-text">最多500字，请详细记录跟进情况</div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('customer.view', id=registration.id) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> 保存跟进
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-user"></i> 客户信息</h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>卡号</strong></td>
                        <td>{{ registration.card_number }}</td>
                    </tr>
                    <tr>
                        <td><strong>登记人</strong></td>
                        <td>{{ registration.registrar.real_name }}</td>
                    </tr>
                    <tr>
                        <td><strong>渠道</strong></td>
                        <td>{{ registration.activation_channel.channel_name }}</td>
                    </tr>
                    <tr>
                        <td><strong>最近来院</strong></td>
                        <td>{{ registration.last_visit_date.strftime('%Y-%m-%d') }}</td>
                    </tr>
                    <tr>
                        <td><strong>已跟进</strong></td>
                        <td>{{ registration.get_follow_up_count() }} 次</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-lightbulb"></i> 跟进建议</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li><i class="fas fa-check text-success"></i> 了解客户当前需求</li>
                    <li><i class="fas fa-check text-success"></i> 记录客户反馈意见</li>
                    <li><i class="fas fa-check text-success"></i> 提供专业建议</li>
                    <li><i class="fas fa-check text-success"></i> 约定下次联系时间</li>
                    <li><i class="fas fa-check text-success"></i> 记录客户意向程度</li>
                </ul>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-history"></i> 咨询内容</h6>
            </div>
            <div class="card-body">
                <p class="small text-muted">{{ registration.consultation_content }}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 字符计数
document.getElementById('follow_up_content').addEventListener('input', function() {
    const maxLength = 500;
    const currentLength = this.value.length;
    const remaining = maxLength - currentLength;
    
    // 限制字符数
    if (remaining < 0) {
        this.value = this.value.substring(0, maxLength);
    }
});
</script>
{% endblock %}
