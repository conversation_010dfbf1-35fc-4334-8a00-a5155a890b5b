import pymysql

def get_actual_column_names(cursor, table_name):
    """获取表中实际存在的列名"""
    cursor.execute(f"SHOW COLUMNS FROM {table_name}")
    return [col[0] for col in cursor.fetchall()]

def reorder_columns(cursor, table_name, desired_order):
    try:
        # 1. 获取原表所有列信息
        actual_columns = get_actual_column_names(cursor, table_name)
        
        # 2. 获取主键信息
        cursor.execute(f"SHOW KEYS FROM {table_name} WHERE Key_name = 'PRIMARY'")
        primary_key_info = cursor.fetchone()
        primary_key = primary_key_info[4] if primary_key_info else None
        
        # 3. 匹配实际存在的列
        matched_order = [col for col in desired_order if col in actual_columns]
        
        if not matched_order:
            print("错误: 没有有效的列可以排序")
            return False
            
        # 4. 检查并删除已存在的临时表
        temp_table = f"{table_name}_temp"
        cursor.execute(f"DROP TABLE IF EXISTS {temp_table}")
        cursor.connection.commit()
        
        # 5. 创建临时表，按新顺序定义列
        cursor.execute(f"SHOW CREATE TABLE {table_name}")
        create_sql = cursor.fetchone()[1]
        
        # 解析原表列定义
        column_defs = []
        cursor.execute(f"SHOW COLUMNS FROM {table_name}")
        for col in cursor.fetchall():
            col_name = col[0]
            col_type = col[1]
            col_null = "NOT NULL" if col[2] == "NO" else "NULL"
            col_default = f"DEFAULT {col[4]}" if col[4] else ""
            column_defs.append(f"`{col_name}` {col_type} {col_null} {col_default}")
        
        # 按desired_order重新排序列定义
        reordered_defs = []
        for col in desired_order:
            if col in actual_columns:
                reordered_defs.append(next(d for d in column_defs if d.startswith(f"`{col}`")))
        
        # 添加未在desired_order中的列
        for col_def in column_defs:
            col_name = col_def.split('`')[1]
            if col_name not in desired_order and col_name in actual_columns:
                reordered_defs.append(col_def)
        
        # 创建临时表
        create_temp_sql = f"CREATE TABLE `{temp_table}` (" + ",\n  ".join(reordered_defs) + ")"
        cursor.execute(create_temp_sql)
        cursor.connection.commit()
        
        # 6. 获取临时表列名
        temp_columns = get_actual_column_names(cursor, temp_table)
        
        # 7. 构建INSERT语句，包含所有列
        insert_cols = []
        select_cols = []
        
        for col in temp_columns:
            insert_cols.append(f"`{col}`")
            if col in matched_order or col == primary_key:
                select_cols.append(f"`{col}`")
            else:
                select_cols.append(f"`{col}`")
        
        # 8. 执行数据复制
        insert_sql = f"INSERT INTO {temp_table} ({', '.join(insert_cols)}) " \
                     f"SELECT {', '.join(select_cols)} FROM {table_name}"
        cursor.execute(insert_sql)
        cursor.connection.commit()
        
        # 9. 替换原表
        cursor.execute(f"DROP TABLE {table_name}")
        cursor.execute(f"RENAME TABLE {temp_table} TO {table_name}")
        cursor.connection.commit()
        
        print(f"表{table_name}的列顺序已成功调整")
        return True
        
    except Exception as e:
        print(f"处理表{table_name}时发生错误: {str(e)}")
        cursor.connection.rollback()
        return False

# 主程序
if __name__ == "__main__":
    # 连接数据库
    connection = pymysql.connect(
        host='localhost',
        user='root',
        password='Yj198704!',
        database='wdly'
    )
    
    try:
        with connection.cursor() as cursor:
            # 获取所有RFM表
            cursor.execute("SHOW TABLES LIKE '%RFM%'")
            rfm_tables = [table[0] for table in cursor.fetchall()]
            
            if not rfm_tables:
                print("未找到RFM相关的表")
                exit()
                
            # 用户选择表
            print("可用的RFM表:")
            for i, table in enumerate(rfm_tables, 1):
                print(f"{i}. {table}")
                
            selected_table = rfm_tables[int(input("请选择要处理的表(输入编号): "))-1]
            
            # 定义期望的列顺序
            desired_order = [
                '客户ID', '客户名称', '性别', '年龄', 
                '24Q1_现场', '24Q2_现场', '24Q3_现场', '24Q4_现场',
                '24Q1_R', '24Q2_R', '24Q3_R', '24Q4_R',
                '24Q1_F', '24Q2_F', '24Q3_F', '24Q4_F',
                '24Q1_M', '24Q2_M', '24Q3_M', '24Q4_M',
                '加权总分', '模型', 'RFM', '等级',
                '24Q1_执行金额', '24Q2_执行金额', '24Q3_执行金额', '24Q4_执行金额'
            ]
            
            # 调用函数调整列顺序
            reorder_columns(cursor, selected_table, desired_order)
            
    finally:
        connection.close()