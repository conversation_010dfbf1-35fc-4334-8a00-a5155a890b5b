#!/usr/bin/env python3
"""
系统管理脚本
提供各种管理功能
"""

import os
import sys
import click
from app import create_app
from app.models import User, DatabaseConfig, DataProcessingTask, SystemSettings, db

@click.group()
def cli():
    """RFM报表系统管理工具"""
    pass

@cli.command()
def init_db():
    """初始化数据库"""
    app = create_app()
    with app.app_context():
        db.create_all()
        click.echo("数据库初始化完成")

@cli.command()
@click.option('--username', prompt='用户名', help='用户名')
@click.option('--password', prompt='密码', hide_input=True, help='密码')
def create_user(username, password):
    """创建新用户"""
    app = create_app()
    with app.app_context():
        # 检查用户是否已存在
        if User.query.filter_by(username=username).first():
            click.echo(f"用户 '{username}' 已存在")
            return
        
        # 创建用户
        user = User(username=username)
        user.set_password(password)
        db.session.add(user)
        db.session.commit()
        
        click.echo(f"用户 '{username}' 创建成功")

@cli.command()
def list_users():
    """列出所有用户"""
    app = create_app()
    with app.app_context():
        users = User.query.all()
        if not users:
            click.echo("没有用户")
            return
        
        click.echo("用户列表：")
        for user in users:
            status = "激活" if user.is_active else "禁用"
            last_login = user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else "从未登录"
            click.echo(f"- {user.username} ({status}) - 最后登录: {last_login}")

@cli.command()
@click.argument('username')
def disable_user(username):
    """禁用用户"""
    app = create_app()
    with app.app_context():
        user = User.query.filter_by(username=username).first()
        if not user:
            click.echo(f"用户 '{username}' 不存在")
            return
        
        user.is_active = False
        db.session.commit()
        click.echo(f"用户 '{username}' 已禁用")

@cli.command()
@click.argument('username')
def enable_user(username):
    """启用用户"""
    app = create_app()
    with app.app_context():
        user = User.query.filter_by(username=username).first()
        if not user:
            click.echo(f"用户 '{username}' 不存在")
            return
        
        user.is_active = True
        db.session.commit()
        click.echo(f"用户 '{username}' 已启用")

@cli.command()
def list_configs():
    """列出数据库配置"""
    app = create_app()
    with app.app_context():
        configs = DatabaseConfig.query.all()
        if not configs:
            click.echo("没有数据库配置")
            return
        
        click.echo("数据库配置列表：")
        for config in configs:
            status = "激活" if config.is_active else "未激活"
            click.echo(f"- {config.name} ({config.host}:{config.port}/{config.database_name}) - {status}")

@cli.command()
def list_tasks():
    """列出处理任务"""
    app = create_app()
    with app.app_context():
        tasks = DataProcessingTask.query.order_by(DataProcessingTask.created_at.desc()).limit(20).all()
        if not tasks:
            click.echo("没有处理任务")
            return
        
        click.echo("最近20个处理任务：")
        for task in tasks:
            creator = task.creator.username if task.creator else "未知"
            click.echo(f"- {task.task_name} ({task.status}) - 创建者: {creator} - 创建时间: {task.created_at}")

@cli.command()
@click.option('--status', type=click.Choice(['pending', 'running', 'completed', 'failed']), help='按状态筛选')
def clean_tasks(status):
    """清理处理任务"""
    app = create_app()
    with app.app_context():
        query = DataProcessingTask.query
        if status:
            query = query.filter_by(status=status)
        
        tasks = query.all()
        if not tasks:
            click.echo("没有符合条件的任务")
            return
        
        click.echo(f"找到 {len(tasks)} 个任务")
        if click.confirm('确定要删除这些任务吗？'):
            # 删除输出文件
            for task in tasks:
                if task.output_file_path and os.path.exists(task.output_file_path):
                    try:
                        os.remove(task.output_file_path)
                    except Exception as e:
                        click.echo(f"删除文件失败: {e}")
            
            # 删除任务记录
            for task in tasks:
                db.session.delete(task)
            
            db.session.commit()
            click.echo(f"已删除 {len(tasks)} 个任务")

@cli.command()
@click.option('--enable/--disable', default=True, help='启用或禁用注册功能')
def toggle_registration(enable):
    """切换用户注册功能"""
    app = create_app()
    with app.app_context():
        SystemSettings.set_setting(
            'registration_enabled', 
            'true' if enable else 'false',
            '是否允许新用户注册'
        )
        status = "启用" if enable else "禁用"
        click.echo(f"用户注册功能已{status}")

@cli.command()
def show_settings():
    """显示系统设置"""
    app = create_app()
    with app.app_context():
        settings = SystemSettings.query.all()
        if not settings:
            click.echo("没有系统设置")
            return
        
        click.echo("系统设置：")
        for setting in settings:
            click.echo(f"- {setting.key}: {setting.value}")
            if setting.description:
                click.echo(f"  描述: {setting.description}")

@cli.command()
def backup_db():
    """备份系统数据库"""
    app = create_app()
    with app.app_context():
        # 这里可以添加数据库备份逻辑
        click.echo("数据库备份功能待实现")

@cli.command()
def check_health():
    """检查系统健康状态"""
    app = create_app()
    with app.app_context():
        try:
            # 检查数据库连接
            db.session.execute('SELECT 1')
            click.echo("✓ 数据库连接正常")
            
            # 检查用户数量
            user_count = User.query.count()
            click.echo(f"✓ 用户数量: {user_count}")
            
            # 检查配置数量
            config_count = DatabaseConfig.query.count()
            click.echo(f"✓ 数据库配置数量: {config_count}")
            
            # 检查激活的配置
            active_config = DatabaseConfig.get_active_config()
            if active_config:
                click.echo(f"✓ 激活的数据库配置: {active_config.name}")
            else:
                click.echo("⚠ 没有激活的数据库配置")
            
            # 检查任务统计
            task_stats = {
                'total': DataProcessingTask.query.count(),
                'completed': DataProcessingTask.query.filter_by(status='completed').count(),
                'failed': DataProcessingTask.query.filter_by(status='failed').count(),
                'running': DataProcessingTask.query.filter_by(status='running').count()
            }
            click.echo(f"✓ 任务统计: 总计{task_stats['total']}, 完成{task_stats['completed']}, 失败{task_stats['failed']}, 运行中{task_stats['running']}")
            
            click.echo("\n系统健康状态检查完成")
            
        except Exception as e:
            click.echo(f"✗ 系统检查失败: {e}")

if __name__ == '__main__':
    cli()
