"""
RFM报表数据模型
"""

from app.models import db, get_current_time
from datetime import datetime
import json


class RFMDataSource(db.Model):
    """RFM数据源模型"""
    __tablename__ = 'rfm_data_sources'

    id = db.Column(db.Integer, primary_key=True)

    # 关联信息
    source_task_id = db.Column(db.Integer, db.ForeignKey('data_processing_tasks.id'), nullable=False, unique=True)
    source_task_name = db.Column(db.String(200), nullable=False)
    report_type = db.Column(db.String(20), nullable=False, index=True)  # 'forward' 或 'result' - 添加索引
    
    # 数据统计
    total_records = db.Column(db.Integer, default=0)
    imported_records = db.Column(db.Integer, default=0)
    
    # 状态信息
    status = db.Column(db.String(20), default='pending', index=True)  # pending, importing, completed, failed - 添加索引
    error_message = db.Column(db.Text)
    
    # 数据信息
    data_columns = db.Column(db.JSON)  # 存储数据列信息
    quarters = db.Column(db.JSON)      # 包含的季度信息
    
    # 权限信息
    imported_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # 时间信息
    created_at = db.Column(db.DateTime, default=get_current_time, index=True)  # 添加索引以优化排序
    updated_at = db.Column(db.DateTime, default=get_current_time, onupdate=get_current_time)
    imported_at = db.Column(db.DateTime)
    
    # 关联关系
    source_task = db.relationship('DataProcessingTask', backref='rfm_data_source')
    importer = db.relationship('User', backref='imported_rfm_data_sources')
    
    def __repr__(self):
        return f'<RFMDataSource {self.source_task_name}>'
    
    @property
    def is_available(self):
        """数据源是否可用"""
        return self.status == 'completed' and self.imported_records > 0
    
    @property
    def is_locked(self):
        """数据源是否锁定了原始任务"""
        return self.status in ['importing', 'completed', 'deleting']
    
    def get_summary(self):
        """获取数据源摘要"""
        return {
            'id': self.id,
            'source_task_id': self.source_task_id,
            'source_task_name': self.source_task_name,
            'report_type': self.report_type,
            'total_records': self.total_records,
            'imported_records': self.imported_records,
            'status': self.status,
            'error_message': self.error_message,
            'quarters': self.quarters or [],
            'is_available': self.is_available,
            'is_locked': self.is_locked,
            'imported_by': self.importer.username if self.importer else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'imported_at': self.imported_at.isoformat() if self.imported_at else None
        }


class RFMAnalysisData(db.Model):
    """RFM分析数据存储表"""
    __tablename__ = 'rfm_analysis_data'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # 关联信息
    data_source_id = db.Column(db.Integer, db.ForeignKey('rfm_data_sources.id'), nullable=False)
    member_id = db.Column(db.String(100), nullable=False, index=True)  # 会员卡号
    
    # 基础数据 - 23Q4
    comprehensive_level_23q4 = db.Column(db.String(10), index=True)
    detailed_level_23q4 = db.Column(db.String(10), index=True)
    member_card_level_23q4 = db.Column(db.String(50))
    department_tag_23q4 = db.Column(db.String(20), index=True)
    
    # 基础数据 - 24Q1
    comprehensive_level_24q1 = db.Column(db.String(10), index=True)
    detailed_level_24q1 = db.Column(db.String(10), index=True)
    member_card_level_24q1 = db.Column(db.String(50))
    department_tag_24q1 = db.Column(db.String(20), index=True)
    
    # 基础数据 - 24Q2
    comprehensive_level_24q2 = db.Column(db.String(10), index=True)
    detailed_level_24q2 = db.Column(db.String(10), index=True)
    member_card_level_24q2 = db.Column(db.String(50))
    department_tag_24q2 = db.Column(db.String(20), index=True)
    
    # 基础数据 - 24Q3
    comprehensive_level_24q3 = db.Column(db.String(10), index=True)
    detailed_level_24q3 = db.Column(db.String(10), index=True)
    member_card_level_24q3 = db.Column(db.String(50))
    department_tag_24q3 = db.Column(db.String(20), index=True)
    
    # 扩展数据字段（JSON格式存储其他季度或特殊字段）
    extended_data = db.Column(db.JSON)
    
    # 时间信息
    created_at = db.Column(db.DateTime, default=get_current_time)
    
    # 关联关系
    data_source = db.relationship('RFMDataSource', backref='analysis_data')
    
    def __repr__(self):
        return f'<RFMAnalysisData {self.member_id}>'
    
    def get_quarter_data(self, quarter):
        """获取指定季度的数据"""
        quarter_lower = quarter.lower()
        return {
            'comprehensive_level': getattr(self, f'comprehensive_level_{quarter_lower}', None),
            'detailed_level': getattr(self, f'detailed_level_{quarter_lower}', None),
            'member_card_level': getattr(self, f'member_card_level_{quarter_lower}', None),
            'department_tag': getattr(self, f'department_tag_{quarter_lower}', None)
        }


class RFMReportCache(db.Model):
    """RFM报表缓存表"""
    __tablename__ = 'rfm_report_cache'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # 缓存标识
    data_source_id = db.Column(db.Integer, db.ForeignKey('rfm_data_sources.id'), nullable=False)
    report_type = db.Column(db.String(20), nullable=False)  # 'forward' 或 'result'
    analysis_type = db.Column(db.String(50), nullable=False)  # 分析类型
    cache_key = db.Column(db.String(200), nullable=False, unique=True, index=True)
    
    # 缓存数据
    cache_data = db.Column(db.JSON, nullable=False)
    
    # 时间信息
    created_at = db.Column(db.DateTime, default=get_current_time)
    expires_at = db.Column(db.DateTime)
    
    # 关联关系
    data_source = db.relationship('RFMDataSource', backref='report_cache')
    
    def __repr__(self):
        return f'<RFMReportCache {self.cache_key}>'
    
    @property
    def is_expired(self):
        """缓存是否过期"""
        if not self.expires_at:
            return False
        return datetime.utcnow() > self.expires_at
    
    @classmethod
    def get_cache_key(cls, data_source_id, report_type, analysis_type, **kwargs):
        """生成缓存键"""
        import hashlib
        
        key_parts = [str(data_source_id), report_type, analysis_type]
        for k, v in sorted(kwargs.items()):
            key_parts.append(f"{k}:{v}")
        
        key_str = "|".join(key_parts)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    @classmethod
    def get_cached_data(cls, data_source_id, report_type, analysis_type, **kwargs):
        """获取缓存数据"""
        cache_key = cls.get_cache_key(data_source_id, report_type, analysis_type, **kwargs)
        cache = cls.query.filter_by(cache_key=cache_key).first()
        
        if cache and not cache.is_expired:
            return cache.cache_data
        
        return None
    
    @classmethod
    def set_cached_data(cls, data_source_id, report_type, analysis_type, data, expires_hours=24, **kwargs):
        """设置缓存数据"""
        from datetime import timedelta
        
        cache_key = cls.get_cache_key(data_source_id, report_type, analysis_type, **kwargs)
        
        # 删除旧缓存
        cls.query.filter_by(cache_key=cache_key).delete()
        
        # 创建新缓存
        cache = cls(
            data_source_id=data_source_id,
            report_type=report_type,
            analysis_type=analysis_type,
            cache_key=cache_key,
            cache_data=data,
            expires_at=datetime.utcnow() + timedelta(hours=expires_hours)
        )
        
        db.session.add(cache)
        db.session.commit()
        
        return cache
