"""
数据预览管理器
负责生成各种表的数据预览统计信息
"""

import pandas as pd
from sqlalchemy import create_engine, text, inspect
from typing import Dict, List, Any, Optional
import logging
import re
import numpy as np


class DataPreviewManager:
    """数据预览管理器"""
    
    def __init__(self, db_engine):
        self.db_engine = db_engine
        self.logger = logging.getLogger(__name__)

    def _convert_to_json_serializable(self, obj):
        """将pandas数据类型转换为JSON可序列化的类型"""
        if isinstance(obj, (np.integer, np.int64, np.int32)):
            return int(obj)
        elif isinstance(obj, (np.floating, np.float64, np.float32)):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {key: self._convert_to_json_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_to_json_serializable(item) for item in obj]
        elif pd.isna(obj):
            return None
        else:
            return obj
    
    def get_all_tables_preview(self) -> Dict[str, Any]:
        """获取所有表的预览信息，按季度表和业务表分类"""
        try:
            inspector = inspect(self.db_engine)
            table_names = inspector.get_table_names()

            quarterly_tables = []
            business_tables = []

            for table_name in table_names:
                try:
                    if self._is_quarterly_table(table_name):
                        preview = self._get_quarterly_table_preview_simple(table_name)
                        quarterly_tables.append(preview)
                    else:
                        preview = self._get_business_table_preview_simple(table_name)
                        business_tables.append(preview)
                except Exception as e:
                    self.logger.error(f"获取表 {table_name} 预览失败: {str(e)}")
                    # 添加错误信息但继续处理其他表
                    error_preview = {
                        'table_name': table_name,
                        'table_type': 'quarterly' if self._is_quarterly_table(table_name) else 'business',
                        'error': str(e),
                        'total_records': 0
                    }
                    if self._is_quarterly_table(table_name):
                        quarterly_tables.append(error_preview)
                    else:
                        business_tables.append(error_preview)

            result = {
                'quarterly_tables': sorted(quarterly_tables, key=lambda x: x['table_name']),
                'business_tables': sorted(business_tables, key=lambda x: x['table_name'])
            }

            # 转换数据类型以确保JSON序列化
            return self._convert_to_json_serializable(result)

        except Exception as e:
            self.logger.error(f"获取表预览失败: {str(e)}")
            raise Exception(f"获取表预览失败: {str(e)}")

    def _get_quarterly_table_preview_simple(self, table_name: str) -> Dict[str, Any]:
        """获取季度表简化预览信息"""
        try:
            with self.db_engine.connect() as conn:
                # 基本信息
                total_count = pd.read_sql(text(f"SELECT COUNT(*) as count FROM `{table_name}`"), conn).iloc[0]['count']

                preview = {
                    'table_name': table_name,
                    'table_type': 'quarterly',
                    'total_records': int(total_count),
                    'member_card_count': 0,
                    'comprehensive_levels': {},
                    'detailed_levels': {},
                    'card_levels': {},
                    'department_tags': {}
                }

                if total_count == 0:
                    return preview

                # 获取列名
                columns_query = text(f"SHOW COLUMNS FROM `{table_name}`")
                columns_df = pd.read_sql(columns_query, conn)
                column_names = [str(col) for col in columns_df['Field'].tolist()]

                # 会员卡号统计
                card_columns = ['会员卡号', '卡号', 'member_card', 'card_no']
                card_column = self._find_column(column_names, card_columns)
                if card_column:
                    try:
                        card_count = pd.read_sql(text(f"SELECT COUNT(DISTINCT `{card_column}`) as count FROM `{table_name}`"), conn).iloc[0]['count']
                        preview['member_card_count'] = int(card_count)
                    except:
                        preview['member_card_count'] = 0

                # 综合等级统计（限制结果数量）
                comp_level_columns = ['综合等级', 'comprehensive_level', '等级']
                comp_level_column = self._find_column(column_names, comp_level_columns)
                if comp_level_column:
                    try:
                        level_stats = pd.read_sql(text(f"SELECT `{comp_level_column}`, COUNT(*) as count FROM `{table_name}` GROUP BY `{comp_level_column}` LIMIT 10"), conn)
                        preview['comprehensive_levels'] = {str(k): int(v) for k, v in zip(level_stats[comp_level_column], level_stats['count']) if pd.notna(k)}
                    except:
                        preview['comprehensive_levels'] = {}

                return preview

        except Exception as e:
            self.logger.error(f"获取季度表 {table_name} 预览失败: {str(e)}")
            return {
                'table_name': table_name,
                'table_type': 'quarterly',
                'error': str(e),
                'total_records': 0
            }

    def _get_business_table_preview_simple(self, table_name: str) -> Dict[str, Any]:
        """获取业务表详细预览信息"""
        try:
            with self.db_engine.connect() as conn:
                # 基本信息
                total_count = pd.read_sql(text(f"SELECT COUNT(*) as count FROM `{table_name}`"), conn).iloc[0]['count']

                preview = {
                    'table_name': table_name,
                    'table_type': 'business',
                    'total_records': int(total_count),
                    'statistics': {}
                }

                if total_count == 0:
                    return preview

                # 获取列信息
                columns_query = text(f"SHOW COLUMNS FROM `{table_name}`")
                columns_df = pd.read_sql(columns_query, conn)
                column_names = [str(col) for col in columns_df['Field'].tolist()]

                # 根据表名类型生成不同的统计
                table_name_lower = table_name.lower()

                if '医生绑定' in table_name or 'doctor' in table_name_lower or '绑定' in table_name:
                    preview['statistics'] = self._get_doctor_binding_stats_simple(table_name, conn, column_names)
                elif '客户待执行' in table_name or 'pending' in table_name_lower or '待执行' in table_name:
                    preview['statistics'] = self._get_pending_execution_stats_simple(table_name, conn, column_names)
                elif '客户执行' in table_name or 'execution' in table_name_lower or '执行明细' in table_name:
                    preview['statistics'] = self._get_execution_stats_simple(table_name, conn, column_names)
                elif '客户来院' in table_name or 'visit' in table_name_lower or '来院' in table_name:
                    preview['statistics'] = self._get_visit_stats_simple(table_name, conn, column_names)
                elif '客户消费' in table_name or 'consumption' in table_name_lower or '消费明细' in table_name:
                    preview['statistics'] = self._get_consumption_stats_simple(table_name, conn, column_names)
                elif '预交金余额' in table_name or 'prepaid' in table_name_lower or '预交金' in table_name:
                    preview['statistics'] = self._get_prepaid_balance_stats_simple(table_name, conn, column_names)
                elif '金额卡余额' in table_name or 'card_balance' in table_name_lower or '卡余额' in table_name:
                    preview['statistics'] = self._get_card_balance_stats_simple(table_name, conn, column_names)
                else:
                    # 通用统计
                    preview['statistics'] = self._get_general_stats_simple(table_name, conn, column_names)

                return preview

        except Exception as e:
            self.logger.error(f"获取业务表 {table_name} 预览失败: {str(e)}")
            return {
                'table_name': table_name,
                'table_type': 'business',
                'error': str(e),
                'total_records': 0
            }
    
    def _is_quarterly_table(self, table_name: str) -> bool:
        """判断是否为季度表"""
        quarterly_keywords = ['季度', 'quarterly', 'quarter', 'q1', 'q2', 'q3', 'q4']
        table_name_lower = table_name.lower()
        
        for keyword in quarterly_keywords:
            if keyword in table_name_lower:
                return True
        
        # 检查季度模式，如 2024Q1, 2024_Q2 等
        quarterly_pattern = r'(20\d{2}[_\-]?q[1-4]|q[1-4][_\-]?20\d{2})'
        if re.search(quarterly_pattern, table_name_lower):
            return True
            
        return False
    
    def _get_quarterly_table_preview(self, table_name: str) -> Dict[str, Any]:
        """获取季度表预览信息"""
        try:
            with self.db_engine.connect() as conn:
                # 基本信息
                total_count = pd.read_sql(text(f"SELECT COUNT(*) as count FROM `{table_name}`"), conn).iloc[0]['count']
                
                # 获取列名
                columns_query = text(f"SHOW COLUMNS FROM `{table_name}`")
                columns_df = pd.read_sql(columns_query, conn)
                column_names = columns_df['Field'].tolist()
                
                preview = {
                    'table_name': table_name,
                    'table_type': 'quarterly',
                    'total_records': total_count,
                    'member_card_count': 0,
                    'comprehensive_levels': {},
                    'detailed_levels': {},
                    'card_levels': {},
                    'department_tags': {}
                }
                
                if total_count == 0:
                    return preview
                
                # 会员卡号统计
                card_columns = ['会员卡号', '卡号', 'member_card', 'card_no']
                card_column = self._find_column(column_names, card_columns)
                if card_column:
                    card_count = pd.read_sql(text(f"SELECT COUNT(DISTINCT `{card_column}`) as count FROM `{table_name}`"), conn).iloc[0]['count']
                    preview['member_card_count'] = int(card_count)
                
                # 综合等级统计
                comp_level_columns = ['综合等级', 'comprehensive_level', '等级']
                comp_level_column = self._find_column(column_names, comp_level_columns)
                if comp_level_column:
                    level_stats = pd.read_sql(text(f"SELECT `{comp_level_column}`, COUNT(*) as count FROM `{table_name}` GROUP BY `{comp_level_column}`"), conn)
                    preview['comprehensive_levels'] = {str(k): int(v) for k, v in zip(level_stats[comp_level_column], level_stats['count'])}
                
                # 细分等级统计
                detail_level_columns = ['细分等级', 'detailed_level', '详细等级']
                detail_level_column = self._find_column(column_names, detail_level_columns)
                if detail_level_column:
                    detail_stats = pd.read_sql(text(f"SELECT `{detail_level_column}`, COUNT(*) as count FROM `{table_name}` GROUP BY `{detail_level_column}`"), conn)
                    preview['detailed_levels'] = {str(k): int(v) for k, v in zip(detail_stats[detail_level_column], detail_stats['count'])}
                
                # 会员卡级统计
                card_level_columns = ['会员卡级', 'card_level', '卡级']
                card_level_column = self._find_column(column_names, card_level_columns)
                if card_level_column:
                    card_level_stats = pd.read_sql(text(f"SELECT `{card_level_column}`, COUNT(*) as count FROM `{table_name}` GROUP BY `{card_level_column}`"), conn)
                    preview['card_levels'] = {str(k): int(v) for k, v in zip(card_level_stats[card_level_column], card_level_stats['count'])}
                
                # 科室标签统计
                dept_columns = ['科室标签', 'department_tag', '科室', 'department']
                dept_column = self._find_column(column_names, dept_columns)
                if dept_column:
                    dept_stats = pd.read_sql(text(f"SELECT `{dept_column}`, COUNT(*) as count FROM `{table_name}` GROUP BY `{dept_column}`"), conn)
                    preview['department_tags'] = {str(k): int(v) for k, v in zip(dept_stats[dept_column], dept_stats['count'])}
                
                return preview
                
        except Exception as e:
            self.logger.error(f"获取季度表 {table_name} 预览失败: {str(e)}")
            return {
                'table_name': table_name,
                'table_type': 'quarterly',
                'error': str(e)
            }
    
    def _get_business_table_preview(self, table_name: str) -> Dict[str, Any]:
        """获取业务表预览信息"""
        try:
            with self.db_engine.connect() as conn:
                # 基本信息
                total_count = pd.read_sql(text(f"SELECT COUNT(*) as count FROM `{table_name}`"), conn).iloc[0]['count']
                
                preview = {
                    'table_name': table_name,
                    'table_type': 'business',
                    'total_records': int(total_count),
                    'statistics': {}
                }
                
                if total_count == 0:
                    return preview
                
                # 根据表名类型生成不同的统计
                table_name_lower = table_name.lower()
                
                if '医生绑定' in table_name or 'doctor' in table_name_lower:
                    preview['statistics'] = self._get_doctor_binding_stats(table_name, conn)
                elif '客户待执行' in table_name or 'pending' in table_name_lower:
                    preview['statistics'] = self._get_pending_execution_stats(table_name, conn)
                elif '客户执行' in table_name or 'execution' in table_name_lower:
                    preview['statistics'] = self._get_execution_stats(table_name, conn)
                elif '客户来院' in table_name or 'visit' in table_name_lower:
                    preview['statistics'] = self._get_visit_stats(table_name, conn)
                elif '客户消费' in table_name or 'consumption' in table_name_lower:
                    preview['statistics'] = self._get_consumption_stats(table_name, conn)
                elif '预交金余额' in table_name or 'prepaid' in table_name_lower:
                    preview['statistics'] = self._get_prepaid_balance_stats(table_name, conn)
                elif '金额卡余额' in table_name or 'card_balance' in table_name_lower:
                    preview['statistics'] = self._get_card_balance_stats(table_name, conn)
                else:
                    # 通用统计
                    preview['statistics'] = self._get_general_stats(table_name, conn)
                
                return preview
                
        except Exception as e:
            self.logger.error(f"获取业务表 {table_name} 预览失败: {str(e)}")
            return {
                'table_name': table_name,
                'table_type': 'business',
                'error': str(e)
            }
    
    def _find_column(self, column_names: List[str], possible_names: List[str]) -> Optional[str]:
        """在列名列表中查找匹配的列名"""
        for col in column_names:
            for possible in possible_names:
                if possible.lower() in col.lower() or col.lower() in possible.lower():
                    return col
        return None
    
    def _get_doctor_binding_stats(self, table_name: str, conn) -> Dict[str, Any]:
        """医生绑定明细统计"""
        try:
            # 获取列名
            columns_query = text(f"SHOW COLUMNS FROM `{table_name}`")
            columns_df = pd.read_sql(columns_query, conn)
            column_names = columns_df['Field'].tolist()
            
            stats = {}
            
            # 医生客户数统计
            doctor_columns = ['医生', 'doctor', '医生姓名', 'doctor_name']
            doctor_column = self._find_column(column_names, doctor_columns)
            
            if doctor_column:
                doctor_stats = pd.read_sql(text(f"""
                    SELECT `{doctor_column}`, COUNT(*) as customer_count 
                    FROM `{table_name}` 
                    GROUP BY `{doctor_column}` 
                    ORDER BY customer_count DESC
                """), conn)
                stats['doctor_customer_counts'] = {str(k): int(v) for k, v in zip(doctor_stats[doctor_column], doctor_stats['customer_count'])}
                
                # 医生综合等级统计
                comp_level_column = self._find_column(column_names, ['综合等级', 'comprehensive_level'])
                if comp_level_column:
                    doctor_level_stats = pd.read_sql(text(f"""
                        SELECT `{doctor_column}`, `{comp_level_column}`, COUNT(*) as count 
                        FROM `{table_name}` 
                        GROUP BY `{doctor_column}`, `{comp_level_column}`
                    """), conn)
                    
                    doctor_levels = {}
                    for _, row in doctor_level_stats.iterrows():
                        doctor = row[doctor_column]
                        level = row[comp_level_column]
                        count = row['count']
                        
                        if doctor not in doctor_levels:
                            doctor_levels[doctor] = {}
                        doctor_levels[doctor][level] = count
                    
                    stats['doctor_level_distribution'] = doctor_levels
            
            return stats
            
        except Exception as e:
            return {'error': str(e)}

    def _get_pending_execution_stats(self, table_name: str, conn) -> Dict[str, Any]:
        """客户待执行明细统计"""
        try:
            columns_query = text(f"SHOW COLUMNS FROM `{table_name}`")
            columns_df = pd.read_sql(columns_query, conn)
            column_names = columns_df['Field'].tolist()

            stats = {}

            # 按年度求和待执行金额
            amount_column = self._find_column(column_names, ['未执行金额（真实金额）', '未执行金额', 'pending_amount'])
            date_column = self._find_column(column_names, ['日期', 'date', '创建时间', 'create_time'])

            if amount_column and date_column:
                yearly_stats = pd.read_sql(text(f"""
                    SELECT YEAR(`{date_column}`) as year, SUM(`{amount_column}`) as total_amount
                    FROM `{table_name}`
                    WHERE `{amount_column}` IS NOT NULL
                    GROUP BY YEAR(`{date_column}`)
                    ORDER BY year
                """), conn)
                stats['yearly_pending_amount'] = dict(zip(yearly_stats['year'], yearly_stats['total_amount']))

            # 按科室维度求和待执行金额
            dept_column = self._find_column(column_names, ['科室', 'department', '一级分类'])
            if amount_column and dept_column:
                dept_stats = pd.read_sql(text(f"""
                    SELECT `{dept_column}`, SUM(`{amount_column}`) as total_amount
                    FROM `{table_name}`
                    WHERE `{amount_column}` IS NOT NULL
                    GROUP BY `{dept_column}`
                    ORDER BY total_amount DESC
                """), conn)
                stats['department_pending_amount'] = dict(zip(dept_stats[dept_column], dept_stats['total_amount']))

            return stats

        except Exception as e:
            return {'error': str(e)}

    def _get_execution_stats(self, table_name: str, conn) -> Dict[str, Any]:
        """客户执行明细统计"""
        try:
            columns_query = text(f"SHOW COLUMNS FROM `{table_name}`")
            columns_df = pd.read_sql(columns_query, conn)
            column_names = columns_df['Field'].tolist()

            stats = {}

            # 按年度求和执行金额
            amount_column = self._find_column(column_names, ['真实金额', 'execution_amount', '执行金额'])
            date_column = self._find_column(column_names, ['日期', 'date', '执行时间', 'execution_time'])

            if amount_column and date_column:
                yearly_stats = pd.read_sql(text(f"""
                    SELECT YEAR(`{date_column}`) as year, SUM(`{amount_column}`) as total_amount
                    FROM `{table_name}`
                    WHERE `{amount_column}` IS NOT NULL
                    GROUP BY YEAR(`{date_column}`)
                    ORDER BY year
                """), conn)
                stats['yearly_execution_amount'] = dict(zip(yearly_stats['year'], yearly_stats['total_amount']))

                # 按年度+季度求和执行金额
                quarterly_stats = pd.read_sql(text(f"""
                    SELECT YEAR(`{date_column}`) as year, QUARTER(`{date_column}`) as quarter,
                           SUM(`{amount_column}`) as total_amount
                    FROM `{table_name}`
                    WHERE `{amount_column}` IS NOT NULL
                    GROUP BY YEAR(`{date_column}`), QUARTER(`{date_column}`)
                    ORDER BY year, quarter
                """), conn)

                quarterly_dict = {}
                for _, row in quarterly_stats.iterrows():
                    key = f"{int(row['year'])}Q{int(row['quarter'])}"
                    quarterly_dict[key] = row['total_amount']
                stats['quarterly_execution_amount'] = quarterly_dict

            return stats

        except Exception as e:
            return {'error': str(e)}

    def _get_visit_stats(self, table_name: str, conn) -> Dict[str, Any]:
        """客户来院明细统计"""
        try:
            columns_query = text(f"SHOW COLUMNS FROM `{table_name}`")
            columns_df = pd.read_sql(columns_query, conn)
            column_names = columns_df['Field'].tolist()

            stats = {}

            # 按年度+季度计数求和客户状态
            status_column = self._find_column(column_names, ['客户状态', 'customer_status', '状态'])
            date_column = self._find_column(column_names, ['日期', 'date', '来院时间', 'visit_time'])

            if status_column and date_column:
                quarterly_status_stats = pd.read_sql(text(f"""
                    SELECT YEAR(`{date_column}`) as year, QUARTER(`{date_column}`) as quarter,
                           `{status_column}`, COUNT(*) as count
                    FROM `{table_name}`
                    GROUP BY YEAR(`{date_column}`), QUARTER(`{date_column}`), `{status_column}`
                    ORDER BY year, quarter, `{status_column}`
                """), conn)

                quarterly_status_dict = {}
                for _, row in quarterly_status_stats.iterrows():
                    key = f"{int(row['year'])}Q{int(row['quarter'])}"
                    if key not in quarterly_status_dict:
                        quarterly_status_dict[key] = {}
                    quarterly_status_dict[key][row[status_column]] = row['count']

                stats['quarterly_customer_status'] = quarterly_status_dict

            return stats

        except Exception as e:
            return {'error': str(e)}

    def _get_consumption_stats(self, table_name: str, conn) -> Dict[str, Any]:
        """客户消费明细统计"""
        try:
            columns_query = text(f"SHOW COLUMNS FROM `{table_name}`")
            columns_df = pd.read_sql(columns_query, conn)
            column_names = columns_df['Field'].tolist()

            stats = {}

            consumption_column = self._find_column(column_names, ['消费金额', 'consumption_amount'])
            currency_column = self._find_column(column_names, ['货币金额', 'currency_amount'])
            date_column = self._find_column(column_names, ['日期', 'date', '消费时间', 'consumption_time'])

            if date_column:
                # 按年度求和
                if consumption_column:
                    yearly_consumption = pd.read_sql(text(f"""
                        SELECT YEAR(`{date_column}`) as year, SUM(`{consumption_column}`) as total_consumption
                        FROM `{table_name}`
                        WHERE `{consumption_column}` IS NOT NULL
                        GROUP BY YEAR(`{date_column}`)
                        ORDER BY year
                    """), conn)
                    stats['yearly_consumption_amount'] = dict(zip(yearly_consumption['year'], yearly_consumption['total_consumption']))

                if currency_column:
                    yearly_currency = pd.read_sql(text(f"""
                        SELECT YEAR(`{date_column}`) as year, SUM(`{currency_column}`) as total_currency
                        FROM `{table_name}`
                        WHERE `{currency_column}` IS NOT NULL
                        GROUP BY YEAR(`{date_column}`)
                        ORDER BY year
                    """), conn)
                    stats['yearly_currency_amount'] = dict(zip(yearly_currency['year'], yearly_currency['total_currency']))

                # 按年度+季度求和
                if consumption_column:
                    quarterly_consumption = pd.read_sql(text(f"""
                        SELECT YEAR(`{date_column}`) as year, QUARTER(`{date_column}`) as quarter,
                               SUM(`{consumption_column}`) as total_consumption
                        FROM `{table_name}`
                        WHERE `{consumption_column}` IS NOT NULL
                        GROUP BY YEAR(`{date_column}`), QUARTER(`{date_column}`)
                        ORDER BY year, quarter
                    """), conn)

                    quarterly_consumption_dict = {}
                    for _, row in quarterly_consumption.iterrows():
                        key = f"{int(row['year'])}Q{int(row['quarter'])}"
                        quarterly_consumption_dict[key] = row['total_consumption']
                    stats['quarterly_consumption_amount'] = quarterly_consumption_dict

                if currency_column:
                    quarterly_currency = pd.read_sql(text(f"""
                        SELECT YEAR(`{date_column}`) as year, QUARTER(`{date_column}`) as quarter,
                               SUM(`{currency_column}`) as total_currency
                        FROM `{table_name}`
                        WHERE `{currency_column}` IS NOT NULL
                        GROUP BY YEAR(`{date_column}`), QUARTER(`{date_column}`)
                        ORDER BY year, quarter
                    """), conn)

                    quarterly_currency_dict = {}
                    for _, row in quarterly_currency.iterrows():
                        key = f"{int(row['year'])}Q{int(row['quarter'])}"
                        quarterly_currency_dict[key] = row['total_currency']
                    stats['quarterly_currency_amount'] = quarterly_currency_dict

            return stats

        except Exception as e:
            return {'error': str(e)}

    def _get_prepaid_balance_stats(self, table_name: str, conn) -> Dict[str, Any]:
        """客户预交金余额统计"""
        try:
            columns_query = text(f"SHOW COLUMNS FROM `{table_name}`")
            columns_df = pd.read_sql(columns_query, conn)
            column_names = columns_df['Field'].tolist()

            stats = {}

            # 以现场咨询求和预交余额
            source_column = self._find_column(column_names, ['来源', 'source', '咨询方式'])
            balance_column = self._find_column(column_names, ['预交余额', 'prepaid_balance', '余额'])

            if source_column and balance_column:
                # 现场咨询统计
                onsite_balance = pd.read_sql(text(f"""
                    SELECT SUM(`{balance_column}`) as total_balance
                    FROM `{table_name}`
                    WHERE `{source_column}` LIKE '%现场%' AND `{balance_column}` IS NOT NULL
                """), conn)

                if not onsite_balance.empty and onsite_balance.iloc[0]['total_balance'] is not None:
                    stats['onsite_consultation_balance'] = float(onsite_balance.iloc[0]['total_balance'])

                # 按来源分组统计
                source_balance = pd.read_sql(text(f"""
                    SELECT `{source_column}`, SUM(`{balance_column}`) as total_balance
                    FROM `{table_name}`
                    WHERE `{balance_column}` IS NOT NULL
                    GROUP BY `{source_column}`
                    ORDER BY total_balance DESC
                """), conn)
                stats['source_balance_distribution'] = dict(zip(source_balance[source_column], source_balance['total_balance']))

            return stats

        except Exception as e:
            return {'error': str(e)}

    def _get_card_balance_stats(self, table_name: str, conn) -> Dict[str, Any]:
        """客户金额卡余额统计"""
        try:
            columns_query = text(f"SHOW COLUMNS FROM `{table_name}`")
            columns_df = pd.read_sql(columns_query, conn)
            column_names = columns_df['Field'].tolist()

            stats = {}

            # 以卡名称求和真实余额
            card_name_column = self._find_column(column_names, ['卡名称', 'card_name', '卡类型'])
            balance_column = self._find_column(column_names, ['真实余额', 'real_balance', '余额'])

            if card_name_column and balance_column:
                card_balance = pd.read_sql(text(f"""
                    SELECT `{card_name_column}`, SUM(`{balance_column}`) as total_balance
                    FROM `{table_name}`
                    WHERE `{balance_column}` IS NOT NULL
                    GROUP BY `{card_name_column}`
                    ORDER BY total_balance DESC
                """), conn)
                stats['card_balance_by_name'] = dict(zip(card_balance[card_name_column], card_balance['total_balance']))

            return stats

        except Exception as e:
            return {'error': str(e)}

    def _get_general_stats(self, table_name: str, conn) -> Dict[str, Any]:
        """通用统计"""
        try:
            columns_query = text(f"SHOW COLUMNS FROM `{table_name}`")
            columns_df = pd.read_sql(columns_query, conn)
            column_names = columns_df['Field'].tolist()

            stats = {
                'column_count': len(column_names),
                'columns': column_names[:10]  # 只显示前10个列名
            }

            return stats

        except Exception as e:
            return {'error': str(e)}

    def _get_doctor_binding_stats_simple(self, table_name: str, conn, column_names: List[str]) -> Dict[str, Any]:
        """医生绑定明细统计 - 简化版"""
        try:
            stats = {'table_type': '医生绑定明细表'}

            # 查找医生列
            doctor_columns = ['医生', 'doctor', '医生姓名', 'doctor_name', '医师', '主治医生']
            doctor_column = self._find_column(column_names, doctor_columns)

            if doctor_column:
                # 医生客户数统计（前10名）
                doctor_stats = pd.read_sql(text(f"""
                    SELECT `{doctor_column}`, COUNT(*) as customer_count
                    FROM `{table_name}`
                    WHERE `{doctor_column}` IS NOT NULL AND `{doctor_column}` != ''
                    GROUP BY `{doctor_column}`
                    ORDER BY customer_count DESC
                    LIMIT 10
                """), conn)

                if not doctor_stats.empty:
                    stats['doctor_customer_counts'] = {
                        str(k): int(v) for k, v in zip(doctor_stats[doctor_column], doctor_stats['customer_count'])
                        if pd.notna(k) and str(k).strip() != ''
                    }

                # 综合等级分布
                comp_level_column = self._find_column(column_names, ['综合等级', 'comprehensive_level', '等级', '客户等级'])
                if comp_level_column:
                    level_stats = pd.read_sql(text(f"""
                        SELECT `{comp_level_column}`, COUNT(*) as count
                        FROM `{table_name}`
                        WHERE `{comp_level_column}` IS NOT NULL AND `{comp_level_column}` != ''
                        GROUP BY `{comp_level_column}`
                        LIMIT 10
                    """), conn)

                    if not level_stats.empty:
                        stats['comprehensive_level_distribution'] = {
                            str(k): int(v) for k, v in zip(level_stats[comp_level_column], level_stats['count'])
                            if pd.notna(k) and str(k).strip() != ''
                        }

            stats['available_columns'] = column_names[:10]  # 显示前10个列名供参考
            return stats

        except Exception as e:
            return {'error': f'医生绑定统计失败: {str(e)}', 'available_columns': column_names[:10]}

    def _get_pending_execution_stats_simple(self, table_name: str, conn, column_names: List[str]) -> Dict[str, Any]:
        """客户待执行明细统计 - 简化版"""
        try:
            stats = {'table_type': '客户待执行明细表'}

            # 查找金额列
            amount_columns = ['未执行金额（真实金额）', '未执行金额', 'pending_amount', '金额', '待执行金额', '真实金额']
            amount_column = self._find_column(column_names, amount_columns)

            # 查找日期列
            date_columns = ['日期', 'date', '创建时间', 'create_time', '开单时间', '时间']
            date_column = self._find_column(column_names, date_columns)

            if amount_column:
                # 总待执行金额
                total_amount = pd.read_sql(text(f"""
                    SELECT SUM(`{amount_column}`) as total_amount
                    FROM `{table_name}`
                    WHERE `{amount_column}` IS NOT NULL AND `{amount_column}` > 0
                """), conn)

                if not total_amount.empty and pd.notna(total_amount.iloc[0]['total_amount']):
                    stats['total_pending_amount'] = float(total_amount.iloc[0]['total_amount'])

                # 按年度统计（如果有日期列）
                if date_column:
                    yearly_stats = pd.read_sql(text(f"""
                        SELECT YEAR(`{date_column}`) as year, SUM(`{amount_column}`) as total_amount
                        FROM `{table_name}`
                        WHERE `{amount_column}` IS NOT NULL AND `{amount_column}` > 0
                        AND `{date_column}` IS NOT NULL
                        GROUP BY YEAR(`{date_column}`)
                        ORDER BY year DESC
                        LIMIT 5
                    """), conn)

                    if not yearly_stats.empty:
                        stats['yearly_pending_amount'] = {
                            str(int(k)): float(v) for k, v in zip(yearly_stats['year'], yearly_stats['total_amount'])
                            if pd.notna(k) and pd.notna(v)
                        }

                # 按科室统计
                dept_columns = ['科室', 'department', '一级分类', '科室名称', '部门']
                dept_column = self._find_column(column_names, dept_columns)
                if dept_column:
                    dept_stats = pd.read_sql(text(f"""
                        SELECT `{dept_column}`, SUM(`{amount_column}`) as total_amount
                        FROM `{table_name}`
                        WHERE `{amount_column}` IS NOT NULL AND `{amount_column}` > 0
                        AND `{dept_column}` IS NOT NULL AND `{dept_column}` != ''
                        GROUP BY `{dept_column}`
                        ORDER BY total_amount DESC
                        LIMIT 10
                    """), conn)

                    if not dept_stats.empty:
                        stats['department_pending_amount'] = {
                            str(k): float(v) for k, v in zip(dept_stats[dept_column], dept_stats['total_amount'])
                            if pd.notna(k) and pd.notna(v) and str(k).strip() != ''
                        }

            stats['available_columns'] = column_names[:10]
            return stats

        except Exception as e:
            return {'error': f'待执行统计失败: {str(e)}', 'available_columns': column_names[:10]}

    def _get_execution_stats_simple(self, table_name: str, conn, column_names: List[str]) -> Dict[str, Any]:
        """客户执行明细统计 - 简化版"""
        try:
            stats = {'table_type': '客户执行明细表'}

            # 查找金额列
            amount_columns = ['真实金额', 'execution_amount', '执行金额', '金额', '实际金额']
            amount_column = self._find_column(column_names, amount_columns)

            # 查找日期列
            date_columns = ['日期', 'date', '执行时间', 'execution_time', '完成时间', '时间']
            date_column = self._find_column(column_names, date_columns)

            if amount_column:
                # 总执行金额
                total_amount = pd.read_sql(text(f"""
                    SELECT SUM(`{amount_column}`) as total_amount
                    FROM `{table_name}`
                    WHERE `{amount_column}` IS NOT NULL AND `{amount_column}` > 0
                """), conn)

                if not total_amount.empty and pd.notna(total_amount.iloc[0]['total_amount']):
                    stats['total_execution_amount'] = float(total_amount.iloc[0]['total_amount'])

                # 按年度统计
                if date_column:
                    yearly_stats = pd.read_sql(text(f"""
                        SELECT YEAR(`{date_column}`) as year, SUM(`{amount_column}`) as total_amount
                        FROM `{table_name}`
                        WHERE `{amount_column}` IS NOT NULL AND `{amount_column}` > 0
                        AND `{date_column}` IS NOT NULL
                        GROUP BY YEAR(`{date_column}`)
                        ORDER BY year DESC
                        LIMIT 5
                    """), conn)

                    if not yearly_stats.empty:
                        stats['yearly_execution_amount'] = {
                            str(int(k)): float(v) for k, v in zip(yearly_stats['year'], yearly_stats['total_amount'])
                            if pd.notna(k) and pd.notna(v)
                        }

                    # 按季度统计
                    quarterly_stats = pd.read_sql(text(f"""
                        SELECT YEAR(`{date_column}`) as year, QUARTER(`{date_column}`) as quarter,
                               SUM(`{amount_column}`) as total_amount
                        FROM `{table_name}`
                        WHERE `{amount_column}` IS NOT NULL AND `{amount_column}` > 0
                        AND `{date_column}` IS NOT NULL
                        GROUP BY YEAR(`{date_column}`), QUARTER(`{date_column}`)
                        ORDER BY year DESC, quarter DESC
                        LIMIT 8
                    """), conn)

                    if not quarterly_stats.empty:
                        quarterly_dict = {}
                        for _, row in quarterly_stats.iterrows():
                            if pd.notna(row['year']) and pd.notna(row['quarter']) and pd.notna(row['total_amount']):
                                key = f"{int(row['year'])}Q{int(row['quarter'])}"
                                quarterly_dict[key] = float(row['total_amount'])
                        stats['quarterly_execution_amount'] = quarterly_dict

            stats['available_columns'] = column_names[:10]
            return stats

        except Exception as e:
            return {'error': f'执行统计失败: {str(e)}', 'available_columns': column_names[:10]}

    def _get_visit_stats_simple(self, table_name: str, conn, column_names: List[str]) -> Dict[str, Any]:
        """客户来院明细统计 - 简化版"""
        try:
            stats = {'table_type': '客户来院明细表'}

            # 查找客户状态列
            status_columns = ['客户状态', 'customer_status', '状态', '来院状态']
            status_column = self._find_column(column_names, status_columns)

            # 查找日期列
            date_columns = ['日期', 'date', '来院时间', 'visit_time', '就诊时间', '时间']
            date_column = self._find_column(column_names, date_columns)

            if status_column:
                # 客户状态分布
                status_stats = pd.read_sql(text(f"""
                    SELECT `{status_column}`, COUNT(*) as count
                    FROM `{table_name}`
                    WHERE `{status_column}` IS NOT NULL AND `{status_column}` != ''
                    GROUP BY `{status_column}`
                    ORDER BY count DESC
                    LIMIT 10
                """), conn)

                if not status_stats.empty:
                    stats['customer_status_distribution'] = {
                        str(k): int(v) for k, v in zip(status_stats[status_column], status_stats['count'])
                        if pd.notna(k) and str(k).strip() != ''
                    }

                # 按年度+季度统计客户状态
                if date_column:
                    quarterly_status_stats = pd.read_sql(text(f"""
                        SELECT YEAR(`{date_column}`) as year, QUARTER(`{date_column}`) as quarter,
                               `{status_column}`, COUNT(*) as count
                        FROM `{table_name}`
                        WHERE `{status_column}` IS NOT NULL AND `{status_column}` != ''
                        AND `{date_column}` IS NOT NULL
                        GROUP BY YEAR(`{date_column}`), QUARTER(`{date_column}`), `{status_column}`
                        ORDER BY year DESC, quarter DESC
                        LIMIT 20
                    """), conn)

                    if not quarterly_status_stats.empty:
                        quarterly_status_dict = {}
                        for _, row in quarterly_status_stats.iterrows():
                            if pd.notna(row['year']) and pd.notna(row['quarter']) and pd.notna(row[status_column]):
                                key = f"{int(row['year'])}Q{int(row['quarter'])}"
                                if key not in quarterly_status_dict:
                                    quarterly_status_dict[key] = {}
                                quarterly_status_dict[key][str(row[status_column])] = int(row['count'])

                        stats['quarterly_customer_status'] = quarterly_status_dict

            stats['available_columns'] = column_names[:10]
            return stats

        except Exception as e:
            return {'error': f'来院统计失败: {str(e)}', 'available_columns': column_names[:10]}

    def _get_prepaid_balance_stats_simple(self, table_name: str, conn, column_names: List[str]) -> Dict[str, Any]:
        """客户预交金余额统计 - 简化版"""
        try:
            stats = {'table_type': '客户预交金余额表'}

            # 查找来源列
            source_columns = ['来源', 'source', '咨询方式', '渠道']
            source_column = self._find_column(column_names, source_columns)

            # 查找余额列
            balance_columns = ['预交余额', 'prepaid_balance', '余额', '预交金余额']
            balance_column = self._find_column(column_names, balance_columns)

            if balance_column:
                # 总预交金余额
                total_balance = pd.read_sql(text(f"""
                    SELECT SUM(`{balance_column}`) as total_balance
                    FROM `{table_name}`
                    WHERE `{balance_column}` IS NOT NULL AND `{balance_column}` > 0
                """), conn)

                if not total_balance.empty and pd.notna(total_balance.iloc[0]['total_balance']):
                    stats['total_prepaid_balance'] = float(total_balance.iloc[0]['total_balance'])

                # 现场咨询余额统计
                if source_column:
                    onsite_balance = pd.read_sql(text(f"""
                        SELECT SUM(`{balance_column}`) as total_balance
                        FROM `{table_name}`
                        WHERE `{source_column}` LIKE '%现场%' AND `{balance_column}` IS NOT NULL AND `{balance_column}` > 0
                    """), conn)

                    if not onsite_balance.empty and pd.notna(onsite_balance.iloc[0]['total_balance']):
                        stats['onsite_consultation_balance'] = float(onsite_balance.iloc[0]['total_balance'])

                    # 按来源分组统计
                    source_balance = pd.read_sql(text(f"""
                        SELECT `{source_column}`, SUM(`{balance_column}`) as total_balance
                        FROM `{table_name}`
                        WHERE `{balance_column}` IS NOT NULL AND `{balance_column}` > 0
                        AND `{source_column}` IS NOT NULL AND `{source_column}` != ''
                        GROUP BY `{source_column}`
                        ORDER BY total_balance DESC
                        LIMIT 10
                    """), conn)

                    if not source_balance.empty:
                        stats['source_balance_distribution'] = {
                            str(k): float(v) for k, v in zip(source_balance[source_column], source_balance['total_balance'])
                            if pd.notna(k) and pd.notna(v) and str(k).strip() != ''
                        }

            stats['available_columns'] = column_names[:10]
            return stats

        except Exception as e:
            return {'error': f'预交金统计失败: {str(e)}', 'available_columns': column_names[:10]}

    def _get_card_balance_stats_simple(self, table_name: str, conn, column_names: List[str]) -> Dict[str, Any]:
        """客户金额卡余额统计 - 简化版"""
        try:
            stats = {'table_type': '客户金额卡余额表'}

            # 查找卡名称列
            card_name_columns = ['卡名称', 'card_name', '卡类型', '卡种']
            card_name_column = self._find_column(column_names, card_name_columns)

            # 查找余额列
            balance_columns = ['真实余额', 'real_balance', '余额', '卡余额']
            balance_column = self._find_column(column_names, balance_columns)

            if balance_column:
                # 总卡余额
                total_balance = pd.read_sql(text(f"""
                    SELECT SUM(`{balance_column}`) as total_balance
                    FROM `{table_name}`
                    WHERE `{balance_column}` IS NOT NULL AND `{balance_column}` > 0
                """), conn)

                if not total_balance.empty and pd.notna(total_balance.iloc[0]['total_balance']):
                    stats['total_card_balance'] = float(total_balance.iloc[0]['total_balance'])

                # 按卡名称统计余额
                if card_name_column:
                    card_balance = pd.read_sql(text(f"""
                        SELECT `{card_name_column}`, SUM(`{balance_column}`) as total_balance
                        FROM `{table_name}`
                        WHERE `{balance_column}` IS NOT NULL AND `{balance_column}` > 0
                        AND `{card_name_column}` IS NOT NULL AND `{card_name_column}` != ''
                        GROUP BY `{card_name_column}`
                        ORDER BY total_balance DESC
                        LIMIT 10
                    """), conn)

                    if not card_balance.empty:
                        stats['card_balance_by_name'] = {
                            str(k): float(v) for k, v in zip(card_balance[card_name_column], card_balance['total_balance'])
                            if pd.notna(k) and pd.notna(v) and str(k).strip() != ''
                        }

            stats['available_columns'] = column_names[:10]
            return stats

        except Exception as e:
            return {'error': f'卡余额统计失败: {str(e)}', 'available_columns': column_names[:10]}

    def _get_consumption_stats_simple(self, table_name: str, conn, column_names: List[str]) -> Dict[str, Any]:
        """客户消费明细统计 - 简化版"""
        try:
            stats = {'table_type': '客户消费明细表'}

            # 查找消费金额列
            consumption_columns = ['消费金额', 'consumption_amount', '金额', '消费总额']
            consumption_column = self._find_column(column_names, consumption_columns)

            # 查找货币金额列
            currency_columns = ['货币金额', 'currency_amount', '现金金额', '实收金额']
            currency_column = self._find_column(column_names, currency_columns)

            # 查找日期列
            date_columns = ['日期', 'date', '消费时间', 'consumption_time', '交易时间', '时间']
            date_column = self._find_column(column_names, date_columns)

            if consumption_column:
                # 总消费金额
                total_consumption = pd.read_sql(text(f"""
                    SELECT SUM(`{consumption_column}`) as total_amount
                    FROM `{table_name}`
                    WHERE `{consumption_column}` IS NOT NULL AND `{consumption_column}` > 0
                """), conn)

                if not total_consumption.empty and pd.notna(total_consumption.iloc[0]['total_amount']):
                    stats['total_consumption_amount'] = float(total_consumption.iloc[0]['total_amount'])

                # 按年度统计消费金额
                if date_column:
                    yearly_consumption = pd.read_sql(text(f"""
                        SELECT YEAR(`{date_column}`) as year, SUM(`{consumption_column}`) as total_consumption
                        FROM `{table_name}`
                        WHERE `{consumption_column}` IS NOT NULL AND `{consumption_column}` > 0
                        AND `{date_column}` IS NOT NULL
                        GROUP BY YEAR(`{date_column}`)
                        ORDER BY year DESC
                        LIMIT 5
                    """), conn)

                    if not yearly_consumption.empty:
                        stats['yearly_consumption_amount'] = {
                            str(int(k)): float(v) for k, v in zip(yearly_consumption['year'], yearly_consumption['total_consumption'])
                            if pd.notna(k) and pd.notna(v)
                        }

                    # 按季度统计消费金额
                    quarterly_consumption = pd.read_sql(text(f"""
                        SELECT YEAR(`{date_column}`) as year, QUARTER(`{date_column}`) as quarter,
                               SUM(`{consumption_column}`) as total_consumption
                        FROM `{table_name}`
                        WHERE `{consumption_column}` IS NOT NULL AND `{consumption_column}` > 0
                        AND `{date_column}` IS NOT NULL
                        GROUP BY YEAR(`{date_column}`), QUARTER(`{date_column}`)
                        ORDER BY year DESC, quarter DESC
                        LIMIT 8
                    """), conn)

                    if not quarterly_consumption.empty:
                        quarterly_consumption_dict = {}
                        for _, row in quarterly_consumption.iterrows():
                            if pd.notna(row['year']) and pd.notna(row['quarter']) and pd.notna(row['total_consumption']):
                                key = f"{int(row['year'])}Q{int(row['quarter'])}"
                                quarterly_consumption_dict[key] = float(row['total_consumption'])
                        stats['quarterly_consumption_amount'] = quarterly_consumption_dict

            if currency_column:
                # 总货币金额
                total_currency = pd.read_sql(text(f"""
                    SELECT SUM(`{currency_column}`) as total_amount
                    FROM `{table_name}`
                    WHERE `{currency_column}` IS NOT NULL AND `{currency_column}` > 0
                """), conn)

                if not total_currency.empty and pd.notna(total_currency.iloc[0]['total_amount']):
                    stats['total_currency_amount'] = float(total_currency.iloc[0]['total_amount'])

            stats['available_columns'] = column_names[:10]
            return stats

        except Exception as e:
            return {'error': f'消费统计失败: {str(e)}', 'available_columns': column_names[:10]}

    def _get_general_stats_simple(self, table_name: str, conn, column_names: List[str]) -> Dict[str, Any]:
        """通用统计 - 简化版"""
        try:
            stats = {
                'table_type': '通用业务表',
                'column_count': len(column_names),
                'available_columns': column_names[:15]  # 显示前15个列名
            }

            # 尝试找到一些常见的统计列
            # 查找金额相关列
            amount_columns = ['金额', 'amount', '费用', '价格', '余额', 'balance']
            amount_column = self._find_column(column_names, amount_columns)

            if amount_column:
                try:
                    amount_stats = pd.read_sql(text(f"""
                        SELECT
                            COUNT(*) as count,
                            SUM(`{amount_column}`) as total_amount,
                            AVG(`{amount_column}`) as avg_amount,
                            MAX(`{amount_column}`) as max_amount,
                            MIN(`{amount_column}`) as min_amount
                        FROM `{table_name}`
                        WHERE `{amount_column}` IS NOT NULL AND `{amount_column}` > 0
                    """), conn)

                    if not amount_stats.empty:
                        row = amount_stats.iloc[0]
                        stats['amount_statistics'] = {
                            'column_name': amount_column,
                            'count': int(row['count']) if pd.notna(row['count']) else 0,
                            'total': float(row['total_amount']) if pd.notna(row['total_amount']) else 0,
                            'average': float(row['avg_amount']) if pd.notna(row['avg_amount']) else 0,
                            'max': float(row['max_amount']) if pd.notna(row['max_amount']) else 0,
                            'min': float(row['min_amount']) if pd.notna(row['min_amount']) else 0
                        }
                except:
                    pass

            # 查找日期相关列
            date_columns = ['日期', 'date', '时间', 'time', '创建时间', 'create_time']
            date_column = self._find_column(column_names, date_columns)

            if date_column:
                try:
                    date_stats = pd.read_sql(text(f"""
                        SELECT
                            MIN(`{date_column}`) as min_date,
                            MAX(`{date_column}`) as max_date,
                            COUNT(DISTINCT DATE(`{date_column}`)) as unique_dates
                        FROM `{table_name}`
                        WHERE `{date_column}` IS NOT NULL
                    """), conn)

                    if not date_stats.empty:
                        row = date_stats.iloc[0]
                        stats['date_statistics'] = {
                            'column_name': date_column,
                            'min_date': str(row['min_date']) if pd.notna(row['min_date']) else None,
                            'max_date': str(row['max_date']) if pd.notna(row['max_date']) else None,
                            'unique_dates': int(row['unique_dates']) if pd.notna(row['unique_dates']) else 0
                        }
                except:
                    pass

            return stats

        except Exception as e:
            return {'error': f'通用统计失败: {str(e)}', 'available_columns': column_names[:10]}
