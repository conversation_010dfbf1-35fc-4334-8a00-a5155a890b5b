import os
import pandas as pd
import time
import shutil
from flask import render_template, request, flash, redirect, url_for, current_app, jsonify, send_file
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename

from app.mapping import bp
from app.mapping.forms import MappingTemplateForm, MappingEditForm, ActivateTemplateForm
from app.models import MappingTemplate, CategoryMapping, db


def safe_file_save(file, file_path, max_retries=3, retry_delay=1):
    """安全保存文件，处理文件占用问题"""
    for attempt in range(max_retries):
        try:
            # 尝试保存文件
            file.save(file_path)
            return True
        except PermissionError as e:
            if "WinError 32" in str(e) or "另一个程序正在使用此文件" in str(e):
                if attempt < max_retries - 1:
                    print(f"文件被占用，等待 {retry_delay} 秒后重试... (尝试 {attempt + 1}/{max_retries})")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避
                    continue
                else:
                    raise Exception(f"文件被其他程序占用，请关闭Excel等程序后重试。错误详情: {str(e)}")
            else:
                raise e
        except Exception as e:
            raise e
    return False


def is_file_locked(file_path):
    """检查文件是否被占用"""
    try:
        # 尝试以独占模式打开文件
        with open(file_path, 'r+b') as f:
            pass
        return False
    except (PermissionError, IOError):
        return True


@bp.route('/')
@login_required
def index():
    """映射管理首页"""
    templates = MappingTemplate.query.order_by(MappingTemplate.created_at.desc()).all()
    active_template = MappingTemplate.get_active_template()
    
    return render_template('mapping/index.html',
                         title='参照表管理',
                         templates=templates,
                         active_template=active_template)


@bp.route('/upload', methods=['GET', 'POST'])
@login_required
def upload_template():
    """上传映射模板"""
    form = MappingTemplateForm()
    
    if form.validate_on_submit():
        try:
            # 保存上传的文件
            file = form.excel_file.data
            filename = secure_filename(file.filename)
            timestamp = pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{timestamp}_{filename}"

            upload_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'mappings')
            os.makedirs(upload_dir, exist_ok=True)
            file_path = os.path.join(upload_dir, filename)

            # 使用安全保存方法
            if not safe_file_save(file, file_path):
                flash('文件保存失败，请重试', 'error')
                return render_template('mapping/upload.html', title='上传参照表', form=form)
            
            # 检查文件是否仍被占用
            if is_file_locked(file_path):
                try:
                    os.remove(file_path)
                except:
                    pass
                flash('文件仍被其他程序占用，请关闭Excel等程序后重试', 'error')
                return render_template('mapping/upload.html', title='上传参照表', form=form)

            # 验证Excel文件格式
            if not validate_excel_format(file_path):
                try:
                    os.remove(file_path)
                except:
                    pass
                flash('Excel文件格式不正确，请检查工作表和列名', 'error')
                return render_template('mapping/upload.html', title='上传参照表', form=form)
            
            # 创建模板记录
            template = MappingTemplate(
                template_name=form.template_name.data,
                description=form.description.data,
                file_path=file_path,
                created_by=current_user.id
            )
            
            db.session.add(template)
            db.session.commit()
            
            # 解析并导入映射数据
            import_mappings_from_excel(template)
            
            flash('参照表上传成功！', 'success')
            return redirect(url_for('mapping.index'))
            
        except Exception as e:
            error_msg = str(e)
            if "WinError 32" in error_msg or "另一个程序正在使用此文件" in error_msg:
                flash('上传失败: 文件被其他程序占用，请关闭Excel等程序后重试', 'error')
            elif "PermissionError" in error_msg:
                flash('上传失败: 没有文件访问权限，请检查文件权限设置', 'error')
            else:
                flash(f'上传失败: {error_msg}', 'error')
    
    return render_template('mapping/upload.html', title='上传参照表', form=form)


@bp.route('/template/<int:template_id>')
@login_required
def view_template(template_id):
    """查看模板详情"""
    template = MappingTemplate.query.get_or_404(template_id)
    
    # 获取映射数据
    level1_mappings = CategoryMapping.query.filter_by(
        mapping_name=template.template_name,
        mapping_type='level1'
    ).all()
    
    level2_mappings = CategoryMapping.query.filter_by(
        mapping_name=template.template_name,
        mapping_type='level2'
    ).all()
    
    level3_mappings = CategoryMapping.query.filter_by(
        mapping_name=template.template_name,
        mapping_type='level3'
    ).all()
    
    return render_template('mapping/template_detail.html',
                         title=f'参照表详情 - {template.template_name}',
                         template=template,
                         level1_mappings=level1_mappings,
                         level2_mappings=level2_mappings,
                         level3_mappings=level3_mappings)


@bp.route('/activate/<int:template_id>', methods=['POST'])
@login_required
def activate_template(template_id):
    """激活模板"""
    template = MappingTemplate.query.get_or_404(template_id)
    
    try:
        template.activate()
        flash(f'参照表 "{template.template_name}" 已激活！', 'success')
    except Exception as e:
        flash(f'激活失败: {str(e)}', 'error')
    
    return redirect(url_for('mapping.index'))


@bp.route('/deactivate', methods=['POST'])
@login_required
def deactivate_all():
    """禁用所有模板"""
    try:
        MappingTemplate.query.update({'is_active': False})
        db.session.commit()
        flash('已禁用所有参照表，将使用原始分类进行计算', 'success')
    except Exception as e:
        flash(f'操作失败: {str(e)}', 'error')
    
    return redirect(url_for('mapping.index'))


@bp.route('/delete/<int:template_id>', methods=['POST'])
@login_required
def delete_template(template_id):
    """删除模板"""
    template = MappingTemplate.query.get_or_404(template_id)
    
    try:
        # 删除相关的映射数据
        CategoryMapping.clear_mappings(template.template_name)
        
        # 删除文件
        if template.file_path and os.path.exists(template.file_path):
            os.remove(template.file_path)
        
        # 删除模板记录
        db.session.delete(template)
        db.session.commit()
        
        flash('参照表删除成功！', 'success')
    except Exception as e:
        flash(f'删除失败: {str(e)}', 'error')
    
    return redirect(url_for('mapping.index'))


@bp.route('/download_template')
@login_required
def download_template():
    """下载模板文件"""
    # 创建示例模板
    template_path = create_template_file()
    return send_file(template_path, as_attachment=True, 
                    download_name='参照表模板.xlsx')


def validate_excel_format(file_path):
    """验证Excel文件格式"""
    try:
        excel_file = pd.ExcelFile(file_path)
        print(f"📋 Excel文件工作表: {excel_file.sheet_names}")

        # 检查必需的工作表
        required_sheets = ['一级分类映射', '二级分类映射', '三级分类映射', '现场映射', '现场小组映射']
        missing_sheets = []

        for sheet in required_sheets:
            if sheet not in excel_file.sheet_names:
                missing_sheets.append(sheet)

        if missing_sheets:
            print(f"❌ 缺少工作表: {missing_sheets}")
            return False

        # 检查列名
        for sheet in required_sheets:
            df = pd.read_excel(file_path, sheet_name=sheet)
            actual_columns = list(df.columns)
            expected_columns = ['原始值', '映射值']

            print(f"📊 工作表 '{sheet}' 列名: {actual_columns}")

            if actual_columns != expected_columns:
                print(f"❌ 工作表 '{sheet}' 列名不正确")
                print(f"   预期: {expected_columns}")
                print(f"   实际: {actual_columns}")
                return False

        print("✅ Excel文件格式验证通过")
        return True

    except Exception as e:
        print(f"❌ Excel文件验证异常: {str(e)}")
        return False


def import_mappings_from_excel(template):
    """从Excel导入映射数据"""
    try:
        # 清除旧的映射数据
        CategoryMapping.clear_mappings(template.template_name)
        
        # 导入新的映射数据
        mapping_types = {
            '一级分类映射': 'level1',
            '二级分类映射': 'level2',
            '三级分类映射': 'level3',
            '现场映射': 'field_mapping',
            '现场小组映射': 'field_group'
        }
        
        for sheet_name, mapping_type in mapping_types.items():
            df = pd.read_excel(template.file_path, sheet_name=sheet_name)
            
            for _, row in df.iterrows():
                if pd.notna(row['原始值']) and pd.notna(row['映射值']):
                    mapping = CategoryMapping(
                        mapping_name=template.template_name,
                        mapping_type=mapping_type,
                        original_value=str(row['原始值']).strip(),
                        mapped_value=str(row['映射值']).strip(),
                        created_by=current_user.id
                    )
                    db.session.add(mapping)
        
        db.session.commit()
        
    except Exception as e:
        db.session.rollback()
        raise e


def create_template_file():
    """创建模板文件"""
    template_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'templates')
    os.makedirs(template_dir, exist_ok=True)
    template_path = os.path.join(template_dir, '参照表模板.xlsx')
    
    # 创建示例数据
    level1_data = pd.DataFrame({
        '原始值': ['皮肤美容项目', '形体美容项目', '口腔美容项目', '注射美容项目'],
        '映射值': ['皮肤', '皮肤', '口腔', '注射']
    })
    
    level2_data = pd.DataFrame({
        '原始值': ['衡力', 'Botox', '瘦脸针', '除皱针'],
        '映射值': ['肉毒素', '肉毒素', '肉毒素', '肉毒素']
    })
    
    level3_data = pd.DataFrame({
        '原始值': ['四代黄金微针', '五代黄金微针', '六代黄金微针'],
        '映射值': ['黄金微针', '黄金微针', '黄金微针']
    })

    # 现场映射数据
    field_mapping_data = pd.DataFrame({
        '原始值': ['刘翠', '张三', '李四', '王五'],
        '映射值': ['刘江珊', '张三', '李四新', '王五']
    })

    # 现场小组映射数据
    field_group_data = pd.DataFrame({
        '原始值': ['刘江珊', '张三', '李四新', '王五'],
        '映射值': ['马文静小组', '马文静小组', '李组长小组', '王组长小组']
    })

    # 写入Excel文件
    with pd.ExcelWriter(template_path, engine='openpyxl') as writer:
        level1_data.to_excel(writer, sheet_name='一级分类映射', index=False)
        level2_data.to_excel(writer, sheet_name='二级分类映射', index=False)
        level3_data.to_excel(writer, sheet_name='三级分类映射', index=False)
        field_mapping_data.to_excel(writer, sheet_name='现场映射', index=False)
        field_group_data.to_excel(writer, sheet_name='现场小组映射', index=False)
    
    return template_path
