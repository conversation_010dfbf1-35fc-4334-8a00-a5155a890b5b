# 老客登记信息反馈系统设计文档

## 概述

老客登记信息反馈系统是一个基于Flask框架的Web应用系统，采用MySQL数据库存储，支持多角色权限管理的客户信息管理平台。系统设计遵循MVC架构模式，采用模块化设计，确保系统的可维护性、可扩展性和安全性。

## 架构

### 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    Web浏览器界面                              │
├─────────────────────────────────────────────────────────────┤
│                    Flask Web应用                             │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐   │
│  │  用户认证    │  基础管理    │  登记系统    │  统计报表    │   │
│  │   模块      │    模块     │    模块     │    模块     │   │
│  └─────────────┴─────────────┴─────────────┴─────────────┘   │
├─────────────────────────────────────────────────────────────┤
│                    权限控制中间件                             │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层 (SQLAlchemy)                   │
├─────────────────────────────────────────────────────────────┤
│                    MySQL数据库                               │
│                 (Old_Customer_System)                       │
└─────────────────────────────────────────────────────────────┘
```

### 技术栈选择

- **后端框架**: Flask 2.3+ - 轻量级、灵活的Python Web框架
- **数据库**: MySQL 5.7+ - 成熟稳定的关系型数据库
- **ORM**: SQLAlchemy - Python最流行的ORM框架
- **前端**: Bootstrap 5 + jQuery - 响应式UI框架
- **表单处理**: Flask-WTF - 表单验证和CSRF保护
- **用户认证**: Flask-Login - 用户会话管理
- **密码加密**: bcrypt - 安全的密码哈希算法
- **配置管理**: python-dotenv - 环境变量管理

### 部署架构

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Nginx     │───▶│   Gunicorn  │───▶│   MySQL     │
│  (反向代理)  │    │  (WSGI服务器) │    │   (数据库)   │
└─────────────┘    └─────────────┘    └─────────────┘
```

## 组件和接口

### 核心模块设计

#### 1. 用户认证模块 (auth)

**职责**: 处理用户登录、注销、密码管理等认证相关功能

**主要组件**:
- `AuthManager`: 认证管理器
- `PasswordService`: 密码服务
- `SessionManager`: 会话管理器

**接口设计**:
```python
class AuthManager:
    def authenticate(username: str, password: str) -> User
    def logout(user_id: int) -> bool
    def change_password(user_id: int, old_password: str, new_password: str) -> bool
    def reset_password(user_id: int, new_password: str) -> bool
```

#### 2. 权限控制模块 (permissions)

**职责**: 实现基于角色的访问控制(RBAC)

**主要组件**:
- `RoleManager`: 角色管理器
- `PermissionChecker`: 权限检查器
- `DataFilter`: 数据过滤器

**接口设计**:
```python
class PermissionChecker:
    def has_permission(user: User, resource: str, action: str) -> bool
    def filter_data(user: User, data: List, resource_type: str) -> List
    def can_access_customer(user: User, customer_id: int) -> bool
```

#### 3. 基础管理模块 (basic_mgmt)

**职责**: 管理用户账号、部门、渠道等基础数据

**主要组件**:
- `UserService`: 用户管理服务
- `DepartmentService`: 部门管理服务
- `ChannelService`: 渠道管理服务
- `SearchService`: 简码搜索服务

**接口设计**:
```python
class UserService:
    def create_user(user_data: dict) -> User
    def update_user(user_id: int, user_data: dict) -> bool
    def deactivate_user(user_id: int) -> bool
    def search_users(query: str) -> List[User]

class ChannelService:
    def create_channel(channel_data: dict) -> Channel
    def import_channels_from_excel(file_path: str) -> ImportResult
    def search_channels(query: str) -> List[Channel]
```

#### 4. 客户登记模块 (registration)

**职责**: 处理客户信息的登记、查询、修改等核心业务功能

**主要组件**:
- `CustomerService`: 客户信息服务
- `RegistrationValidator`: 登记数据验证器
- `FollowUpService`: 跟进管理服务

**接口设计**:
```python
class CustomerService:
    def register_customer(customer_data: dict, operator: User) -> Customer
    def update_customer(customer_id: int, customer_data: dict, operator: User) -> bool
    def get_customers_by_permission(user: User, filters: dict) -> List[Customer]
    def search_customers(user: User, query: str) -> List[Customer]

class FollowUpService:
    def update_follow_up(customer_id: int, content: str, operator: User) -> bool
    def get_follow_up_history(customer_id: int) -> List[FollowUpRecord]
```

#### 5. 统计报表模块 (reports)

**职责**: 生成各种统计报表和数据分析

**主要组件**:
- `ReportGenerator`: 报表生成器
- `StatisticsCalculator`: 统计计算器
- `DataAggregator`: 数据聚合器

**接口设计**:
```python
class ReportGenerator:
    def generate_group_report(user: User, group_id: int, date_range: tuple) -> Report
    def generate_consultant_report(user: User, consultant_id: int, date_range: tuple) -> Report
    def get_dashboard_stats(user: User) -> dict
```

### 数据访问层设计

#### Repository模式实现

```python
class BaseRepository:
    def create(self, entity: BaseModel) -> BaseModel
    def get_by_id(self, id: int) -> BaseModel
    def update(self, id: int, data: dict) -> bool
    def delete(self, id: int) -> bool
    def find_by_criteria(self, criteria: dict) -> List[BaseModel]

class CustomerRepository(BaseRepository):
    def find_by_consultant(self, consultant_id: int) -> List[Customer]
    def find_by_department(self, department_id: int) -> List[Customer]
    def find_pending_follow_up(self) -> List[Customer]
```

### API接口设计

#### RESTful API端点

```
# 认证相关
POST   /api/auth/login          # 用户登录
POST   /api/auth/logout         # 用户登出
POST   /api/auth/change-password # 修改密码

# 用户管理
GET    /api/users               # 获取用户列表
POST   /api/users               # 创建用户
PUT    /api/users/{id}          # 更新用户
DELETE /api/users/{id}          # 删除用户

# 客户管理
GET    /api/customers           # 获取客户列表
POST   /api/customers           # 创建客户记录
PUT    /api/customers/{id}      # 更新客户信息
GET    /api/customers/{id}      # 获取客户详情

# 跟进管理
POST   /api/customers/{id}/follow-up  # 添加跟进记录
GET    /api/customers/{id}/follow-up  # 获取跟进历史

# 统计报表
GET    /api/reports/dashboard    # 仪表板统计
GET    /api/reports/group/{id}   # 小组报表
GET    /api/reports/consultant/{id} # 咨询师报表
```

## 数据模型

### 核心实体关系图

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    User     │────│ Department  │    │   Channel   │
│             │    │             │    │             │
│ - id        │    │ - id        │    │ - id        │
│ - username  │    │ - name      │    │ - name      │
│ - role      │    │ - is_active │    │ - category  │
│ - dept_id   │    └─────────────┘    │ - is_active │
└─────────────┘                       └─────────────┘
       │                                     │
       │                                     │
       ▼                                     ▼
┌─────────────┐                       ┌─────────────┐
│  Customer   │◄──────────────────────│ Registration│
│             │                       │             │
│ - id        │                       │ - customer_id│
│ - card_no   │                       │ - channel_id │
│ - onsite_id │                       │ - content    │
│ - online_id │                       │ - visit_date │
└─────────────┘                       │ - created_at │
       │                              │ - created_by │
       │                              └─────────────┘
       ▼
┌─────────────┐
│  FollowUp   │
│             │
│ - id        │
│ - customer_id│
│ - content   │
│ - follow_time│
│ - follow_by │
└─────────────┘
```

### 数据库表结构设计

#### 用户相关表

**users 表 - 用户信息**
```sql
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    salt VARCHAR(32) NOT NULL,
    real_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'director', 'manager', 'online_consultant', 'onsite_consultant') NOT NULL,
    department_id INT,
    simple_code VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_department (department_id),
    INDEX idx_simple_code (simple_code)
);
```

**departments 表 - 部门信息**
```sql
CREATE TABLE departments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_name (name)
);
```

#### 业务数据表

**channels 表 - 渠道信息**
```sql
CREATE TABLE channels (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(200) NOT NULL,
    category VARCHAR(100),
    simple_code VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_name (name),
    INDEX idx_category (category),
    INDEX idx_simple_code (simple_code)
);
```

**customers 表 - 客户信息**
```sql
CREATE TABLE customers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    card_number VARCHAR(18) NOT NULL,
    onsite_consultant_id INT NOT NULL,
    online_consultant_id INT NOT NULL,
    channel_id INT NOT NULL,
    inquiry_content TEXT,
    last_visit_date DATE,
    follow_up_note TEXT,
    follow_up_time TIMESTAMP NULL,
    follow_up_by INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_card_number (card_number),
    INDEX idx_onsite_consultant (onsite_consultant_id),
    INDEX idx_online_consultant (online_consultant_id),
    INDEX idx_channel (channel_id),
    INDEX idx_created_by (created_by),
    INDEX idx_last_visit_date (last_visit_date),
    FOREIGN KEY (onsite_consultant_id) REFERENCES users(id),
    FOREIGN KEY (online_consultant_id) REFERENCES users(id),
    FOREIGN KEY (channel_id) REFERENCES channels(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (follow_up_by) REFERENCES users(id)
);
```

#### 扩展功能表

**onsite_groups 表 - 现场小组**
```sql
CREATE TABLE onsite_groups (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_name (name)
);
```

**onsite_group_members 表 - 现场小组成员关系**
```sql
CREATE TABLE onsite_group_members (
    id INT PRIMARY KEY AUTO_INCREMENT,
    group_id INT NOT NULL,
    user_id INT NOT NULL,
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_group_user (group_id, user_id),
    FOREIGN KEY (group_id) REFERENCES onsite_groups(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

**business_data 表 - 业务数据（到院信息和消费金额）**
```sql
CREATE TABLE business_data (
    id INT PRIMARY KEY AUTO_INCREMENT,
    card_number VARCHAR(18) NOT NULL,
    visit_date DATE NOT NULL,
    consumption_amount DECIMAL(10,2) DEFAULT 0,
    data_source VARCHAR(100),
    imported_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_card_number (card_number),
    INDEX idx_visit_date (visit_date)
);
```

#### 系统配置表

**system_settings 表 - 系统设置**
```sql
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_key (setting_key)
);
```

## 错误处理

### 错误分类和处理策略

#### 1. 业务逻辑错误
- **验证错误**: 数据格式不正确、必填字段缺失
- **权限错误**: 用户无权限访问资源
- **业务规则错误**: 违反业务规则的操作

**处理策略**:
```python
class BusinessException(Exception):
    def __init__(self, message: str, error_code: str = None):
        self.message = message
        self.error_code = error_code
        super().__init__(message)

class ValidationError(BusinessException):
    pass

class PermissionError(BusinessException):
    pass
```

#### 2. 系统错误
- **数据库连接错误**: 数据库不可用或连接超时
- **文件操作错误**: 文件读写失败
- **网络错误**: 外部服务调用失败

**处理策略**:
```python
@app.errorhandler(500)
def internal_error(error):
    db.session.rollback()
    logger.error(f"Internal server error: {str(error)}")
    return render_template('errors/500.html'), 500

@app.errorhandler(404)
def not_found_error(error):
    return render_template('errors/404.html'), 404
```

#### 3. 全局异常处理

```python
class ErrorHandler:
    @staticmethod
    def handle_business_error(error: BusinessException):
        return {
            'success': False,
            'message': error.message,
            'error_code': error.error_code
        }
    
    @staticmethod
    def handle_system_error(error: Exception):
        logger.exception("System error occurred")
        return {
            'success': False,
            'message': '系统内部错误，请稍后重试',
            'error_code': 'SYSTEM_ERROR'
        }
```

## 测试策略

### 测试层次结构

#### 1. 单元测试
- **模型测试**: 测试数据模型的验证逻辑
- **服务测试**: 测试业务逻辑服务
- **工具测试**: 测试辅助工具函数

```python
class TestCustomerService(unittest.TestCase):
    def setUp(self):
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
    
    def test_register_customer_success(self):
        # 测试客户登记成功场景
        pass
    
    def test_register_customer_duplicate_card(self):
        # 测试重复卡号场景
        pass
```

#### 2. 集成测试
- **API测试**: 测试REST API端点
- **数据库测试**: 测试数据访问层
- **权限测试**: 测试权限控制逻辑

```python
class TestCustomerAPI(unittest.TestCase):
    def test_create_customer_api(self):
        with self.client:
            response = self.client.post('/api/customers', 
                                      json=self.customer_data,
                                      headers=self.auth_headers)
            self.assertEqual(response.status_code, 201)
```

#### 3. 端到端测试
- **用户流程测试**: 测试完整的用户操作流程
- **权限流程测试**: 测试不同角色的权限控制
- **数据一致性测试**: 测试数据的完整性和一致性

### 测试数据管理

```python
class TestDataFactory:
    @staticmethod
    def create_test_user(role='online_consultant'):
        return User(
            username=f'test_{role}_{uuid.uuid4().hex[:8]}',
            password_hash=generate_password_hash('test123'),
            real_name=f'测试{role}',
            role=role
        )
    
    @staticmethod
    def create_test_customer():
        return Customer(
            card_number=f'{random.randint(1000000000, 9999999999)}',
            inquiry_content='测试咨询内容'
        )
```

### 性能测试

#### 1. 负载测试
- **并发用户测试**: 模拟多用户同时访问
- **数据量测试**: 测试大数据量下的性能
- **长时间运行测试**: 测试系统稳定性

#### 2. 性能监控
```python
class PerformanceMonitor:
    @staticmethod
    def monitor_database_query(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            if execution_time > 1.0:  # 超过1秒记录警告
                logger.warning(f"Slow query detected: {func.__name__} took {execution_time:.2f}s")
            return result
        return wrapper
```

## 安全考虑

### 认证和授权

#### 1. 密码安全
```python
class PasswordService:
    @staticmethod
    def hash_password(password: str) -> tuple:
        salt = secrets.token_hex(16)
        password_hash = bcrypt.hashpw(
            (password + salt).encode('utf-8'), 
            bcrypt.gensalt()
        ).decode('utf-8')
        return password_hash, salt
    
    @staticmethod
    def verify_password(password: str, stored_hash: str, salt: str) -> bool:
        return bcrypt.checkpw(
            (password + salt).encode('utf-8'),
            stored_hash.encode('utf-8')
        )
```

#### 2. 会话管理
```python
class SessionConfig:
    PERMANENT_SESSION_LIFETIME = timedelta(hours=8)
    SESSION_COOKIE_SECURE = True  # 生产环境启用
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
```

#### 3. CSRF保护
```python
from flask_wtf.csrf import CSRFProtect

csrf = CSRFProtect()

@app.before_request
def csrf_protect():
    if request.method == "POST":
        csrf.protect()
```

### 数据安全

#### 1. 输入验证
```python
class InputValidator:
    @staticmethod
    def validate_card_number(card_number: str) -> bool:
        if not card_number.isdigit():
            return False
        if len(card_number) < 10 or len(card_number) > 18:
            return False
        return True
    
    @staticmethod
    def sanitize_text_input(text: str) -> str:
        # 清理HTML标签和特殊字符
        return bleach.clean(text, tags=[], strip=True)
```

#### 2. SQL注入防护
```python
# 使用SQLAlchemy的参数化查询
def get_customers_by_consultant(consultant_id: int):
    return Customer.query.filter(
        Customer.onsite_consultant_id == consultant_id
    ).all()
```

#### 3. 数据访问控制
```python
class DataAccessControl:
    @staticmethod
    def filter_customers_by_permission(user: User, customers: List[Customer]) -> List[Customer]:
        if user.role == 'admin' or user.role == 'director':
            return customers
        elif user.role == 'manager':
            # 只返回同部门的数据
            return [c for c in customers if c.online_consultant.department_id == user.department_id]
        elif user.role == 'online_consultant':
            # 只返回自己登记的数据
            return [c for c in customers if c.created_by == user.id]
        elif user.role == 'onsite_consultant':
            # 只返回分配给自己的数据
            return [c for c in customers if c.onsite_consultant_id == user.id]
        else:
            return []
```

### 审计日志

```python
class AuditLogger:
    @staticmethod
    def log_user_action(user_id: int, action: str, resource: str, details: dict = None):
        log_entry = {
            'user_id': user_id,
            'action': action,
            'resource': resource,
            'details': details,
            'timestamp': datetime.now(),
            'ip_address': request.remote_addr,
            'user_agent': request.user_agent.string
        }
        logger.info(f"User action: {json.dumps(log_entry, default=str)}")
```

## 部署和运维

### 部署配置

#### 1. 环境配置
```bash
# .env 文件示例
FLASK_ENV=production
SECRET_KEY=your-production-secret-key
SYSTEM_DB_HOST=localhost
SYSTEM_DB_PORT=3306
SYSTEM_DB_USER=old_customer_user
SYSTEM_DB_PASSWORD=secure_password
SYSTEM_DB_NAME=Old_Customer_System
```

#### 2. Gunicorn配置
```python
# gunicorn.conf.py
bind = "127.0.0.1:5000"
workers = 4
worker_class = "sync"
worker_connections = 1000
timeout = 30
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
```

#### 3. Nginx配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    
    location /static {
        alias /path/to/app/static;
        expires 1y;
    }
}
```

### 监控和维护

#### 1. 健康检查
```python
@app.route('/health')
def health_check():
    try:
        # 检查数据库连接
        db.session.execute('SELECT 1')
        return {'status': 'healthy', 'timestamp': datetime.now().isoformat()}
    except Exception as e:
        return {'status': 'unhealthy', 'error': str(e)}, 500
```

#### 2. 日志管理
```python
import logging
from logging.handlers import RotatingFileHandler

def setup_logging(app):
    if not app.debug:
        file_handler = RotatingFileHandler(
            'logs/old_customer_system.log',
            maxBytes=10240000,  # 10MB
            backupCount=10
        )
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
```

#### 3. 数据备份策略
```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/old_customer_system"
DB_NAME="Old_Customer_System"

mysqldump -u backup_user -p$BACKUP_PASSWORD $DB_NAME > $BACKUP_DIR/backup_$DATE.sql
gzip $BACKUP_DIR/backup_$DATE.sql

# 保留最近30天的备份
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +30 -delete
```

这个设计文档提供了系统的完整技术架构，包括组件设计、数据模型、安全策略和部署方案，为后续的开发实施提供了详细的技术指导。