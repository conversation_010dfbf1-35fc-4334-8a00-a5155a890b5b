#!/usr/bin/env python3
"""
简化的启动脚本
"""

import os
import sys

def main():
    try:
        # 设置环境变量
        os.environ.setdefault('FLASK_ENV', 'development')
        
        print("正在导入应用...")
        from app import create_app
        
        print("正在创建应用实例...")
        app = create_app()
        
        print("启动RFM报表处理系统...")
        print("访问地址: http://localhost:5000")
        print("按 Ctrl+C 停止服务")
        
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True,
            threaded=True
        )
        
    except ImportError as e:
        print(f"导入错误: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
