@echo off
chcp 65001 >nul
cd /d "%~dp0"

echo ================================================
echo   RFM报表处理系统启动器
echo ================================================

echo 当前目录: %CD%
echo.

:: 详细检查文件是否存在
echo 正在检查必要文件...

if not exist "venv" (
    echo [错误] venv 目录不存在！
    echo 当前目录: %CD%
    dir
    pause
    exit /b 1
)

if not exist "venv\Scripts" (
    echo [错误] venv\Scripts 目录不存在！
    echo venv 目录内容:
    dir venv
    pause
    exit /b 1
)

if not exist "venv\Scripts\python.exe" (
    echo [错误] python.exe 不存在！
    echo venv\Scripts 目录内容:
    dir "venv\Scripts"
    pause
    exit /b 1
)

if not exist "start_server.py" (
    echo [错误] start_server.py 不存在！
    echo 当前目录内容:
    dir
    pause
    exit /b 1
)

echo [成功] 所有必要文件都存在
echo 虚拟环境: %CD%\venv\Scripts\python.exe
echo 启动脚本: %CD%\start_server.py
echo.

echo 正在启动系统...
echo.

:: 使用虚拟环境启动系统
"%CD%\venv\Scripts\python.exe" "%CD%\start_server.py"

if %ERRORLEVEL% neq 0 (
    echo.
    echo [错误] 启动失败，错误代码: %ERRORLEVEL%
)

echo.
echo 系统已停止运行
pause
