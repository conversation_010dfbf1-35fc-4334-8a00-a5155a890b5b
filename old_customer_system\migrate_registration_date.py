#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
客户登记时间字段数据库迁移脚本
"""

import os
import sys
import mysql.connector
from datetime import datetime
from config import Config
from sqlalchemy.engine.url import make_url

def get_db_connection():
    """获取数据库连接"""
    try:
        # 从SQLALCHEMY_DATABASE_URI解析连接参数
        db_url = make_url(Config.SQLALCHEMY_DATABASE_URI)
        
        # 构建连接参数，包含默认值处理
        connection_params = {
            'host': db_url.host,
            'port': db_url.port or 3306,  # MySQL默认端口
            'user': db_url.username,
            'password': db_url.password or '',  # 处理空密码情况
            'database': db_url.database,
            'charset': 'utf8mb4'
        }
        
        connection = mysql.connector.connect(**connection_params)
        return connection
    except mysql.connector.Error as e:
        print(f"数据库连接失败: {e}")
        return None

def check_column_exists(cursor, table_name, column_name):
    """检查字段是否已存在"""
    # 从SQLALCHEMY_DATABASE_URI解析数据库名
    db_url = make_url(Config.SQLALCHEMY_DATABASE_URI)
    database_name = db_url.database
    
    # 检查字段是否存在的SQL查询
    check_sql = """
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = %s 
      AND TABLE_NAME = %s 
      AND COLUMN_NAME = %s
    """
    cursor.execute(check_sql, (database_name, table_name, column_name))
    return cursor.fetchone() is not None

def migrate_registration_date():
    """执行登记时间字段迁移"""
    connection = get_db_connection()
    if not connection:
        return False
    
    try:
        cursor = connection.cursor()
        
        print("开始执行客户登记时间字段迁移...")
        
        # 检查字段是否已存在
        if check_column_exists(cursor, 'customers', 'registration_date'):
            print("registration_date字段已存在，跳过迁移")
            return True
        
        # 1. 添加registration_date字段
        print("1. 添加registration_date字段...")
        cursor.execute("""
            ALTER TABLE customers 
            ADD COLUMN registration_date DATE NOT NULL DEFAULT (CURRENT_DATE) 
            AFTER last_visit_date
        """)
        
        # 2. 为现有记录设置登记日期...
        print("2. 为现有记录设置登记日期...")
        cursor.execute("""
            UPDATE customers 
            SET registration_date = COALESCE(DATE(created_at), '1970-01-01') 
            WHERE registration_date = '0000-00-00'
        """)
        updated_rows = cursor.rowcount
        print(f"   更新了 {updated_rows} 条记录")
        
        # 3. 创建索引
        print("3. 创建索引...")
        cursor.execute("""
            CREATE INDEX idx_customers_registration_date ON customers(registration_date)
        """)
        
        # 4. 验证迁移结果
        print("4. 验证迁移结果...")
        cursor.execute("""
            SELECT 
                COUNT(*) as total_customers,
                COUNT(CASE WHEN registration_date IS NOT NULL THEN 1 END) as with_registration_date,
                MIN(registration_date) as earliest_registration,
                MAX(registration_date) as latest_registration
            FROM customers
        """)
        
        result = cursor.fetchone()
        if result:
            total, with_date, earliest, latest = result
            print(f"   总客户数: {total}")
            print(f"   有登记日期的客户数: {with_date}")
            print(f"   最早登记日期: {earliest}")
            print(f"   最晚登记日期: {latest}")
        
        # 提交事务
        connection.commit()
        print("✅ 客户登记时间字段迁移完成！")
        return True
        
    except mysql.connector.Error as e:
        print(f"❌ 迁移失败: {e}")
        connection.rollback()
        return False
    
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

def rollback_migration():
    """回滚迁移（删除registration_date字段）"""
    connection = get_db_connection()
    if not connection:
        return False
    
    try:
        cursor = connection.cursor()
        
        print("开始回滚客户登记时间字段迁移...")
        
        # 检查字段是否存在
        if not check_column_exists(cursor, 'customers', 'registration_date'):
            print("registration_date字段不存在，无需回滚")
            return True
        
        # 删除索引
        print("1. 删除索引...")
        try:
            cursor.execute("DROP INDEX idx_customers_registration_date ON customers")
        except mysql.connector.Error:
            print("   索引不存在或已删除")
        
        # 删除字段
        print("2. 删除registration_date字段...")
        cursor.execute("ALTER TABLE customers DROP COLUMN registration_date")
        
        connection.commit()
        print("✅ 迁移回滚完成！")
        return True
        
    except mysql.connector.Error as e:
        print(f"❌ 回滚失败: {e}")
        connection.rollback()
        return False
    
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == 'rollback':
        rollback_migration()
    else:
        migrate_registration_date()