#!/usr/bin/env python3
"""
检查端口占用情况
"""
import socket
import subprocess
import sys

def check_port(host, port):
    """检查端口是否被占用"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0  # 0表示端口被占用
    except Exception:
        return False

def get_port_process(port):
    """获取占用端口的进程信息"""
    try:
        if sys.platform == "win32":
            # Windows系统
            result = subprocess.run(
                ['netstat', '-ano'], 
                capture_output=True, 
                text=True, 
                encoding='gbk'
            )
            lines = result.stdout.split('\n')
            for line in lines:
                if f':{port}' in line and 'LISTENING' in line:
                    parts = line.split()
                    if len(parts) >= 5:
                        pid = parts[-1]
                        # 获取进程名
                        try:
                            proc_result = subprocess.run(
                                ['tasklist', '/FI', f'PID eq {pid}', '/FO', 'CSV'],
                                capture_output=True,
                                text=True,
                                encoding='gbk'
                            )
                            proc_lines = proc_result.stdout.split('\n')
                            if len(proc_lines) > 1:
                                proc_name = proc_lines[1].split(',')[0].strip('"')
                                return f"PID {pid} ({proc_name})"
                        except:
                            return f"PID {pid}"
        else:
            # Linux/Mac系统
            result = subprocess.run(
                ['lsof', '-i', f':{port}'], 
                capture_output=True, 
                text=True
            )
            if result.stdout:
                lines = result.stdout.split('\n')
                if len(lines) > 1:
                    parts = lines[1].split()
                    if len(parts) >= 2:
                        return f"{parts[0]} (PID {parts[1]})"
    except Exception as e:
        return f"无法获取进程信息: {e}"
    
    return "未知进程"

def main():
    """主函数"""
    print("端口占用检查工具")
    print("=" * 40)
    
    # 检查常用端口
    ports_to_check = [5000, 5001, 5002, 5003, 8000, 8080]
    host = '127.0.0.1'
    
    available_ports = []
    occupied_ports = []
    
    for port in ports_to_check:
        if check_port(host, port):
            process_info = get_port_process(port)
            occupied_ports.append((port, process_info))
            print(f"❌ 端口 {port}: 被占用 - {process_info}")
        else:
            available_ports.append(port)
            print(f"✅ 端口 {port}: 可用")
    
    print("\n" + "=" * 40)
    print("总结:")
    print(f"可用端口: {available_ports}")
    print(f"被占用端口: {[p[0] for p in occupied_ports]}")
    
    if available_ports:
        print(f"\n建议使用端口: {available_ports[0]}")
        print(f"启动命令示例:")
        print(f"python -c \"from app import create_app; from config import get_config; app = create_app(get_config()); app.run(host='127.0.0.1', port={available_ports[0]}, debug=False)\"")
    else:
        print("\n⚠️ 所有常用端口都被占用")
        print("建议:")
        print("1. 关闭占用端口的程序")
        print("2. 使用其他端口号")
    
    # 如果端口5000被占用，提供解决方案
    if any(p[0] == 5000 for p in occupied_ports):
        print(f"\n🔧 端口5000被占用的解决方案:")
        print("1. 关闭占用进程（如果是不需要的程序）")
        print("2. 使用备用启动脚本: python start_alt_port.py")
        print("3. 手动指定其他端口启动")

if __name__ == '__main__':
    main()
