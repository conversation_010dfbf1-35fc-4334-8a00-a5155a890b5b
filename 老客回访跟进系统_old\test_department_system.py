#!/usr/bin/env python3
"""
部门管理系统测试脚本
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.user import User, Role, Department

def test_department_functionality():
    """测试部门管理功能"""
    print("=" * 50)
    print("测试部门管理功能")
    print("=" * 50)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 测试部门创建
            print("\n1. 测试部门创建...")
            test_dept = Department(
                department_name='测试部门',
                department_code='TEST',
                description='这是一个测试部门',
                sort_order=99
            )
            db.session.add(test_dept)
            db.session.commit()
            print(f"✓ 创建测试部门成功: {test_dept.department_name} (ID: {test_dept.id})")
            
            # 2. 测试部门查询
            print("\n2. 测试部门查询...")
            departments = Department.get_active_departments()
            print(f"✓ 查询到 {len(departments)} 个启用的部门:")
            for dept in departments:
                print(f"  - {dept.department_name} ({dept.department_code})")
            
            # 3. 测试用户与部门关联
            print("\n3. 测试用户与部门关联...")
            admin_user = User.query.filter_by(username='admin').first()
            if admin_user:
                print(f"✓ 管理员用户部门: {admin_user.department.department_name if admin_user.department else '无'}")
            else:
                print("⚠ 未找到管理员用户")
            
            # 4. 测试部门层级关系
            print("\n4. 测试部门层级关系...")
            parent_dept = Department.query.filter_by(department_code='ADMIN').first()
            if parent_dept:
                child_dept = Department(
                    department_name='子部门测试',
                    department_code='CHILD_TEST',
                    description='测试子部门',
                    parent_id=parent_dept.id,
                    sort_order=100
                )
                db.session.add(child_dept)
                db.session.commit()
                print(f"✓ 创建子部门成功: {child_dept.full_name}")
                
                # 测试获取子部门
                children = parent_dept.get_all_children()
                print(f"✓ {parent_dept.department_name} 有 {len(children)} 个子部门")
            
            # 5. 测试部门权限过滤
            print("\n5. 测试部门权限过滤...")
            if admin_user:
                # 测试管理员权限
                test_user = User.query.first()
                can_view = admin_user.can_view_user_data(test_user)
                print(f"✓ 管理员查看用户数据权限: {can_view}")
            
            # 6. 清理测试数据
            print("\n6. 清理测试数据...")
            test_departments = Department.query.filter(
                Department.department_code.in_(['TEST', 'CHILD_TEST'])
            ).all()
            for dept in test_departments:
                db.session.delete(dept)
            db.session.commit()
            print("✓ 测试数据清理完成")
            
            print("\n" + "=" * 50)
            print("部门管理功能测试完成 - 所有测试通过！")
            print("=" * 50)
            
        except Exception as e:
            print(f"\n❌ 测试过程中出现错误: {e}")
            db.session.rollback()
            raise

def test_user_creation_with_department():
    """测试使用部门ID创建用户"""
    print("\n" + "=" * 50)
    print("测试用户创建功能（使用部门ID）")
    print("=" * 50)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 获取测试所需的数据
            network_role = Role.query.filter_by(role_code='NETWORK_CONSULTANT').first()
            network_dept = Department.query.filter_by(department_code='NETWORK').first()
            admin_user = User.query.filter_by(username='admin').first()
            
            if not all([network_role, network_dept, admin_user]):
                print("❌ 缺少必要的测试数据")
                return
            
            # 创建测试用户
            test_user = User(
                username='test_network_user',
                real_name='测试网络咨询员',
                department_id=network_dept.id,
                role_id=network_role.id,
                created_by=admin_user.id
            )
            test_user.set_password('test123456')
            
            db.session.add(test_user)
            db.session.commit()
            
            print(f"✓ 创建测试用户成功:")
            print(f"  - 用户名: {test_user.username}")
            print(f"  - 真实姓名: {test_user.real_name}")
            print(f"  - 所属部门: {test_user.department.department_name}")
            print(f"  - 用户角色: {test_user.role.role_name}")
            
            # 测试用户权限
            can_view_self = test_user.can_view_user_data(test_user)
            can_view_admin = test_user.can_view_user_data(admin_user)
            print(f"✓ 用户权限测试:")
            print(f"  - 查看自己数据: {can_view_self}")
            print(f"  - 查看管理员数据: {can_view_admin}")
            
            # 清理测试用户
            db.session.delete(test_user)
            db.session.commit()
            print("✓ 测试用户清理完成")
            
            print("\n用户创建功能测试完成 - 所有测试通过！")
            
        except Exception as e:
            print(f"\n❌ 用户创建测试中出现错误: {e}")
            db.session.rollback()
            raise

def main():
    """主测试函数"""
    print("开始部门管理系统测试...")
    
    try:
        test_department_functionality()
        test_user_creation_with_department()
        
        print("\n🎉 所有测试完成！部门管理系统功能正常。")
        
    except Exception as e:
        print(f"\n💥 测试失败: {e}")
        return False
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)