# Chinese translations for WTForms.
# Copyright (C) 2020 WTForms Team
# This file is distributed under the same license as the WTForms project.
# <AUTHOR> <EMAIL>, 2020.
#
msgid ""
msgstr ""
"Project-Id-Version: WTForms 1.0.3\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2020-04-25 11:34-0700\n"
"PO-Revision-Date: 2012-01-31 13:03-0700\n"
"Last-Translator: Grey Li <<EMAIL>>\n"
"Language: zh\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=2; plural=(n != 1)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.8.0\n"

#: src/wtforms/validators.py:87
#, python-format
msgid "Invalid field name '%s'."
msgstr "'%s' 是无效的字段名。"

#: src/wtforms/validators.py:98
#, python-format
msgid "Field must be equal to %(other_name)s."
msgstr "字段必须和 %(other_name)s 相等。"

#: src/wtforms/validators.py:134
#, python-format
msgid "Field must be at least %(min)d character long."
msgid_plural "Field must be at least %(min)d characters long."
msgstr[0] "字段长度必须至少 %(min)d 个字符。"
msgstr[1] "字段长度必须至少 %(min)d 个字符。"

#: src/wtforms/validators.py:140
#, python-format
msgid "Field cannot be longer than %(max)d character."
msgid_plural "Field cannot be longer than %(max)d characters."
msgstr[0] "字段长度不能超过 %(max)d 个字符。"
msgstr[1] "字段长度不能超过 %(max)d 个字符。"

#: src/wtforms/validators.py:146
#, python-format
msgid "Field must be exactly %(max)d character long."
msgid_plural "Field must be exactly %(max)d characters long."
msgstr[0] "字段长度必须为 %(max)d 个字符。"
msgstr[1] "字段长度必须为 %(max)d 个字符。"

#: src/wtforms/validators.py:152
#, python-format
msgid "Field must be between %(min)d and %(max)d characters long."
msgstr "字段长度必须介于 %(min)d 到 %(max)d 个字符之间。"

#: src/wtforms/validators.py:197
#, python-format
msgid "Number must be at least %(min)s."
msgstr "数值必须大于 %(min)s。"

#: src/wtforms/validators.py:199
#, python-format
msgid "Number must be at most %(max)s."
msgstr "数值必须小于 %(max)s。"

#: src/wtforms/validators.py:201
#, python-format
msgid "Number must be between %(min)s and %(max)s."
msgstr "数值大小必须介于 %(min)s 到 %(max)s 之间。"

#: src/wtforms/validators.py:269 src/wtforms/validators.py:294
msgid "This field is required."
msgstr "该字段是必填字段。"

#: src/wtforms/validators.py:327
msgid "Invalid input."
msgstr "无效的输入。"

#: src/wtforms/validators.py:387
msgid "Invalid email address."
msgstr "无效的 Email 地址。"

#: src/wtforms/validators.py:423
msgid "Invalid IP address."
msgstr "无效的 IP 地址。"

#: src/wtforms/validators.py:466
msgid "Invalid Mac address."
msgstr "无效的 MAC 地址。"

#: src/wtforms/validators.py:501
msgid "Invalid URL."
msgstr "无效的 URL。"

#: src/wtforms/validators.py:522
msgid "Invalid UUID."
msgstr "无效的 UUID。"

#: src/wtforms/validators.py:553
#, python-format
msgid "Invalid value, must be one of: %(values)s."
msgstr "无效的值，必须是下列之一: %(values)s。"

#: src/wtforms/validators.py:588
#, python-format
msgid "Invalid value, can't be any of: %(values)s."
msgstr "无效的值，不能是下列任何一个: %(values)s。"

#: src/wtforms/csrf/core.py:96
msgid "Invalid CSRF Token."
msgstr "无效的 CSRF 验证令牌。"

#: src/wtforms/csrf/session.py:63
msgid "CSRF token missing."
msgstr "缺失 CSRF 验证令牌。"

#: src/wtforms/csrf/session.py:71
msgid "CSRF failed."
msgstr "CSRF 验证失败。"

#: src/wtforms/csrf/session.py:76
msgid "CSRF token expired."
msgstr "CSRF 验证令牌过期。"

#: src/wtforms/fields/core.py:534
msgid "Invalid Choice: could not coerce."
msgstr "选择无效：无法转化类型。"

#: src/wtforms/fields/core.py:538
msgid "Choices cannot be None."
msgstr "选择不能是空值。"

#: src/wtforms/fields/core.py:545
msgid "Not a valid choice."
msgstr "不是有效的选择。"

#: src/wtforms/fields/core.py:573
msgid "Invalid choice(s): one or more data inputs could not be coerced."
msgstr "选择无效：至少一个数据输入无法被转化类型。"

#: src/wtforms/fields/core.py:584
#, python-format
msgid "'%(value)s' is not a valid choice for this field."
msgstr "“%(value)s” 对该字段而言是无效选项。"

#: src/wtforms/fields/core.py:679 src/wtforms/fields/core.py:689
msgid "Not a valid integer value."
msgstr "不是有效的整数。"

#: src/wtforms/fields/core.py:760
msgid "Not a valid decimal value."
msgstr "不是有效的小数。"

#: src/wtforms/fields/core.py:788
msgid "Not a valid float value."
msgstr "不是有效的浮点数。"

#: src/wtforms/fields/core.py:853
msgid "Not a valid datetime value."
msgstr "不是有效的日期与时间值。"

#: src/wtforms/fields/core.py:871
msgid "Not a valid date value."
msgstr "不是有效的日期值。"

#: src/wtforms/fields/core.py:889
msgid "Not a valid time value."
msgstr "不是有效的时间值。"
