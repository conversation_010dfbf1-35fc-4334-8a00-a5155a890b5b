@echo off
chcp 65001 >nul
echo ===================================
echo 老客回访与跟进系统 - 快速安装脚本
echo ===================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ 检测到Python环境

:: 升级pip
echo.
echo 正在升级pip...
python -m pip install --upgrade pip

:: 安装核心依赖
echo.
echo 正在安装核心依赖包...
python -m pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo ⚠️ 使用默认源安装失败，尝试使用国内镜像...
    python -m pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
    
    if errorlevel 1 (
        echo ❌ 依赖包安装失败
        pause
        exit /b 1
    )
)

echo.
echo ✅ 核心依赖包安装成功！

:: 询问是否安装可选依赖
echo.
set /p install_optional="是否安装可选依赖包？(y/n): "
if /i "%install_optional%"=="y" (
    echo 正在安装可选依赖包...
    python -m pip install -r requirements-optional.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
    if errorlevel 1 (
        echo ⚠️ 部分可选依赖包安装失败，但不影响核心功能
    ) else (
        echo ✅ 可选依赖包安装成功！
    )
)

echo.
echo ===================================
echo 🎉 安装完成！
echo ===================================
echo.
echo 下一步操作:
echo 1. 配置数据库连接 (编辑 config.py)
echo 2. 初始化数据库: python init_db.py
echo 3. 启动系统: python run.py
echo.
echo 默认访问地址: http://localhost:5000
echo 默认管理员账号: admin / admin123
echo.
pause
