{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-eye me-2"></i>参照表详情</h2>
                <div>
                    <a href="{{ url_for('mapping.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>返回列表
                    </a>
                    {% if not template.is_active %}
                        <form method="POST" action="{{ url_for('mapping.activate_template', template_id=template.id) }}" 
                              style="display: inline;">
                            <button type="submit" class="btn btn-success" 
                                    onclick="return confirm('确定要激活此参照表吗？')">
                                <i class="fas fa-play me-1"></i>激活参照表
                            </button>
                        </form>
                    {% endif %}
                </div>
            </div>

            <!-- 模板信息 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle me-2"></i>模板信息</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>模板名称：</strong></td>
                                    <td>{{ template.template_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>状态：</strong></td>
                                    <td>
                                        {% if template.is_active %}
                                            <span class="badge bg-success">已启用</span>
                                        {% else %}
                                            <span class="badge bg-secondary">未启用</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>创建者：</strong></td>
                                    <td>{{ template.creator.username }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>创建时间：</strong></td>
                                    <td>{{ template.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>更新时间：</strong></td>
                                    <td>{{ template.updated_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>描述：</strong></td>
                                    <td>{{ template.description or '无描述' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 映射统计 -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-primary">
                                <i class="fas fa-layer-group me-2"></i>一级分类映射
                            </h5>
                            <h3 class="text-primary">{{ level1_mappings|length }}</h3>
                            <p class="card-text">个映射关系</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-success">
                                <i class="fas fa-tags me-2"></i>二级分类映射
                            </h5>
                            <h3 class="text-success">{{ level2_mappings|length }}</h3>
                            <p class="card-text">个映射关系</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-info">
                                <i class="fas fa-tag me-2"></i>三级分类映射
                            </h5>
                            <h3 class="text-info">{{ level3_mappings|length }}</h3>
                            <p class="card-text">个映射关系</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 映射详情 -->
            <div class="row">
                <!-- 一级分类映射 -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-layer-group me-2"></i>一级分类映射</h6>
                        </div>
                        <div class="card-body">
                            {% if level1_mappings %}
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>原始值</th>
                                                <th>映射值</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for mapping in level1_mappings %}
                                            <tr>
                                                <td>{{ mapping.original_value }}</td>
                                                <td><span class="badge bg-primary">{{ mapping.mapped_value }}</span></td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="text-center text-muted py-3">
                                    <i class="fas fa-inbox fa-2x mb-2"></i>
                                    <p>暂无映射关系</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- 二级分类映射 -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-tags me-2"></i>二级分类映射</h6>
                        </div>
                        <div class="card-body">
                            {% if level2_mappings %}
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>原始值</th>
                                                <th>映射值</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for mapping in level2_mappings %}
                                            <tr>
                                                <td>{{ mapping.original_value }}</td>
                                                <td><span class="badge bg-success">{{ mapping.mapped_value }}</span></td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="text-center text-muted py-3">
                                    <i class="fas fa-inbox fa-2x mb-2"></i>
                                    <p>暂无映射关系</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- 三级分类映射 -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-tag me-2"></i>三级分类映射</h6>
                        </div>
                        <div class="card-body">
                            {% if level3_mappings %}
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>原始值</th>
                                                <th>映射值</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for mapping in level3_mappings %}
                                            <tr>
                                                <td>{{ mapping.original_value }}</td>
                                                <td><span class="badge bg-info">{{ mapping.mapped_value }}</span></td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="text-center text-muted py-3">
                                    <i class="fas fa-inbox fa-2x mb-2"></i>
                                    <p>暂无映射关系</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
