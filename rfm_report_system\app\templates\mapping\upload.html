{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-upload me-2"></i>上传参照表</h5>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            {{ form.template_name.label(class="form-label") }}
                            {{ form.template_name(class="form-control") }}
                            {% if form.template_name.errors %}
                                <div class="text-danger">
                                    {% for error in form.template_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.description.label(class="form-label") }}
                            {{ form.description(class="form-control", rows="3", placeholder="可选：描述此参照表的用途和特点") }}
                            {% if form.description.errors %}
                                <div class="text-danger">
                                    {% for error in form.description.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-4">
                            {{ form.excel_file.label(class="form-label") }}
                            {{ form.excel_file(class="form-control", accept=".xlsx,.xls") }}
                            {% if form.excel_file.errors %}
                                <div class="text-danger">
                                    {% for error in form.excel_file.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                支持 .xlsx 和 .xls 格式，请确保文件格式符合要求
                            </div>
                            <div class="alert alert-warning mt-2">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>重要提示：</strong>上传前请确保Excel文件已关闭，否则可能导致上传失败。
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('mapping.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>返回
                            </a>
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- 格式说明 -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6><i class="fas fa-info-circle me-2"></i>Excel格式要求</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <h6>📋 必需的工作表：</h6>
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead>
                                        <tr>
                                            <th>工作表名称</th>
                                            <th>列名</th>
                                            <th>示例数据</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td rowspan="2"><code>一级分类映射</code></td>
                                            <td>原始值</td>
                                            <td>皮肤美容项目</td>
                                        </tr>
                                        <tr>
                                            <td>映射值</td>
                                            <td>皮肤</td>
                                        </tr>
                                        <tr>
                                            <td rowspan="2"><code>二级分类映射</code></td>
                                            <td>原始值</td>
                                            <td>衡力</td>
                                        </tr>
                                        <tr>
                                            <td>映射值</td>
                                            <td>肉毒素</td>
                                        </tr>
                                        <tr>
                                            <td rowspan="2"><code>三级分类映射</code></td>
                                            <td>原始值</td>
                                            <td>四代黄金微针</td>
                                        </tr>
                                        <tr>
                                            <td>映射值</td>
                                            <td>黄金微针</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            
                            <div class="alert alert-info mt-3">
                                <i class="fas fa-lightbulb me-2"></i>
                                <strong>提示：</strong>
                                <ul class="mb-0">
                                    <li>每个工作表必须包含 <code>原始值</code> 和 <code>映射值</code> 两列</li>
                                    <li>空行将被自动忽略</li>
                                    <li>建议先下载模板文件进行编辑</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
