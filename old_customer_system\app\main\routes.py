# -*- coding: utf-8 -*-
"""
主页面路由
"""

from flask import render_template, redirect, url_for
from flask_login import login_required, current_user
from app.main import bp

@bp.route('/')
@bp.route('/index')
def index():
    """首页"""
    # 检查系统是否已初始化
    from app.services.system_init_service import SystemInitService
    status = SystemInitService.check_system_status()
    
    if not status['system_initialized']:
        return redirect(url_for('init.check'))
    
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    return redirect(url_for('auth.login'))

@bp.route('/dashboard')
@login_required
def dashboard():
    """仪表板"""
    return render_template('main/dashboard.html', title='仪表板')