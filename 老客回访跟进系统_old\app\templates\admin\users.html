{% extends "base.html" %}

{% block title %}用户管理 - 老客回访与跟进系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h2><i class="fas fa-users"></i> 用户管理</h2>
            <a href="{{ url_for('admin.create_user') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> 新增用户
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>用户名</th>
                                <th>真实姓名</th>
                                <th>部门</th>
                                <th>角色</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>最后登录</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in users.items %}
                            <tr>
                                <td>{{ user.id }}</td>
                                <td>{{ user.username }}</td>
                                <td>{{ user.real_name }}</td>
                                <td>{{ user.department.department_name if user.department else '-' }}</td>
                                <td>
                                    <span class="badge bg-primary">{{ user.role.role_name }}</span>
                                </td>
                                <td>
                                    {% if user.is_active %}
                                        <span class="badge bg-success">正常</span>
                                    {% else %}
                                        <span class="badge bg-danger">停用</span>
                                    {% endif %}
                                </td>
                                <td>{{ user.created_time.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if user.last_login_time %}
                                        {{ user.last_login_time.strftime('%Y-%m-%d %H:%M') }}
                                    {% else %}
                                        <span class="text-muted">未登录</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        {% if user.id != current_user.id %}
                                        <button class="btn btn-outline-warning" title="重置密码">
                                            <i class="fas fa-key"></i>
                                        </button>
                                        {% if user.is_active %}
                                        <button class="btn btn-outline-danger" title="停用">
                                            <i class="fas fa-ban"></i>
                                        </button>
                                        {% else %}
                                        <button class="btn btn-outline-success" title="启用">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        {% endif %}
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="9" class="text-center text-muted">暂无用户数据</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                {% if users.pages > 1 %}
                <nav aria-label="用户列表分页">
                    <ul class="pagination justify-content-center">
                        {% if users.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.users', page=users.prev_num) }}">上一页</a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in users.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != users.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('admin.users', page=page_num) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">…</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if users.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.users', page=users.next_num) }}">下一页</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
