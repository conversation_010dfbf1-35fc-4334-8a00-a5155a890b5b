#!/usr/bin/env python3
"""测试RFM API接口"""

from app import create_app
from app.models import User
import json

def test_rfm_api():
    app = create_app('development')
    
    with app.test_client() as client:
        with app.app_context():
            # 获取管理员用户
            user = User.query.filter_by(username='gdbinghu').first()
            if not user:
                print("❌ 用户不存在")
                return
            
            print(f"👤 用户: {user.username}")
            print(f"🔑 是否管理员: {user.is_admin}")
            
            # 先登录
            login_response = client.post('/auth/login', data={
                'username': 'gdbinghu',
                'password': 'password123'
            }, follow_redirects=True)
            
            print(f"🔐 登录响应状态码: {login_response.status_code}")
            
            # 测试RFM API
            response = client.get('/rfm/api/available-tasks')
            print(f"📡 API响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.get_json()
                    print(f"✅ API响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                    
                    if data.get('success'):
                        tasks = data.get('tasks', [])
                        print(f"📋 任务数量: {len(tasks)}")
                        for task in tasks:
                            print(f"   - {task.get('name')} ({task.get('type')})")
                    else:
                        print(f"❌ API返回错误: {data.get('error')}")
                        
                except Exception as e:
                    print(f"❌ 解析JSON失败: {e}")
                    print(f"📄 原始响应: {response.get_data(as_text=True)}")
            else:
                print(f"❌ API错误响应: {response.get_data(as_text=True)}")

if __name__ == '__main__':
    test_rfm_api()
