#!/usr/bin/env python3
"""
测试映射摘要信息显示
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_mapping_summary():
    """测试映射摘要信息"""
    
    print("=== 测试映射摘要信息 ===")
    
    # 模拟处理器的映射配置
    mapping_options = {
        'use_level1_mapping': True,
        'use_level2_mapping': True,
        'use_level3_mapping': False,
        'use_field_mapping': True,
        'use_field_group': True
    }
    
    # 模拟映射数据
    level1_mappings = {'美容项目': '美容', '医疗项目': '医疗'}
    level2_mappings = {'皮肤美容项目': '皮肤美容', '注射美容项目': '注射美容'}
    level3_mappings = {}
    field_mappings = {'张三': '李四', '王五': '赵六'}
    field_group_mappings = {'李四': '王婆小组', '赵六': '李婆小组'}
    
    # 生成摘要信息
    mapping_info = {
        'use_level1_mapping': mapping_options.get('use_level1_mapping', False),
        'use_level2_mapping': mapping_options.get('use_level2_mapping', False),
        'use_level3_mapping': mapping_options.get('use_level3_mapping', False),
        'use_field_mapping': mapping_options.get('use_field_mapping', False),
        'use_field_group': mapping_options.get('use_field_group', False),
        'level1_mappings_count': len(level1_mappings),
        'level2_mappings_count': len(level2_mappings),
        'level3_mappings_count': len(level3_mappings),
        'field_mappings_count': len(field_mappings),
        'field_group_mappings_count': len(field_group_mappings)
    }
    
    print("映射配置:")
    for key, value in mapping_options.items():
        status = "✅ 已启用" if value else "❌ 未启用"
        print(f"  {key}: {status}")
    
    print("\n映射数量统计:")
    print(f"  一级分类映射: {mapping_info['level1_mappings_count']} 个")
    print(f"  二级分类映射: {mapping_info['level2_mappings_count']} 个")
    print(f"  三级分类映射: {mapping_info['level3_mappings_count']} 个")
    print(f"  现场映射: {mapping_info['field_mappings_count']} 个")
    print(f"  现场小组映射: {mapping_info['field_group_mappings_count']} 个")
    
    print("\n前端显示效果预览:")
    print("=" * 50)
    
    # 模拟前端显示
    print("📊 分类映射信息:")
    
    # 一级分类映射
    if mapping_info['use_level1_mapping']:
        print(f"  🟢 一级分类映射: 已启用 ({mapping_info['level1_mappings_count']} 个映射)")
    else:
        print(f"  ⚪ 一级分类映射: 未启用 (使用原始分类)")
    
    # 二级分类映射
    if mapping_info['use_level2_mapping']:
        print(f"  🟢 二级分类映射: 已启用 ({mapping_info['level2_mappings_count']} 个映射)")
    else:
        print(f"  ⚪ 二级分类映射: 未启用 (使用原始分类)")
    
    # 三级分类映射
    if mapping_info['use_level3_mapping']:
        print(f"  🟢 三级分类映射: 已启用 ({mapping_info['level3_mappings_count']} 个映射)")
    else:
        print(f"  ⚪ 三级分类映射: 未启用 (使用原始分类)")
    
    # 现场映射
    if mapping_info['use_field_mapping']:
        print(f"  🟡 现场映射: 已启用 ({mapping_info['field_mappings_count']} 个映射) - 添加最新现场列")
    else:
        print(f"  ⚪ 现场映射: 未启用")
    
    # 现场小组映射
    if mapping_info['use_field_group']:
        print(f"  🔴 现场小组映射: 已启用 ({mapping_info['field_group_mappings_count']} 个映射) - 添加现场小组列")
    else:
        print(f"  ⚪ 现场小组映射: 未启用")
    
    print("\n✅ 摘要信息测试完成")
    
    return mapping_info

def test_html_template():
    """测试HTML模板逻辑"""
    
    print("\n=== 测试HTML模板逻辑 ===")
    
    # 模拟摘要数据
    summary = {
        'mapping_info': {
            'use_level1_mapping': True,
            'use_level2_mapping': True,
            'use_level3_mapping': False,
            'use_field_mapping': True,
            'use_field_group': True,
            'level1_mappings_count': 2,
            'level2_mappings_count': 3,
            'level3_mappings_count': 0,
            'field_mappings_count': 2,
            'field_group_mappings_count': 2
        }
    }
    
    # 模拟Jinja2模板逻辑
    mapping_info = summary.get('mapping_info')
    
    if mapping_info:
        print("📋 HTML模板渲染效果:")
        
        # 分类映射部分
        print("\n🔄 分类映射信息:")
        
        # 一级分类
        if mapping_info.get('use_level1_mapping'):
            print(f"  [已启用] 一级分类映射 - {mapping_info.get('level1_mappings_count')} 个映射")
        else:
            print(f"  [未启用] 一级分类映射 - 使用原始分类")
        
        # 二级分类
        if mapping_info.get('use_level2_mapping'):
            print(f"  [已启用] 二级分类映射 - {mapping_info.get('level2_mappings_count')} 个映射")
        else:
            print(f"  [未启用] 二级分类映射 - 使用原始分类")
        
        # 三级分类
        if mapping_info.get('use_level3_mapping'):
            print(f"  [已启用] 三级分类映射 - {mapping_info.get('level3_mappings_count')} 个映射")
        else:
            print(f"  [未启用] 三级分类映射 - 使用原始分类")
        
        # 现场映射部分
        if mapping_info.get('use_field_mapping') or mapping_info.get('use_field_group'):
            print("\n🏢 现场映射信息:")
            
            if mapping_info.get('use_field_mapping'):
                print(f"  [已启用] 现场映射 - {mapping_info.get('field_mappings_count')} 个映射 (添加最新现场列)")
            
            if mapping_info.get('use_field_group'):
                print(f"  [已启用] 现场小组映射 - {mapping_info.get('field_group_mappings_count')} 个映射 (添加现场小组列)")
    
    print("\n✅ HTML模板测试完成")

if __name__ == "__main__":
    mapping_info = test_mapping_summary()
    test_html_template()
    
    print("\n🎉 所有测试完成！")
    print("现在处理摘要应该包含:")
    print("1. 一级分类映射信息")
    print("2. 二级分类映射信息") 
    print("3. 三级分类映射信息")
    print("4. 现场映射信息 ← 新增")
    print("5. 现场小组映射信息 ← 新增")
