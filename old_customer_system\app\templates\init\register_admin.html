{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card mt-5">
                <div class="card-header text-center">
                    <h4><i class="fas fa-user-shield me-2"></i>创建管理员账号</h4>
                    <p class="text-muted mb-0">请创建系统管理员账号</p>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            {{ form.username.label(class="form-label") }}
                            {{ form.username(class="form-control") }}
                            {% if form.username.errors %}
                                <div class="text-danger">
                                    {% for error in form.username.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.real_name.label(class="form-label") }}
                            {{ form.real_name(class="form-control") }}
                            {% if form.real_name.errors %}
                                <div class="text-danger">
                                    {% for error in form.real_name.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.password.label(class="form-label") }}
                            {{ form.password(class="form-control") }}
                            {% if form.password.errors %}
                                <div class="text-danger">
                                    {% for error in form.password.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.password2.label(class="form-label") }}
                            {{ form.password2(class="form-control") }}
                            {% if form.password2.errors %}
                                <div class="text-danger">
                                    {% for error in form.password2.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        {{ form.submit(class="btn btn-primary w-100") }}
                    </form>
                </div>
            </div>
            
            <div class="text-center mt-3">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    管理员账号拥有系统的最高权限，请妥善保管账号信息
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}