#!/usr/bin/env python3
"""
RFM报表处理系统简化安装脚本
"""

import os
import sys
import subprocess
import shutil

def main():
    print("=" * 50)
    print("  RFM报表处理系统安装程序")
    print("=" * 50)
    
    # 确保在正确的目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"当前目录: {os.getcwd()}")
    
    # 检查必要文件
    required_files = ['requirements.txt', '.env.example']
    for file in required_files:
        if not os.path.exists(file):
            print(f"错误: 找不到必要文件 {file}")
            return False
    
    print("\n1. 创建虚拟环境...")
    if os.path.exists('venv'):
        print("虚拟环境已存在")
    else:
        try:
            subprocess.run([sys.executable, '-m', 'venv', 'venv'], check=True)
            print("✓ 虚拟环境创建成功")
        except subprocess.CalledProcessError as e:
            print(f"✗ 创建虚拟环境失败: {e}")
            return False
    
    # 确定Python和pip路径
    if os.name == 'nt':  # Windows
        python_path = os.path.join('venv', 'Scripts', 'python.exe')
        pip_path = os.path.join('venv', 'Scripts', 'pip.exe')
    else:  # Linux/Mac
        python_path = os.path.join('venv', 'bin', 'python')
        pip_path = os.path.join('venv', 'bin', 'pip')
    
    print("\n2. 安装依赖包...")
    try:
        # 使用python -m pip安装，更可靠
        subprocess.run([python_path, '-m', 'pip', 'install', '--upgrade', 'pip'], 
                      check=True, capture_output=True)
        print("✓ pip升级成功")
    except subprocess.CalledProcessError:
        print("⚠ pip升级失败，继续安装依赖")
    
    try:
        subprocess.run([python_path, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                      check=True)
        print("✓ 依赖包安装成功")
    except subprocess.CalledProcessError as e:
        print(f"✗ 依赖包安装失败: {e}")
        return False
    
    print("\n3. 创建配置文件...")
    if os.path.exists('.env'):
        print("配置文件已存在")
    else:
        try:
            shutil.copy('.env.example', '.env')
            print("✓ 配置文件创建成功")
        except Exception as e:
            print(f"✗ 配置文件创建失败: {e}")
            return False
    
    print("\n4. 创建必要目录...")
    dirs = ['logs', 'uploads', 'uploads/results']
    for dir_name in dirs:
        os.makedirs(dir_name, exist_ok=True)
        print(f"✓ 目录 {dir_name}")
    
    print("\n5. 生成安全密钥...")
    try:
        import secrets
        import string
        alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
        secret_key = ''.join(secrets.choice(alphabet) for _ in range(32))
        
        # 读取并修改.env文件
        with open('.env', 'r', encoding='utf-8') as f:
            content = f.read()
        
        content = content.replace(
            'SECRET_KEY=your-very-secret-key-here',
            f'SECRET_KEY={secret_key}'
        )
        
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✓ 安全密钥生成成功")
    except Exception as e:
        print(f"⚠ 安全密钥生成失败: {e}")
    
    print("\n" + "=" * 50)
    print("安装完成！")
    print("=" * 50)
    
    print("\n后续步骤:")
    print("1. 编辑 .env 文件，配置MySQL数据库连接信息")
    print("2. 运行初始化: python init_db.py")
    print("3. 启动系统: python run.py")
    print("\n或者使用一键启动脚本:")
    if os.name == 'nt':
        print("  start.bat")
    else:
        print("  ./start.sh")
    
    return True

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n安装已取消")
        sys.exit(1)
    except Exception as e:
        print(f"\n安装失败: {e}")
        sys.exit(1)
