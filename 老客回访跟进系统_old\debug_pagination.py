#!/usr/bin/env python3
"""
调试分页问题
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.customer import Channel
from config import get_config

def debug_pagination():
    """调试分页问题"""
    print("调试渠道分页问题...")
    
    config = get_config()
    app = create_app(config)
    
    with app.app_context():
        try:
            # 1. 检查总数
            total = Channel.query.count()
            print(f"总渠道数: {total}")
            
            # 2. 检查前几条记录
            print("\n前10条记录:")
            first_10 = Channel.query.order_by(Channel.created_time.desc()).limit(10).all()
            for i, ch in enumerate(first_10):
                print(f"  {i+1}. ID={ch.id}, 创建时间={ch.created_time}, 名称={ch.channel_name}")
            
            # 3. 测试分页查询
            print("\n测试分页查询:")
            for page_num in [1, 2, 3]:
                try:
                    page_result = Channel.query.order_by(Channel.created_time.desc()).paginate(
                        page=page_num, per_page=20, error_out=False
                    )
                    print(f"第{page_num}页:")
                    print(f"  - 记录数: {len(page_result.items)}")
                    print(f"  - 总数: {page_result.total}")
                    print(f"  - 总页数: {page_result.pages}")
                    print(f"  - 当前页: {page_result.page}")
                    print(f"  - 有上一页: {page_result.has_prev}")
                    print(f"  - 有下一页: {page_result.has_next}")
                    
                    if page_result.items:
                        print(f"  - 第一条: ID={page_result.items[0].id}, 名称={page_result.items[0].channel_name}")
                        if len(page_result.items) > 1:
                            print(f"  - 最后一条: ID={page_result.items[-1].id}, 名称={page_result.items[-1].channel_name}")
                    print()
                    
                except Exception as e:
                    print(f"第{page_num}页查询失败: {e}")
            
            # 4. 检查是否有重复的创建时间
            print("检查创建时间分布:")
            time_counts = db.session.execute(
                "SELECT created_time, COUNT(*) as cnt FROM channels GROUP BY created_time HAVING cnt > 1 ORDER BY cnt DESC LIMIT 5"
            ).fetchall()
            
            if time_counts:
                print("发现重复的创建时间:")
                for time_val, count in time_counts:
                    print(f"  {time_val}: {count} 条记录")
            else:
                print("没有重复的创建时间")
            
            # 5. 检查数据分布
            print("\n检查ID分布:")
            id_range = db.session.execute(
                "SELECT MIN(id) as min_id, MAX(id) as max_id FROM channels"
            ).fetchone()
            print(f"ID范围: {id_range[0]} - {id_range[1]}")
            
            # 6. 使用ID排序测试分页
            print("\n使用ID排序测试分页:")
            for page_num in [1, 2, 3]:
                try:
                    page_result = Channel.query.order_by(Channel.id.desc()).paginate(
                        page=page_num, per_page=20, error_out=False
                    )
                    print(f"第{page_num}页 (ID排序): {len(page_result.items)} 条记录")
                    if page_result.items:
                        print(f"  - ID范围: {page_result.items[0].id} - {page_result.items[-1].id}")
                    
                except Exception as e:
                    print(f"第{page_num}页查询失败: {e}")
            
        except Exception as e:
            print(f"调试失败: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    debug_pagination()
