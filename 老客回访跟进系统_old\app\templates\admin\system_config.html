{% extends "base.html" %}

{% block title %}系统配置 - 老客回访与跟进系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-cog"></i> 系统配置</h2>
        <p class="text-muted">管理系统参数和设置</p>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-list"></i> 配置项列表</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>配置项</th>
                                <th>当前值</th>
                                <th>描述</th>
                                <th>类型</th>
                                <th>更新时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for config in configs %}
                            <tr>
                                <td><code>{{ config.config_key }}</code></td>
                                <td>
                                    {% if config.config_type == 'boolean' %}
                                        {% if config.config_value == 'true' %}
                                            <span class="badge bg-success">是</span>
                                        {% else %}
                                            <span class="badge bg-secondary">否</span>
                                        {% endif %}
                                    {% else %}
                                        <strong>{{ config.config_value }}</strong>
                                    {% endif %}
                                </td>
                                <td>{{ config.config_description }}</td>
                                <td>
                                    <span class="badge bg-info">{{ config.config_type }}</span>
                                </td>
                                <td>
                                    {% if config.updated_time %}
                                        {{ config.updated_time.strftime('%Y-%m-%d %H:%M') }}
                                    {% else %}
                                        {{ config.created_time.strftime('%Y-%m-%d %H:%M') }}
                                    {% endif %}
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" 
                                            onclick="editConfig('{{ config.config_key }}', '{{ config.config_value }}', '{{ config.config_type }}')">
                                        <i class="fas fa-edit"></i> 编辑
                                    </button>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="6" class="text-center text-muted">暂无配置项</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 编辑配置模态框 -->
<div class="modal fade" id="editConfigModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-edit"></i> 编辑配置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editConfigForm" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="configKey" class="form-label">配置项</label>
                        <input type="text" class="form-control" id="configKey" name="config_key" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="configValue" class="form-label">配置值</label>
                        <input type="text" class="form-control" id="configValue" name="config_value" required>
                        <div class="form-text" id="configHelp"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function editConfig(key, value, type) {
    document.getElementById('configKey').value = key;
    document.getElementById('configValue').value = value;
    
    // 根据类型设置帮助文本
    const helpText = document.getElementById('configHelp');
    switch(type) {
        case 'integer':
            helpText.textContent = '请输入整数值';
            document.getElementById('configValue').type = 'number';
            break;
        case 'boolean':
            helpText.textContent = '请输入 true 或 false';
            document.getElementById('configValue').type = 'text';
            break;
        default:
            helpText.textContent = '请输入文本值';
            document.getElementById('configValue').type = 'text';
    }
    
    // 显示模态框
    new bootstrap.Modal(document.getElementById('editConfigModal')).show();
}

// 表单提交处理
document.getElementById('editConfigForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // 这里可以添加AJAX提交逻辑
    alert('配置保存功能待实现');
});
</script>
{% endblock %}
