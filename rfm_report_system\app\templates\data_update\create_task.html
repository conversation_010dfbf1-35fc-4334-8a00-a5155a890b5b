{% extends "base.html" %}

{% block title %}创建数据更新任务{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题和导航 -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('data_update.index') }}">数据更新</a></li>
                    <li class="breadcrumb-item active">创建任务</li>
                </ol>
            </nav>
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-tasks me-2 text-primary"></i>创建数据更新任务</h2>
                    <p class="text-muted">
                        数据库: {{ db_config.name }} |
                        操作模式:
                        {% if operation == 'append' %}
                        <span class="badge bg-success">追加数据</span>
                        {% elif operation == 'update' %}
                        <span class="badge bg-warning">更新数据</span>
                        {% elif operation == 'create_table' %}
                        <span class="badge bg-info">创建新表</span>
                        {% endif %}
                        {% if table_structure %}
                        | 目标表: {{ table_structure.table_name }}
                        {% endif %}
                    </p>
                </div>
                <div>
                    <a href="{{ url_for('data_update.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>返回
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 表结构信息（仅在有目标表时显示） -->
    {% if table_structure %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-table me-2"></i>目标表结构</h5>
                        <a href="{{ url_for('data_update.download_template', table_name=table_structure.table_name) }}"
                           class="btn btn-outline-success btn-sm">
                            <i class="fas fa-download me-2"></i>下载模板文件
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>列名</th>
                                            <th>数据类型</th>
                                            <th>允许NULL</th>
                                            <th>主键</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for column in table_structure.columns %}
                                        <tr>
                                            <td>
                                                <code>{{ column.name }}</code>
                                                {% if column.name in table_structure.primary_keys %}
                                                <i class="fas fa-key text-warning ms-1" title="主键"></i>
                                                {% endif %}
                                            </td>
                                            <td><span class="badge bg-info">{{ column.type }}</span></td>
                                            <td>
                                                {% if column.nullable %}
                                                <i class="fas fa-check text-success"></i>
                                                {% else %}
                                                <i class="fas fa-times text-danger"></i>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if column.name in table_structure.primary_keys %}
                                                <i class="fas fa-check text-warning"></i>
                                                {% else %}
                                                <i class="fas fa-times text-muted"></i>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h6>示例数据</h6>
                            {% if table_structure.sample_data %}
                            <div class="table-responsive">
                                <table class="table table-sm table-striped">
                                    <thead>
                                        <tr>
                                            {% for column in table_structure.column_names[:3] %}
                                            <th class="small">{{ column }}</th>
                                            {% endfor %}
                                            {% if table_structure.column_names|length > 3 %}
                                            <th class="small">...</th>
                                            {% endif %}
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for row in table_structure.sample_data[:3] %}
                                        <tr>
                                            {% for column in table_structure.column_names[:3] %}
                                            <td class="small">{{ row[column] if row[column] is not none else '' }}</td>
                                            {% endfor %}
                                            {% if table_structure.column_names|length > 3 %}
                                            <td class="small">...</td>
                                            {% endif %}
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <p class="text-muted small">表中暂无数据</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 文件上传和配置 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-cog me-2"></i>操作配置</h5>
                </div>
                <div class="card-body">
                    <form id="taskForm" enctype="multipart/form-data">
                        <!-- 当前操作模式显示 -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="alert
                                    {% if operation == 'append' %}alert-success
                                    {% elif operation == 'update' %}alert-warning
                                    {% elif operation == 'create_table' %}alert-info
                                    {% endif %}">
                                    <h6 class="mb-2">
                                        {% if operation == 'append' %}
                                        <i class="fas fa-plus-circle me-2"></i>追加数据模式
                                        {% elif operation == 'update' %}
                                        <i class="fas fa-sync-alt me-2"></i>更新数据模式 <i class="fas fa-exclamation-triangle text-danger" title="高风险操作"></i>
                                        {% elif operation == 'create_table' %}
                                        <i class="fas fa-table me-2"></i>创建新表模式
                                        {% endif %}
                                    </h6>
                                    <p class="mb-0">
                                        {% if operation == 'append' %}
                                        将Excel文件中的数据追加到现有表的末尾，不影响原有数据。
                                        {% elif operation == 'update' %}
                                        清空现有表中的所有数据，然后导入Excel文件中的新数据。<strong>此操作不可逆！</strong>
                                        {% elif operation == 'create_table' %}
                                        根据Excel文件的结构创建一个新的数据表，并导入数据。
                                        {% endif %}
                                    </p>
                                </div>
                                <input type="hidden" name="operation_type" value="{{ operation }}">
                            </div>
                        </div>

                        <!-- 新表名称输入（仅创建新表时显示） -->
                        {% if operation == 'create_table' %}
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="new_table_name" class="form-label">新表名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="new_table_name" name="new_table_name"
                                       placeholder="输入新表名称（3-64个字符，字母开头）">
                                <div class="form-text">只能包含字母、数字和下划线，必须以字母开头</div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- 文件上传 -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <label for="excel_file" class="form-label">上传Excel文件 <span class="text-danger">*</span></label>
                                <div class="upload-area" id="uploadArea">
                                    <div class="upload-content">
                                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                        <p class="mb-2">拖拽文件到此处或点击选择文件</p>
                                        <p class="small text-muted">支持 .xlsx, .xls 格式，最大50MB</p>
                                    <p class="small text-info">💡 支持大文件：最多50万行数据，超过20万行将分批处理</p>
                                        <input type="file" class="form-control" id="excel_file" name="excel_file" 
                                               accept=".xlsx,.xls" style="display: none;">
                                        <button type="button" class="btn btn-outline-primary" onclick="$('#excel_file').click()">
                                            <i class="fas fa-folder-open me-2"></i>选择文件
                                        </button>
                                    </div>
                                </div>
                                <div id="fileInfo" style="display: none;" class="mt-2">
                                    <div class="alert alert-info">
                                        <i class="fas fa-file-excel me-2"></i>
                                        <span id="fileName"></span>
                                        <span class="badge bg-secondary ms-2" id="fileSize"></span>
                                        <button type="button" class="btn btn-sm btn-outline-danger ms-2" onclick="clearFile()">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ url_for('data_update.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>返回
                                    </a>
                                    <button type="button" class="btn btn-outline-info me-2" onclick="testProgress()">
                                        <i class="fas fa-vial me-1"></i>测试进度
                                    </button>
                                    <button type="submit" class="btn btn-success" id="submitBtn" disabled>
                                        <i class="fas fa-play me-2"></i>开始执行
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 执行进度区域 -->
    <div class="row mt-4" id="progressArea" style="display: none;">
        <div class="col-12">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-cog fa-spin me-2"></i>正在执行数据操作
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>当前状态：</strong>
                        <span id="currentStatus">准备中...</span>
                    </div>
                    <div class="mb-3">
                        <div class="alert alert-info">
                            <small>
                                <i class="fas fa-info-circle me-1"></i>
                                <strong>提示：</strong>大文件处理需要时间，详细进度将在操作完成后显示。
                                请耐心等待，不要关闭页面。
                            </small>
                        </div>
                    </div>
                    <div>
                        <strong>执行日志：</strong>
                        <div id="executionLogArea" style="max-height: 300px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 14px; margin-top: 10px;">
                            <!-- 日志将在这里显示 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let selectedOperation = '{{ operation }}';
let uploadedFile = null;

$(document).ready(function() {
    // 文件上传事件
    $('#excel_file').change(function() {
        handleFileSelect(this.files[0]);
    });

    // 拖拽上传
    $('#uploadArea').on('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('drag-over');
    });

    $('#uploadArea').on('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('drag-over');
    });

    $('#uploadArea').on('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('drag-over');
        const files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelect(files[0]);
        }
    });

    // 表单提交
    $('#taskForm').submit(function(e) {
        e.preventDefault();
        submitTask();
    });

    // 初始化表单验证
    checkFormValid();
});

// 处理文件选择
function handleFileSelect(file) {
    if (!file) return;

    // 防止重复处理同一个文件
    if (uploadedFile && uploadedFile.name === file.name && uploadedFile.size === file.size) {
        console.log('文件已经选择，跳过重复处理');
        return;
    }

    // 验证文件类型
    const allowedTypes = ['.xlsx', '.xls'];
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

    if (!allowedTypes.includes(fileExtension)) {
        alert('只支持Excel文件格式(.xlsx, .xls)');
        return;
    }

    // 验证文件大小（50MB）
    if (file.size > 50 * 1024 * 1024) {
        alert('文件大小不能超过50MB');
        return;
    }

    uploadedFile = file;

    // 显示文件信息
    $('#fileName').text(file.name);
    $('#fileSize').text(formatFileSize(file.size));
    $('#fileInfo').show();

    console.log('文件选择成功:', file.name, formatFileSize(file.size));
    checkFormValid();
}

// 清除文件
function clearFile() {
    uploadedFile = null;
    $('#excel_file').val('');
    $('#fileInfo').hide();
    checkFormValid();
}

// 检查表单有效性
function checkFormValid() {
    let isValid = uploadedFile;

    // 如果是创建新表，还需要检查表名
    if (selectedOperation === 'create_table') {
        const tableName = $('#new_table_name').val().trim();
        isValid = isValid && tableName.length >= 3;
    }

    $('#submitBtn').prop('disabled', !isValid);
}

// 提交任务
function submitTask() {
    if (!uploadedFile) {
        alert('请上传Excel文件');
        return;
    }

    if (selectedOperation === 'create_table') {
        const tableName = $('#new_table_name').val().trim();
        if (!tableName || tableName.length < 3) {
            alert('请输入有效的表名（至少3个字符）');
            return;
        }
    }

    // 显示加载状态
    $('#submitBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>执行中...');

    // 创建进度显示区域
    showProgressArea();

    // 创建FormData
    const formData = new FormData();
    formData.append('operation_type', selectedOperation);
    formData.append('excel_file', uploadedFile);

    if (selectedOperation === 'create_table') {
        const tableName = $('#new_table_name').val().trim();
        formData.append('new_table_name', tableName);
    }

    // 添加初始日志
    addLogMessage('开始执行操作...');
    addLogMessage('正在上传文件...');

    // 显示进度区域
    function showProgressArea() {
        $('#progressArea').show();
        $('html, body').animate({
            scrollTop: $('#progressArea').offset().top - 100
        }, 500);
    }

    // 更新状态的函数
    function updateStatus(message) {
        const statusElement = document.getElementById('currentStatus');
        if (statusElement) {
            statusElement.textContent = message;
        }
        console.log('状态更新:', message);
    }

    // 添加日志的函数
    function addLogMessage(message) {
        console.log('添加日志:', message);
        const logElement = document.getElementById('executionLogArea');
        if (logElement) {
            const logEntry = document.createElement('div');
            logEntry.style.marginBottom = '5px';
            logEntry.innerHTML = `<span style="color: #666;">[${new Date().toLocaleTimeString()}]</span> ${message}`;
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
            console.log('日志已添加到DOM');
        } else {
            console.error('找不到executionLogArea元素');
        }
    }

    // 模拟进度更新
    setTimeout(() => {
        updateStatus('正在验证文件格式...');
        addLogMessage('正在验证文件格式...');
    }, 500);
    setTimeout(() => {
        updateStatus('正在读取Excel数据...');
        addLogMessage('正在读取Excel数据...');
    }, 1000);
    setTimeout(() => {
        updateStatus('正在执行数据库操作...');
        addLogMessage('正在执行数据库操作...');
    }, 1500);

    // 提交请求
    $.ajax({
        url: '{{ url_for("data_update.create_task") }}',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            console.log('收到服务器响应:', response);
            updateStatus('操作完成！');
            addLogMessage('操作完成！');

            // 显示服务器返回的执行日志
            if (response.execution_log) {
                console.log('服务器执行日志:', response.execution_log);
                // 清空之前的模拟日志，显示真实的服务器日志
                const logElement = document.getElementById('executionLogArea');
                if (logElement) {
                    logElement.innerHTML = ''; // 清空
                }

                response.execution_log.forEach(function(logEntry) {
                    addLogMessage(logEntry);
                });
            } else {
                console.log('服务器未返回执行日志');
                addLogMessage('服务器未返回详细日志');
            }

            setTimeout(() => {
                if (response.success) {
                    // 显示成功结果
                    addLogMessage('✅ 操作执行成功！');
                    updateStatus('执行完成');

                    if (response.result) {
                        if (response.result.rows_inserted) {
                            addLogMessage(`📊 插入行数: ${response.result.rows_inserted}`);
                        }
                        if (response.result.rows_deleted) {
                            addLogMessage(`🗑️ 删除行数: ${response.result.rows_deleted}`);
                        }
                        if (response.result.rows_after) {
                            addLogMessage(`📋 总行数: ${response.result.rows_after}`);
                        }
                    }

                    // 显示操作完成按钮
                    $('#submitBtn').html('<i class="fas fa-check me-2"></i>操作完成').removeClass('btn-success').addClass('btn-outline-success');

                    // 添加返回按钮
                    const buttonArea = $('#submitBtn').parent();
                    buttonArea.append(`
                        <button type="button" class="btn btn-primary ms-2" onclick="window.location.href='{{ url_for("data_update.index") }}'">
                            <i class="fas fa-home me-2"></i>返回首页
                        </button>
                        <button type="button" class="btn btn-secondary ms-2" onclick="location.reload()">
                            <i class="fas fa-redo me-2"></i>继续操作
                        </button>
                    `);

                } else {
                    addLogMessage('❌ 操作失败: ' + response.message);
                    updateStatus('执行失败');
                    $('#submitBtn').prop('disabled', false).html('<i class="fas fa-play me-2"></i>重新执行');
                }
            }, 1000);
        },
        error: function(xhr, status, error) {
            console.error('AJAX请求失败:', status, error);
            addLogMessage('❌ 请求失败: ' + error);
            updateStatus('请求失败');
            $('#submitBtn').prop('disabled', false).html('<i class="fas fa-play me-2"></i>重新执行');
        }
    });
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 新表名称输入验证
$('#new_table_name').on('input', function() {
    checkFormValid();
});

// 测试进度显示
function testProgress() {
    let progressDialog = Swal.fire({
        title: '测试进度显示',
        html: '<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x text-primary mb-3"></i><br><span id="testStatus">正在初始化...</span></div>',
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false
    });

    function updateTestStatus(message) {
        const statusElement = document.getElementById('testStatus');
        if (statusElement) {
            statusElement.textContent = message;
        }
    }

    // 模拟进度更新
    setTimeout(() => updateTestStatus('正在验证文件格式...'), 500);
    setTimeout(() => updateTestStatus('正在读取Excel数据...'), 1000);
    setTimeout(() => updateTestStatus('正在生成预览...'), 1500);
    setTimeout(() => updateTestStatus('处理完成！'), 2000);
    setTimeout(() => {
        Swal.close();
        Swal.fire('测试完成', '进度显示功能正常工作！', 'success');
    }, 2500);
}
</script>

<style>
.operation-card {
    cursor: pointer;
    transition: all 0.3s ease;
}

.operation-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover, .upload-area.drag-over {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.upload-content {
    pointer-events: none;
}
</style>
{% endblock %}
