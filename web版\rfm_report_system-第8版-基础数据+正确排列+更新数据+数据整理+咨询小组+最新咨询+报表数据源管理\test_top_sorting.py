"""
测试TOP字段排序
"""

def is_exact_top_match(column_name: str, pattern: str) -> bool:
    """精确匹配TOP字段"""
    if not column_name.startswith(pattern):
        return False
    
    # 检查模式后面是否紧跟下划线或结束
    remaining = column_name[len(pattern):]
    return remaining.startswith('_') or remaining == ''

def test_top_sorting():
    """测试TOP字段的精确匹配"""
    
    # 测试数据 - 模拟实际的列名
    test_columns = [
        '皮肤_TOP品项1_五代热玛吉',
        '皮肤_TOP品项10_黑金超光子',
        '皮肤_TOP品项11_嗨体_美白嫩肤',
        '皮肤_TOP品项12_皮秒',
        '皮肤_TOP品项13_M22_美白嫩肤',
        '皮肤_TOP品项14_半岛超声炮',
        '皮肤_TOP品项15_唯缇',
        '皮肤_TOP品项2_四代热玛吉',
        '皮肤_TOP品项3_半岛黄金超声炮',
        '皮肤_TOP品项4_富勒烯',
        '皮肤_TOP品项5_薇旖美',
        '皮肤_TOP品项6_艾维岚',
        '皮肤_TOP品项7_形体管理',
        '皮肤_TOP品项8_纪芙秀',
        '皮肤_TOP品项9_润致_皮肤',
        '注射_TOP品项1_濡白天使',
        '注射_TOP品项10_伊妍仕',
        '注射_TOP品项11_公主',
        '注射_TOP品项12_塑妍萃',
        '注射_TOP品项13_Botox_肌肉塑形',
        '注射_TOP品项14_艾维岚_微整',
        '注射_TOP品项15_润致',
        '注射_TOP品项2_爱贝芙',
        '注射_TOP品项3_艾塑菲',
        '注射_TOP品项4_乔雅登',
        '注射_TOP品项5_双美',
        '注射_TOP品项6_瑞蓝_填充塑形',
        '注射_TOP品项7_肤美达',
        '注射_TOP品项8_Botox_祛动态纹',
        '注射_TOP品项9_艾美姿'
    ]
    
    print("原始列顺序:")
    for i, col in enumerate(test_columns):
        print(f"{i+1:2d}. {col}")
    
    # 模拟新的排序逻辑
    sorted_columns = []
    used_columns = set()
    
    # 皮肤TOP品项 1-15 (按数字顺序)
    print("\n皮肤TOP品项匹配过程:")
    for i in range(1, 16):
        pattern = f'皮肤_TOP品项{i}'
        matching_cols = [col for col in test_columns 
                       if col not in used_columns and is_exact_top_match(col, pattern)]
        if matching_cols:
            print(f"  {pattern} 匹配: {matching_cols}")
            sorted_columns.extend(matching_cols)
            used_columns.update(matching_cols)
        else:
            print(f"  {pattern} 无匹配")
    
    # 注射TOP品项 1-15 (按数字顺序)
    print("\n注射TOP品项匹配过程:")
    for i in range(1, 16):
        pattern = f'注射_TOP品项{i}'
        matching_cols = [col for col in test_columns 
                       if col not in used_columns and is_exact_top_match(col, pattern)]
        if matching_cols:
            print(f"  {pattern} 匹配: {matching_cols}")
            sorted_columns.extend(matching_cols)
            used_columns.update(matching_cols)
        else:
            print(f"  {pattern} 无匹配")
    
    print(f"\n排序后列顺序:")
    for i, col in enumerate(sorted_columns):
        print(f"{i+1:2d}. {col}")
    
    # 验证排序是否正确
    print("\n验证排序结果:")
    
    # 检查皮肤TOP品项是否按1-15顺序
    skin_items = [col for col in sorted_columns if '皮肤_TOP品项' in col]
    expected_skin_order = [
        '皮肤_TOP品项1_五代热玛吉',
        '皮肤_TOP品项2_四代热玛吉',
        '皮肤_TOP品项3_半岛黄金超声炮',
        '皮肤_TOP品项4_富勒烯',
        '皮肤_TOP品项5_薇旖美',
        '皮肤_TOP品项6_艾维岚',
        '皮肤_TOP品项7_形体管理',
        '皮肤_TOP品项8_纪芙秀',
        '皮肤_TOP品项9_润致_皮肤',
        '皮肤_TOP品项10_黑金超光子',
        '皮肤_TOP品项11_嗨体_美白嫩肤',
        '皮肤_TOP品项12_皮秒',
        '皮肤_TOP品项13_M22_美白嫩肤',
        '皮肤_TOP品项14_半岛超声炮',
        '皮肤_TOP品项15_唯缇'
    ]
    
    print(f"皮肤TOP品项排序: {skin_items}")
    print(f"期望排序: {expected_skin_order}")
    
    if skin_items == expected_skin_order:
        print("✓ 皮肤TOP品项排序正确")
    else:
        print("✗ 皮肤TOP品项排序错误")
    
    # 检查注射TOP品项是否按1-15顺序
    injection_items = [col for col in sorted_columns if '注射_TOP品项' in col]
    expected_injection_order = [
        '注射_TOP品项1_濡白天使',
        '注射_TOP品项2_爱贝芙',
        '注射_TOP品项3_艾塑菲',
        '注射_TOP品项4_乔雅登',
        '注射_TOP品项5_双美',
        '注射_TOP品项6_瑞蓝_填充塑形',
        '注射_TOP品项7_肤美达',
        '注射_TOP品项8_Botox_祛动态纹',
        '注射_TOP品项9_艾美姿',
        '注射_TOP品项10_伊妍仕',
        '注射_TOP品项11_公主',
        '注射_TOP品项12_塑妍萃',
        '注射_TOP品项13_Botox_肌肉塑形',
        '注射_TOP品项14_艾维岚_微整',
        '注射_TOP品项15_润致'
    ]
    
    print(f"注射TOP品项排序: {injection_items}")
    print(f"期望排序: {expected_injection_order}")
    
    if injection_items == expected_injection_order:
        print("✓ 注射TOP品项排序正确")
    else:
        print("✗ 注射TOP品项排序错误")

if __name__ == "__main__":
    test_top_sorting()
