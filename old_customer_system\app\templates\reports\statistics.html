{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-bar"></i>
                        跟进统计报表
                    </h3>
                    <div class="card-tools">
                        <a href="{{ url_for('reports.dashboard') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回报表
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- 查询条件表单 -->
                    <form method="POST" class="mb-4">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    {{ form.start_date.label(class="form-label") }}
                                    {{ form.start_date(class="form-select") }}
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    {{ form.end_date.label(class="form-label") }}
                                    {{ form.end_date(class="form-select") }}
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    {{ form.onsite_consultant_id.label(class="form-label") }}
                                    {{ form.onsite_consultant_id(class="form-select") }}
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    {{ form.online_consultant_id.label(class="form-label") }}
                                    {{ form.online_consultant_id(class="form-select") }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    {{ form.channel_id.label(class="form-label") }}
                                    {{ form.channel_id(class="form-select") }}
                                </div>
                            </div>
                            
                            <div class="col-md-9">
                                <div class="form-group">
                                    <label class="form-label">&nbsp;</label>
                                    <div>
                                        {{ form.submit(class="btn btn-primary") }}
                                        <button type="reset" class="btn btn-secondary">重置</button>
                                        {% if statistics_data %}
                                        <a href="{{ url_for('reports.export_statistics', 
                                                  start_date=request.form.get('start_date', '0'),
                                                  end_date=request.form.get('end_date', '0'),
                                                  onsite_consultant_id=request.form.get('onsite_consultant_id', 0),
                                                  online_consultant_id=request.form.get('online_consultant_id', 0),
                                                  channel_id=request.form.get('channel_id', 0)) }}" 
                                           class="btn btn-success">
                                            <i class="fas fa-download"></i> 导出Excel
                                        </a>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                    
                    {% if statistics_data %}
                    <!-- 统计概览 -->
                    <div class="row mb-4">
                        <div class="col-md-2">
                            <div class="info-box">
                                <span class="info-box-icon bg-info">
                                    <i class="fas fa-users"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">总登记客户</span>
                                    <span class="info-box-number">{{ statistics_data.total_customers }}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-2">
                            <div class="info-box">
                                <span class="info-box-icon bg-success">
                                    <i class="fas fa-hospital"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">跟进到院成功</span>
                                    <span class="info-box-number">{{ statistics_data.visit_success_count }}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-2">
                            <div class="info-box">
                                <span class="info-box-icon bg-warning">
                                    <i class="fas fa-shopping-cart"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">跟进消费成功</span>
                                    <span class="info-box-number">{{ statistics_data.consumption_success_count }}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-2">
                            <div class="info-box">
                                <span class="info-box-icon bg-danger">
                                    <i class="fas fa-yen-sign"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">总消费金额</span>
                                    <span class="info-box-number">{{ "%.2f"|format(statistics_data.total_consumption_amount) }}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-2">
                            <div class="info-box">
                                <span class="info-box-icon bg-primary">
                                    <i class="fas fa-percentage"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">到院成功率</span>
                                    <span class="info-box-number">{{ "%.1f"|format(statistics_data.visit_success_rate) }}%</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-2">
                            <div class="info-box">
                                <span class="info-box-icon bg-secondary">
                                    <i class="fas fa-percentage"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">消费成功率</span>
                                    <span class="info-box-number">{{ "%.1f"|format(statistics_data.consumption_success_rate) }}%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 客户详细数据表格 -->
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">客户详细数据</h4>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped" id="customer-details-table">
                                    <thead>
                                        <tr>
                                            <th>客户卡号</th>
                                            <th>登记日期</th>
                                            <th>现场顾问</th>
                                            <th>网络咨询员</th>
                                            <th>激活渠道</th>
                                            <th>是否到院</th>
                                            <th>到院次数</th>
                                            <th>是否消费</th>
                                            <th>消费次数</th>
                                            <th>消费金额</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for detail in statistics_data.customer_details %}
                                        <tr>
                                            <td>{{ detail.customer.card_number }}</td>
                                            <td>{{ detail.customer.registration_date }}</td>
                                            <td>{{ detail.customer.onsite_consultant.real_name if detail.customer.onsite_consultant else '-' }}</td>
                                            <td>{{ detail.customer.online_consultant.real_name if detail.customer.online_consultant else '-' }}</td>
                                            <td>{{ detail.customer.channel.name if detail.customer.channel else '-' }}</td>
                                            <td>
                                                {% if detail.has_visit %}
                                                    <span class="badge bg-success">是</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">否</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ detail.visit_count }}</td>
                                            <td>
                                                {% if detail.has_consumption %}
                                                    <span class="badge bg-success">是</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">否</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ detail.consumption_count }}</td>
                                            <td>{{ "%.2f"|format(detail.consumption_amount) }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        请选择查询条件并点击"生成统计报表"按钮查看统计数据
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    {% if statistics_data %}
    // 初始化数据表格
    $('#customer-details-table').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Chinese.json"
        },
        "pageLength": 25,
        "order": [[ 1, "desc" ]],  // 按登记日期降序排列
        "columnDefs": [
            { "orderable": false, "targets": [5, 7] }  // 是否到院和是否消费列不可排序
        ]
    });
    {% endif %}

    // 重置按钮功能
    $('button[type="reset"]').click(function() {
        $(this).closest('form').find('select').each(function() {
            $(this).val($(this).find('option:first').val());
        });
    });
});
</script>
{% endblock %}
