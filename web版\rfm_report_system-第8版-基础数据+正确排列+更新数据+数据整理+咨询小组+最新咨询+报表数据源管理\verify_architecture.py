#!/usr/bin/env python3
"""
验证系统架构是否正确
"""

import pymysql
from app import create_app
from app.models import DatabaseConfig

def verify_system_architecture():
    """验证系统架构"""
    print("🔍 验证RFM系统架构")
    print("=" * 50)
    
    app = create_app()
    
    with app.app_context():
        # 1. 检查系统管理数据库
        print("1. 检查系统管理数据库 (rfm_office)")
        try:
            from app.models import User, SystemSettings
            
            # 检查系统表是否存在
            user_count = User.query.count()
            print(f"   ✅ users 表存在，用户数: {user_count}")
            
            config_count = DatabaseConfig.query.count()
            print(f"   ✅ database_configs 表存在，配置数: {config_count}")
            
            # 检查其他系统表
            from app import db
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            
            expected_tables = [
                'users', 
                'database_configs', 
                'data_processing_tasks', 
                'system_settings'
            ]
            
            print(f"   📋 系统管理数据库中的表:")
            for table in tables:
                status = "✅" if table in expected_tables else "📄"
                print(f"      {status} {table}")
            
            missing_tables = set(expected_tables) - set(tables)
            if missing_tables:
                print(f"   ❌ 缺少表: {missing_tables}")
            else:
                print(f"   ✅ 所有必需的系统表都存在")
                
        except Exception as e:
            print(f"   ❌ 系统管理数据库检查失败: {e}")
            return False
        
        # 2. 检查业务数据库配置
        print(f"\n2. 检查业务数据库配置")
        
        active_config = DatabaseConfig.get_active_config()
        if not active_config:
            print("   ❌ 没有激活的业务数据库配置")
            print("   💡 请在系统中添加并激活一个业务数据库配置")
            return False
        
        print(f"   ✅ 找到激活的业务数据库配置: {active_config.name}")
        print(f"      数据库: {active_config.host}:{active_config.port}/{active_config.database_name}")
        print(f"      用户: {active_config.username}")
        
        # 3. 检查业务数据库连接和表
        print(f"\n3. 检查业务数据库")
        try:
            connection = pymysql.connect(
                host=active_config.host,
                port=active_config.port,
                user=active_config.username,
                password=active_config.get_password(),
                database=active_config.database_name,
                charset='utf8mb4'
            )
            
            print(f"   ✅ 成功连接到业务数据库")
            
            # 获取业务数据库中的表
            with connection.cursor() as cursor:
                cursor.execute("SHOW TABLES")
                business_tables = [row[0] for row in cursor.fetchall()]
            
            print(f"   📋 业务数据库中的表 ({len(business_tables)} 个):")
            
            # 分类显示表
            quarter_tables = []
            detail_table_found = False
            
            for table in business_tables:
                print(f"      📄 {table}")
                
                # 检查季度表（支持大小写）
                import re
                if re.match(r'^\d{2}[qQ][1-4]$', table):
                    quarter_tables.append(table)
                
                # 检查明细表
                if table == '客户执行明细表':
                    detail_table_found = True
            
            print(f"\n   🎯 业务表分析:")
            print(f"      季度表 (24Q1格式): {len(quarter_tables)} 个")
            for table in quarter_tables:
                print(f"        ✅ {table}")
            
            if detail_table_found:
                print(f"      ✅ 客户执行明细表存在")
            else:
                print(f"      ❌ 客户执行明细表不存在")
            
            connection.close()
            
            # 4. 架构验证总结
            print(f"\n4. 架构验证总结")
            print(f"   系统管理数据库 (rfm_office): ✅")
            print(f"   业务数据库配置: ✅")
            print(f"   业务数据库连接: ✅")
            print(f"   季度表: {'✅' if quarter_tables else '❌'} ({len(quarter_tables)} 个)")
            print(f"   客户执行明细表: {'✅' if detail_table_found else '❌'}")
            
            if quarter_tables and detail_table_found:
                print(f"\n🎉 系统架构验证通过！可以开始使用数据处理功能。")
                return True
            else:
                print(f"\n⚠️  业务数据库中缺少必要的表，请检查:")
                if not quarter_tables:
                    print(f"     - 季度表 (格式: 24Q1, 24Q2, 24Q3, 24Q4)")
                if not detail_table_found:
                    print(f"     - 客户执行明细表")
                return False
                
        except Exception as e:
            print(f"   ❌ 业务数据库检查失败: {e}")
            return False

def show_correct_architecture():
    """显示正确的架构说明"""
    print(f"\n📚 正确的系统架构说明")
    print("=" * 50)
    
    print(f"🏗️  双数据库架构:")
    print(f"")
    print(f"1. 系统管理数据库 (rfm_office)")
    print(f"   - 由系统自动创建和管理")
    print(f"   - 存储系统配置和用户数据")
    print(f"   - 表: users, database_configs, data_processing_tasks, system_settings")
    print(f"")
    print(f"2. 业务数据库 (用户配置)")
    print(f"   - 用户通过系统界面配置连接")
    print(f"   - 只包含业务数据，系统只读取不修改")
    print(f"   - 季度表: 24Q1, 24Q2, 24Q3, 24Q4 等")
    print(f"   - 明细表: 客户执行明细表")
    print(f"")
    print(f"🔄 数据处理流程:")
    print(f"   1. 系统从业务数据库读取季度表和明细表")
    print(f"   2. 在内存中进行RFM分析计算")
    print(f"   3. 生成Excel报表文件")
    print(f"   4. 任务记录保存在系统管理数据库中")

if __name__ == '__main__':
    try:
        success = verify_system_architecture()
        if not success:
            show_correct_architecture()
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
