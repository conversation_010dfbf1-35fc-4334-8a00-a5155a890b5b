{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">编辑部门 - {{ department.name }}</h1>
    <a href="{{ url_for('basic_mgmt.departments') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>返回列表
    </a>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.name.label(class="form-label") }}
                        {{ form.name(class="form-control") }}
                        {% if form.name.errors %}
                            <div class="text-danger">
                                {% for error in form.name.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control", rows="3") }}
                        {% if form.description.errors %}
                            <div class="text-danger">
                                {% for error in form.description.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_active(class="form-check-input") }}
                            {{ form.is_active.label(class="form-check-label") }}
                        </div>
                        {% if department.users %}
                            <small class="form-text text-muted">
                                注意：该部门下有 {{ department.users|length }} 个用户，停用部门可能会影响这些用户的权限。
                            </small>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('basic_mgmt.departments') }}" class="btn btn-secondary me-md-2">取消</a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-building me-2"></i>部门信息
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <strong>创建时间:</strong><br>
                        {{ department.created_at.strftime('%Y-%m-%d %H:%M:%S') }}
                    </li>
                    <li class="mb-2">
                        <strong>更新时间:</strong><br>
                        {{ department.updated_at.strftime('%Y-%m-%d %H:%M:%S') }}
                    </li>
                    <li class="mb-2">
                        <strong>用户数量:</strong><br>
                        总计 {{ department.users|length }} 人
                        {% if department.users %}
                            (激活: {{ department.users|selectattr('is_active')|list|length }} 人)
                        {% endif %}
                    </li>
                    <li class="mb-2">
                        <strong>当前状态:</strong><br>
                        {% if department.is_active %}
                            <span class="badge bg-success">激活</span>
                        {% else %}
                            <span class="badge bg-secondary">停用</span>
                        {% endif %}
                    </li>
                </ul>
            </div>
        </div>
        
        {% if department.users %}
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-users me-2"></i>部门用户
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    {% for user in department.users %}
                    <div class="list-group-item px-0 py-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>{{ user.real_name }}</strong>
                                <small class="text-muted">({{ user.username }})</small>
                            </div>
                            <div>
                                {% if user.role == 'admin' %}
                                    <span class="badge bg-danger">管理员</span>
                                {% elif user.role == 'director' %}
                                    <span class="badge bg-warning">经营院长</span>
                                {% elif user.role == 'manager' %}
                                    <span class="badge bg-info">部门主管</span>
                                {% elif user.role == 'online_consultant' %}
                                    <span class="badge bg-primary">网络咨询</span>
                                {% elif user.role == 'onsite_consultant' %}
                                    <span class="badge bg-success">现场咨询</span>
                                {% endif %}
                                {% if not user.is_active %}
                                    <span class="badge bg-secondary">停用</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}