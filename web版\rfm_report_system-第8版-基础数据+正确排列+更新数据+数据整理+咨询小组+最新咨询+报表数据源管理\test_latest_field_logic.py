#!/usr/bin/env python3
"""
测试最新现场判断逻辑
"""

import pandas as pd
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.data_processing.processor import RFMDataProcessor

def test_latest_field_logic():
    """测试最新现场判断逻辑"""
    
    print("=== 测试最新现场判断逻辑 ===")
    
    # 创建测试数据
    test_data = {
        '会员卡号': ['000001', '000002', '000003', '000004', '000005'],
        '23Q4_现场': ['张三', '', '王五', '不在盘内', '李六'],
        '24Q1_现场': ['', '李四', '不在盘内', '不在盘内', ''],
        '24Q2_现场': ['', '', '赵六', '', '不在盘内'],
        '24Q3_现场': ['不在盘内', '不在盘内', '不在盘内', '不在盘内', '不在盘内']
    }
    
    df = pd.DataFrame(test_data)
    print("原始数据:")
    print(df)
    print()
    
    # 创建处理器实例
    processor = RFMDataProcessor(None)
    
    # 现场列按时间排序（从新到旧）
    consultation_columns = ['24Q3_现场', '24Q2_现场', '24Q1_现场', '23Q4_现场']
    
    print("=== 测试各个用户的最新现场判断 ===")
    
    for index, row in df.iterrows():
        card_no = row['会员卡号']
        latest_field = processor._get_latest_consultation(row, consultation_columns)
        
        print(f"用户 {card_no}:")
        for col in consultation_columns:
            value = row[col] if pd.notna(row[col]) and row[col] != '' else '空'
            print(f"  {col}: {value}")
        print(f"  → 最新现场: {latest_field}")
        print()
    
    print("=== 预期结果验证 ===")
    
    # 测试用例验证
    test_cases = [
        {
            'card_no': '000001',
            'expected': '张三',  # 24Q3是"不在盘内"，跳过，使用23Q4的"张三"
            'description': '24Q3不在盘内，使用23Q4数据'
        },
        {
            'card_no': '000002', 
            'expected': '李四',  # 24Q3是"不在盘内"，跳过，使用24Q1的"李四"
            'description': '24Q3不在盘内，使用24Q1数据'
        },
        {
            'card_no': '000003',
            'expected': '赵六',  # 24Q3是"不在盘内"，跳过，使用24Q2的"赵六"
            'description': '24Q3不在盘内，使用24Q2数据'
        },
        {
            'card_no': '000004',
            'expected': '其他咨询',  # 所有季度都是"不在盘内"，返回"其他咨询"
            'description': '所有季度都不在盘内，返回其他咨询'
        },
        {
            'card_no': '000005',
            'expected': '李六',  # 24Q3是"不在盘内"，跳过，使用23Q4的"李六"
            'description': '24Q3不在盘内，使用23Q4数据'
        }
    ]
    
    all_passed = True
    for test_case in test_cases:
        row = df[df['会员卡号'] == test_case['card_no']].iloc[0]
        actual = processor._get_latest_consultation(row, consultation_columns)
        expected = test_case['expected']
        
        if actual == expected:
            print(f"✅ {test_case['card_no']}: {test_case['description']} - {actual}")
        else:
            print(f"❌ {test_case['card_no']}: 预期 {expected}, 实际 {actual}")
            all_passed = False
    
    print()
    if all_passed:
        print("🎉 所有测试用例通过！最新现场判断逻辑正确。")
    else:
        print("❌ 部分测试用例失败，需要检查逻辑。")

def test_field_mapping_logic():
    """测试现场映射逻辑"""
    
    print("\n=== 测试现场映射逻辑 ===")
    
    # 模拟现场映射表
    field_mappings = {
        '张三': '李四',
        '王五': '赵六',
        '李六': '李四'
    }
    
    field_group_mappings = {
        '李四': '王婆小组',
        '赵六': '李婆小组'
    }
    
    def apply_mapping(value, mapping_dict):
        """应用映射"""
        return mapping_dict.get(value, value)
    
    # 测试映射逻辑
    test_values = ['张三', '李四', '王五', '其他咨询', '']
    
    print("现场映射测试:")
    for value in test_values:
        if value and value != '其他咨询' and value != '':
            mapped_field = apply_mapping(value, field_mappings)
            field_group = apply_mapping(mapped_field, field_group_mappings)
        else:
            mapped_field = value if value else '其他咨询'
            field_group = ''
        
        print(f"  {value} → 最新现场: {mapped_field}, 现场小组: {field_group}")

if __name__ == "__main__":
    test_latest_field_logic()
    test_field_mapping_logic()
