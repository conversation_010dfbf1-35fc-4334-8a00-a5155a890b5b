{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">用户管理</h1>
    <a href="{{ url_for('basic_mgmt.create_user') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>添加用户
    </a>
</div>

<!-- 搜索表单 -->
<div class="card mb-3">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                {{ search_form.query.label(class="form-label") }}
                {{ search_form.query(class="form-control", placeholder="用户名、姓名或简码") }}
            </div>
            <div class="col-md-2">
                {{ search_form.role.label(class="form-label") }}
                {{ search_form.role(class="form-select") }}
            </div>
            <div class="col-md-2">
                {{ search_form.department_id.label(class="form-label") }}
                {{ search_form.department_id(class="form-select") }}
            </div>
            <div class="col-md-2">
                {{ search_form.is_active.label(class="form-label") }}
                {{ search_form.is_active(class="form-select") }}
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-search me-1"></i>搜索
                </button>
                <a href="{{ url_for('basic_mgmt.users') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-undo me-1"></i>重置
                </a>
            </div>
        </form>
    </div>
</div>

<!-- 用户列表 -->
<div class="card">
    <div class="card-body">
        {% if pagination.items %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>用户名</th>
                            <th>真实姓名</th>
                            <th>角色</th>
                            <th>部门</th>
                            <th>简码</th>
                            <th>状态</th>
                            <th>最后登录</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in pagination.items %}
                        <tr>
                            <td>{{ user.username }}</td>
                            <td>{{ user.real_name }}</td>
                            <td>
                                {% if user.role == 'admin' %}
                                    <span class="badge bg-danger">管理员</span>
                                {% elif user.role == 'director' %}
                                    <span class="badge bg-warning">经营院长</span>
                                {% elif user.role == 'manager' %}
                                    <span class="badge bg-info">部门主管</span>
                                {% elif user.role == 'online_consultant' %}
                                    <span class="badge bg-primary">网络咨询</span>
                                {% elif user.role == 'onsite_consultant' %}
                                    <span class="badge bg-success">现场咨询</span>
                                {% endif %}
                            </td>
                            <td>{{ user.department.name if user.department else '-' }}</td>
                            <td>{{ user.simple_code or '-' }}</td>
                            <td>
                                {% if user.is_active %}
                                    <span class="badge bg-success">激活</span>
                                {% else %}
                                    <span class="badge bg-secondary">停用</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if user.last_login %}
                                    {{ user.last_login.strftime('%Y-%m-%d %H:%M') }}
                                {% else %}
                                    从未登录
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{{ url_for('basic_mgmt.edit_user', user_id=user.id) }}" 
                                       class="btn btn-outline-primary" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-warning" 
                                            onclick="resetPassword({{ user.id }}, '{{ user.username }}')" title="重置密码">
                                        <i class="fas fa-key"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-{{ 'secondary' if user.is_active else 'success' }}" 
                                            onclick="toggleUserStatus({{ user.id }}, '{{ user.username }}', {{ user.is_active|lower }})" 
                                            title="{{ '停用' if user.is_active else '激活' }}">
                                        <i class="fas fa-{{ 'ban' if user.is_active else 'check' }}"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            {% if pagination.pages > 1 %}
            <nav aria-label="用户列表分页">
                <ul class="pagination justify-content-center">
                    {% if pagination.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('basic_mgmt.users', page=pagination.prev_num, **request.args) }}">上一页</a>
                        </li>
                    {% endif %}
                    
                    {% for page_num in range(1, pagination.pages + 1) %}
                        {% if page_num == pagination.page %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% else %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('basic_mgmt.users', page=page_num, **request.args) }}">{{ page_num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if pagination.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('basic_mgmt.users', page=pagination.next_num, **request.args) }}">下一页</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
            
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <p class="text-muted">暂无用户数据</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- 重置密码模态框 -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">重置密码</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="resetPasswordForm">
                <div class="modal-body">
                    <input type="hidden" id="resetUserId" name="user_id">
                    <p>确定要重置用户 <strong id="resetUsername"></strong> 的密码吗？</p>
                    <div class="mb-3">
                        <label for="newPassword" class="form-label">新密码</label>
                        <input type="password" class="form-control" id="newPassword" name="new_password" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-warning">重置密码</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function toggleUserStatus(userId, username, isActive) {
    const action = isActive ? '停用' : '激活';
    if (confirm(`确定要${action}用户 "${username}" 吗？`)) {
        fetch(`/basic/users/${userId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('操作失败: ' + data.message);
            }
        })
        .catch(error => {
            alert('操作失败: ' + error);
        });
    }
}

function resetPassword(userId, username) {
    document.getElementById('resetUserId').value = userId;
    document.getElementById('resetUsername').textContent = username;
    document.getElementById('newPassword').value = '';
    new bootstrap.Modal(document.getElementById('resetPasswordModal')).show();
}

document.getElementById('resetPasswordForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const userId = formData.get('user_id');
    
    fetch(`/basic/users/${userId}/reset-password`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': '{{ csrf_token() }}'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            bootstrap.Modal.getInstance(document.getElementById('resetPasswordModal')).hide();
            alert('密码重置成功');
        } else {
            alert('密码重置失败: ' + data.message);
        }
    })
    .catch(error => {
        alert('密码重置失败: ' + error);
    });
});
</script>
{% endblock %}