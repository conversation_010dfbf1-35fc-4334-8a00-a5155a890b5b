"""
Excel报表模板定义
将Excel报表逻辑转换为配置化的模板
"""

# 示例报表模板配置
REPORT_TEMPLATES = {
    "会员RFM分析报表": {
        "description": "基于Excel公式的会员RFM分析",
        "source_tables": ["会员基础数据", "消费明细"],
        "columns": [
            {
                "name": "会员卡号",
                "formula": "[会员卡号]",
                "source_data": "会员基础数据",
                "description": "会员卡号"
            },
            {
                "name": "R值计算",
                "formula": "=IF([最近消费日期]=\"\", 999, (TODAY()-[最近消费日期]).days)",
                "source_data": "会员基础数据",
                "description": "最近消费间隔天数"
            },
            {
                "name": "F值计算", 
                "formula": "=COUNTIF(消费明细[会员卡号], [会员卡号])",
                "source_data": "会员基础数据",
                "description": "消费频次"
            },
            {
                "name": "M值计算",
                "formula": "=SUMIF(消费明细[会员卡号], [会员卡号], 消费明细[消费金额])",
                "source_data": "会员基础数据", 
                "description": "消费金额"
            },
            {
                "name": "R等级",
                "formula": "=IF([R值计算]<=30, \"高\", IF([R值计算]<=90, \"中\", \"低\"))",
                "source_data": "会员基础数据",
                "description": "R值等级"
            },
            {
                "name": "F等级",
                "formula": "=IF([F值计算]>=10, \"高\", IF([F值计算]>=5, \"中\", \"低\"))",
                "source_data": "会员基础数据",
                "description": "F值等级"
            },
            {
                "name": "M等级",
                "formula": "=IF([M值计算]>=50000, \"高\", IF([M值计算]>=20000, \"中\", \"低\"))",
                "source_data": "会员基础数据",
                "description": "M值等级"
            },
            {
                "name": "综合等级",
                "formula": "=CONCATENATE([R等级], [F等级], [M等级])",
                "source_data": "会员基础数据",
                "description": "RFM综合等级"
            }
        ]
    },
    
    "科室业绩分析报表": {
        "description": "科室业绩统计分析",
        "source_tables": ["执行明细", "会员信息"],
        "columns": [
            {
                "name": "科室",
                "formula": "[科室]",
                "source_data": "科室汇总",
                "description": "科室名称"
            },
            {
                "name": "总业绩",
                "formula": "=SUMIF(执行明细[科室], [科室], 执行明细[执行业绩])",
                "source_data": "科室汇总",
                "description": "科室总业绩"
            },
            {
                "name": "会员数量",
                "formula": "=COUNTIF(执行明细[科室], [科室])",
                "source_data": "科室汇总", 
                "description": "消费会员数量"
            },
            {
                "name": "平均客单价",
                "formula": "=IF([会员数量]>0, [总业绩]/[会员数量], 0)",
                "source_data": "科室汇总",
                "description": "平均客单价"
            },
            {
                "name": "业绩占比",
                "formula": "=[总业绩]/SUM(科室汇总[总业绩])*100",
                "source_data": "科室汇总",
                "description": "业绩占比百分比"
            }
        ]
    },
    
    "TOP品类分析报表": {
        "description": "TOP品类业绩分析",
        "source_tables": ["执行明细"],
        "columns": [
            {
                "name": "二级分类",
                "formula": "[二级分类]",
                "source_data": "品类汇总",
                "description": "品类名称"
            },
            {
                "name": "业绩金额",
                "formula": "=SUMIF(执行明细[二级分类], [二级分类], 执行明细[执行业绩])",
                "source_data": "品类汇总",
                "description": "品类总业绩"
            },
            {
                "name": "排名",
                "formula": "=RANK([业绩金额], 品类汇总[业绩金额], 0)",
                "source_data": "品类汇总",
                "description": "业绩排名"
            },
            {
                "name": "是否TOP5",
                "formula": "=IF([排名]<=5, \"是\", \"否\")",
                "source_data": "品类汇总",
                "description": "是否为TOP5品类"
            }
        ]
    }
}


def get_template(template_name: str) -> dict:
    """获取报表模板"""
    return REPORT_TEMPLATES.get(template_name)


def list_templates() -> list:
    """列出所有可用模板"""
    return list(REPORT_TEMPLATES.keys())


def add_custom_template(template_name: str, template_config: dict):
    """添加自定义模板"""
    REPORT_TEMPLATES[template_name] = template_config


# Excel公式示例库
FORMULA_EXAMPLES = {
    "条件判断": {
        "IF单条件": "=IF([金额]>1000, \"高消费\", \"普通消费\")",
        "IF多条件": "=IF([金额]>5000, \"VIP\", IF([金额]>1000, \"高消费\", \"普通消费\"))",
        "AND条件": "=IF(AND([金额]>1000, [次数]>5), \"优质客户\", \"普通客户\")",
        "OR条件": "=IF(OR([金额]>5000, [次数]>10), \"重要客户\", \"普通客户\")"
    },
    
    "数据统计": {
        "求和": "=SUM([金额列])",
        "条件求和": "=SUMIF([条件列], \"条件值\", [求和列])",
        "多条件求和": "=SUMIFS([求和列], [条件列1], \"条件1\", [条件列2], \"条件2\")",
        "计数": "=COUNTIF([条件列], \"条件值\")",
        "平均值": "=AVERAGE([数值列])"
    },
    
    "文本处理": {
        "连接文本": "=CONCATENATE([文本1], \"-\", [文本2])",
        "提取左侧": "=LEFT([文本], 3)",
        "提取右侧": "=RIGHT([文本], 3)",
        "提取中间": "=MID([文本], 2, 3)",
        "文本长度": "=LEN([文本])"
    },
    
    "日期处理": {
        "当前日期": "=TODAY()",
        "日期差值": "=(TODAY()-[日期列])",
        "提取年份": "=YEAR([日期列])",
        "提取月份": "=MONTH([日期列])",
        "提取日": "=DAY([日期列])"
    },
    
    "查找引用": {
        "垂直查找": "=VLOOKUP([查找值], [查找表], [列号], FALSE)",
        "索引匹配": "=INDEX([返回列], MATCH([查找值], [查找列], 0))"
    }
}
