import pymysql
from datetime import datetime
import time
import traceback
import re

# 数据库连接配置
db_config = {
    'host': '127.0.0.1',
    'user': 'root',
    'password': 'Yj198704!',
    'database': 'wdly',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

def get_confirmation(prompt):
    """获取用户确认输入，支持多种肯定回答"""
    while True:
        response = input(prompt).strip().lower()
        if response in ('y', 'yes', '是', '确认'):
            return True
        elif response in ('n', 'no', '否', '取消'):
            return False
        print("无效输入，请重新输入(y/n)")

# 获取用户指定的表名
def get_user_selected_tables():
    print("\n请输入需要处理的表名(用逗号分隔，例如: 24Q1,24Q2,25Q1):")
    tables_input = input("表名列表: ").strip()
    selected_tables = [table.strip() for table in tables_input.split(',') if table.strip()]
    
    # 验证表名格式
    for table in selected_tables:
        if not (table.startswith('24Q') or table.startswith('25Q')):
            print(f"警告: 表名 '{table}' 不符合季度表命名格式(如24Q1)")
    
    print(f"\n您选择了以下表: {selected_tables}")
    if not get_confirmation("确认继续吗?(y/n): "):
        return []
    return selected_tables

def get_column_type(column_name):
    """根据列名返回合适的数据类型"""
    column_name = column_name.lower()
    if any(keyword in column_name for keyword in ['金额', '价格', '费用', '业绩']):
        return 'DECIMAL(15,2) DEFAULT 0'
    elif any(keyword in column_name for keyword in ['日期', '时间']):
        return 'DATE'
    elif any(keyword in column_name for keyword in ['数量', '次数', '人数']):
        return 'INT DEFAULT 0'
    else:
        return 'TEXT'

# 连接数据库
def process_data():
    connection = None
    try:
        start_time = time.time()
        print(f'[{datetime.now()}] 开始处理数据...')
        
        # 1. 获取用户选择的表
        quarter_tables = get_user_selected_tables()
        if not quarter_tables:
            print(f'[{datetime.now()}] 用户取消操作')
            return
        
        # 2. 连接数据库
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        print(f'[{datetime.now()}] 成功连接到数据库')
        
        # 3. 验证表是否存在
        valid_tables = []
        for table in quarter_tables:
            cursor.execute(f"SHOW TABLES LIKE '{table}'")
            if cursor.fetchone():
                valid_tables.append(table)
            else:
                print(f'[{datetime.now()}] 警告: 表 {table} 不存在，已跳过')
        
        # 只处理第一个表
        if valid_tables:
            valid_tables = [valid_tables[0]]
            print(f'[{datetime.now()}] 注意: 只处理第一个表 {valid_tables[0]}')
        if not valid_tables:
            print(f'[{datetime.now()}] 错误: 没有有效的表可供处理')
            return
        
        # 4. 确认会员卡号列
        card_column = '会员卡号'
        print(f'\n[{datetime.now()}] 确认信息:')
        print(f'1. 将使用 [{card_column}] 列作为关键列')
        print(f'2. 将对 [{card_column}] 列进行去重处理')
        print(f'3. 将使用 [{card_column}] 列匹配其他表数据')
        if not get_confirmation("确认以上信息正确吗?(y/n): "):
            print(f'[{datetime.now()}] 用户取消操作')
            return
        
        # 5. 获取所有表的列信息
        all_columns = {}
        for table in valid_tables:
            cursor.execute(f"SHOW COLUMNS FROM {table}")
            columns = [col['Field'] for col in cursor.fetchall()]
            all_columns[table] = columns
            print(f'[{datetime.now()}] 表 {table} 包含列: {columns}')
        
        # 6. 创建缓存表
        current_time = datetime.now().strftime('%Y%m%d_%H_%M')
        cache_table_name = f'RFM正向盘_科室业绩分析_{current_time}'
        
        create_table_sql = f"CREATE TABLE IF NOT EXISTS `{cache_table_name}` (`id` INT AUTO_INCREMENT PRIMARY KEY, `{card_column}` VARCHAR(255) UNIQUE"
        
        for table in sorted(valid_tables):
            for column in all_columns[table]:
                if column != card_column:
                    safe_column = f"`{table}_{column}`"
                    col_type = get_column_type(column)
                    create_table_sql += f", {safe_column} {col_type}"
        
        create_table_sql += ") ROW_FORMAT=DYNAMIC"
        cursor.execute(create_table_sql)
        print(f'[{datetime.now()}] 成功创建缓存表 {cache_table_name}')

        # 添加索引
        cursor.execute(f"ALTER TABLE `{cache_table_name}` ADD INDEX idx_card(`{card_column}`)")
        print(f'[{datetime.now()}] 已为表 {cache_table_name} 添加索引')

        # 7. 合并会员卡号并去重
        distinct_cards = set()
        for table in valid_tables:
            cursor.execute(f"SELECT DISTINCT {card_column} FROM {table} WHERE {card_column} IS NOT NULL")
            cards = [row[card_column] for row in cursor.fetchall()]
            distinct_cards.update(cards)
            print(f'[{datetime.now()}] 从表 {table} 获取到 {len(cards)} 个会员卡号')
        
        print(f'[{datetime.now()}] 总共获取到 {len(distinct_cards)} 个唯一会员卡号')
        
        # 8. 批量插入会员卡号
        batch_size = 5000
        cards_list = list(distinct_cards)
        
        def batch_insert_cards(cursor, table_name, card_column, cards_list, batch_size=5000):
            try:
                connection = cursor.connection
                connection.begin()
                
                for i in range(0, len(cards_list), batch_size):
                    batch = cards_list[i:i+batch_size]
                    sql = f"INSERT IGNORE INTO `{table_name}` (`{card_column}`) VALUES {','.join(['(%s)']*len(batch))}"
                    cursor.execute(sql, batch)
                    
                connection.commit()
                print(f'[{datetime.now()}] 成功插入 {len(cards_list)} 个会员卡号')
            except Exception as e:
                connection.rollback()
                raise e
        
        batch_insert_cards(cursor, cache_table_name, card_column, cards_list)
        
        # 9. 更新其他列数据
        def update_table_data(cursor, cache_table, source_table, card_column, columns, batch_size=10000):
            total_updated = 0
            start_time = time.time()
            
            cursor.execute(f"SELECT COUNT(*) as total FROM `{source_table}`")
            total_records = cursor.fetchone()['total']
            
            cursor.execute(f"SELECT MIN(id) as min_id, MAX(id) as max_id FROM `{cache_table}`")
            id_range = cursor.fetchone()
            min_id, max_id = id_range['min_id'], id_range['max_id']
            
            update_columns = [col for col in columns if col != card_column]
            
            for i in range(0, len(update_columns), 20):
                batch_columns = update_columns[i:i+20]
                print(f'[{datetime.now()}] 开始批量更新 {source_table} 的列: {batch_columns} (共{total_records}条记录)...')
                
                current_min = min_id
                while current_min <= max_id:
                    current_max = min(current_min + batch_size - 1, max_id)
                    
                    set_clauses = []
                    for col in batch_columns:
                        set_clauses.append(f"cache.`{source_table}_{col}` = src.`{col}`")
                    
                    update_sql = f"""
                    UPDATE `{cache_table}` cache
                    JOIN `{source_table}` src ON cache.`{card_column}` = src.`{card_column}`
                    SET {', '.join(set_clauses)}
                    WHERE cache.id BETWEEN {current_min} AND {current_max}
                    AND (""" + " OR ".join([f"cache.`{source_table}_{col}` IS NULL" for col in batch_columns]) + ")"
                    
                    cursor.execute(update_sql)
                    affected_rows = cursor.rowcount
                    total_updated += affected_rows
                    current_min = current_max + 1
                    
                    progress = min(current_min - min_id, max_id - min_id + 1)
                    elapsed = time.time() - start_time
                    remaining = (max_id - current_min + 1) * (elapsed / progress) if progress > 0 else 0
                    
                    print(f'[{datetime.now()}] 进度: {progress}/{max_id-min_id+1} ({progress/(max_id-min_id+1):.1%}) | '
                          f'已更新: {total_updated}条 | 预计剩余: {remaining:.1f}秒')
                print(f'[{datetime.now()}] 完成批量更新 {source_table} 的列: {batch_columns}')
        
        for table in sorted(valid_tables):
            columns = all_columns[table]
            update_table_data(cursor, cache_table_name, table, card_column, columns)
        
        connection.commit()
        print(f'[{datetime.now()}] 数据处理完成，总耗时: {time.time()-start_time:.2f}秒')
        
        # 直接继续处理执行业绩数据，不询问用户确认
        process_performance_data(cache_table_name, valid_tables)
        
        # 优化列类型
        print(f'[{datetime.now()}] 开始优化列数据类型...')
        cursor.execute(f"SHOW COLUMNS FROM {cache_table_name}")
        columns = [col['Field'] for col in cursor.fetchall()]
        
        for column in columns:
            if column != 'id' and column != card_column:
                col_type = get_column_type(column)
                cursor.execute(f"ALTER TABLE `{cache_table_name}` MODIFY COLUMN `{column}` {col_type}")
        print(f'[{datetime.now()}] 列数据类型优化完成')
        
    except Exception as e:
        error_msg = f'[{datetime.now()}] 错误: {str(e)}'
        print(error_msg)
        with open('mysql_data_processor_error.log', 'a', encoding='utf-8') as f:
            f.write(f'{datetime.now()}\n{error_msg}\n{traceback.format_exc()}\n\n')
    finally:
        if connection:
            connection.close()

def process_performance_data(cache_table_name, quarter_tables):
    connection = None
    try:
        start_time = time.time()
        print(f'[{datetime.now()}] 开始处理执行业绩数据...')
        
        # 1. 连接数据库
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        # 2. 检查客户执行明细表是否存在
        cursor.execute("SHOW TABLES LIKE '客户执行明细表'")
        if not cursor.fetchone():
            print(f'[{datetime.now()}] 错误: 客户执行明细表不存在')
            return
        
        # 科室映射关系
        department_mapping = {
            '皮肤美容项目': '皮肤',
            '口腔美容项目': '口腔',
            '注射美容项目': '注射',
            '检验项目': '整形',
            '形体美容项目': '皮肤',
            '整形美容项目': '整形',
            '麻醉项目': '整形',
            '毛发种植项目': '毛发'
        }
        
        # 新增：安全添加列函数
        def add_column_if_not_exists(cursor, table_name, column_name, column_type):
            """安全地添加列，如果列不存在"""
            cursor.execute(f"""
                SELECT COUNT(*) as col_exists 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = '{table_name}' 
                AND COLUMN_NAME = '{column_name}'
            """)
            if not cursor.fetchone()['col_exists']:
                cursor.execute(f"ALTER TABLE `{table_name}` ADD COLUMN `{column_name}` {column_type}")
        
        # 3. 处理每个季度数据
        card_column = '会员卡号'
        
        def sanitize_column_name(name):
            """清理列名中的特殊字符"""
            return re.sub(r'[^\w_]', '', name)
        
        def calculate_top_items(cursor, department_type):
            """计算指定科室的TOP品类和品项"""
            department = department_mapping[department_type]
            
            # 计算品类TOP5
            cursor.execute(f"""
                SELECT 
                    二级分类,
                    SUM(`执行业绩（真实金额）`) as total_amount
                FROM 客户执行明细表
                WHERE `一级分类` = '{department_type}'
                GROUP BY 二级分类
                ORDER BY total_amount DESC
                LIMIT 5
            """)
            top_categories = cursor.fetchall()
            
            # 计算品项TOP15
            cursor.execute(f"""
                SELECT 
                    三级分类,
                    SUM(`执行业绩（真实金额）`) as total_amount
                FROM 客户执行明细表
                WHERE `一级分类` = '{department_type}'
                GROUP BY 三级分类
                ORDER BY total_amount DESC
                LIMIT 15
            """)
            top_items = cursor.fetchall()
            
            return department, top_categories, top_items
        
        # 处理皮肤和注射科室的TOP数据
        for department_type in ['皮肤美容项目', '注射美容项目']:
            department, top_categories, top_items = calculate_top_items(cursor, department_type)
            
            # 添加品类TOP列并计算值
            for i, category in enumerate(top_categories, 1):
                safe_category = sanitize_column_name(category['二级分类'])
                col_name = f"{department}_品类TOP{i}_{safe_category}"
                
                # 使用安全添加列方法
                add_column_if_not_exists(cursor, cache_table_name, col_name, 'DECIMAL(15,2) DEFAULT 0')
                
                # 计算值
                for quarter in quarter_tables:
                    year_part = quarter[:2]
                    q_part = quarter[3]
                    year = int(year_part) + 2000
                    q = int(q_part)
                    
                    update_sql = f"""
                    UPDATE `{cache_table_name}` cache
                    JOIN (
                        SELECT 
                            会员卡号, 
                            ROUND(SUM(`执行业绩（真实金额）`), 2) as performance
                        FROM 客户执行明细表
                        WHERE YEAR(执行日期) = {year} 
                        AND QUARTER(执行日期) = {q}
                        AND `一级分类` = '{department_type}'
                        AND `二级分类` = '{category['二级分类']}'
                        GROUP BY 会员卡号
                    ) src ON cache.`{card_column}` = src.会员卡号
                    SET cache.`{col_name}` = src.performance
                    """
                    cursor.execute(update_sql)
                    print(f'[{datetime.now()}] 已更新 {col_name} 数据')
            
            # 添加品项TOP列并计算值
            for i, item in enumerate(top_items, 1):
                safe_item = sanitize_column_name(item['三级分类'])
                col_name = f"{department}_品项TOP{i}_{safe_item}"
                
                # 使用安全添加列方法
                add_column_if_not_exists(cursor, cache_table_name, col_name, 'DECIMAL(15,2) DEFAULT 0')
                
                # 计算值
                for quarter in quarter_tables:
                    year_part = quarter[:2]
                    q_part = quarter[3]
                    year = int(year_part) + 2000
                    q = int(q_part)
                    
                    update_sql = f"""
                    UPDATE `{cache_table_name}` cache
                    JOIN (
                        SELECT 
                            会员卡号, 
                            ROUND(SUM(`执行业绩（真实金额）`), 2) as performance
                        FROM 客户执行明细表
                        WHERE YEAR(执行日期) = {year} 
                        AND QUARTER(执行日期) = {q}
                        AND `一级分类` = '{department_type}'
                        AND `三级分类` = '{item['三级分类']}'
                        GROUP BY 会员卡号
                    ) src ON cache.`{card_column}` = src.会员卡号
                    SET cache.`{col_name}` = src.performance
                    """
                    cursor.execute(update_sql)
                    print(f'[{datetime.now()}] 已更新 {col_name} 数据')
        
        connection.commit()
        print(f'[{datetime.now()}] 执行业绩数据处理完成，总耗时: {time.time()-start_time:.2f}秒')
        
    except Exception as e:
        error_msg = f'[{datetime.now()}] 错误: {str(e)}'
        print(error_msg)
        with open('mysql_data_processor_error.log', 'a', encoding='utf-8') as f:
            f.write(f'{datetime.now()}\n{error_msg}\n{traceback.format_exc()}\n\n')
    finally:
        if connection:
            connection.close()

if __name__ == '__main__':
    process_data()