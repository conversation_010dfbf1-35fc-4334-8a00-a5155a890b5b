# 安全说明

## 🔒 密码安全

### 问题说明
之前在 `start_server.py` 中硬编码了明文密码，这是不安全的做法。

### 已修复的安全问题

#### 1. **移除明文密码显示**
- ✅ `start_server.py` 不再显示明文密码
- ✅ 改为提示用户查看初始化信息

#### 2. **安全的用户初始化**
- ✅ `init_db.py` 使用交互式方式创建管理员账户
- ✅ 用户自定义用户名和密码
- ✅ 密码长度验证（至少6位）

#### 3. **用户管理工具**
- ✅ 提供 `manage_users.py` 脚本进行用户管理
- ✅ 使用 `getpass` 模块安全输入密码
- ✅ 支持创建用户、重置密码、列出用户等功能

## 📋 安全使用指南

### 首次部署
```bash
# 1. 初始化数据库（会提示创建管理员账户）
python init_db.py

# 2. 启动系统
python start_server.py
```

### 用户管理
```bash
# 创建新用户
python manage_users.py create

# 重置用户密码
python manage_users.py reset

# 列出所有用户
python manage_users.py list

# 显示登录信息
python manage_users.py info
```

### 密码要求
- 最少6位字符
- 建议包含字母、数字和特殊字符
- 定期更换密码

## 🛡️ 安全建议

### 生产环境部署
1. **使用强密码**：至少12位，包含大小写字母、数字和特殊字符
2. **定期更换密码**：建议每3-6个月更换一次
3. **限制访问**：配置防火墙，只允许必要的IP访问
4. **HTTPS部署**：使用SSL证书加密传输
5. **备份数据**：定期备份数据库和配置文件

### 开发环境
1. **不要提交密码**：确保密码不会被提交到版本控制系统
2. **使用环境变量**：敏感信息通过环境变量传递
3. **本地测试**：开发时使用测试账户，不要使用生产密码

## 🔧 故障排除

### 忘记密码
```bash
# 重置指定用户密码
python manage_users.py reset
```

### 查看现有用户
```bash
# 列出所有用户
python manage_users.py list
```

### 系统无法登录
1. 确认用户名和密码正确
2. 检查数据库连接是否正常
3. 查看系统日志排查问题

## 📞 联系支持

如果遇到安全相关问题，请：
1. 立即停止系统运行
2. 检查日志文件
3. 联系系统管理员
4. 必要时重新初始化系统

---

**重要提醒**：请妥善保管登录凭据，不要在不安全的环境中使用系统。
