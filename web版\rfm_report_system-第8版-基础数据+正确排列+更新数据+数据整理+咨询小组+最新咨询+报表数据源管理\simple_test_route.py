#!/usr/bin/env python3
"""
添加一个简单的测试路由来调试表单问题
"""

from app import create_app
from app.models import DatabaseConfig
from app.data_processing.processor import RFMDataProcessor
from flask import request, jsonify
import re
from datetime import datetime

app = create_app()

@app.route('/debug_form', methods=['GET', 'POST'])
def debug_form():
    """调试表单提交"""
    if request.method == 'POST':
        data = request.form.to_dict(flat=False)
        return jsonify({
            'method': 'POST',
            'form_data': data,
            'quarters_selected': data.get('quarters', []),
            'task_type': data.get('task_type', [''])[0],
            'base_quarter_table': data.get('base_quarter_table', [''])[0]
        })
    
    # GET 请求 - 显示调试表单
    active_config = DatabaseConfig.get_active_config()
    if not active_config:
        return "没有激活的数据库配置"
    
    processor = RFMDataProcessor(active_config)
    if not processor.connect_database():
        return "无法连接到数据库"
    
    quarter_tables = processor.get_quarter_tables()
    
    # 生成季度选项
    quarters = set()
    for table in quarter_tables:
        match = re.match(r'^(\d{2})[qQ]([1-4])$', table)
        if match:
            year, quarter = match.groups()
            quarters.add(f"{year}Q{quarter}")
    
    current_year = datetime.now().year % 100
    for year in range(current_year - 2, current_year + 2):
        for q in range(1, 5):
            quarters.add(f"{year:02d}Q{q}")
    
    sorted_quarters = sorted(quarters)
    
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>表单调试</title>
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    </head>
    <body>
        <h2>表单调试</h2>
        <form method="POST" id="debugForm">
            <h3>处理类型</h3>
            <input type="radio" name="task_type" value="forward" id="forward" checked>
            <label for="forward">正向盘</label><br>
            <input type="radio" name="task_type" value="result" id="result">
            <label for="result">结果盘</label><br>
            
            <h3>基础会员来源（正向盘）</h3>
            <select name="base_quarter_table" id="base_quarter_table">
                <option value="">请选择</option>
                {''.join([f'<option value="{table}">{table}</option>' for table in quarter_tables])}
            </select>
            
            <h3>分析季度</h3>
            {''.join([f'<input type="checkbox" name="quarters" value="{q}" id="q_{q}"><label for="q_{q}">{q}</label><br>' for q in sorted_quarters])}
            
            <h3>科室</h3>
            <input type="checkbox" name="departments" value="皮肤" id="skin" checked>
            <label for="skin">皮肤</label><br>
            <input type="checkbox" name="departments" value="注射" id="injection" checked>
            <label for="injection">注射</label><br>
            
            <br>
            <button type="submit">提交测试</button>
        </form>
        
        <script>
        $('#debugForm').submit(function(e) {{
            e.preventDefault();
            
            var formData = $(this).serialize();
            console.log('提交的数据:', formData);
            
            $.post('/debug_form', formData)
                .done(function(response) {{
                    console.log('服务器响应:', response);
                    alert('提交成功！查看控制台了解详情。');
                }})
                .fail(function(xhr) {{
                    console.log('提交失败:', xhr.responseText);
                    alert('提交失败！查看控制台了解详情。');
                }});
        }});
        </script>
    </body>
    </html>
    """
    
    return html

if __name__ == '__main__':
    app.run(debug=True, port=5001)
