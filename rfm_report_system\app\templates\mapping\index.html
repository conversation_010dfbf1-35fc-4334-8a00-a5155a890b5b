{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-exchange-alt me-2"></i>参照表管理</h2>
                <div>
                    <a href="{{ url_for('mapping.download_template') }}" class="btn btn-outline-info me-2">
                        <i class="fas fa-download me-1"></i>下载模板
                    </a>
                    <a href="{{ url_for('mapping.upload_template') }}" class="btn btn-primary">
                        <i class="fas fa-upload me-1"></i>上传参照表
                    </a>
                </div>
            </div>

            <!-- 当前状态 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-info-circle me-2"></i>当前状态</h5>
                        </div>
                        <div class="card-body">
                            {% if active_template %}
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i>
                                    当前启用的参照表: <strong>{{ active_template.template_name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ active_template.description or '无描述' }}</small>
                                </div>
                                <form method="POST" action="{{ url_for('mapping.deactivate_all') }}" style="display: inline;">
                                    <button type="submit" class="btn btn-outline-warning" 
                                            onclick="return confirm('确定要禁用所有参照表吗？系统将使用原始分类进行计算。')">
                                        <i class="fas fa-times me-1"></i>禁用参照表
                                    </button>
                                </form>
                            {% else %}
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    当前未启用任何参照表，系统将使用原始分类进行TOP排名和计算
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- 参照表列表 -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-list me-2"></i>参照表列表</h5>
                </div>
                <div class="card-body">
                    {% if templates %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>模板名称</th>
                                        <th>描述</th>
                                        <th>状态</th>
                                        <th>创建者</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for template in templates %}
                                    <tr>
                                        <td>
                                            <a href="{{ url_for('mapping.view_template', template_id=template.id) }}" 
                                               class="text-decoration-none">
                                                {{ template.template_name }}
                                            </a>
                                        </td>
                                        <td>{{ template.description or '-' }}</td>
                                        <td>
                                            {% if template.is_active %}
                                                <span class="badge bg-success">已启用</span>
                                            {% else %}
                                                <span class="badge bg-secondary">未启用</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ template.creator.username }}</td>
                                        <td>{{ template.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ url_for('mapping.view_template', template_id=template.id) }}" 
                                                   class="btn btn-outline-primary btn-sm" title="查看详情">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                
                                                {% if not template.is_active %}
                                                    <form method="POST" action="{{ url_for('mapping.activate_template', template_id=template.id) }}" 
                                                          style="display: inline;">
                                                        <button type="submit" class="btn btn-outline-success btn-sm" 
                                                                title="激活模板"
                                                                onclick="return confirm('确定要激活此参照表吗？')">
                                                            <i class="fas fa-play"></i>
                                                        </button>
                                                    </form>
                                                {% endif %}
                                                
                                                <form method="POST" action="{{ url_for('mapping.delete_template', template_id=template.id) }}" 
                                                      style="display: inline;">
                                                    <button type="submit" class="btn btn-outline-danger btn-sm" 
                                                            title="删除模板"
                                                            onclick="return confirm('确定要删除此参照表吗？此操作不可恢复！')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">暂无参照表</h5>
                            <p class="text-muted">点击上方"上传参照表"按钮开始创建</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- 使用说明 -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="fas fa-question-circle me-2"></i>使用说明</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>📋 Excel格式要求：</h6>
                            <ul>
                                <li>必须包含三个工作表：<code>一级分类映射</code>、<code>二级分类映射</code>、<code>三级分类映射</code></li>
                                <li>每个工作表包含两列：<code>原始值</code> 和 <code>映射值</code></li>
                                <li>支持 .xlsx 和 .xls 格式</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>🔄 映射示例：</h6>
                            <ul>
                                <li><strong>二级分类：</strong>衡力 → 肉毒素，Botox → 肉毒素</li>
                                <li><strong>三级分类：</strong>四代黄金微针 → 黄金微针</li>
                                <li><strong>一级分类：</strong>皮肤美容项目 → 皮肤</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
