"""
测试咨询映射功能
"""

import pandas as pd
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_consultation_columns_sorting():
    """测试咨询列排序功能"""
    
    # 模拟列名
    columns = [
        '会员卡号',
        '23Q4_现场',
        '24Q1_现场', 
        '24Q2_现场',
        '24Q3_现场',
        '其他列'
    ]
    
    def _get_consultation_columns_sorted(columns):
        """获取现场相关列并按时间排序（从新到旧）"""
        consultation_columns = []
        
        for col in columns:
            if '现场' in col:
                consultation_columns.append(col)
        
        # 按季度排序（从新到旧）
        def extract_quarter_year(col_name):
            """从列名中提取年份和季度，用于排序"""
            import re
            # 匹配 23Q4, 24Q1 等格式
            match = re.search(r'(\d{2})Q(\d)', col_name)
            if match:
                year = int('20' + match.group(1))  # 23 -> 2023
                quarter = int(match.group(2))
                return (year, quarter)
            
            # 匹配 2023Q4, 2024Q1 等格式
            match = re.search(r'(\d{4})Q(\d)', col_name)
            if match:
                year = int(match.group(1))
                quarter = int(match.group(2))
                return (year, quarter)
            
            # 如果无法解析，返回一个很小的值，排在最后
            return (0, 0)
        
        # 按年份和季度降序排序（最新的在前）
        consultation_columns.sort(key=extract_quarter_year, reverse=True)
        
        return consultation_columns
    
    sorted_columns = _get_consultation_columns_sorted(columns)
    print("原始列:", [col for col in columns if '现场' in col])
    print("排序后:", sorted_columns)
    print("预期顺序: ['24Q3_现场', '24Q2_现场', '24Q1_现场', '23Q4_现场']")
    
    expected = ['24Q3_现场', '24Q2_现场', '24Q1_现场', '23Q4_现场']
    assert sorted_columns == expected, f"排序错误: {sorted_columns} != {expected}"
    print("✅ 排序测试通过")

def test_latest_consultation_logic():
    """测试最新咨询逻辑"""
    
    # 模拟数据行
    test_data = {
        '会员卡号': '000001',
        '23Q4_现场': '张三',
        '24Q1_现场': '',
        '24Q2_现场': '李四',
        '24Q3_现场': ''
    }
    
    consultation_columns = ['24Q3_现场', '24Q2_现场', '24Q1_现场', '23Q4_现场']
    
    def _get_latest_consultation(row, consultation_columns):
        """获取某行的最新咨询值"""
        for col in consultation_columns:
            value = row.get(col)
            if pd.notna(value) and value != '' and value != '不在盘内':
                return value
        
        # 如果所有现场列都为空，返回"其他咨询"
        return '其他咨询'
    
    # 测试用例1：24Q2有数据
    row1 = pd.Series(test_data)
    result1 = _get_latest_consultation(row1, consultation_columns)
    print(f"测试用例1 - 结果: {result1}, 预期: 李四")
    assert result1 == '李四', f"测试1失败: {result1} != 李四"
    
    # 测试用例2：只有23Q4有数据
    test_data2 = test_data.copy()
    test_data2['24Q2_现场'] = ''
    row2 = pd.Series(test_data2)
    result2 = _get_latest_consultation(row2, consultation_columns)
    print(f"测试用例2 - 结果: {result2}, 预期: 张三")
    assert result2 == '张三', f"测试2失败: {result2} != 张三"
    
    # 测试用例3：所有列都为空
    test_data3 = {col: '' for col in test_data.keys()}
    test_data3['会员卡号'] = '000002'
    row3 = pd.Series(test_data3)
    result3 = _get_latest_consultation(row3, consultation_columns)
    print(f"测试用例3 - 结果: {result3}, 预期: 其他咨询")
    assert result3 == '其他咨询', f"测试3失败: {result3} != 其他咨询"
    
    print("✅ 最新咨询逻辑测试通过")

def test_mapping_logic():
    """测试映射逻辑（使用现场映射）"""

    # 模拟现场映射表
    field_mappings = {
        '张三': '李四',
        '王五': '赵六'
    }

    field_group_mappings = {
        '李四': '王婆小组',
        '赵六': '李婆小组'
    }

    def apply_mapping(value, mapping_dict):
        """应用映射"""
        return mapping_dict.get(value, value)

    # 测试映射链
    original_consultation = '张三'
    mapped_consultation = apply_mapping(original_consultation, field_mappings)
    consultation_group = apply_mapping(mapped_consultation, field_group_mappings)

    print(f"原始现场: {original_consultation}")
    print(f"映射后现场: {mapped_consultation}")
    print(f"现场小组: {consultation_group}")

    assert mapped_consultation == '李四', f"现场映射失败: {mapped_consultation} != 李四"
    assert consultation_group == '王婆小组', f"小组映射失败: {consultation_group} != 王婆小组"

    print("✅ 现场映射逻辑测试通过")

def test_complete_workflow():
    """测试完整工作流程"""
    
    print("\n=== 完整工作流程测试 ===")
    
    # 模拟缓存表数据
    cache_data = {
        '会员卡号': ['000001', '000002', '000003'],
        '23Q4_现场': ['张三', '', '王五'],
        '24Q1_现场': ['', '李四', ''],
        '24Q2_现场': ['', '', ''],
        '24Q3_现场': ['', '', '赵六']
    }
    
    cache_df = pd.DataFrame(cache_data)
    print("原始数据:")
    print(cache_df)
    
    # 模拟现场映射表
    field_mappings = {
        '张三': '李四',
        '李四': '李四',  # 自映射
        '王五': '赵六',
        '赵六': '赵六'   # 自映射
    }

    field_group_mappings = {
        '李四': '王婆小组',
        '赵六': '李婆小组'
    }
    
    # 获取咨询列并排序
    consultation_columns = ['24Q3_现场', '24Q2_现场', '24Q1_现场', '23Q4_现场']
    
    # 计算最新咨询和咨询小组
    latest_consultation_values = []
    consultation_group_values = []
    
    for index, row in cache_df.iterrows():
        # 获取最新咨询
        latest_consultation = None
        for col in consultation_columns:
            value = row.get(col)
            if pd.notna(value) and value != '' and value != '不在盘内':
                latest_consultation = value
                break
        
        if not latest_consultation:
            latest_consultation = '其他咨询'
        
        # 应用现场映射
        mapped_consultation = field_mappings.get(latest_consultation, latest_consultation)

        # 获取现场小组
        consultation_group = field_group_mappings.get(mapped_consultation, '')
        
        latest_consultation_values.append(mapped_consultation)
        consultation_group_values.append(consultation_group)
    
    # 在会员卡号列后插入新列
    card_col_index = cache_df.columns.get_loc('会员卡号')
    cache_df.insert(card_col_index + 1, '现场小组', consultation_group_values)
    cache_df.insert(card_col_index + 2, '最新现场', latest_consultation_values)
    
    print("\n处理后数据:")
    print(cache_df)
    
    # 验证结果
    expected_consultations = ['李四', '李四', '赵六']  # 张三->李四, 李四->李四, 赵六->赵六
    expected_groups = ['王婆小组', '王婆小组', '李婆小组']
    
    actual_consultations = cache_df['最新现场'].tolist()
    actual_groups = cache_df['现场小组'].tolist()
    
    print(f"\n预期最新现场: {expected_consultations}")
    print(f"实际最新现场: {actual_consultations}")
    print(f"预期现场小组: {expected_groups}")
    print(f"实际现场小组: {actual_groups}")
    
    assert actual_consultations == expected_consultations, f"最新现场不匹配: {actual_consultations} != {expected_consultations}"
    assert actual_groups == expected_groups, f"现场小组不匹配: {actual_groups} != {expected_groups}"
    
    print("✅ 完整工作流程测试通过")

if __name__ == "__main__":
    print("开始测试咨询映射功能...")
    
    try:
        test_consultation_columns_sorting()
        print()
        test_latest_consultation_logic()
        print()
        test_mapping_logic()
        print()
        test_complete_workflow()
        
        print("\n🎉 所有测试通过！咨询映射功能实现正确。")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
