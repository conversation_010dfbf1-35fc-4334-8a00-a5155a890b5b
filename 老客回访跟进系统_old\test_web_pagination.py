#!/usr/bin/env python3
"""
测试Web分页功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.customer import Channel
from app.models.user import User
from config import get_config
from flask import url_for
import requests

def test_web_channel_pagination():
    """测试Web渠道分页功能"""
    print("测试Web渠道分页功能...")
    
    config = get_config()
    app = create_app(config)
    
    with app.app_context():
        # 模拟请求上下文
        with app.test_request_context():
            # 导入视图函数
            from app.views.channel import index
            from flask import request
            from werkzeug.test import EnvironBuilder
            
            # 测试第1页
            print("测试第1页...")
            with app.test_request_context('/?page=1'):
                try:
                    # 模拟登录用户
                    from flask_login import login_user
                    admin_user = User.query.filter_by(username='admin').first()
                    if admin_user:
                        login_user(admin_user)
                    
                    response = index()
                    print(f"第1页响应类型: {type(response)}")
                    
                except Exception as e:
                    print(f"第1页测试失败: {e}")
                    import traceback
                    traceback.print_exc()
            
            # 测试第2页
            print("\n测试第2页...")
            with app.test_request_context('/?page=2'):
                try:
                    # 模拟登录用户
                    from flask_login import login_user
                    admin_user = User.query.filter_by(username='admin').first()
                    if admin_user:
                        login_user(admin_user)
                    
                    response = index()
                    print(f"第2页响应类型: {type(response)}")
                    
                except Exception as e:
                    print(f"第2页测试失败: {e}")
                    import traceback
                    traceback.print_exc()

def test_channel_view_directly():
    """直接测试渠道视图函数"""
    print("\n直接测试渠道视图函数...")
    
    config = get_config()
    app = create_app(config)
    
    with app.app_context():
        # 模拟不同页码的请求
        for page_num in [1, 2, 3]:
            print(f"\n测试第{page_num}页...")
            
            # 模拟请求参数
            with app.test_request_context(f'/?page={page_num}'):
                try:
                    from flask import request
                    
                    # 获取请求参数
                    page = request.args.get('page', 1, type=int)
                    per_page = 20
                    
                    print(f"请求页码: {page}")
                    
                    # 验证页码参数
                    if page < 1:
                        page = 1
                    
                    # 限制每页数量，避免查询过大数据集
                    if per_page > 100:
                        per_page = 100
                    
                    # 获取筛选参数
                    category = request.args.get('category', '').strip()
                    name = request.args.get('name', '').strip()
                    status = request.args.get('status')
                    
                    print(f"筛选参数: category='{category}', name='{name}', status='{status}'")
                    
                    # 构建查询
                    query = Channel.query
                    
                    if category:
                        query = query.filter(Channel.channel_category.like(f'%{category}%'))
                        print(f"应用分类筛选: {category}")
                    
                    if name:
                        query = query.filter(Channel.channel_name.like(f'%{name}%'))
                        print(f"应用名称筛选: {name}")
                    
                    if status == 'active':
                        query = query.filter(Channel.is_active == True)
                        print("应用状态筛选: 启用")
                    elif status == 'inactive':
                        query = query.filter(Channel.is_active == False)
                        print("应用状态筛选: 停用")
                    
                    # 先检查总数
                    total_count = query.count()
                    max_page = (total_count + per_page - 1) // per_page if total_count > 0 else 1
                    
                    print(f"查询总数: {total_count}, 最大页数: {max_page}")
                    
                    if page > max_page:
                        print(f"页码 {page} 超出最大页数 {max_page}")
                        page = 1
                    
                    # 执行分页查询
                    channels = query.order_by(Channel.created_time.desc()).paginate(
                        page=page, per_page=per_page, error_out=False
                    )
                    
                    print(f"分页结果: 第{channels.page}页, 共{channels.pages}页, 本页{len(channels.items)}条, 总共{channels.total}条")
                    
                    if channels.items:
                        print(f"第一条: ID={channels.items[0].id}, 名称={channels.items[0].channel_name}")
                        print(f"最后一条: ID={channels.items[-1].id}, 名称={channels.items[-1].channel_name}")
                    else:
                        print("本页无数据")
                    
                except Exception as e:
                    print(f"第{page_num}页测试失败: {e}")
                    import traceback
                    traceback.print_exc()

if __name__ == "__main__":
    test_channel_view_directly()
    test_web_channel_pagination()
