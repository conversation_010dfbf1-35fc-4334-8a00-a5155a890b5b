"""
测试列排序功能
"""

import pandas as pd
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.data_processing.processor import RFMDataProcessor

def test_column_sorting():
    """测试列排序功能"""
    
    # 创建测试数据
    test_columns = [
        '会员卡号',
        '24Q4_现场', '25Q1_现场', '25Q2_现场', '25Q3_现场',
        '24Q4_综合等级', '25Q1_综合等级', '25Q2_综合等级', '25Q3_综合等级',
        '24Q4_细分等级', '25Q1_细分等级', '25Q2_细分等级', '25Q3_细分等级',
        '24Q4_科室标签', '25Q1_科室标签', '25Q2_科室标签', '25Q3_科室标签',
        '24Q4_会员等级', '25Q1_会员等级', '25Q2_会员等级', '25Q3_会员等级',
        '24Q4_R1', '25Q1_R1', '25Q2_R1', '25Q3_R1',
        '24Q4_R2', '25Q1_R2', '25Q2_R2', '25Q3_R2',
        '24Q4_F1', '25Q1_F1', '25Q2_F1', '25Q3_F1',
        '24Q4_F2', '25Q1_F2', '25Q2_F2', '25Q3_F2',
        '24Q4_M1', '25Q1_M1', '25Q2_M1', '25Q3_M1',
        '24Q4_M2', '25Q1_M2', '25Q2_M2', '25Q3_M2',
        '24Q4_R1区间', '25Q1_R1区间', '25Q2_R1区间', '25Q3_R1区间',
        '24Q4_R2区间', '25Q1_R2区间', '25Q2_R2区间', '25Q3_R2区间',
        '24Q4_F1区间', '25Q1_F1区间', '25Q2_F1区间', '25Q3_F1区间',
        '24Q4_F2区间', '25Q1_F2区间', '25Q2_F2区间', '25Q3_F2区间',
        '24Q4_M1区间', '25Q1_M1区间', '25Q2_M1区间', '25Q3_M1区间',
        '24Q4_M2区间', '25Q1_M2区间', '25Q2_M2区间', '25Q3_M2区间',
        '24Q4_F1分值', '25Q1_F1分值', '25Q2_F1分值', '25Q3_F1分值',
        '24Q4_F2分值', '25Q1_F2分值', '25Q2_F2分值', '25Q3_F2分值',
        '24Q4_M1分值', '25Q1_M1分值', '25Q2_M1分值', '25Q3_M1分值',
        '24Q4_M2分值', '25Q1_M2分值', '25Q2_M2分值', '25Q3_M2分值',
        '24Q4_R1分值', '25Q1_R1分值', '25Q2_R1分值', '25Q3_R1分值',
        '24Q4_R2分值', '25Q1_R2分值', '25Q2_R2分值', '25Q3_R2分值',
        '24Q4_加权总分', '25Q1_加权总分', '25Q2_加权总分', '25Q3_加权总分',
        '24Q4_年龄', '25Q1_年龄', '25Q2_年龄', '25Q3_年龄',
        '24Q4_模型', '25Q1_模型', '25Q2_模型', '25Q3_模型',
        '24Q4_消费年限', '25Q1_消费年限', '25Q2_消费年限', '25Q3_消费年限',
        '24Q4_RFM', '25Q1_RFM', '25Q2_RFM', '25Q3_RFM',
        '24Q4_执行业绩', '25Q1_执行业绩', '25Q2_执行业绩', '25Q3_执行业绩',
        '24Q4_皮肤', '25Q1_皮肤', '25Q2_皮肤', '25Q3_皮肤',
        '24Q4_注射', '25Q1_注射', '25Q2_注射', '25Q3_注射',
        '24Q4_TOP品类1', '25Q1_TOP品类1', '25Q2_TOP品类1', '25Q3_TOP品类1',
        '24Q4_TOP品项1', '25Q1_TOP品项1', '25Q2_TOP品项1', '25Q3_TOP品项1',
        '24Q4_TOP品项15', '25Q1_TOP品项15', '25Q2_TOP品项15', '25Q3_TOP品项15',
    ]
    
    # 创建测试DataFrame
    test_data = {col: [f'test_{i}' for i in range(5)] for col in test_columns}
    df = pd.DataFrame(test_data)
    
    print("原始列顺序:")
    for i, col in enumerate(df.columns):
        print(f"{i+1:2d}. {col}")
    
    # 创建处理器实例（不需要真实的数据库连接）
    processor = RFMDataProcessor(None)
    
    # 应用排序
    sorted_df = processor._sort_columns_by_field_type(df)
    
    print("\n排序后列顺序:")
    for i, col in enumerate(sorted_df.columns):
        print(f"{i+1:2d}. {col}")
    
    # 验证排序规则
    print("\n验证排序规则:")

    # 检查会员卡号是否在第一位
    if sorted_df.columns[0] == '会员卡号':
        print("✓ 会员卡号在第一位")
    else:
        print("✗ 会员卡号不在第一位")

    # 检查现场字段是否按季度排序
    field_columns = [col for col in sorted_df.columns if '现场' in col]
    expected_field_order = ['24Q4_现场', '25Q1_现场', '25Q2_现场', '25Q3_现场']
    if field_columns == expected_field_order:
        print("✓ 现场字段按季度正确排序")
    else:
        print(f"✗ 现场字段排序错误，实际: {field_columns}, 期望: {expected_field_order}")

    # 检查综合等级字段是否在现场之后
    comprehensive_columns = [col for col in sorted_df.columns if '综合等级' in col]
    expected_comprehensive_order = ['24Q4_综合等级', '25Q1_综合等级', '25Q2_综合等级', '25Q3_综合等级']
    if comprehensive_columns == expected_comprehensive_order:
        print("✓ 综合等级字段按季度正确排序")
    else:
        print(f"✗ 综合等级字段排序错误，实际: {comprehensive_columns}, 期望: {expected_comprehensive_order}")

    # 检查R1字段是否在会员等级之后
    r1_columns = [col for col in sorted_df.columns if 'R1' in col and 'R1区间' not in col and 'R1分值' not in col]
    expected_r1_order = ['24Q4_R1', '25Q1_R1', '25Q2_R1', '25Q3_R1']
    if r1_columns == expected_r1_order:
        print("✓ R1字段按季度正确排序")
    else:
        print(f"✗ R1字段排序错误，实际: {r1_columns}, 期望: {expected_r1_order}")

    # 检查分值字段是否在区间字段之后
    f1_score_columns = [col for col in sorted_df.columns if 'F1分值' in col]
    expected_f1_score_order = ['24Q4_F1分值', '25Q1_F1分值', '25Q2_F1分值', '25Q3_F1分值']
    if f1_score_columns == expected_f1_score_order:
        print("✓ F1分值字段按季度正确排序")
    else:
        print(f"✗ F1分值字段排序错误，实际: {f1_score_columns}, 期望: {expected_f1_score_order}")

    # 检查年龄字段是否在加权总分之后
    age_columns = [col for col in sorted_df.columns if '年龄' in col]
    expected_age_order = ['24Q4_年龄', '25Q1_年龄', '25Q2_年龄', '25Q3_年龄']
    if age_columns == expected_age_order:
        print("✓ 年龄字段按季度正确排序")
    else:
        print(f"✗ 年龄字段排序错误，实际: {age_columns}, 期望: {expected_age_order}")

    # 检查TOP品类字段是否在执行业绩之后
    top_category_columns = [col for col in sorted_df.columns if 'TOP品类1' in col]
    expected_top_category_order = ['24Q4_TOP品类1', '25Q1_TOP品类1', '25Q2_TOP品类1', '25Q3_TOP品类1']
    if top_category_columns == expected_top_category_order:
        print("✓ TOP品类1字段按季度正确排序")
    else:
        print(f"✗ TOP品类1字段排序错误，实际: {top_category_columns}, 期望: {expected_top_category_order}")

    # 检查字段组的相对位置
    columns_list = list(sorted_df.columns)

    # 现场应该在综合等级之前
    if columns_list.index('24Q4_现场') < columns_list.index('24Q4_综合等级'):
        print("✓ 现场字段在综合等级之前")
    else:
        print("✗ 现场字段不在综合等级之前")

    # R1应该在R1区间之前
    if columns_list.index('24Q4_R1') < columns_list.index('24Q4_R1区间'):
        print("✓ R1字段在R1区间之前")
    else:
        print("✗ R1字段不在R1区间之前")

    # R1区间应该在F1分值之前
    if columns_list.index('24Q4_R1区间') < columns_list.index('24Q4_F1分值'):
        print("✓ R1区间字段在F1分值之前")
    else:
        print("✗ R1区间字段不在F1分值之前")

if __name__ == "__main__":
    test_column_sorting()
