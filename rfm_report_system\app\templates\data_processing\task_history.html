{% extends "base.html" %}

{% block content %}
<div class="row mt-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2><i class="fas fa-history me-2"></i>任务历史</h2>
            <a href="{{ url_for('data_processing.new_task') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>新建任务
            </a>
        </div>
    </div>
</div>

<div class="row mt-3">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if tasks.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>任务名称</th>
                                    <th>类型</th>
                                    <th>状态</th>
                                    <th>进度</th>
                                    <th>创建时间</th>
                                    <th>完成时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for task in tasks.items %}
                                <tr>
                                    <td>
                                        <a href="{{ url_for('data_processing.task_detail', task_id=task.id) }}" 
                                           class="text-decoration-none">
                                            <strong>{{ task.task_name }}</strong>
                                        </a>
                                        {% if task.get_parameters().get('description') %}
                                            <br><small class="text-muted">{{ task.get_parameters().get('description')[:50] }}{% if task.get_parameters().get('description')|length > 50 %}...{% endif %}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'primary' if task.task_type == 'forward' else 'info' }}">
                                            {{ '正向盘' if task.task_type == 'forward' else '结果盘' }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if task.status == 'completed' %}
                                            <span class="badge bg-success">已完成</span>
                                        {% elif task.status == 'failed' %}
                                            <span class="badge bg-danger">失败</span>
                                        {% elif task.status == 'running' %}
                                            <span class="badge bg-primary">运行中</span>
                                        {% else %}
                                            <span class="badge bg-secondary">等待中</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if task.status == 'running' %}
                                            <div class="progress" style="width: 100px;">
                                                <div class="progress-bar" role="progressbar" 
                                                     style="width: {{ task.progress }}%">
                                                    {{ task.progress }}%
                                                </div>
                                            </div>
                                        {% elif task.status == 'completed' %}
                                            <span class="text-success">100%</span>
                                        {% elif task.status == 'failed' %}
                                            <span class="text-danger">{{ task.progress }}%</span>
                                        {% else %}
                                            <span class="text-muted">0%</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ task.created_at | local_datetime('%Y-%m-%d %H:%M') }}</td>
                                    <td>
                                        {% if task.completed_at %}
                                            {{ task.completed_at | local_datetime('%Y-%m-%d %H:%M') }}
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{{ url_for('data_processing.task_detail', task_id=task.id) }}" 
                                               class="btn btn-outline-info" title="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if task.status == 'completed' and task.output_file_path %}
                                                <a href="{{ url_for('data_processing.download_result', task_id=task.id) }}"
                                                   class="btn btn-outline-success" title="下载结果">
                                                    <i class="fas fa-download"></i>
                                                </a>
                                            {% endif %}

                                            {% if task.status == 'running' %}
                                                <button type="button" class="btn btn-outline-warning"
                                                        title="强制停止任务"
                                                        onclick="stopTask({{ task.id }})">
                                                    <i class="fas fa-stop"></i>
                                                </button>
                                            {% endif %}

                                            {% if task.status == 'pending' %}
                                                <button type="button" class="btn btn-outline-primary"
                                                        title="手动启动任务"
                                                        onclick="startTask({{ task.id }})">
                                                    <i class="fas fa-play"></i>
                                                </button>
                                            {% endif %}

                                            {% if task.status in ['completed', 'failed', 'pending'] %}
                                                <form method="POST" action="{{ url_for('data_processing.delete_task', task_id=task.id) }}"
                                                      style="display: inline;">
                                                    <button type="submit" class="btn btn-outline-danger"
                                                            title="删除任务"
                                                            onclick="return confirm('确定要删除此任务吗？此操作不可恢复！')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    {% if tasks.pages > 1 %}
                        <nav aria-label="任务列表分页">
                            <ul class="pagination justify-content-center">
                                {% if tasks.has_prev %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('data_processing.task_history', page=tasks.prev_num) }}">上一页</a>
                                    </li>
                                {% endif %}
                                
                                {% for page_num in tasks.iter_pages() %}
                                    {% if page_num %}
                                        {% if page_num != tasks.page %}
                                            <li class="page-item">
                                                <a class="page-link" href="{{ url_for('data_processing.task_history', page=page_num) }}">{{ page_num }}</a>
                                            </li>
                                        {% else %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ page_num }}</span>
                                            </li>
                                        {% endif %}
                                    {% else %}
                                        <li class="page-item disabled">
                                            <span class="page-link">…</span>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if tasks.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('data_processing.task_history', page=tasks.next_num) }}">下一页</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    {% endif %}
                    
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无任务记录</h5>
                        <p class="text-muted">创建您的第一个数据处理任务</p>
                        <a href="{{ url_for('data_processing.new_task') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>新建任务
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 统计信息 -->
{% if tasks.items %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-chart-pie me-2"></i>任务统计</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <h4 class="text-primary">{{ tasks.total }}</h4>
                        <small class="text-muted">总任务数</small>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-success">
                            {{ tasks.items | selectattr('status', 'equalto', 'completed') | list | length }}
                        </h4>
                        <small class="text-muted">已完成</small>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-primary">
                            {{ tasks.items | selectattr('status', 'equalto', 'running') | list | length }}
                        </h4>
                        <small class="text-muted">运行中</small>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-danger">
                            {{ tasks.items | selectattr('status', 'equalto', 'failed') | list | length }}
                        </h4>
                        <small class="text-muted">失败</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // 自动刷新运行中的任务状态
    function refreshRunningTasks() {
        $('.badge:contains("运行中")').each(function() {
            var row = $(this).closest('tr');
            var taskLink = row.find('a[href*="task_detail"]').attr('href');
            if (taskLink) {
                var taskId = taskLink.split('/').pop();
                
                $.get('/data/api/task_status/' + taskId)
                    .done(function(data) {
                        if (data.status !== 'running') {
                            // 任务状态已改变，刷新页面
                            location.reload();
                        } else {
                            // 更新进度条
                            var progressBar = row.find('.progress-bar');
                            if (progressBar.length) {
                                progressBar.css('width', data.progress + '%').text(data.progress + '%');
                            }
                        }
                    })
                    .fail(function() {
                        console.log('获取任务状态失败');
                    });
            }
        });
    }
    
    // 每10秒刷新一次运行中的任务
    if ($('.badge:contains("运行中")').length > 0) {
        setInterval(refreshRunningTasks, 10000);
    }
});

// 手动启动任务
function startTask(taskId) {
    if (confirm('确定要手动启动此任务吗？')) {
        $.post('/data/api/start_task/' + taskId)
            .done(function(response) {
                if (response.success) {
                    alert('任务启动成功！');
                    location.reload();
                } else {
                    alert('任务启动失败: ' + response.message);
                }
            })
            .fail(function(xhr, status, error) {
                console.log('请求失败:', xhr.responseText);
                console.log('状态:', status, '错误:', error);
                alert('请求失败，请稍后重试。错误: ' + error);
            });
    }
}

// 停止任务
function stopTask(taskId) {
    if (confirm('确定要强制停止此任务吗？')) {
        $.post('/data/api/stop_task/' + taskId)
            .done(function(response) {
                if (response.success) {
                    alert('任务已停止！');
                    location.reload();
                } else {
                    alert('停止任务失败: ' + response.message);
                }
            })
            .fail(function(xhr, status, error) {
                console.log('请求失败:', xhr.responseText);
                console.log('状态:', status, '错误:', error);
                alert('请求失败，请稍后重试。错误: ' + error);
            });
    }
}
</script>
{% endblock %}
