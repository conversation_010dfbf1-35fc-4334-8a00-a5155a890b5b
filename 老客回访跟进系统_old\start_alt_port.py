from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
import sqlite3
import os

app = Flask(__name__)
app.secret_key = 'your-secret-key'

# 数据库路径
DATABASE = 'customers.db'

def get_db_connection():
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/customers')
def customers():
    conn = get_db_connection()
    customers = conn.execute('SELECT * FROM customers').fetchall()
    conn.close()
    return render_template('customers.html', customers=customers)

@app.route('/customer/edit/<int:id>', methods=['GET', 'POST'])
def edit_customer(id):
    try:
        conn = get_db_connection()
        
        if request.method == 'POST':
            # 处理表单提交
            name = request.form['name']
            phone = request.form['phone']
            email = request.form['email']
            address = request.form['address']
            
            conn.execute('UPDATE customers SET name = ?, phone = ?, email = ?, address = ? WHERE id = ?',
                        (name, phone, email, address, id))
            conn.commit()
            conn.close()
            
            flash('客户信息更新成功！')
            return redirect(url_for('customers'))
        else:
            # 显示编辑表单
            customer = conn.execute('SELECT * FROM customers WHERE id = ?', (id,)).fetchone()
            conn.close()
            
            if customer is None:
                flash('客户不存在！')
                return redirect(url_for('customers'))
            
            return render_template('edit_customer.html', customer=customer)
    except sqlite3.Error as e:
        # 处理数据库连接异常
        if 'conn' in locals():
            conn.close()
        print(f"Database error in edit_customer: {str(e)}")
        flash('数据库连接错误，请稍后重试！')
        return redirect(url_for('customers'))
    except Exception as e:
        # 处理其他异常
        if 'conn' in locals():
            conn.close()
        # 记录错误日志
        print(f"Error in edit_customer: {str(e)}")
        flash('服务器内部错误，请稍后重试！')
        return redirect(url_for('customers'))

if __name__ == '__main__':
    # 检查模板文件是否存在
    template_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'templates')
    required_templates = ['index.html', 'customers.html', 'edit_customer.html']
    missing_templates = []
    
    for template in required_templates:
        template_path = os.path.join(template_dir, template)
        if not os.path.exists(template_path):
            missing_templates.append(template)
    
    if missing_templates:
        print("错误: 找不到以下模板文件:")
        for template in missing_templates:
            print(f"  - templates/{template}")
        print("请确保所有模板文件都存在于正确位置")
        exit(1)
    
    app.run(debug=True, port=5001)