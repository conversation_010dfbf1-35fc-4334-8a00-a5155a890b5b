<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试数据源API</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>测试数据源API</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>正向盘数据源</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary" onclick="testForwardAPI()">测试正向盘API</button>
                        <div id="forwardResult" class="mt-3"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>结果盘数据源</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary" onclick="testResultAPI()">测试结果盘API</button>
                        <div id="resultResult" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function testForwardAPI() {
            const resultDiv = document.getElementById('forwardResult');
            resultDiv.innerHTML = '<div class="spinner-border" role="status"></div> 正在测试...';
            
            fetch('/rfm/api/forward-data-sources')
                .then(response => {
                    console.log('正向盘API响应状态:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('正向盘API响应数据:', data);
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h6>API调用成功</h6>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                })
                .catch(error => {
                    console.error('正向盘API错误:', error);
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h6>API调用失败</h6>
                            <p>${error.message}</p>
                        </div>
                    `;
                });
        }

        function testResultAPI() {
            const resultDiv = document.getElementById('resultResult');
            resultDiv.innerHTML = '<div class="spinner-border" role="status"></div> 正在测试...';
            
            fetch('/rfm/api/result-data-sources')
                .then(response => {
                    console.log('结果盘API响应状态:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('结果盘API响应数据:', data);
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h6>API调用成功</h6>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                })
                .catch(error => {
                    console.error('结果盘API错误:', error);
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h6>API调用失败</h6>
                            <p>${error.message}</p>
                        </div>
                    `;
                });
        }
    </script>
</body>
</html>