<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Select2测试页面</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
    
    <style>
        .select2-container--bootstrap-5 .select2-selection {
            min-height: 38px;
        }
        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
            line-height: 36px;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h2>Select2搜索功能测试</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="consultant_select" class="form-label">现场顾问</label>
                    <select class="form-select" id="consultant_select" name="consultant_select">
                        <option value="">请选择现场顾问</option>
                    </select>
                    <div class="form-text">支持输入姓名或拼音首字母搜索</div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="channel_select" class="form-label">激活渠道</label>
                    <select class="form-select" id="channel_select" name="channel_select">
                        <option value="">请选择激活渠道</option>
                    </select>
                    <div class="form-text">支持输入渠道名称或拼音首字母搜索</div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h4>测试说明</h4>
                <ul>
                    <li>现场顾问搜索：输入"text"或"t"应该能找到顾问</li>
                    <li>渠道搜索：输入"百度"、"bd"、"推广"、"tg"应该能找到相关渠道</li>
                    <li>如果没有数据显示，请检查API接口是否正常</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <script>
        $(document).ready(function() {
            console.log('页面加载完成，初始化Select2...');
            
            // 初始化现场顾问选择框
            $('#consultant_select').select2({
                theme: 'bootstrap-5',
                placeholder: '请选择现场顾问',
                allowClear: true,
                ajax: {
                    url: '/api/consultants',
                    dataType: 'json',
                    delay: 250,
                    data: function (params) {
                        console.log('现场顾问搜索参数:', params.term);
                        return {
                            search: params.term || ''
                        };
                    },
                    processResults: function (data) {
                        console.log('现场顾问API响应:', data);
                        if (data.success) {
                            return {
                                results: data.data.map(function(item) {
                                    return {
                                        id: item.id,
                                        text: item.text,
                                        name: item.name,
                                        department: item.department,
                                        pinyin: item.pinyin
                                    };
                                })
                            };
                        }
                        return { results: [] };
                    },
                    cache: true
                },
                minimumInputLength: 0
            });
            
            // 初始化激活渠道选择框
            $('#channel_select').select2({
                theme: 'bootstrap-5',
                placeholder: '请选择激活渠道',
                allowClear: true,
                ajax: {
                    url: '/api/channels',
                    dataType: 'json',
                    delay: 250,
                    data: function (params) {
                        console.log('渠道搜索参数:', params.term);
                        return {
                            search: params.term || ''
                        };
                    },
                    processResults: function (data) {
                        console.log('渠道API响应:', data);
                        if (data.success) {
                            return {
                                results: data.data.map(function(item) {
                                    return {
                                        id: item.id,
                                        text: item.text,
                                        category: item.category,
                                        name: item.name,
                                        pinyin: item.pinyin
                                    };
                                })
                            };
                        }
                        return { results: [] };
                    },
                    cache: true
                },
                minimumInputLength: 0
            });
            
            console.log('Select2初始化完成');
        });
    </script>
</body>
</html>
