# RFM报表处理系统 - 项目结构说明

## 项目目录结构

```
rfm_report_system/
├── app/                          # 应用主目录
│   ├── __init__.py              # 应用包初始化
│   ├── models.py                # 数据库模型定义
│   ├── auth/                    # 用户认证模块
│   │   ├── __init__.py
│   │   ├── forms.py            # 认证相关表单
│   │   └── routes.py           # 认证路由
│   ├── main/                    # 主页面模块
│   │   ├── __init__.py
│   │   └── routes.py           # 主页面路由
│   ├── config_mgmt/             # 配置管理模块
│   │   ├── __init__.py
│   │   ├── forms.py            # 配置管理表单
│   │   └── routes.py           # 配置管理路由
│   ├── data_processing/         # 数据处理模块
│   │   ├── __init__.py
│   │   ├── forms.py            # 数据处理表单
│   │   ├── routes.py           # 数据处理路由
│   │   └── processor.py        # 核心数据处理引擎
│   └── templates/               # 模板文件
│       ├── base.html           # 基础模板
│       ├── auth/               # 认证模板
│       │   ├── login.html
│       │   └── change_password.html
│       ├── main/               # 主页面模板
│       │   └── index.html
│       ├── config_mgmt/        # 配置管理模板
│       │   ├── list_configs.html
│       │   ├── add_config.html
│       │   └── edit_config.html
│       ├── data_processing/    # 数据处理模板
│       │   ├── new_task.html
│       │   ├── task_history.html
│       │   └── task_detail.html
│       └── errors/             # 错误页面模板
│           ├── 404.html
│           └── 500.html
├── logs/                        # 日志文件目录
├── uploads/                     # 上传文件目录
│   └── results/                # 处理结果文件
├── app.py                      # Flask应用工厂
├── config.py                   # 应用配置
├── run.py                      # 应用启动脚本
├── init_db.py                  # 数据库初始化脚本
├── manage.py                   # 系统管理脚本
├── reset_password.py           # 密码重置脚本
├── check_system.py             # 系统环境检查脚本
├── install.py                  # 自动安装脚本
├── start.bat                   # Windows启动脚本
├── start.sh                    # Linux/Mac启动脚本
├── requirements.txt            # Python依赖包列表
├── .env.example               # 环境配置示例文件
├── README.md                  # 项目说明文档
└── PROJECT_STRUCTURE.md       # 项目结构说明（本文件）
```

## 核心文件说明

### 应用核心文件

- **app.py**: Flask应用工厂，负责创建和配置Flask应用实例
- **config.py**: 应用配置类，包含数据库连接、安全设置等配置
- **run.py**: 应用启动入口，用于开发环境启动

### 数据库相关

- **app/models.py**: 定义所有数据库模型
  - `User`: 用户模型
  - `DatabaseConfig`: 数据库配置模型
  - `DataProcessingTask`: 数据处理任务模型
  - `SystemSettings`: 系统设置模型

### 业务模块

#### 用户认证模块 (app/auth/)
- **forms.py**: 登录、注册、修改密码表单
- **routes.py**: 用户认证相关路由处理

#### 主页面模块 (app/main/)
- **routes.py**: 首页、仪表板等主要页面路由

#### 配置管理模块 (app/config_mgmt/)
- **forms.py**: 数据库配置表单
- **routes.py**: 数据库配置的增删改查路由

#### 数据处理模块 (app/data_processing/)
- **forms.py**: 数据处理任务表单
- **routes.py**: 任务管理相关路由
- **processor.py**: 核心数据处理引擎，实现RFM分析逻辑

### 管理脚本

- **init_db.py**: 数据库初始化，创建管理员账户
- **manage.py**: 系统管理工具，提供用户管理、任务清理等功能
- **reset_password.py**: 密码重置工具
- **check_system.py**: 系统环境检查工具
- **install.py**: 自动安装配置脚本

### 启动脚本

- **start.bat**: Windows一键启动脚本
- **start.sh**: Linux/Mac一键启动脚本

## 数据库设计

### 系统管理数据库 (rfm_office)

#### users 表 - 用户信息
- `id`: 主键
- `username`: 用户名
- `password_hash`: 密码哈希
- `salt`: 密码盐值
- `created_at`: 创建时间
- `last_login`: 最后登录时间
- `is_active`: 是否激活

#### database_configs 表 - 数据库配置
- `id`: 主键
- `name`: 配置名称
- `host`: 数据库主机
- `port`: 端口
- `username`: 用户名
- `password_encrypted`: 加密密码
- `database_name`: 数据库名
- `is_active`: 是否激活
- `created_at`: 创建时间
- `updated_at`: 更新时间
- `created_by`: 创建者ID

#### data_processing_tasks 表 - 数据处理任务
- `id`: 主键
- `task_name`: 任务名称
- `task_type`: 任务类型 (forward/result)
- `parameters`: 任务参数 (JSON)
- `status`: 状态 (pending/running/completed/failed)
- `progress`: 进度百分比
- `result_summary`: 结果摘要 (JSON)
- `error_message`: 错误信息
- `output_file_path`: 输出文件路径
- `created_at`: 创建时间
- `started_at`: 开始时间
- `completed_at`: 完成时间
- `created_by`: 创建者ID

#### system_settings 表 - 系统设置
- `id`: 主键
- `key`: 设置键
- `value`: 设置值
- `description`: 描述
- `created_at`: 创建时间
- `updated_at`: 更新时间

### 业务数据库

#### 季度客户表 (如: 24Q3_customer_data)
- `会员卡号`: 会员标识 (必需)
- 其他客户相关字段

#### 客户执行明细表
- `执行日期`: 消费日期 (必需)
- `一级分类`: 科室分类 (必需)
- `二级分类`: 品类 (必需)
- `三级分类`: 品项 (必需)
- `执行业绩（真实金额）`: 消费金额 (必需)
- `会员卡号`: 会员标识 (必需)

## 数据处理流程

### 1. 基础缓存表创建
- 根据处理类型收集目标会员
- 合并季度表数据，添加表名前缀

### 2. 季度业绩计算
- 按季度汇总每个会员的执行业绩
- 生成 `{季度}_执行业绩` 列

### 3. 科室业绩计算
- 按科室和季度汇总业绩
- 生成 `{季度}_{科室}_执行业绩` 列

### 4. TOP排名计算
- 计算滚动一年窗口内的TOP排名
- 生成品类TOP5和品项TOP15列
- 填充每个会员在各TOP项目的业绩

## 安全特性

### 密码安全
- 用户密码使用bcrypt加盐哈希
- 数据库密码使用Fernet对称加密

### 访问控制
- 基于Flask-Login的会话管理
- 用户只能访问自己创建的任务
- CSRF保护

### 数据安全
- 敏感信息加密存储
- 文件访问权限控制
- 日志记录用户操作

## 扩展说明

### 添加新功能模块
1. 在 `app/` 下创建新模块目录
2. 创建 `__init__.py`, `routes.py`, `forms.py`
3. 在 `app.py` 中注册蓝图
4. 添加相应的模板文件

### 添加新数据模型
1. 在 `app/models.py` 中定义新模型
2. 运行数据库迁移（如使用Flask-Migrate）
3. 更新相关的表单和路由

### 自定义数据处理逻辑
1. 修改 `app/data_processing/processor.py`
2. 添加新的处理方法
3. 更新任务表单和参数处理

## 部署建议

### 开发环境
- 使用 `python run.py` 启动
- 启用调试模式
- 使用SQLite或本地MySQL

### 生产环境
- 使用WSGI服务器 (如Gunicorn)
- 配置反向代理 (如Nginx)
- 使用生产级MySQL数据库
- 配置日志轮转
- 设置定期备份

### 性能优化
- 数据库索引优化
- 连接池配置
- 缓存策略
- 异步任务队列 (如Celery)

---

更多详细信息请参考 README.md 文件。
