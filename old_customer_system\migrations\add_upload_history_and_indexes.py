#!/usr/bin/env python3
"""
添加上传历史表和唯一索引
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, db
from app.models import UploadHistory
from sqlalchemy import text


def create_upload_history_table():
    """创建上传历史表"""
    print("创建上传历史表...")
    
    try:
        # 创建表
        db.create_all()
        print("✅ 上传历史表创建成功")
        return True
    except Exception as e:
        print(f"❌ 创建上传历史表失败: {str(e)}")
        return False


def add_unique_indexes():
    """添加唯一索引"""
    print("添加唯一索引...")

    try:
        # 检查并添加到院记录唯一索引
        try:
            visit_index_sql = """
            CREATE UNIQUE INDEX uk_visit_member_date
            ON visit_records (member_card_number, visit_date)
            """
            db.session.execute(text(visit_index_sql))
            print("✅ 到院记录唯一索引添加成功")
        except Exception as e:
            if "Duplicate key name" in str(e) or "already exists" in str(e):
                print("ℹ️  到院记录唯一索引已存在")
            else:
                print(f"⚠️  到院记录唯一索引添加失败: {str(e)}")

        # 检查并添加消费记录唯一索引
        try:
            consumption_index_sql = """
            CREATE UNIQUE INDEX uk_consumption_member_date_item
            ON consumption_records (member_card_number, consumption_date, item_name, consumption_time)
            """
            db.session.execute(text(consumption_index_sql))
            print("✅ 消费记录唯一索引添加成功")
        except Exception as e:
            if "Duplicate key name" in str(e) or "already exists" in str(e):
                print("ℹ️  消费记录唯一索引已存在")
            else:
                print(f"⚠️  消费记录唯一索引添加失败: {str(e)}")

        db.session.commit()
        print("✅ 唯一索引处理完成")
        return True

    except Exception as e:
        print(f"❌ 添加唯一索引失败: {str(e)}")
        db.session.rollback()
        return False


def add_performance_indexes():
    """添加性能优化索引"""
    print("添加性能优化索引...")

    try:
        # 为上传历史表添加索引
        upload_history_indexes = [
            ("idx_upload_history_user_type", "CREATE INDEX idx_upload_history_user_type ON upload_history (user_id, upload_type)"),
            ("idx_upload_history_date_range", "CREATE INDEX idx_upload_history_date_range ON upload_history (date_range_start, date_range_end)"),
            ("idx_upload_history_upload_time", "CREATE INDEX idx_upload_history_upload_time ON upload_history (upload_time DESC)")
        ]

        # 为记录表添加复合索引
        record_indexes = [
            ("idx_visit_date_member", "CREATE INDEX idx_visit_date_member ON visit_records (visit_date, member_card_number)"),
            ("idx_consumption_date_member", "CREATE INDEX idx_consumption_date_member ON consumption_records (consumption_date, member_card_number)"),
            ("idx_visit_created_by", "CREATE INDEX idx_visit_created_by ON visit_records (created_by, visit_date)"),
            ("idx_consumption_created_by", "CREATE INDEX idx_consumption_created_by ON consumption_records (created_by, consumption_date)")
        ]

        # 执行所有索引创建
        all_indexes = upload_history_indexes + record_indexes
        for index_name, index_sql in all_indexes:
            try:
                db.session.execute(text(index_sql))
                print(f"✅ 索引 {index_name} 添加成功")
            except Exception as e:
                if "Duplicate key name" in str(e) or "already exists" in str(e):
                    print(f"ℹ️  索引 {index_name} 已存在")
                else:
                    print(f"⚠️  索引 {index_name} 添加失败: {str(e)}")

        db.session.commit()
        print("✅ 性能优化索引处理完成")
        return True

    except Exception as e:
        print(f"❌ 添加性能优化索引失败: {str(e)}")
        db.session.rollback()
        return False


def check_existing_duplicates():
    """检查现有重复数据"""
    print("检查现有重复数据...")
    
    try:
        # 检查到院记录重复
        visit_duplicates_sql = """
        SELECT member_card_number, visit_date, COUNT(*) as count
        FROM visit_records 
        GROUP BY member_card_number, visit_date 
        HAVING COUNT(*) > 1
        LIMIT 10
        """
        
        visit_duplicates = db.session.execute(text(visit_duplicates_sql)).fetchall()
        
        if visit_duplicates:
            print(f"⚠️  发现 {len(visit_duplicates)} 组重复的到院记录:")
            for dup in visit_duplicates[:5]:
                print(f"   会员卡号: {dup[0]}, 日期: {dup[1]}, 重复次数: {dup[2]}")
            if len(visit_duplicates) > 5:
                print(f"   ... 还有 {len(visit_duplicates) - 5} 组重复记录")
        else:
            print("✅ 到院记录无重复数据")
        
        # 检查消费记录重复
        consumption_duplicates_sql = """
        SELECT member_card_number, consumption_date, item_name, COUNT(*) as count
        FROM consumption_records 
        GROUP BY member_card_number, consumption_date, item_name, consumption_time
        HAVING COUNT(*) > 1
        LIMIT 10
        """
        
        consumption_duplicates = db.session.execute(text(consumption_duplicates_sql)).fetchall()
        
        if consumption_duplicates:
            print(f"⚠️  发现 {len(consumption_duplicates)} 组重复的消费记录:")
            for dup in consumption_duplicates[:5]:
                print(f"   会员卡号: {dup[0]}, 日期: {dup[1]}, 项目: {dup[2]}, 重复次数: {dup[3]}")
            if len(consumption_duplicates) > 5:
                print(f"   ... 还有 {len(consumption_duplicates) - 5} 组重复记录")
        else:
            print("✅ 消费记录无重复数据")
            
        return len(visit_duplicates) == 0 and len(consumption_duplicates) == 0
        
    except Exception as e:
        print(f"❌ 检查重复数据失败: {str(e)}")
        return False


def main():
    """主函数"""
    print("=== 数据库迁移：添加上传历史表和唯一索引 ===\n")
    
    app = create_app('development')
    
    with app.app_context():
        # 1. 检查现有重复数据
        has_no_duplicates = check_existing_duplicates()
        
        if not has_no_duplicates:
            print("\n⚠️  警告：发现重复数据！")
            print("建议先清理重复数据，或者使用批量UPSERT模式处理现有数据。")
            print("是否继续添加唯一索引？(y/n): ", end="")
            # 在实际使用中，这里可以添加用户输入确认
            # 为了演示，我们继续执行
            print("y (自动继续)")
        
        # 2. 创建上传历史表
        if not create_upload_history_table():
            print("❌ 迁移失败")
            return False
        
        # 3. 添加性能优化索引（先添加这些，因为它们不会因重复数据失败）
        if not add_performance_indexes():
            print("❌ 迁移失败")
            return False
        
        # 4. 尝试添加唯一索引（如果有重复数据可能会失败）
        if not add_unique_indexes():
            print("⚠️  唯一索引添加失败，可能是因为存在重复数据")
            print("系统将使用应用层去重逻辑来处理重复数据")
        
        print("\n✅ 数据库迁移完成！")
        print("\n📊 迁移总结:")
        print("   ✅ 上传历史表已创建")
        print("   ✅ 性能优化索引已添加")
        print("   ⚠️  唯一索引状态：请检查上述输出")
        print("\n🚀 现在可以使用组合上传方案了！")
        
        return True


if __name__ == "__main__":
    main()
