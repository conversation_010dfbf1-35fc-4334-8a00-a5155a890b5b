# 故障排除指南

## 🔧 常见安装问题

### 问题1：celery版本冲突
**错误信息**：
```
ERROR: No matching distribution found for celery==5.3.2
```

**解决方案**：
celery是可选依赖，不影响核心功能。已从核心依赖中移除。
```bash
# 使用更新后的requirements.txt
pip install -r requirements.txt

# 如需celery功能，单独安装
pip install celery
```

### 问题2：依赖包版本冲突
**错误信息**：版本不兼容或找不到指定版本

**解决方案**：
```bash
# 方法1：使用自动安装脚本
python start.py

# 方法2：使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 方法3：逐个安装核心包
pip install flask flask-sqlalchemy flask-login pymysql pandas openpyxl
```

### 问题3：网络连接问题
**错误信息**：连接超时或下载失败

**解决方案**：
```bash
# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 或使用阿里云镜像
pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/

# 或使用豆瓣镜像
pip install -r requirements.txt -i https://pypi.douban.com/simple/
```

### 问题4：权限问题
**错误信息**：Permission denied 或 Access denied

**解决方案**：
```bash
# Windows: 以管理员身份运行命令提示符
# Linux/Mac: 使用用户安装
pip install --user -r requirements.txt

# 或使用虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows
pip install -r requirements.txt
```

## 🗄️ 数据库问题

### 问题1：数据库连接失败
**错误信息**：
```
Can't connect to MySQL server
Access denied for user
```

**解决方案**：
1. 检查MySQL服务是否启动
2. 确认数据库连接信息：
   ```python
   # 编辑 config.py
   MYSQL_HOST = 'localhost'     # 数据库主机
   MYSQL_PORT = 3306           # 数据库端口
   MYSQL_USER = 'your_user'    # 数据库用户名
   MYSQL_PASSWORD = 'your_pwd' # 数据库密码
   ```
3. 测试数据库连接：
   ```bash
   mysql -h localhost -u your_user -p
   ```

### 问题2：数据库不存在
**错误信息**：
```
Unknown database 'Old_Customer_System'
```

**解决方案**：
```bash
# 运行初始化脚本会自动创建数据库
python init_db.py

# 或手动创建
mysql -u root -p
CREATE DATABASE Old_Customer_System CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 问题3：表不存在
**错误信息**：
```
Table 'Old_Customer_System.user_accounts' doesn't exist
```

**解决方案**：
```bash
# 重新初始化数据库
python init_db.py
```

## 🚀 启动问题

### 问题1：端口被占用
**错误信息**：
```
Address already in use
```

**解决方案**：
```bash
# 方法1：更改端口
# 编辑启动脚本，修改端口号
app.run(host='0.0.0.0', port=5001)

# 方法2：终止占用进程
# Windows:
netstat -ano | findstr :5000
taskkill /PID <进程ID> /F

# Linux/Mac:
lsof -i :5000
kill -9 <进程ID>
```

### 问题2：模块导入失败
**错误信息**：
```
ModuleNotFoundError: No module named 'app'
```

**解决方案**：
```bash
# 确保在项目根目录运行
cd 老客回访跟进系统
python run.py

# 检查Python路径
python -c "import sys; print(sys.path)"
```

### 问题3：配置文件错误
**错误信息**：配置相关错误

**解决方案**：
```bash
# 检查config.py语法
python -c "from config import get_config; print('配置文件正常')"

# 重置配置文件
# 参考项目中的config.py模板
```

## 🔐 权限问题

### 问题1：登录失败
**错误信息**：用户名或密码错误

**解决方案**：
```bash
# 使用默认管理员账号
用户名: admin
密码: admin123

# 重置管理员密码
python init_db.py  # 会重新创建默认管理员
```

### 问题2：权限不足
**错误信息**：403 Forbidden 或权限不足

**解决方案**：
1. 检查用户角色是否正确
2. 重新初始化权限：
   ```bash
   python init_db.py
   ```

## 📊 功能问题

### 问题1：Excel导入失败
**错误信息**：文件格式不支持或解析失败

**解决方案**：
```bash
# 安装Excel支持包
pip install openpyxl xlrd

# 检查文件格式
# 支持: .xlsx, .xls
# 确保文件未损坏且格式正确
```

### 问题2：中文显示乱码
**解决方案**：
1. 确保数据库字符集为utf8mb4
2. 检查浏览器编码设置
3. 确保Excel文件编码正确

## 🛠️ 开发环境问题

### 问题1：虚拟环境问题
**解决方案**：
```bash
# 删除旧的虚拟环境
rm -rf venv  # Linux/Mac
rmdir /s venv  # Windows

# 重新创建
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows
```

### 问题2：IDE配置问题
**解决方案**：
1. 设置正确的Python解释器路径
2. 配置项目根目录
3. 安装必要的IDE插件

## 📞 获取帮助

### 自动诊断
```bash
# 运行诊断脚本
python start.py  # 会自动检查环境和依赖

# 检查系统状态
python -c "
import sys
print('Python版本:', sys.version)
print('Python路径:', sys.executable)
"
```

### 日志查看
```bash
# 查看应用日志
tail -f app.log  # Linux/Mac
type app.log     # Windows

# 查看错误详情
python run.py  # 控制台会显示详细错误信息
```

### 环境信息收集
```bash
# 收集环境信息
python -c "
import sys, platform
print('操作系统:', platform.system(), platform.release())
print('Python版本:', sys.version)
print('已安装包:')
import pkg_resources
for pkg in pkg_resources.working_set:
    print(f'  {pkg.project_name}=={pkg.version}')
"
```

## 🔄 重置系统

### 完全重置
```bash
# 1. 删除数据库
mysql -u root -p
DROP DATABASE Old_Customer_System;

# 2. 重新初始化
python init_db.py

# 3. 重启系统
python run.py
```

### 重置用户数据
```bash
# 只重置用户表，保留其他数据
# 需要手动执行SQL或重新初始化
```

---

如果以上方法都无法解决问题，请：
1. 记录完整的错误信息
2. 收集环境信息
3. 联系技术支持团队
