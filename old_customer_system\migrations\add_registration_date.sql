-- 添加客户登记时间字段的数据库迁移脚本
-- 执行日期: 2025-01-26

USE Old_Customer_System;

-- 添加registration_date字段
ALTER TABLE customers 
ADD COLUMN registration_date DATE NOT NULL DEFAULT (CURRENT_DATE) 
AFTER last_visit_date;

-- 为现有记录设置登记日期（使用created_at的日期部分）
UPDATE customers 
SET registration_date = DATE(created_at) 
WHERE registration_date IS NULL OR registration_date = '0000-00-00';

-- 创建索引以优化查询性能
CREATE INDEX idx_customers_registration_date ON customers(registration_date);

-- 验证数据迁移结果
SELECT 
    COUNT(*) as total_customers,
    COUNT(CASE WHEN registration_date IS NOT NULL THEN 1 END) as with_registration_date,
    MIN(registration_date) as earliest_registration,
    MAX(registration_date) as latest_registration
FROM customers;

-- 显示迁移完成信息
SELECT 'Registration date field migration completed successfully' as status;