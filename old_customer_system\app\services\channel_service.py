# -*- coding: utf-8 -*-
"""
渠道服务
"""

from typing import Optional, Dict, List, Tuple
import pandas as pd
from app import db
from app.models import Channel
from app.repositories.channel_repository import ChannelRepository

class ChannelService:
    """渠道业务逻辑服务"""
    
    def __init__(self):
        self.channel_repo = ChannelRepository()
    
    def create_channel(self, channel_data: Dict) -> Channel:
        """创建渠道"""
        try:
            # 检查渠道名称是否已存在
            if self.channel_repo.find_by_name(channel_data['name']):
                raise ValueError("渠道名称已存在")
            
            # 生成简码
            if not channel_data.get('simple_code'):
                channel_data['simple_code'] = self.generate_simple_code(channel_data['name'])
            
            # 创建渠道对象
            channel = Channel(
                name=channel_data['name'],
                category=channel_data.get('category'),
                simple_code=channel_data.get('simple_code'),
                is_active=channel_data.get('is_active', True)
            )
            
            # 保存到数据库
            db.session.add(channel)
            db.session.commit()
            
            return channel
            
        except Exception as e:
            db.session.rollback()
            raise e
    
    def update_channel(self, channel_id: int, channel_data: Dict) -> bool:
        """更新渠道信息"""
        try:
            channel = self.channel_repo.get_by_id(channel_id)
            if not channel:
                return False
            
            # 检查名称唯一性（排除自己）
            existing = self.channel_repo.find_by_name(channel_data['name'])
            if existing and existing.id != channel_id:
                raise ValueError("渠道名称已存在")
            
            # 更新渠道信息
            for key, value in channel_data.items():
                if hasattr(channel, key):
                    setattr(channel, key, value)
            
            db.session.commit()
            return True
            
        except Exception as e:
            db.session.rollback()
            raise e
    
    def get_channel_by_id(self, channel_id: int) -> Optional[Channel]:
        """根据ID获取渠道"""
        return self.channel_repo.get_by_id(channel_id)
    
    def get_active_channels(self) -> List[Channel]:
        """获取所有激活的渠道"""
        return self.channel_repo.get_active_channels()
    
    def search_channels(self, query: str) -> List[Channel]:
        """搜索渠道"""
        return self.channel_repo.search_by_simple_code(query)
    
    def activate_channel(self, channel_id: int) -> bool:
        """激活渠道"""
        return self.channel_repo.activate_channel(channel_id)
    
    def deactivate_channel(self, channel_id: int) -> bool:
        """停用渠道"""
        return self.channel_repo.deactivate_channel(channel_id)
    
    def import_channels_from_excel(self, file_path: str) -> Tuple[bool, str, Dict]:
        """从Excel导入渠道"""
        try:
            # 读取Excel文件
            df = pd.read_excel(file_path)
            
            # 验证必需的列
            required_columns = ['渠道名称']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                return False, f"Excel文件缺少必需的列: {', '.join(missing_columns)}", {}
            
            # 处理数据
            channels_data = []
            for index, row in df.iterrows():
                channel_data = {
                    'name': str(row['渠道名称']).strip(),
                    'category': str(row.get('渠道分类', '')).strip() or None,
                    'simple_code': str(row.get('简码', '')).strip() or None,
                    'is_active': True
                }
                
                # 跳过空行
                if not channel_data['name'] or channel_data['name'] == 'nan':
                    continue
                
                channels_data.append(channel_data)
            
            # 批量创建渠道
            created_channels = self.channel_repo.bulk_create_channels(channels_data)
            
            result = {
                'total_rows': len(df),
                'processed_rows': len(channels_data),
                'created_channels': len(created_channels),
                'skipped_rows': len(df) - len(channels_data)
            }
            
            return True, f"成功导入 {len(created_channels)} 个渠道", result
            
        except Exception as e:
            return False, f"导入失败: {str(e)}", {}
    
    def generate_simple_code(self, name: str) -> str:
        """生成简码"""
        try:
            from pypinyin import lazy_pinyin, Style
            import re
            
            # 获取拼音首字母
            pinyin_list = lazy_pinyin(name, style=Style.FIRST_LETTER)
            simple_code = ''.join(pinyin_list).upper()
            
            # 如果简码为空或太短，使用原名称
            if len(simple_code) < 2:
                # 移除特殊字符，保留中文、英文和数字
                clean_name = re.sub(r'[^\u4e00-\u9fff\w]', '', name)
                simple_code = clean_name.upper()[:10]
            
            return simple_code[:10]  # 限制长度
        except ImportError:
            # 如果没有pypinyin库，使用简单逻辑
            import re
            clean_name = re.sub(r'[^\u4e00-\u9fff\w]', '', name)
            return clean_name.upper()[:10]
        except:
            return name.upper()[:10]
    
    def get_channel_statistics(self) -> Dict:
        """获取渠道统计信息"""
        return self.channel_repo.get_channel_statistics()