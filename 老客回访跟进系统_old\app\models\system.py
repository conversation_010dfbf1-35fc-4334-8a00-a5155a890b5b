"""
系统配置相关数据模型
"""
from datetime import datetime
from flask import request
from app import db

class SystemConfig(db.Model):
    """系统配置表"""
    __tablename__ = 'system_config'
    
    id = db.Column(db.Integer, primary_key=True)
    config_key = db.Column(db.String(100), unique=True, nullable=False, comment='配置键')
    config_value = db.Column(db.Text, comment='配置值')
    config_description = db.Column(db.Text, comment='配置描述')
    config_type = db.Column(db.String(50), default='string', comment='配置类型')
    is_active = db.Column(db.<PERSON>, default=True, comment='是否启用')
    created_time = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_time = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    updated_by = db.Column(db.Integer, db.<PERSON>('user_accounts.id'), comment='更新人ID')
    
    # 关系
    updater = db.relationship('User', backref='updated_configs', lazy=True)
    
    # 索引
    __table_args__ = (
        db.Index('idx_config_key', 'config_key'),
    )
    
    @classmethod
    def get_config(cls, key, default_value=None):
        """获取配置值"""
        config = cls.query.filter_by(config_key=key, is_active=True).first()
        if not config:
            return default_value
        
        # 根据类型转换值
        if config.config_type == 'integer':
            try:
                return int(config.config_value)
            except (ValueError, TypeError):
                return default_value
        elif config.config_type == 'float':
            try:
                return float(config.config_value)
            except (ValueError, TypeError):
                return default_value
        elif config.config_type == 'boolean':
            return config.config_value.lower() in ['true', '1', 'yes', 'on']
        else:
            return config.config_value
    
    @classmethod
    def set_config(cls, key, value, config_type='string', description=None, updated_by=None):
        """设置配置值"""
        config = cls.query.filter_by(config_key=key).first()
        
        if config:
            config.config_value = str(value)
            config.config_type = config_type
            if description:
                config.config_description = description
            config.updated_by = updated_by
            config.updated_time = datetime.utcnow()
        else:
            config = cls(
                config_key=key,
                config_value=str(value),
                config_type=config_type,
                config_description=description,
                updated_by=updated_by
            )
            db.session.add(config)
        
        db.session.commit()
        return config
    
    @classmethod
    def get_all_configs(cls):
        """获取所有配置"""
        return cls.query.filter_by(is_active=True).all()
    
    def __repr__(self):
        return f'<SystemConfig {self.config_key}={self.config_value}>'

class AuditLog(db.Model):
    """操作审计日志表"""
    __tablename__ = 'audit_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user_accounts.id'), comment='用户ID')
    action = db.Column(db.String(100), nullable=False, comment='操作动作')
    resource_type = db.Column(db.String(50), nullable=False, comment='资源类型')
    resource_id = db.Column(db.String(50), comment='资源ID')
    old_values = db.Column(db.JSON, comment='修改前的值')
    new_values = db.Column(db.JSON, comment='修改后的值')
    details = db.Column(db.Text, comment='操作详情')
    ip_address = db.Column(db.String(45), comment='IP地址')
    user_agent = db.Column(db.Text, comment='用户代理')
    created_time = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    
    # 关系
    user = db.relationship('User', backref='audit_logs', lazy=True)
    
    # 索引
    __table_args__ = (
        db.Index('idx_user_id', 'user_id'),
        db.Index('idx_action', 'action'),
        db.Index('idx_resource_type', 'resource_type'),
        db.Index('idx_created_time', 'created_time'),
        db.Index('idx_user_action_time', 'user_id', 'action', 'created_time'),
    )
    
    @classmethod
    def log_action(cls, user_id, action, resource_type, resource_id=None, 
                   old_values=None, new_values=None, details=None):
        """记录操作日志"""
        log = cls(
            user_id=user_id,
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            old_values=old_values,
            new_values=new_values,
            details=details,
            ip_address=request.remote_addr if request else None,
            user_agent=request.user_agent.string if request else None
        )
        
        db.session.add(log)
        db.session.commit()
        return log
    
    @classmethod
    def get_user_logs(cls, user_id, limit=100):
        """获取用户操作日志"""
        return cls.query.filter_by(user_id=user_id).order_by(
            cls.created_time.desc()
        ).limit(limit).all()
    
    @classmethod
    def get_resource_logs(cls, resource_type, resource_id, limit=50):
        """获取资源操作日志"""
        return cls.query.filter_by(
            resource_type=resource_type,
            resource_id=resource_id
        ).order_by(cls.created_time.desc()).limit(limit).all()
    
    def __repr__(self):
        return f'<AuditLog {self.action} on {self.resource_type}>'

class DataImportLog(db.Model):
    """数据导入日志表"""
    __tablename__ = 'data_import_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    import_type = db.Column(db.String(50), nullable=False, comment='导入类型')
    file_name = db.Column(db.String(255), comment='文件名')
    import_batch = db.Column(db.String(100), comment='导入批次号')
    total_rows = db.Column(db.Integer, default=0, comment='总行数')
    success_rows = db.Column(db.Integer, default=0, comment='成功行数')
    error_rows = db.Column(db.Integer, default=0, comment='错误行数')
    error_details = db.Column(db.JSON, comment='错误详情')
    status = db.Column(db.String(20), default='PROCESSING', comment='状态')
    start_time = db.Column(db.DateTime, default=datetime.utcnow, comment='开始时间')
    end_time = db.Column(db.DateTime, comment='结束时间')
    imported_by = db.Column(db.Integer, db.ForeignKey('user_accounts.id'), comment='导入人ID')
    
    # 关系
    importer = db.relationship('User', backref='import_logs', lazy=True)
    
    # 索引
    __table_args__ = (
        db.Index('idx_import_type', 'import_type'),
        db.Index('idx_import_batch', 'import_batch'),
        db.Index('idx_imported_by', 'imported_by'),
        db.Index('idx_start_time', 'start_time'),
    )
    
    def mark_completed(self, success_count, error_count, error_details=None):
        """标记导入完成"""
        self.success_rows = success_count
        self.error_rows = error_count
        self.error_details = error_details
        self.status = 'COMPLETED' if error_count == 0 else 'COMPLETED_WITH_ERRORS'
        self.end_time = datetime.utcnow()
        db.session.commit()
    
    def mark_failed(self, error_message):
        """标记导入失败"""
        self.status = 'FAILED'
        self.error_details = {'error': error_message}
        self.end_time = datetime.utcnow()
        db.session.commit()
    
    @property
    def duration(self):
        """获取导入耗时"""
        if self.end_time and self.start_time:
            return (self.end_time - self.start_time).total_seconds()
        return None
    
    @property
    def success_rate(self):
        """获取成功率"""
        if self.total_rows > 0:
            return (self.success_rows / self.total_rows) * 100
        return 0
    
    def __repr__(self):
        return f'<DataImportLog {self.import_type} - {self.status}>'
