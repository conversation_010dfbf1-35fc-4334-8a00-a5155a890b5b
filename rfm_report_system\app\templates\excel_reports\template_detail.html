{% extends "base.html" %}

{% block title %}{{ template_name }} - 报表模板详情{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-chart-bar me-2 text-primary"></i>{{ template_name }}</h2>
                    <p class="text-muted">{{ template.description }}</p>
                </div>
                <div>
                    <a href="{{ url_for('excel_reports.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>返回列表
                    </a>
                    <button class="btn btn-success" onclick="generateReport('{{ template_name }}')">
                        <i class="fas fa-download me-2"></i>生成报表
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 模板信息 -->
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle me-2"></i>模板信息</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-chart-line me-2"></i>功能说明</h6>
                        <p class="mb-0">{{ template.description }}</p>
                    </div>

                    {% if template.features %}
                    <div class="mb-4">
                        <h6><i class="fas fa-star me-2"></i>核心特性</h6>
                        <ul class="list-group list-group-flush">
                            {% for feature in template.features %}
                            <li class="list-group-item border-0 px-0">
                                <i class="fas fa-check text-success me-2"></i>{{ feature }}
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}

                    {% if template.columns %}
                    <div class="mb-4">
                        <h6><i class="fas fa-columns me-2"></i>输出字段</h6>
                        <div class="row">
                            {% for column in template.columns %}
                            <div class="col-md-6 col-lg-4 mb-2">
                                <span class="badge bg-secondary">{{ column }}</span>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-play me-2"></i>操作面板</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" onclick="previewReport('{{ template_name }}')">
                            <i class="fas fa-eye me-2"></i>预览数据
                        </button>
                        <button class="btn btn-success" onclick="generateReport('{{ template_name }}')">
                            <i class="fas fa-download me-2"></i>生成报表
                        </button>
                    </div>

                    <hr>

                    <div class="small text-muted">
                        <h6>数据说明</h6>
                        <ul class="mb-0">
                            <li>数据来源：客户执行明细表</li>
                            <li>数据范围：最近1-2年</li>
                            <li>更新频率：实时</li>
                            <li>输出格式：Excel文件</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

<!-- 预览模态框 -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">报表预览 - {{ template_name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="previewContent">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在生成预览...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-success" onclick="generateReport('{{ template_name }}')">生成完整报表</button>
            </div>
        </div>
    </div>
</div>

<!-- 加载提示模态框 -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">处理中...</span>
                </div>
                <p id="loadingMessage">正在生成报表，请稍候...</p>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
<script>
// 预览报表
function previewReport(templateName) {
    $('#previewModal').modal('show');

    $.ajax({
        url: '{{ url_for("excel_reports.preview_report") }}',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({template_name: templateName}),
        success: function(response) {
            if (response.success) {
                displayPreview(response.preview);
            } else {
                $('#previewContent').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        预览失败: ${response.message}
                    </div>
                `);
            }
        },
        error: function() {
            $('#previewContent').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    预览请求失败，请稍后重试
                </div>
            `);
        }
    });
}

// 显示预览数据
function displayPreview(preview) {
    let html = `
        <div class="mb-3">
            <span class="badge bg-info">总计 ${preview.total_rows} 行数据</span>
            <span class="badge bg-secondary">预览前 10 行</span>
        </div>
        <div class="table-responsive">
            <table class="table table-striped table-sm">
                <thead class="table-dark">
                    <tr>
    `;

    // 表头
    preview.columns.forEach(col => {
        html += `<th>${col}</th>`;
    });
    html += '</tr></thead><tbody>';

    // 数据行
    preview.data.forEach(row => {
        html += '<tr>';
        row.forEach(cell => {
            html += `<td>${cell !== null ? cell : ''}</td>`;
        });
        html += '</tr>';
    });

    html += '</tbody></table></div>';
    $('#previewContent').html(html);
}

// 生成报表
function generateReport(templateName) {
    $('#loadingModal').modal('show');

    $.ajax({
        url: `{{ url_for("excel_reports.generate_report", template_name="TEMPLATE") }}`.replace('TEMPLATE', templateName),
        method: 'POST',
        success: function(response) {
            $('#loadingModal').modal('hide');

            if (response.success) {
                // 显示成功消息并提供下载链接
                Swal.fire({
                    icon: 'success',
                    title: '报表生成成功！',
                    text: response.message,
                    showCancelButton: true,
                    confirmButtonText: '立即下载',
                    cancelButtonText: '稍后下载'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = response.download_url;
                    }
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: '生成失败',
                    text: response.message
                });
            }
        },
        error: function() {
            $('#loadingModal').modal('hide');
            Swal.fire({
                icon: 'error',
                title: '请求失败',
                text: '报表生成请求失败，请稍后重试'
            });
        }
    });
}
</script>
{% endblock %}
