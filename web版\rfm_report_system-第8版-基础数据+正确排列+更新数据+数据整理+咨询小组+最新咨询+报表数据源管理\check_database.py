#!/usr/bin/env python3
"""
检查业务数据库中的表结构
"""

import re
from app import create_app
from app.models import DatabaseConfig

def check_business_database():
    """检查业务数据库"""
    app = create_app()
    
    with app.app_context():
        # 获取激活的数据库配置
        active_config = DatabaseConfig.get_active_config()
        
        if not active_config:
            print("❌ 没有激活的数据库配置")
            print("请先在系统中添加并激活一个数据库配置")
            return
        
        print(f"✅ 找到激活的数据库配置: {active_config.name}")
        print(f"   数据库: {active_config.host}:{active_config.port}/{active_config.database_name}")
        print(f"   用户: {active_config.username}")
        
        # 直接连接数据库获取表列表
        try:
            import pymysql
            connection = pymysql.connect(
                host=active_config.host,
                port=active_config.port,
                user=active_config.username,
                password=active_config.get_password(),
                database=active_config.database_name,
                charset='utf8mb4'
            )

            print("✅ 成功连接到业务数据库")

            # 获取所有表
            with connection.cursor() as cursor:
                cursor.execute("SHOW TABLES")
                all_tables = [row[0] for row in cursor.fetchall()]

            connection.close()

        except Exception as e:
            print(f"❌ 无法连接到业务数据库: {e}")
            return
        print(f"\n📋 数据库中共有 {len(all_tables)} 个表:")
        
        if not all_tables:
            print("   (没有找到任何表)")
            return
        
        # 分类显示表
        quarter_tables = []
        customer_tables = []
        detail_tables = []
        other_tables = []
        
        for table in all_tables:
            print(f"   - {table}")
            
            # 检查是否是季度表
            if re.match(r'\d{2}Q\d_customer_data', table):
                quarter_tables.append(table)
            elif 'customer' in table.lower():
                customer_tables.append(table)
            elif '明细' in table or 'detail' in table.lower():
                detail_tables.append(table)
            else:
                other_tables.append(table)
        
        print(f"\n🎯 表分类分析:")
        print(f"   标准季度表 (格式: 24Q3_customer_data): {len(quarter_tables)} 个")
        for table in quarter_tables:
            print(f"     ✅ {table}")
        
        print(f"   包含'customer'的表: {len(customer_tables)} 个")
        for table in customer_tables:
            print(f"     📊 {table}")
        
        print(f"   包含'明细'或'detail'的表: {len(detail_tables)} 个")
        for table in detail_tables:
            print(f"     📝 {table}")
        
        print(f"   其他表: {len(other_tables)} 个")
        for table in other_tables[:10]:  # 只显示前10个
            print(f"     📄 {table}")
        if len(other_tables) > 10:
            print(f"     ... 还有 {len(other_tables) - 10} 个表")
        
        # 检查是否有客户执行明细表
        detail_table_found = False
        for table in all_tables:
            if table == '客户执行明细表':
                detail_table_found = True
                print(f"\n✅ 找到客户执行明细表: {table}")
                break
        
        if not detail_table_found:
            print(f"\n❌ 未找到'客户执行明细表'")
            print("   系统需要一个名为'客户执行明细表'的表来进行数据分析")
        
        # 给出建议
        print(f"\n💡 建议:")
        
        if not quarter_tables:
            print("   1. 季度表命名建议:")
            print("      - 标准格式: 24Q3_customer_data (表示2024年第3季度客户数据)")
            print("      - 年份用两位数字 (如24表示2024年)")
            print("      - 季度用Q1、Q2、Q3、Q4表示")
            print("      - 后缀必须是_customer_data")
            
            if customer_tables:
                print("   2. 您可以重命名以下表为标准格式:")
                for table in customer_tables:
                    # 尝试推测应该的命名
                    suggested_name = suggest_quarter_table_name(table)
                    if suggested_name:
                        print(f"      {table} -> {suggested_name}")
        
        if not detail_table_found:
            print("   3. 请确保有一个名为'客户执行明细表'的表，包含以下字段:")
            print("      - 执行日期")
            print("      - 一级分类 (科室)")
            print("      - 二级分类 (品类)")
            print("      - 三级分类 (品项)")
            print("      - 执行业绩（真实金额）")
            print("      - 会员卡号")

def suggest_quarter_table_name(table_name):
    """根据表名推测标准的季度表名"""
    table_lower = table_name.lower()
    
    # 尝试从表名中提取年份和季度信息
    patterns = [
        r'(\d{4})[年_-]?([1-4])[季度]?',  # 2024年1季度
        r'(\d{2})[年_-]?([1-4])[季度]?',   # 24年1季度
        r'([1-4])[季度][_-]?(\d{4})',     # 1季度2024
        r'([1-4])[季度][_-]?(\d{2})',     # 1季度24
        r'q([1-4])[_-]?(\d{4})',          # Q1_2024
        r'q([1-4])[_-]?(\d{2})',          # Q1_24
    ]
    
    for pattern in patterns:
        match = re.search(pattern, table_lower)
        if match:
            if len(match.group(1)) == 4:  # 年份在前
                year = match.group(1)[-2:]  # 取后两位
                quarter = match.group(2)
            elif len(match.group(2)) == 4:  # 年份在后
                year = match.group(2)[-2:]  # 取后两位
                quarter = match.group(1)
            else:
                year = match.group(2) if len(match.group(2)) == 2 else match.group(1)
                quarter = match.group(1) if len(match.group(2)) == 2 else match.group(2)
            
            return f"{year}Q{quarter}_customer_data"
    
    return None

if __name__ == '__main__':
    try:
        check_business_database()
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
