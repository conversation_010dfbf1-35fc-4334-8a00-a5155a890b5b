# -*- coding: utf-8 -*-
"""
部门服务
"""

from typing import Optional, Dict, List, Tuple
from app import db
from app.models import Department
from app.repositories.department_repository import DepartmentRepository

class DepartmentService:
    """部门业务逻辑服务"""
    
    def __init__(self):
        self.dept_repo = DepartmentRepository()
    
    def create_department(self, dept_data: Dict) -> Department:
        """创建部门"""
        try:
            # 检查部门名称是否已存在
            if self.dept_repo.find_by_name(dept_data['name']):
                raise ValueError("部门名称已存在")
            
            # 创建部门对象
            department = Department(
                name=dept_data['name'],
                description=dept_data.get('description'),
                is_active=dept_data.get('is_active', True)
            )
            
            # 保存到数据库
            db.session.add(department)
            db.session.commit()
            
            return department
            
        except Exception as e:
            db.session.rollback()
            raise e
    
    def update_department(self, dept_id: int, dept_data: Dict) -> bool:
        """更新部门信息"""
        try:
            department = self.dept_repo.get_by_id(dept_id)
            if not department:
                return False
            
            # 检查名称唯一性（排除自己）
            existing = self.dept_repo.find_by_name(dept_data['name'])
            if existing and existing.id != dept_id:
                raise ValueError("部门名称已存在")
            
            # 更新部门信息
            for key, value in dept_data.items():
                if hasattr(department, key):
                    setattr(department, key, value)
            
            db.session.commit()
            return True
            
        except Exception as e:
            db.session.rollback()
            raise e
    
    def get_department_by_id(self, dept_id: int) -> Optional[Department]:
        """根据ID获取部门"""
        return self.dept_repo.get_by_id(dept_id)
    
    def get_department_by_name(self, name: str) -> Optional[Department]:
        """根据名称获取部门"""
        return self.dept_repo.find_by_name(name)
    
    def get_all_departments(self) -> List[Department]:
        """获取所有部门"""
        return self.dept_repo.get_all()
    
    def get_active_departments(self) -> List[Department]:
        """获取所有激活的部门"""
        return self.dept_repo.get_active_departments()
    
    def search_departments(self, query: str) -> List[Department]:
        """搜索部门"""
        return self.dept_repo.search_departments(query)
    
    def activate_department(self, dept_id: int) -> bool:
        """激活部门"""
        return self.dept_repo.activate_department(dept_id)
    
    def deactivate_department(self, dept_id: int) -> bool:
        """停用部门"""
        return self.dept_repo.deactivate_department(dept_id)
    
    def get_department_statistics(self) -> Dict:
        """获取部门统计信息"""
        return self.dept_repo.get_department_statistics()
    
    def get_department_with_users(self, dept_id: int) -> Optional[Department]:
        """获取部门及其用户信息"""
        return self.dept_repo.get_department_with_users(dept_id)
    
    def can_delete_department(self, dept_id: int) -> Tuple[bool, str]:
        """检查是否可以删除部门"""
        department = self.get_department_with_users(dept_id)
        if not department:
            return False, "部门不存在"
        
        # 检查是否有用户关联
        if department.users:
            active_users = [u for u in department.users if u.is_active]
            if active_users:
                return False, f"部门下还有 {len(active_users)} 个激活用户，无法删除"
        
        return True, "可以删除"